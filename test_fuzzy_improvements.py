#!/usr/bin/env python3
"""
Test script for the improved fuzzy matching functionality
"""

import sys
import pandas as pd
import numpy as np
from src.service_provider_fuzzy import (
    fuzzy_match_service_provider, 
    _create_empty_result,
    _is_valid_field_value,
    _process_numerical_field,
    _process_web_field
)

def test_helper_functions():
    """Test the helper functions"""
    print("Testing helper functions...")
    
    # Test _create_empty_result
    empty_result = _create_empty_result()
    assert 'has_ties' in empty_result
    assert 'tie_count' in empty_result
    assert 'tied_matches' in empty_result
    assert empty_result['has_ties'] == False
    assert empty_result['tie_count'] == 0
    print("✓ _create_empty_result works")
    
    # Test _is_valid_field_value
    assert _is_valid_field_value("test") == True
    assert _is_valid_field_value("") == False
    assert _is_valid_field_value(None) == False
    assert _is_valid_field_value(np.NaN) == False
    assert _is_valid_field_value([]) == False
    assert _is_valid_field_value(["test"]) == True
    print("✓ _is_valid_field_value works")
    
    # Test _process_numerical_field
    numerical_result = _process_numerical_field(["**********1"])
    assert isinstance(numerical_result, list)
    print("✓ _process_numerical_field works")
    
    # Test _process_web_field
    web_result = _process_web_field(["<EMAIL>"])
    assert isinstance(web_result, list)
    print("✓ _process_web_field works")

def test_empty_inputs():
    """Test with empty inputs"""
    print("\nTesting empty inputs...")
    
    # Empty DataFrame
    empty_df = pd.DataFrame()
    service_info = {'Name': 'Test Vet', 'Address': '123 Test St'}
    result = fuzzy_match_service_provider(service_info, empty_df)
    assert result['has_ties'] == False
    assert result['tie_count'] == 0
    print("✓ Empty DataFrame handled correctly")
    
    # Empty service info
    test_df = pd.DataFrame({
        'ServiceProviderNo': [1, 2],
        'ServiceProviderName': ['Vet A', 'Vet B'],
        'Address': ['123 St', '456 Ave'],
        'ABN': [**********1, **********9],
        'Email': ['', ''],
        'HomePage': ['', ''],
        'PhoneNo_Home': [**********, **********],
        'PhoneNo_Work': [**********, **********],
        'PhoneNo_Mobile': [**********, **********],
        'FaxNo': [**********, **********]
    })
    
    empty_service_info = {}
    result = fuzzy_match_service_provider(empty_service_info, test_df)
    assert result['has_ties'] == False
    assert result['tie_count'] == 0
    print("✓ Empty service info handled correctly")

def test_tie_detection():
    """Test tie detection with identical matches"""
    print("\nTesting tie detection...")
    
    # Create test DataFrame with potential ties
    test_df = pd.DataFrame({
        'ServiceProviderNo': [1, 2, 3],
        'ServiceProviderName': ['Test Vet Clinic', 'Test Vet Clinic', 'Different Vet'],
        'Address': ['123 Test St Melbourne VIC 3000', '456 Test Ave Melbourne VIC 3000', '789 Other St'],
        'ABN': [**********1, **********9, ***********],
        'Email': ['', '', ''],
        'HomePage': ['', '', ''],
        'PhoneNo_Home': [**********, **********, **********],
        'PhoneNo_Work': [**********, **********, **********],
        'PhoneNo_Mobile': [**********, **********, **********],
        'FaxNo': [**********, **********, **********]
    })
    
    # Service info that should match multiple providers
    service_info = {
        'Name': 'Test Vet Clinic',
        'Address': '123 Test St Melbourne VIC 3000',
        'abn': [],
        'email': [],
        'web': [],
        'phone_home': [],
        'phone_work': [],
        'phone_mobile': [],
        'fax': []
    }
    
    result = fuzzy_match_service_provider(service_info, test_df)
    print(f"Has ties: {result['has_ties']}")
    print(f"Tie count: {result['tie_count']}")
    print(f"Number of tied matches: {len(result['tied_matches'])}")
    
    # Should find matches (though may not be ties depending on exact scoring)
    assert 'has_ties' in result
    assert 'tie_count' in result
    assert 'tied_matches' in result
    print("✓ Tie detection structure works")

if __name__ == "__main__":
    try:
        test_helper_functions()
        test_empty_inputs()
        test_tie_detection()
        print("\n🎉 All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
