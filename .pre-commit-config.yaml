repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.2.0
    hooks:
      - id: ruff
        types_or: [ python, pyi, jupyter ]
        args: [ --fix, --show-fixes]
      - id: ruff-format
        types_or: [ python, pyi, jupyter ]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.2.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: debug-statements
        language_version: python3
      - id: name-tests-test
      - id: requirements-txt-fixer
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: detect-private-key
      - id: check-added-large-files
        args: [ --maxkb=1000 ]
      - id: mixed-line-ending

  - repo: https://github.com/econchick/interrogate
    rev: 1.5.0
    hooks:
      - id: interrogate
        args: [ src, -vv, --fail-under=80 ]
        pass_filenames: false
