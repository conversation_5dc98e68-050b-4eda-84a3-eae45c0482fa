[project]
name = "Crispy_Framework"
version = "0.0.1"
description = "A module component library for fast prototype implementation and new module development in Data Science team"
authors = [
    {name="<PERSON><PERSON>", email="<EMAIL>"},
    {name="<PERSON>", email="<EMAIL>"},
]
dependencies = [
    "fuzzywuzzy>=0.18.0",
]


[tool.ruff]
lint.ignore = ["E501"]
lint.select = ["B", "C", "E", "F", "W", "B9", "I", "Q"]
fix = true
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88

[tool.ruff.lint.mccabe]
max-complexity = 12

[tool.interrogate]
ignore-init-method = true
ignore-init-module = true
ignore-semiprivate = true
ignore-private = true
ignore-magic = true
ignore-property-decorators = true
ignore-module = true
exclude = ["src/models/predict_model.py", "src/models/train_model.py", "src/verify/classes.py"]
