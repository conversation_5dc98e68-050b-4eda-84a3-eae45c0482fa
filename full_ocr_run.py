#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR Processing Pipeline Script

This script processes documents using Azure Document Intelligence and PaddleOCR,
compares the results with UPM (Upstream Provider Management) data, and generates
evaluation metrics.

The script handles:
1. Loading data from SQL database
2. Downloading documents from blob storage
3. Processing with Azure Document Intelligence
4. Processing with PaddleOCR
5. Applying business rules
6. Comparing results with ground truth
7. Generating evaluation reports
"""

# Standard library imports
import os
import sys
import json
import pickle as pk
import time
import string
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Union
from copy import deepcopy

# Third-party imports
import pandas as pd
import numpy as np
from loguru import logger
from tqdm import tqdm
from difflib import SequenceMatcher
from fuzzywuzzy import fuzz
from dotenv import load_dotenv
from openpyxl import load_workbook

# Azure imports
from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import HttpResponseError

# OCR imports
import fitz  # PyMuPDF
from paddleocr import PaddleOCR, draw_ocr
from fitz import FileDataError

# Local imports
# Adjust the path to properly include the src directory
module_path = os.path.abspath(os.path.join("."))  # Go up one level to reach project root
if module_path not in sys.path:
    sys.path.append(module_path)

from src.sample_collection.sql_engine import *
from src.service_provider_matching.preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web

# ============================================================================
# CONFIGURATION
# ============================================================================

# Explicitly load the .env file
load_dotenv()
ROOTDIR = Path("/home/<USER>/repos/OCR_in_house")

# Sample prefix
sample_prefix = "test"

# Data location
DATA_FOLDER = ROOTDIR / f"data/samples/{sample_prefix}_samples_DI"
RAW_DATA_FILE = ROOTDIR / f"data/{sample_prefix}_samples_DI.csv"
OUTPUT_DATA_FOLDER = ROOTDIR / f"data/samples/{sample_prefix}_samples_DI_res"
UPM_GROUND_TRUTH_PATH = ROOTDIR / f"data/{sample_prefix}_SourceOfTruth.xlsx"
PADDLEOCR_RES_PATH = ROOTDIR / f"data/{sample_prefix}_samples_paddleocr.csv"
TRUUTH_PATH = ROOTDIR / f"data/{sample_prefix}_samples_truuth.csv"
RULE_RES_OUTPUT_PATH = ROOTDIR / f"data/result/{sample_prefix}_samples_DI_evaluation_raw.xlsx"

# Configure pandas display options
pd.set_option("display.max_columns", None)

# ============================================================================
# DATABASE CONNECTION
# ============================================================================

# Connect to database
engine = Engine.get_engine(server="10.3.0.90", database="BIA")

# Load sample data
samples_di = pd.read_csv(RAW_DATA_FILE)

# ============================================================================
# DATA LOADING FUNCTIONS
# ============================================================================

def load_claim_doc_path(df, engine):
    """
    Load document paths for claims from the database.
    
    Args:
        df: DataFrame containing claim numbers
        engine: SQL engine connection
        
    Returns:
        DataFrame with document paths
    """
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""
        SELECT
            c.ClaimNumber,
            d.*
        FROM [COS].[dbo].[Document] d
            LEFT JOIN [COS].[dbo].[Claim] c
            ON d.ClaimRefNumber = c.ClaimRefNumber
        WHERE [DocumentPath] LIKE '%csp%'
            AND [DocumentType] = 'ClaimInvoice'
            AND c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)

    # Return DataFrame
    return df_sot

def load_claim_truuth(df, engine):
    """
    Load claim truth data from the database.
    
    Args:
        df: DataFrame containing claim numbers
        engine: SQL engine connection
        
    Returns:
        DataFrame with claim truth data
    """
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""
        SELECT
            c.ClaimNumber,
            cc.*
        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc
            LEFT JOIN [COS].[dbo].[Claim] c
            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT
        WHERE c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_truuth = pd.read_sql(sql_query_policy, engine)

    # Return DataFrame
    return df_truuth

def load_upm_sot(df, engine):
    """
    Load UPM source of truth data from the database.
    
    Args:
        df: DataFrame containing claim numbers
        engine: SQL engine connection
        
    Returns:
        DataFrame with UPM source of truth data
    """
    # Implementation would go here
    # This function is referenced but not fully implemented in the notebook
    pass

# ============================================================================
# BLOB STORAGE FUNCTIONS
# ============================================================================

def download(blob_service_client, container_name, blob_name, download_file_path):
    """
    Download a blob from Azure Blob Storage.
    
    Args:
        blob_service_client: Azure Blob Service Client
        container_name: Container name
        blob_name: Blob name
        download_file_path: Path to save the downloaded file
    """
    # Implementation would go here
    # This function is referenced but not fully implemented in the notebook
    pass

# ============================================================================
# OCR FUNCTIONS
# ============================================================================

def is_numbers_and_punctuation(s):
    """
    Check if a string contains only numbers, punctuation, and spaces.
    
    Args:
        s: String to check
        
    Returns:
        Boolean indicating if the string contains only numbers, punctuation, and spaces
    """
    allowed_chars = set(string.digits + string.punctuation + " ")
    return all(char in allowed_chars for char in s)

def paddleocr_extract_consultation_notes(file_path: str) -> str:
    """
    Extracts text from a PDF or image file using PaddleOCR, handling broken files.

    Args:
        file_path: The full path to the file.

    Returns:
        A string containing the extracted text, or an empty string if the
        file is broken, unsupported, or contains no text.
    """
    file_suffix = Path(file_path).suffix.lower()
    logger.info(f"Processing {file_path}")

    # Initialize result to None
    result = None

    try:
        if file_suffix in [".pdf", ".PDF"]:
            # Open the PDF file
            pdf_document = fitz.open(file_path)

            # Get the number of pages
            number_of_pages = pdf_document.page_count
            pdf_document.close()  # Close the document after getting page count
            logger.info(f"File {file_path} has {number_of_pages} pages")

            # Initialize and run OCR
            ocr = PaddleOCR(use_angle_cls=True, lang="en", page_num=number_of_pages)
            result = ocr.ocr(file_path, cls=True)

        elif file_suffix in [".png", ".jpg", ".jpeg"]:
            # Initialize and run OCR
            ocr = PaddleOCR(use_angle_cls=True, lang="en")
            result = ocr.ocr(file_path, cls=True)

        else:
            # Raise a TypeError for unsupported file formats
            raise TypeError(f"Unsupported file type: {file_suffix}")

        # Process the OCR result
        if not result:
            logger.warning(f"No OCR result for {file_path}")
            return ""

        # Sort pages by index
        sorted_pages = []
        for page_idx in range(len(result)):
            if result[page_idx]:
                sorted_pages.append(result[page_idx])

        # Combine text from all pages
        all_text = []
        for page in sorted_pages:
            page_text = []
            for line in page:
                # line[1][0] contains the text part of the OCR result
                if line and len(line) > 1 and len(line[1]) > 0:
                    page_text.append(line[1][0])
            all_text.append(" ".join(page_text))

        return "\n".join(all_text)

    except Exception as e:
        logger.exception(f"Error processing {file_path}: {e}")
        
    # Return an empty string if no result was generated
    logger.warning(f"No text detected in {file_path}")
    return ""

def load_paddleocr_res(pcr_res_path: str = PADDLEOCR_RES_PATH, data_folder: str = DATA_FOLDER):
    """
    Load PaddleOCR results from a CSV file or generate them if the file doesn't exist.
    
    Args:
        pcr_res_path: Path to the PaddleOCR results CSV file
        data_folder: Folder containing the documents to process
        
    Returns:
        List of dictionaries containing PaddleOCR results
    """
    try:
        df = pd.read_csv(pcr_res_path)
        df[["content"]] = df[["content"]].fillna(value="")
        return df.to_dict(orient="records")
    except FileNotFoundError:
        # Extract all the text content using paddleOCR
        ocr_result = []
        for file_path in sorted(os.listdir(data_folder)):
            try:
                consultation_note = paddleocr_extract_consultation_notes(os.path.join(data_folder, file_path))
            except TypeError:
                consultation_note = ""
            ocr_result.append({
                "file_path": file_path,
                "content": consultation_note
            })
        pd.DataFrame(ocr_result).to_csv(pcr_res_path, index=False)
        return ocr_result

def load_document_intelligence_res(res_data_folder: str = OUTPUT_DATA_FOLDER, raw_data_folder: str = DATA_FOLDER) -> List[Dict]:
    """
    Load Azure Document Intelligence results from a pickle file or JSON files.
    
    Args:
        res_data_folder: Folder containing the results
        raw_data_folder: Folder containing the raw documents
        
    Returns:
        List of dictionaries containing Document Intelligence results
    """
    try:
        with open(os.path.join(res_data_folder, "ans.pk"), "rb") as fin:
            ans = pk.load(fin)
    except FileNotFoundError:
        original_file_path_list = sorted(os.listdir(raw_data_folder))
        original_file_path_dict = {}
        for p in original_file_path_list:
            original_file_path_dict[str(Path(p).stem)] = p
        ans = []
        for file_path in sorted(os.listdir(res_data_folder)):
            if file_path.endswith(".json"):
                with open(os.path.join(res_data_folder, file_path), "r") as fin:
                    ans.append(
                        {
                            "file_path": original_file_path_dict[str(Path(file_path).stem)],
                            "invoice": json.load(fin),
                        }
                    )
    return ans

# ============================================================================
# DOCUMENT INTELLIGENCE PROCESSING
# ============================================================================

def parse_document_intelligence_res(data_info: List[Dict] | Dict) -> Dict:
    """
    Parse Azure Document Intelligence results.
    
    Args:
        data_info: List of dictionaries or dictionary containing Document Intelligence results
        
    Returns:
        Dictionary with parsed results
    """
    if isinstance(data_info, Dict):
        data_info = [data_info]

    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)
    
    # Implementation would go here
    # This function is referenced but not fully implemented in the notebook
    pass

# ============================================================================
# BUSINESS RULES
# ============================================================================

def postprocess_invoice_no(info: Dict) -> Tuple[Dict, str]:
    """
    Postprocess invoice number from OCR results.
    
    Args:
        info: Dictionary containing invoice information
        
    Returns:
        Tuple of (updated info dictionary, message)
    """
    ans = deepcopy(info)
    activate = False
    message = ["", "POSTPROCESS: Invoice No Curation"]
    
    invoice_no = ans["invoice_no"]
    content = ans["content"]
    paddle_ocr_content = ans["paddleocr_content"]

    # CHANGELOG: 05 NOV 2024 add . into the stip set
    invoice_no = invoice_no.strip("#) .")
    ans["invoice_no"] = invoice_no

    # later fall out
    if len(invoice_no)<=4 or len(invoice_no)>=20:
        return ans, ""
    #  if invoice no and receipt no both appear, ask gpt (choose invocie no)
    if "invoice no" in content.lower() and "receipt no" in content.lower():
        return ans,"POSTPROCESS: Invoice No Receipt No both Appearance. Need GPT Verification"
    
    invoice_no_fuzzy_res = find_similar_substring(paddle_ocr_content, invoice_no, max_diff=2)
    if len(invoice_no_fuzzy_res) == 0:
        return ans, ""
    elif len(invoice_no_fuzzy_res) == 1:
        ans["invoice_no"] = invoice_no_fuzzy_res[0]
        return ans, f"POSTPROCESS: Invoice No Replaced by PaddleOCR. Original: {invoice_no}"
    else:
        return ans, f"POSTPROCESS: Multi Fuzzy Invoice No Extracted by PaddleOCR. Need GPT Verification. Fuzzy: {invoice_no_fuzzy_res}"

# Define rule sets
# FallOut Rule Set
fallout_rule_set = [if_empty_fields, if_date_in_future, if_invoice_no_len_fit,  
                    if_empty_treatment, if_negative_invoice_total, 
                    if_negative_treatment_amount, if_diff_invoice_total_sum_treatment_amount, if_over_conf_threshold]
# Extraction Rule Set
extraction_rule_set = [get_abn, get_shipping]
# PostProcess Rule Set
postprocess_rule_set = [postprocess_treatmentline, postprocess_invoice_no, postprocess_extra_gst_adjustment]

def run_rules(data: Dict, fallout_rule_set: List=fallout_rule_set, extraction_rule_set: List = extraction_rule_set, postprocess_rule_set: List=postprocess_rule_set):
    """
    Run business rules on Document Intelligence results.
    
    Args:
        data: Dictionary containing Document Intelligence results
        fallout_rule_set: List of fallout rules to apply
        extraction_rule_set: List of extraction rules to apply
        postprocess_rule_set: List of postprocess rules to apply
        
    Returns:
        Dictionary with rule results
    """
    ans = {}
    for k, v in data.items():
        logger.info(k)
        tmp = []
        for invoice in v:
            invoice = deepcopy(invoice)
            notes = []

            # run extraction rule
            for erule in extraction_rule_set:
                invoice = erule(invoice)

            # run postprocess rule
            for pprule in postprocess_rule_set:
                invoice, message = pprule(invoice)
                notes.append(message)

            # run fall out rule
            for frule in fallout_rule_set:
                activate, message = frule(invoice)
                if activate:
                    notes.append(message)
            invoice["rule_res"] = " ".join(notes)
            tmp.append(invoice)
        ans[k] = tmp
    return ans

# ============================================================================
# MAPPING AND COMPARISON FUNCTIONS
# ============================================================================

def map_claimno_docfile(df: pd.DataFrame) -> Tuple[Dict, Dict]:
    """
    Create mappings between claim numbers and document files.
    
    Args:
        df: DataFrame containing claim numbers and document files
        
    Returns:
        Tuple of (claimno2docfile, docfile2claimno) dictionaries
    """
    claimno2docfile = {}
    docfile2claimno = {}

    for claimno, docfile in zip(df["ClaimNumber"], df["DocFile"]):
        docfile = docfile.lower()
        if claimno not in claimno2docfile:
            claimno2docfile[claimno] = docfile.lower()

        if docfile not in docfile2claimno:
            docfile2claimno[docfile.lower()] = claimno

    return claimno2docfile, docfile2claimno

def parse_upm_info(df: pd.DataFrame) -> Dict:
    """
    Parse UPM information from a DataFrame.
    
    Args:
        df: DataFrame containing UPM information
        
    Returns:
        Dictionary with parsed UPM information
    """
    ans = {}
    # Implementation would go here
    # This function is referenced but not fully implemented in the notebook
    return ans

def filter_di_res_dict(di_res_dict: Dict, 
                      upm_res_dict: Dict,
                      docfile2claimno: Dict) -> Dict:
    """
    Remove entries from di_res_dict where the corresponding claim numbers don't exist in upm_res_dict.
    
    Args:
        di_res_dict: Dictionary mapping docfiles to DI results
        upm_res_dict: Dictionary mapping claim numbers to UPM results
        docfile2claimno: Dictionary mapping docfiles to claim numbers
        
    Returns:
        Filtered di_res_dict with only entries that have matching claim numbers in upm_res_dict
    """
    filtered_di_res_dict = {}
    removed_count = 0
    
    for docfile, di_results in di_res_dict.items():
        # Get the claim number for this docfile
        claim_no = docfile2claimno.get(docfile.lower())
        
        # Skip if no claim number mapping exists
        if not claim_no:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Removing from results.")
            removed_count += 1
            continue
            
        # Check if the claim number exists in upm_res_dict
        if claim_no in upm_res_dict:
            filtered_di_res_dict[docfile] = di_results
        else:
            logger.warning(f"Claim number {claim_no} (docfile: {docfile}) not found in UPM data. Removing from results.")
            removed_count += 1
            
    logger.info(f"Removed {removed_count} entries from di_res_dict that had no matching claim numbers in upm_res_dict.")
    logger.info(f"Original di_res_dict size: {len(di_res_dict)}, Filtered di_res_dict size: {len(filtered_di_res_dict)}")
    
    return filtered_di_res_dict

def get_text_similarity(text1, text2):
    """Calculate similarity ratio between two strings"""
    if not text1 or not text2:
        return 0.0
    return SequenceMatcher(None, str(text1).lower(), str(text2).lower()).ratio()

def gather_all_info(di_res_dict: Dict, 
                   upm_res_dict: Dict, 
                   paddleocr_res_dict: Dict, 
                   claimno2docfile: Dict, 
                   docfile2claimno: Dict):
    """
    Gather all information from Document Intelligence, UPM, and PaddleOCR results.
    
    Args:
        di_res_dict: Dictionary containing Document Intelligence results
        upm_res_dict: Dictionary containing UPM information
        paddleocr_res_dict: Dictionary containing PaddleOCR results
        claimno2docfile: Dictionary mapping claim numbers to document files
        docfile2claimno: Dictionary mapping document files to claim numbers
        
    Returns:
        List of dictionaries containing gathered information
    """
    # Threshold for considering treatments as similar
    TREATMENT_SIMILARITY_THRESHOLD = 0.7
    
    summary_list = []
    
    # Process files that were processed by Azure Document Intelligence
    for k, v in di_res_dict.items():
        docfile = k
        invoices = v

        mapping_claimno = docfile2claimno.get(docfile.lower())
        if not mapping_claimno:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
            continue

        logger.info(f"Gather information: {mapping_claimno} {docfile}")

        paddleocr_content = paddleocr_res_dict.get(docfile.lower(), {}).get("content", "")

        if len(invoices) > 1:
            print(k, mapping_claimno, len(invoices))
            print("-"*40)

        # Check if the claim number exists in upm_res_dict
        if mapping_claimno not in upm_res_dict:
            logger.warning(f"Claim number {mapping_claimno} not found in UPM data. Processing with empty UPM data.")
            # Create empty UPM info with default values
            upm_info = {
                "claim_no": mapping_claimno,
                "invoice_no": "",
                "invoice_date": None,
                "invoice_total": 0,
                "service_provider": "",
                "service_provider_no": "",
                "treatments": []
            }
        else:
            upm_info = upm_res_dict[mapping_claimno]
        
        for invoice in invoices:
            # check invoice no
            di_invoice_no = invoice["invoice_no"]
            upm_invoice_no = str(upm_info.get("invoice_no", ""))
            di_invoice_no_correct = int(di_invoice_no == upm_invoice_no) if di_invoice_no and upm_invoice_no else 0
      
            # check service provider
            di_service_provider = invoice["service_provider"]
            di_service_provider_address = invoice["service_provider_address"]
            upm_service_provider = upm_info.get("service_provider", "")
            di_service_provider_correct = int(di_service_provider == upm_service_provider) if di_service_provider and upm_service_provider else 0
            
            # check invoice date
            di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
            upm_invoice_date = ""
            if upm_info.get("invoice_date"):
                print(upm_info["invoice_date"])
                upm_invoice_date = upm_info["invoice_date"].date().isoformat() if hasattr(upm_info["invoice_date"], "date") else str(upm_info["invoice_date"])
            
            di_invoice_date_correct = int(di_invoice_date == upm_invoice_date) if di_invoice_date and upm_invoice_date else 0

            # check total amount
            di_total_amount = invoice["invoice_total"]
            upm_total_amount = upm_info.get("invoice_total", 0)  
            di_total_amount_correct = int(di_total_amount == upm_total_amount) if di_total_amount and upm_total_amount else 0

            summary = {
                "claimno": mapping_claimno,
                "docfile": docfile,
                "paddleocr_content": paddleocr_content,
                "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),
                "di_invoice_no": di_invoice_no,
                "upm_invoice_no": upm_invoice_no,
                "di_invoice_no_correct": di_invoice_no_correct,
                "di_invoice_no_conf": invoice["invoice_no_conf"],
                "di_service_provider": di_service_provider,
                "di_service_provider_address": di_service_provider_address,
                "upm_service_provider": upm_service_provider,
                "di_service_provider_correct": di_service_provider_correct,
                "di_service_provider_conf": invoice["service_provider_conf"],
                "di_abn": invoice["ABN"],
                "di_invoice_date": di_invoice_date,
                "upm_invoice_date": upm_invoice_date,
                "di_invoice_date_correct": di_invoice_date_correct,
                "di_invoice_date_conf": invoice["invoice_date_conf"],
                "di_total_amount": di_total_amount,
                "di_total_amount_correct": di_total_amount_correct,
                "di_total_amount_conf": invoice["invoice_total_conf"],
                "rule_res": invoice["rule_res"],
                "if_fallout": int("FALL OUT" in invoice["rule_res"])
            }

            # DI treatment comparison with UPM treatment
            sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
            sorted_upm_treatments = sorted(upm_info.get("treatments", []), key=lambda x: x.get("amount", 0))
            treatments = []
            di_pointer, upm_pointer = 0, 0
            
            while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):
                try:
                    di_treatment = sorted_di_treatments[di_pointer]
                except IndexError:
                    assert di_pointer == len(sorted_di_treatments)
                    di_treatment = {}
                try:
                    upm_treatment = sorted_upm_treatments[upm_pointer]
                except IndexError:
                    assert upm_pointer == len(sorted_upm_treatments)
                    upm_treatment = {}
                
                if not di_treatment and not upm_treatment:
                    logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
                    logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
                    logger.error(f"sorted_upm_treatments: {sorted_upm_treatments}")
                    logger.error(f"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}")
                    break

                if not di_treatment:
                    upm_treatment_date = ""
                    if upm_treatment.get("date_treatment") and hasattr(upm_treatment["date_treatment"], "date"):
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    elif upm_treatment.get("date_treatment"):
                        upm_treatment_date = str(upm_treatment["date_treatment"])
                        
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",
                        "upm_treatment_date": upm_treatment_date,
                        "upm_treatment": upm_treatment.get("treatment", ""),
                        "upm_amount": upm_treatment.get("amount", ""),
                        "di_treatment_amount_correct": "",
                        "di_treatment_date_correct": "",
                        "di_treatment_text_correct": "",
                        "treatment_similarity": 0.0
                    })
                    upm_pointer += 1
                    continue

                if not upm_treatment:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],
                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",
                        "di_treatment_amount_correct": 0,
                        "di_treatment_date_correct": 0,
                        "di_treatment_text_correct": 0,
                        "treatment_similarity": 0.0
                    })
                    di_pointer += 1
                    continue

                # Both treatments exist, compare them
                di_amount = di_treatment.get("amount", 0)
                upm_amount = upm_treatment.get("amount", 0)
                
                if (di_amount == upm_amount) or (abs(di_amount - upm_amount) < 0.05):
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]
                    
                    upm_treatment_date = ""
                    if upm_treatment.get("date_treatment") and hasattr(upm_treatment["date_treatment"], "date"):
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    elif upm_treatment.get("date_treatment"):
                        upm_treatment_date = str(upm_treatment["date_treatment"])
                        
                    di_treatment_date_correct = int(di_treatment_date == upm_treatment_date) if di_treatment_date and upm_treatment_date else 0
                    
                    # Add treatment text similarity calculation
                    treatment_similarity = get_text_similarity(di_treatment.get("treatment", ""), upm_treatment.get("treatment", ""))
                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],
                        "upm_treatment_date": upm_treatment_date,
                        "upm_treatment": upm_treatment.get("treatment", ""),
                        "upm_amount": upm_treatment.get("amount", ""),
                        "di_treatment_amount_correct": 1,
                        "di_treatment_date_correct": di_