FROM python:3.10-slim

ENV PYTHONPATH=/workspaces/crispy_framework/src/
# Keeps Python from generating .pyc files in the container  
ENV PYTHONDONTWRITEBYTECODE=1  
# Turns off buffering for easier container logging  
ENV PYTHONUNBUFFERED=1

RUN apt update

RUN apt install -y curl wget git gcc gnupg2 procps unzip vim

RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg \
    && wget -qO- https://raw.githubusercontent.com/eza-community/eza/main/deb.asc | gpg --dearmor -o /etc/apt/keyrings/gierens.gpg \
    && curl https://packages.microsoft.com/keys/microsoft.asc | tee /etc/apt/trusted.gpg.d/microsoft.asc \
    && curl https://packages.microsoft.com/config/debian/12/prod.list | tee /etc/apt/sources.list.d/mssql-release.list \
    && echo "deb [signed-by=/etc/apt/keyrings/gierens.gpg] http://deb.gierens.de stable main" | tee /etc/apt/sources.list.d/gierens.list \
    && chmod 644 /etc/apt/keyrings/gierens.gpg /etc/apt/sources.list.d/gierens.list \
    && apt update

RUN apt install -y eza ffmpeg libsm6 libxext6 unixodbc-dev \
    && ACCEPT_EULA=Y apt install -y msodbcsql17 mssql-tools

RUN apt install -y libreoffice libreoffice-style-breeze

# install Azure CLI
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

# install shell tools
RUN curl -s https://ohmyposh.dev/install.sh | bash -s
RUN echo "export PATH=\$PATH:/root/.local/bin" >> ~/.bashrc
RUN echo "eval \"\$(oh-my-posh init bash --config 'https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/hotstick.minimal.omp.json')\"" >> ~/.bashrc
RUN echo "alias ls=\"eza -al --color=always --icons=always\"" >> ~/.bashrc

ENV PATH=$PATH:/opt/mssql-tools/bin:/root/.local/bin

RUN pip install --upgrade pip

WORKDIR /code

COPY requirements.txt .

RUN pip install --no-cache-dir --upgrade -r requirements.txt
