// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "Python",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	// "image": "mcr.microsoft.com/devcontainers/python:0-3.10",
	"build": {
		"dockerfile": "Dockerfile",
		"context": ".."
	},
	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],
	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "curl -sL https://aka.ms/InstallAzureCLIDeb | bash", // install azure cli tool
	// Configure tool-specific properties.
	"customizations": {
		// configure properties specific to VS Code
		"vscode": {
			"settings": {
				"[python]": {
					"editor.defaultFormatter": "charliermarsh.ruff",
					"editor.formatOnSave": true,
					"editor.codeActionsOnSave": {
						"source.organizeImports.ruff": "explicit",
						"source.fixAll.ruff": "explicit"
					},
					"editor.formatOnType": true
				},
				"editor.codeActionsOnSave": {
					"source.organizeImports": "explicit"
				}
			},
			"extensions": [
				"ms-azuretools.vscode-azureappservice",
				"ms-python.python",
				"charliermarsh.ruff",
				"ms-toolsai.jupyter",
				"njpwerner.autodocstring",
				"ms-mssql.sql-database-projects-vscode",
				"aaron-bond.better-comments",
				"streetsidesoftware.code-spell-checker",
				"donjayamanne.githistory",
				"eamodio.gitlens",
				"pkief.material-icon-theme",
				"esbenp.prettier-vscode",
				"wayou.vscode-todo-highlight",
				"tomoki1207.pdf",
				"vizzuhq.code-viz-stat",
				"github.copilot"
			]
		}
	},
	"runArgs": [
		"--name",
		"${localEnv:USER}_ocr_in_house"
	]
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "vscode"
}