#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR Processing Pipeline Main Script

This script orchestrates the OCR processing pipeline by importing and using
functions from separate modules and configuration from a config file.
"""

# Standard library imports
import os
import sys
import json
import pickle as pk
import time
from pathlib import Path
from copy import deepcopy

# Third-party imports
import pandas as pd
from loguru import logger
from dotenv import load_dotenv
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from openpyxl import load_workbook

# Ensure the src directory is in the path
module_path = os.path.abspath(os.path.join(".."))  # Go up two levels to reach project root
if module_path not in sys.path:
    sys.path.append(module_path)

# Load environment variables
load_dotenv()

# Import configuration
from src.config import *

# Import functions
from src.ocr_functions import paddleocr_extract_consultation_notes, load_paddleocr_res, load_document_intelligence_res
from src.document_processing import parse_document_intelligence_res, flatten_ocr_response
from src.business_rules import run_rules, fallout_rule_set, extraction_rule_set, postprocess_rule_set
from src.mapping import gather_all_info_truuth_di, map_claimno_docfile, filter_di_res_dict, parse_upm_info,add_pass_column,parse_truuth_info
from src.data_loading import download_blob_storage, load_claim_doc_path, load_claim_truuth, load_upm_sot, setup_custom_logging
from src.service_provider_fuzzy import *
from src.di_run import run_document_intelligence
from src.sample_collection.sql_engine import Engine


def main():
    """Main function to run the OCR processing pipeline."""
    # Setup logging first
    log_filename = setup_custom_logging()

    # Start timing the entire process
    start_time = time.time()
    total_pages = 0

    logger.info("Starting OCR processing pipeline...")

    # Connect to database
    engine = Engine.get_engine(server=DB_SERVER, database=DB_NAME)
    
    # Load sample data
    samples_di = pd.read_csv(RAW_DATA_FILE)
    
    # Load document paths
    doc_path = load_claim_doc_path(samples_di, engine)
    
    # Load Truuth data
    df_truuth = load_claim_truuth(samples_di, engine)
    df_truuth.to_excel(TRUUTH_PATH, index=False)

    # Process Truuth data to flatten the json structure
    df_truuth_processed = flatten_ocr_response(df_truuth)

    # Download Files from Azure Blob Storage if files are not already present
    download_blob_storage(
        doc_path_df=doc_path,
        output_directory=DATA_FOLDER
    )

    # Create claim number to document file mappings
    claimno2docfile, docfile2claimno = map_claimno_docfile(doc_path)
    
    # Run documents on Azure Document Intelligence if output directory does not exist
    di_start_time = time.time()
    logger.info("Starting Document Intelligence processing...")
    run_document_intelligence(
    input_directory=DATA_FOLDER,
    output_directory=DI_OUTPUT_DATA_FOLDER,
    model_id="prebuilt-invoice" # You can change the model here if needed
    )
    di_end_time = time.time()
    logger.info(f"Document Intelligence processing completed in {di_end_time - di_start_time:.2f} seconds")
    
    # Load Azure Document Intelligence results
    document_intelligence_res = load_document_intelligence_res(
        res_data_folder=DI_OUTPUT_DATA_FOLDER,
        raw_data_folder=DATA_FOLDER
    )

    # Count total pages processed
    # document_intelligence_res is a list of dictionaries with structure: [{"file_path": "...", "invoice": {...}}, ...]
    total_pages = len(document_intelligence_res)

    logger.info(f"Total pages processed: {total_pages}")

    # Parse Document Intelligence results
    document_intelligence_parsed_res = parse_document_intelligence_res(document_intelligence_res)
             
    # Load PaddleOCR results
    paddleocr_res = load_paddleocr_res(
        pcr_res_path=PADDLEOCR_RES_PATH, 
        data_folder=DATA_FOLDER
    )
    paddleocr_info_dict = {item["file_path"].lower(): item for item in paddleocr_res}
    
    # Add PaddleOCR content to Document Intelligence results
    for k, v in document_intelligence_parsed_res.items():
        for invoice in v:
            if k.lower() in paddleocr_info_dict:
                invoice["paddleocr_content"] = paddleocr_info_dict[k.lower()]["content"]
            else:
                invoice["paddleocr_content"] = ""

    # Parse Truuth information
    truuth_info_dict = parse_truuth_info(df_truuth_processed)
    
    # Filter Document Intelligence results
    filtered_di_res_dict = filter_di_res_dict(
        document_intelligence_parsed_res, 
        truuth_info_dict, 
        docfile2claimno
    )

    # Update DI with Service Provider Fuzzy Matching
    sp_start_time = time.time()
    logger.info("Starting Service Provider fuzzy matching...")
    sp_di_res_dict = process_service_provider_matching(
        filtered_di_res_dict,
        csv_file_path=SERVICE_PROVIDER_CSV_PATH
    )
    sp_end_time = time.time()
    logger.info(f"Service Provider fuzzy matching completed in {sp_end_time - sp_start_time:.2f} seconds")

    # Run business rules
    rules_start_time = time.time()
    logger.info("Starting business rules processing...")
    rule_res_dict = run_rules(
        sp_di_res_dict,
        #  filtered_di_res_dict,
        fallout_rule_set=fallout_rule_set,
        extraction_rule_set=extraction_rule_set,
        postprocess_rule_set=postprocess_rule_set
    )
    rules_end_time = time.time()
    logger.info(f"Business rules processing completed in {rules_end_time - rules_start_time:.2f} seconds")
    
    # Gather all information
    summary_list = gather_all_info_truuth_di(
        rule_res_dict, 
        truuth_info_dict, 
        paddleocr_info_dict, 
        claimno2docfile, 
        docfile2claimno
    )
    
    # Convert to DataFrame and save results
    df_summary = pd.DataFrame(summary_list)

    # Create Pass indicator column
    df_summary = add_pass_column(df_summary)
    df_summary.to_excel(RULE_RES_OUTPUT_TRUUTH_PATH, index=False)

    # Calculate total processing time
    end_time = time.time()
    total_time = end_time - start_time

    # Log completion with timing and page count information
    logger.info(f"OCR processing pipeline completed successfully!")
    logger.info(f"Total pages processed: {total_pages}")
    logger.info(f"Total processing time: {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
    logger.info(f"Average time per page: {total_time/total_pages:.2f} seconds" if total_pages > 0 else "Average time per page: N/A (no pages processed)")
    logger.info(f"Results saved to: {RULE_RES_OUTPUT_TRUUTH_PATH}")
    logger.info(f"Log file saved to: {log_filename}")

    return df_summary

if __name__ == "__main__":
    main()