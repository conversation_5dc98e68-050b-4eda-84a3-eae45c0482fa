"""
Service provider fuzzy matching functionality.
"""
from typing import List, Dict, <PERSON><PERSON>
import re
from fuzzywuzzy import fuzz
from loguru import logger
import numpy as np
import pandas as pd
from tqdm import tqdm
from collections import defaultdict
from src.service_provider_matching.preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web

# Dictionary to link between OCR data and list of Service Provider columns
sp_dict = {
    "Name": "ServiceProviderName",
    "Address": "Address",
    # "streetname_name": "ServiceProviderName",
    # "suburb_name": "ServiceProviderName",
    "abn": "ABN",
    "email": "Email",
    "web": "HomePage",
    "phone_home": "PhoneNo_Home",
    "phone_work": "PhoneNo_Work",
    "phone_mobile": "PhoneNo_Mobile",
    "fax": "FaxNo",
}

# List of hyperparameters
top_n = 10  # Top number of fuzzy matches to keep for Name, Address
cos_l = 0.5  # Lower limit on cosine acceptance
A_log = 1.0  # Logarithmic amplitude for scaling ABN multi-matches
priority_dict = {  # Dictionary to link OCR fields to priority
    "Name": 1.0,
    "Address": 1.0,
    # "streetname_name": 0.5,
    # "suburb_name": 0.5,
    "abn": 1.0,
    "email": 1.0,
    "web": 0.25,
    "phone_home": 0.5,
    "phone_work": 0.5,
    "phone_mobile": 0.5,
    "fax": 0.5,
}

def validate_abn(nums: List[int]) -> bool:
    """
    Validate an ABN number
    
    Args:
        nums (List[int]): List of digits in the ABN
        
    Returns:
        bool: True if valid ABN, False otherwise
    """
    if len(nums) != 11:
        return False
    if not all(isinstance(x, int) for x in nums):
        return False
    if any(x > 9 for x in nums):
        return False
    if any(x < 0 for x in nums):
        return False

    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))
    return s % 89 == 0


def extract_abn(info: Dict) -> List[str]:
    """
    Extract ABN from document content
    
    Args:
        info (Dict): Document information
        
    Returns:
        List[str]: List of extracted ABNs
    """
    content = info.get("content", "")
    abns = []
    
    # Extract ABNs using regex patterns
    abn_extract_regex = r"(?:\d *){11}"
    abn_extract_regex1 = r"\d{2}-\d{3}-\d{3}-\d{3}"
    
    # Find all matches for the first pattern
    matches = re.findall(abn_extract_regex, content)
    for match in matches:
        # Remove spaces and check if it's a valid ABN
        digits = [int(d) for d in match if d.isdigit()]
        if validate_abn(digits):
            abns.append(''.join(str(d) for d in digits))
    
    # Find all matches for the second pattern
    matches = re.findall(abn_extract_regex1, content)
    for match in matches:
        # Remove hyphens and check if it's a valid ABN
        digits = [int(d) for d in match if d.isdigit()]
        if validate_abn(digits):
            abns.append(''.join(str(d) for d in digits))
    
    return abns

def extract_fax_number(content: str) -> List[str]:
    # Regex to capture Australian phone numbers including:
    # 1. Landlines like (02) 9876 5432
    # 2. Mobile numbers like 0412 345 678
    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.
    # Exclude international format like +61
    
    phone_number_regex = r"(\(0\d{1,2}\) \d{4} \d{4})|(\(04\d{2}\) \d{3} \d{3})|(\d{4} \d{3} \d{3})|13\d{4}|1300 \d{3} \d{3}|1800 \d{3} \d{3}"
    
    # Extract all matches
    matches = re.findall(phone_number_regex, content)
    
    # Flatten the list and remove any empty strings
    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]
    
    return flattened_matches


def extract_phone_number(content: str) -> List[str]:
    """
    Extract phone numbers from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted phone numbers
    """
    # Regex to capture Australian phone numbers including:
    # 1. Landlines like (02) 9876 5432
    # 2. Mobile numbers like 0412 345 678
    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.
    # Exclude international format like +61
    
    phone_number_regex = r"(\(0\d{1,2}\) \d{4} \d{4})|(\(04\d{2}\) \d{3} \d{3})|(\d{4} \d{3} \d{3})|13\d{4}|1300 \d{3} \d{3}|1800 \d{3} \d{3}"
    
    # Extract all matches
    matches = re.findall(phone_number_regex, content)
    
    # Flatten the list and remove any empty strings
    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]
    
    return flattened_matches


def extract_mobile_number(content: str) -> List[str]:
    """
    Extract mobile numbers from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted mobile numbers
    """
    # phone number extraction
    phone_number_regex = r"\+?61[- ]?\d{1,2}[- ]?\d{4}[- ]?\d{4}"
    matches = re.findall(phone_number_regex, content) 
    return matches


def extract_email(content: str) -> List[str]:
    """
    Extract email addresses from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted email addresses
    """
    # Replace newlines with spaces to normalize text
    content = content.replace("\n", " ")
    
    # Improved regex to capture emails after a colon or space
    pattern = r"(?<=[:\s])([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?!\S)"
    
    matches = re.findall(pattern, content)
    
    return matches


def extract_weblink(content: str) -> List[str]:
    """
    Extract web links from content
    
    Args:
        content (str): Document content
        
    Returns:
        List[str]: List of extracted web links
    """
    weblink_regex = r"(http|ftp|https)://([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?"
    matches = re.findall(weblink_regex, content)
    return matches


def prepare_service_provider_info(info: Dict) -> Dict:
    """
    Prepare service provider information from extracted data
    
    Args:
        info (Dict): Dictionary containing extracted information
        
    Returns:
        Dict: Dictionary with prepared service provider information
    """
    service_provider_info = {}
    content = info.get("content", "")
    
    service_provider_info["phone_home"] = extract_phone_number(content)
    service_provider_info["phone_work"] = service_provider_info["phone_home"]
    service_provider_info["fax"] = service_provider_info["phone_home"]
    service_provider_info["phone_mobile"] = extract_mobile_number(content)
    service_provider_info["email"] = extract_email(content)
    service_provider_info["web"] = extract_weblink(content)
    service_provider_info["abn"] = extract_abn(info)
    service_provider_info["Name"] = info.get("service_provider", "")
    service_provider_info["Address"] = info.get("service_provider_address", "")
    service_provider_info["customer_postcode"] = info.get("customer_address_postcode", "")
    
    return service_provider_info


def _create_empty_result() -> Dict:
    """Create empty result structure for when no matches are found"""
    return {
        "best_match_list": ["", ""],
        "best_match_evidence": (None, 0, 0),
        "best_match_list2": [None],
        "best_match_evidence2": (None, 0, 0),
        "list": [],
        "has_ties": False,
        "tied_matches": [],
        "tie_count": 0
    }


def _is_valid_field_value(field_value) -> bool:
    """Check if field value is valid for processing"""
    if field_value is None or field_value is np.NaN:
        return False
    if isinstance(field_value, str) and field_value.strip() == "":
        return False
    if isinstance(field_value, list) and len(field_value) == 0:
        return False
    return True


def _process_numerical_field(field_value: List[str]) -> List:
    """Process numerical fields like ABN, phone numbers"""
    query_list = [
        preprocess_numbers(query)
        for query in field_value
    ]
    return [query for query in query_list if query != 99999999]


def _process_web_field(field_value: List[str]) -> List[str]:
    """Process web-related fields like email and web"""
    return [preprocess_web(query) for query in field_value]


def _process_fuzzy_field(field: str, field_value: str, serv_prov: pd.DataFrame) -> Tuple[List[str], Dict]:
    """Process Name and Address fields with fuzzy matching optimization"""
    query_list = [field_value]

    # Vectorized fuzzy matching for better performance
    field_column = serv_prov[sp_dict[field]].str.lower()
    query_lower = field_value.lower()

    # Calculate similarity scores for all entries at once
    similarity_scores = field_column.apply(lambda x: fuzz.ratio(query_lower, x))

    # Filter by threshold
    valid_matches = similarity_scores >= cos_l * 100

    if valid_matches.any():
        # Get matching items and their scores
        matching_items = serv_prov.loc[valid_matches, sp_dict[field]].tolist()
        matching_scores = similarity_scores[valid_matches].tolist()

        # Create fuzzy list and sort
        fuzzy_list = list(zip(matching_items, matching_scores))
        fuzzy_list = sorted(fuzzy_list, key=lambda x: x[1], reverse=True)

        # Get top N results
        query_fuzzy_list = [item[0] for item in fuzzy_list[:top_n]]
        cosine_dict = {item[0]: item[1] / 100.0 for item in fuzzy_list[:top_n]}

        # Combine original query with fuzzy results
        query_list = query_list + query_fuzzy_list

        logger.debug(f"Fuzzy matching for {field}: found {len(fuzzy_list)} matches above threshold")
    else:
        cosine_dict = {}
        logger.debug(f"Fuzzy matching for {field}: no matches above threshold")

    return query_list, cosine_dict


def _get_field_matches(field: str, query_list: List, serv_prov: pd.DataFrame) -> Tuple[List[int], List]:
    """Get matches for a specific field with proper ABN handling"""
    if field == "abn" and len(query_list) > 0:
        # Fixed ABN matching logic - use individual queries instead of entire list
        match_index = []
        match_query = []
        for query in query_list:
            match_check = serv_prov[sp_dict[field]].isin([query])  # Fixed: use [query] not query_list
            match_index.extend(serv_prov[match_check].index.values.tolist())
            match_query.extend(serv_prov[match_check][sp_dict[field]].tolist())
    else:
        match_check = serv_prov[sp_dict[field]].isin(query_list)
        match_index = serv_prov[match_check].index.values.tolist()
        match_query = serv_prov[match_check][sp_dict[field]].tolist()

    return match_index, match_query


def _calculate_fuzzy_scores(match_index: List[int], match_query: List, cosine_dict: Dict) -> List[float]:
    """Calculate cosine scores for fuzzy matches with validation"""
    match_cosine = []
    for idx, query_item in zip(match_index, match_query):
        if query_item in cosine_dict:
            match_cosine.append(cosine_dict[query_item])
        else:
            logger.warning(f"Query item '{query_item}' not found in cosine_dict for index {idx}")
            match_cosine.append(0.5)  # Default score for missing items
    return match_cosine


def _calculate_unique_matches(match_index_list: List, match_cosine_list: List,
                            field_list: List[str], priority_scores: List[float]) -> List[Tuple]:
    """Calculate unique field matches avoiding duplicate counting"""
    # Use defaultdict to track unique field matches per service provider
    provider_matches = defaultdict(lambda: {'fields': set(), 'priority_score': 0.0, 'field_scores': {}})

    for n, (match_indices, match_cosines) in enumerate(zip(match_index_list, match_cosine_list)):
        field_name = field_list[n]
        field_priority = priority_scores[n]

        # Track unique matches per provider for this field
        unique_indices = set(match_indices)

        for idx in unique_indices:
            # Get the best cosine score for this provider in this field (in case of duplicates)
            idx_positions = [i for i, x in enumerate(match_indices) if x == idx]
            best_cosine = max(match_cosines[pos] for pos in idx_positions) if idx_positions else 1.0

            provider_matches[idx]['fields'].add(field_name)
            provider_matches[idx]['field_scores'][field_name] = best_cosine
            provider_matches[idx]['priority_score'] += field_priority * best_cosine

    # Convert to the expected tuple format
    count_list = []
    for provider_idx, match_data in provider_matches.items():
        field_count = len(match_data['fields'])  # Count unique fields matched
        fields_matched = list(match_data['fields'])
        priority_score = match_data['priority_score']

        count_list.append((provider_idx, field_count, priority_score, fields_matched))

    logger.debug(f"Calculated unique matches for {len(count_list)} providers")
    return count_list


def fuzzy_match_service_provider(service_provider_info: Dict, serv_prov: pd.DataFrame) -> Dict:
    """
    Perform fuzzy matching on service provider information with improved tie handling

    Args:
        service_provider_info (Dict): Dictionary with service provider information
        serv_prov (pd.DataFrame): DataFrame with service provider reference data

    Returns:
        Dict: Dictionary with matching results including tie indicators
    """
    # Validation checks
    if serv_prov.empty:
        logger.warning("Service provider DataFrame is empty")
        return _create_empty_result()

    if not service_provider_info:
        logger.warning("Service provider info is empty")
        return _create_empty_result()

    # Prepare list of match index and cosine values
    match_index_list = []
    match_cosine_list = []
    field_list = list(sp_dict.keys())
    priority_scores = [priority_dict[field] for field in field_list]

    logger.debug(f"Processing fields: {field_list}")

    # Begin iterating over field list
    for field in field_list:
        field_value = service_provider_info.get(field, "")

        # Improved validation for field values
        if not _is_valid_field_value(field_value):
            match_index_list.append([])
            match_cosine_list.append([])
            continue

        logger.debug(f"Processing field '{field}' with value: {field_value}")

        try:
            # Process different field types
            if field in ["abn", "phone_home", "phone_work", "phone_mobile", "fax"]:
                query_list = _process_numerical_field(field_value)
            elif field in ["email", "web"]:
                query_list = _process_web_field(field_value)
            elif field in ["Name", "Address"]:
                query_list, cosine_dict = _process_fuzzy_field(field, field_value, serv_prov)
            else:
                query_list = [preprocess(field_value)]

            if not query_list:
                match_index_list.append([])
                match_cosine_list.append([])
                continue

            # Get matches for this field
            match_index, match_query = _get_field_matches(field, query_list, serv_prov)

            # Calculate cosine scores
            if field in ["Name", "Address"] and 'cosine_dict' in locals():
                match_cosine = _calculate_fuzzy_scores(match_index, match_query, cosine_dict)
            else:
                match_cosine = [1.0] * len(match_index)

            match_index_list.append(match_index)
            match_cosine_list.append(match_cosine)

            logger.debug(f"Field '{field}' found {len(match_index)} matches")

        except Exception as e:
            logger.error(f"Error processing field '{field}': {str(e)}")
            match_index_list.append([])
            match_cosine_list.append([])

    # Calculate unique field matches (avoiding duplicates)
    count_list = _calculate_unique_matches(match_index_list, match_cosine_list, field_list, priority_scores)

    # Sort with deterministic tie-breaking (using service provider index as final tiebreaker)
    sorted_count_list = sorted(
        count_list,
        key=lambda tup: (tup[2], len(tup[3]), -tup[0]),  # priority_score, field_count, -index (for deterministic order)
        reverse=True
    )[:10]  # Keep top 10 instead of 5 to better handle ties

    logger.debug(f"Found {len(sorted_count_list)} potential matches")

    # Handle ties and create results
    return _handle_ties_and_create_results(sorted_count_list, serv_prov)


def _handle_ties_and_create_results(sorted_count_list: List[Tuple], serv_prov: pd.DataFrame) -> Dict:
    """
    Handle ties in matching results and create comprehensive result structure

    Args:
        sorted_count_list: List of tuples (index, field_count, priority_score, fields_matched)
        serv_prov: Service provider DataFrame

    Returns:
        Dict: Comprehensive results with tie handling
    """
    if not sorted_count_list:
        logger.info("No matches found")
        return _create_empty_result()

    # Get the best match
    best_match = sorted_count_list[0]
    best_priority_score = best_match[2]
    best_field_count = best_match[3]

    # Find all matches with the same priority score and field count (ties)
    tied_matches = []
    for match in sorted_count_list:
        if (abs(match[2] - best_priority_score) < 1e-10 and  # Use small epsilon for float comparison
            len(match[3]) == len(best_field_count)):
            tied_matches.append(match)
        else:
            break  # Since list is sorted, no more ties possible

    has_ties = len(tied_matches) > 1
    tie_count = len(tied_matches)

    if has_ties:
        logger.warning(f"Found {tie_count} tied matches with priority score {best_priority_score:.4f} "
                      f"and {len(best_field_count)} fields matched")

        # Log details of tied matches
        for i, match in enumerate(tied_matches):
            provider_info = serv_prov.iloc[match[0]]
            logger.warning(f"Tied match {i+1}: Provider {provider_info['ServiceProviderNo']} - "
                          f"{provider_info['ServiceProviderName']} (fields: {match[3]})")

    # Create result structure
    result = {
        "has_ties": has_ties,
        "tie_count": tie_count,
        "tied_matches": [],
        "list": sorted_count_list
    }

    if tied_matches:
        # Store all tied matches
        for match in tied_matches:
            provider_data = serv_prov[["ServiceProviderNo", "ServiceProviderName"]].iloc[match[0]].values.tolist()
            result["tied_matches"].append({
                "provider_info": provider_data,
                "evidence": (match[3], match[1], match[2])  # fields, count, priority
            })

        # Set primary match (first in tied list)
        primary_match = tied_matches[0]
        result["best_match_list"] = serv_prov[["ServiceProviderNo", "ServiceProviderName"]].iloc[primary_match[0]].values.tolist()
        result["best_match_evidence"] = (primary_match[3], primary_match[1], primary_match[2])

        # Set secondary match if available
        if len(tied_matches) > 1:
            secondary_match = tied_matches[1]
            result["best_match_list2"] = serv_prov[["ServiceProviderNo", "ServiceProviderName"]].iloc[secondary_match[0]].values.tolist()
            result["best_match_evidence2"] = (secondary_match[3], secondary_match[1], secondary_match[2])
        else:
            result["best_match_list2"] = [None]
            result["best_match_evidence2"] = (None, 0, 0)
    else:
        # Single best match
        best_match = sorted_count_list[0]
        result["best_match_list"] = serv_prov[["ServiceProviderNo", "ServiceProviderName"]].iloc[best_match[0]].values.tolist()
        result["best_match_evidence"] = (best_match[3], best_match[1], best_match[2])

        # Second best match (if exists and different from best)
        if len(sorted_count_list) > 1:
            second_match = sorted_count_list[1]
            result["best_match_list2"] = serv_prov[["ServiceProviderNo", "ServiceProviderName"]].iloc[second_match[0]].values.tolist()
            result["best_match_evidence2"] = (second_match[3], second_match[1], second_match[2])
        else:
            result["best_match_list2"] = [None]
            result["best_match_evidence2"] = (None, 0, 0)

    logger.info(f"Matching completed: Best match priority={result['best_match_evidence'][2]:.4f}, "
               f"Has ties={has_ties}, Tie count={tie_count}")

    return result


def if_extra_gst_service_provider(info: Dict) -> bool:
    """
    Check if service provider requires extra GST processing
    
    Args:
        info (Dict): Document information
        
    Returns:
        bool: True if service provider requires extra GST, False otherwise
    """
    extra_gst_servie_provider_abn_set = {
        "***********",  # Pet Chemist Online
        "***********",  # Perth Animal Eye Hospital
        "***********",  # Melbourne Veterinary Specialist Centre-Essendon
        "***********",  # Hamilton Hill Veterinary Hospital
        "***********",  # Animal Eye Care
        "***********",  # Pets At Peace
        "***********",  # Dermatology for Animals
        "***********",  # Walk-In Clinic for Animals
    }
    abn = info.get("ABN", [])
    return len(extra_gst_servie_provider_abn_set & set(abn)) > 0


def find_similar_substring(content: str, target: str, max_diff: int = 2) -> List[str]:
    """
    Find a substring in the content that has the same length as the target substring,
    with only a few different characters or digits.

    Args:
        content (str): The string to search within.
        target (str): The substring to compare with.
        max_diff (int): Maximum number of allowed differences. Default is 2.

    Returns:
        List[str]: List of similar substrings found
    """
    similar_substrings = []
    
    # Implementation of fuzzy substring matching
    # This is a placeholder - implement the actual logic based on your requirements
    
    return similar_substrings


def load_service_provider_data(csv_path: str) -> pd.DataFrame:
    """
    Load and preprocess service provider data
    
    Args:
        csv_path (str): Path to the CSV file with service provider data
        
    Returns:
        pd.DataFrame: Preprocessed service provider DataFrame
    """
    # Read in Service Provider raw data
    serv_prov_raw = pd.read_csv(csv_path)
    serv_prov_raw = serv_prov_raw[serv_prov_raw['Is_Blocked']==0].reset_index(drop=True)
    serv_prov = serv_prov_raw.copy()  # create copy of raw data
    serv_prov = serv_prov.fillna("")
    serv_prov["PostCode"] = serv_prov["PostCode"].astype(str)
    
    # Preprocessing of Service Provider List
    serv_prov["Address"] = (
        serv_prov["Address"]
        + " "
        + serv_prov["City"]
        + " "
        + serv_prov["State"]
        + " "
        + serv_prov["PostCode"]
    )  # Concat fields to form full Address
    
    for field in [
        "ServiceProviderName",
        "Address",
        "ABN",
        "Email",
        "HomePage",
        "PhoneNo_Home",
        "PhoneNo_Work",
        "PhoneNo_Mobile",
        "FaxNo",
    ]:
        if field in ["ServiceProviderName", "Address"]:
            serv_prov[field] = serv_prov[field].apply(preprocess)
        elif field in ["Email", "HomePage"]:
            serv_prov[field] = serv_prov[field].apply(preprocess_web)
        elif field in [
            "PhoneNo_Home",
            "PhoneNo_Work",
            "PhoneNo_Mobile",
            "FaxNo",
            "PhoneNo_Home",
        ]:
            serv_prov[field] = serv_prov[field].apply(preprocess_numbers)
        elif field in ['ABN']:
            serv_prov[field] = serv_prov[field].apply(lambda x: int(x) if x != "" and pd.notnull(x) else x)
    
    return serv_prov


def process_service_provider_matching(document_intelligence_rule_res: Dict, csv_file_path: str) -> Dict:
    """
    Process service provider matching for all documents
    
    Args:
        document_intelligence_rule_res (Dict): Dictionary with document intelligence results
        csv_file_path (str): Path to the CSV file with service providers
        
    Returns:
        Dict: Updated document intelligence results with service provider matching
    """
    # Load service provider data
    serv_prov = load_service_provider_data(csv_file_path)
    logger.info("Service Provider data loaded and preprocessed")
    
    # Process each document
    for _, info_list in tqdm(document_intelligence_rule_res.items(), desc="Processing service provider matching"):
        for info in info_list:
            # Prepare service provider info
            service_provider_info = prepare_service_provider_info(info)

            # Perform fuzzy matching
            match_result = fuzzy_match_service_provider(service_provider_info, serv_prov)

            # Update the document with matching results
            info["service_provider_info"] = service_provider_info
            info["service_provider_info"].update(match_result)

            # Add convenience fields with improved error handling
            try:
                info["service_provider_fm"] = match_result['best_match_list'][1] if len(match_result['best_match_list']) > 1 else ""
                info["service_provider_no_fm"] = match_result['best_match_list'][0] if len(match_result['best_match_list']) > 0 else ""
                info["service_provider_evidence"] = match_result['best_match_evidence']

                # Add tie information
                info["has_ties"] = match_result.get('has_ties', False)
                info["tie_count"] = match_result.get('tie_count', 0)
                info["tied_matches"] = match_result.get('tied_matches', [])

                # Log ties for monitoring
                if info["has_ties"]:
                    logger.warning(f"Document has {info['tie_count']} tied service provider matches")

            except (IndexError, KeyError, TypeError) as e:
                logger.error(f"Error setting primary match fields: {e}")
                info["service_provider_fm"] = ""
                info["service_provider_no_fm"] = ""
                info["service_provider_evidence"] = (None, 0, 0)
                info["has_ties"] = False
                info["tie_count"] = 0
                info["tied_matches"] = []

            try:
                # Handle secondary match
                best_match_list_2 = match_result.get('best_match_list2', [None])
                if best_match_list_2 and best_match_list_2[0] is not None:
                    info["service_provider_fm2"] = best_match_list_2[1] if len(best_match_list_2) > 1 else None
                    info["service_provider_no_fm2"] = best_match_list_2[0]
                    info["service_provider_evidence2"] = match_result.get('best_match_evidence2', (None, 0, 0))
                else:
                    info["service_provider_fm2"] = None
                    info["service_provider_no_fm2"] = None
                    info["service_provider_evidence2"] = (None, 0, 0)

            except (IndexError, KeyError, TypeError) as e:
                logger.error(f"Error setting secondary match fields: {e}")
                info["service_provider_fm2"] = None
                info["service_provider_no_fm2"] = None
                info["service_provider_evidence2"] = (None, 0, 0)

    return document_intelligence_rule_res


def main():
    """Example usage of the service provider matching functionality"""
    try:
        # Path to the service provider CSV file
        csv_file_path = "/workspaces/OCR_in_house/data/ref_service_provider_updated.csv"
        
        # Load service provider data
        serv_prov = load_service_provider_data(csv_file_path)
        print(f"Loaded {len(serv_prov)} service providers")
        
        # Example document info
        example_info = {
            "content": "JOHNSTON ST VET CLINIC\n123 Johnston Street\nFitzroy VIC 3065\nPhone: (03) 9417 4228\nABN: **************",
            "service_provider": "JOHNSTON ST VET CLINIC",
            "service_provider_address": "123 Johnston Street Fitzroy VIC 3065"
        }
        
        # Prepare service provider info
        service_provider_info = prepare_service_provider_info(example_info)
        print("Service Provider Info:")
        for key, value in service_provider_info.items():
            print(f"  {key}: {value}")
        
        # Perform fuzzy matching
        match_result = fuzzy_match_service_provider(service_provider_info, serv_prov)
        print("\nMatching Result:")
        print(f"  Best Match: {match_result['best_match_list']}")
        print(f"  Evidence: {match_result['best_match_evidence']}")
        print(f"  Second Best Match: {match_result['best_match_list2']}")

    except FileNotFoundError:
        print(
            "Error: service_providers.csv file not found. Please create the CSV file."
        )
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()