import os
import sys
import pandas as pd
from pathlib import Path
import pyodbc
import re

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

module = '.'
sys.path.insert(1, module)

# from scripts.sql_engine import *
def execute_sql_file_to_dataframe(sql_file, server_name, database_name, trusted_connection=True):
    """
    Execute an SQL query from a file and return the results as a pandas DataFrame.
    
    Parameters:
    -----------
    sql_file : str
        Path to the SQL file containing the query to execute
    server_name : str
        Name of the SQL Server
    database_name : str
        Name of the database
    trusted_connection : bool, optional
        Whether to use Windows Authentication (default is True)
    
    Returns:
    --------
    pandas.DataFrame
        Results of the SQL query as a DataFrame
    """
    try:
        # Read SQL query from file
        with open(sql_file, 'r') as file:
            sql_query = file.read()
        
        # Create connection string
        if trusted_connection:
            conn_str = f'DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={server_name};DATABASE={database_name};Trusted_Connection=yes;'
        else:
            # You might want to add username/password parameters if not using trusted connection
            raise ValueError("Non-trusted connections require username and password parameters")
        
        # Connect to database
        conn = pyodbc.connect(conn_str)
        
        # Execute query and fetch results into DataFrame
        df = pd.read_sql(sql_query, conn)
        
        # Close connection
        conn.close()
        
        print(f"Successfully executed SQL query from {sql_file}")
        print(f"Retrieved {len(df)} rows")
        
        return df
    
    except FileNotFoundError:
        print(f"Error: SQL file not found at {sql_file}")
        raise
    except pyodbc.Error as e:
        print(f"Database error: {str(e)}")
        raise
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        raise

current_directory = os.path.dirname(os.path.abspath(__file__))

# Execute SQL query to extract claims related data if not found
if not os.path.exists(os.path.join(current_directory,'claims_path.csv')):
    df = execute_sql_file_to_dataframe(
                                    sql_file=os.path.join(current_directory,'claim_document_path.sql'), 
                                    server_name='PS-PRD-AZS-DWH1',
                                    database_name='BIA'
                                    )
    
    df["ContainerName"] = df["DocumentPath"].apply(lambda x: str(Path(x).parent))
    df["DocumentFile"] = df["DocumentPath"].apply(lambda x: str(Path(x).name))

    df.to_csv(os.path.join(current_directory,'claims_path.csv'))
    print(f"Saved CSV to {os.path.join(current_directory,'claims_path.csv')}")