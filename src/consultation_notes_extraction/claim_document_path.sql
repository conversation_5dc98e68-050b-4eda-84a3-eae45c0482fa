SELECT
    xcil.ClaimNo,
    xcil.InvoiceLineNo,
    cd.DocumentId,
    cd.DocumentName,
    cd.DocumentType,
    cd.DocumentPath,
    CAST(cd.DocumentDate AS DATETIME2) AS DocumentDate,
    xci.CspReferenceNo
  FROM [COS].[dbo].[Document] cd
  JOIN [BIA].[model].[XML-ClaimInvoice] xci
  ON cd.ClaimRefNumber COLLATE DATABASE_DEFAULT = xci.CspReferenceNo
  JOIN [BIA].[model].[XML-ClaimInvoice-Link] xcil
  ON xci.ClaimId = xcil.ClaimId
  AND xci.InvoiceId = xcil.InvoiceId
  AND LEN(DocumentId) = 36
  --AND cd.DocumentDate >= '2021-01-01'
  ANd IsMergedDocument = 1