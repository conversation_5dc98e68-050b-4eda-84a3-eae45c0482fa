import os
import time
from typing import List

import requests
from loguru import logger
from openai import AzureOpenAI
from rich import print

from src.utils.openai_agent import create_messages


class OpenAIAgent(dict):
    # NOTE: by inherited from dict class, the OpenAIAgent class can be JSON serializable
    # without modifying JSON default, JSON Encoder, or add another function calls to make
    # the instance in serializable format.
    def __init__(
        self,
        deployment: str = "gpt4",  # could be gpt35, gpt4, or gpt4-vision
        endpoint: str = "",
        temperature: float = 0.0,
        max_tokens: int = 800,
        top_p: float = 0.95,
        **kwargs,
    ) -> None:
        # assert (
        #     deployment in settings.openai.available_models
        # ), f"Model {deployment} not available"
        self.deployment = deployment

        self.api_key = os.environ["OPENAI_API_KEY"]
        self.endpoint = endpoint or os.environ["AZURE_OPENAI_ENDPOINT"]

        if deployment != "gpt4-vision":
            self.client = AzureOpenAI(
                azure_endpoint=self.endpoint,
                azure_deployment=deployment,
                api_key=self.api_key,
            )

        dict.__init__(
            self,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            deployment=deployment,
        )

        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p

    def prepare_messages(
        self,
        existing_messages: list = None,
        sys_prompt: str = "",
        user_prompt: str = "",
        **kwargs,
    ):
        """
        Prepares messages for the OpenAI API request.

        Args:
            existing_messages (list, optional): Pre-existing message history. Defaults to None.
            sys_prompt (str, optional): System-level instructions. Defaults to "".
            user_prompt (str, optional): User input to be included. Defaults to "".

        Returns:
            _type_: _description_
        """
        messages = [] if not existing_messages else existing_messages
        if sys_prompt:
            sys_message = create_messages(role="system", text=sys_prompt)
            messages.append(sys_message)
        if user_prompt:
            if "images" in kwargs:
                user_message = create_messages(
                    role="user", text=user_prompt, images=kwargs.get("images")
                )
            else:
                user_message = create_messages(role="user", text=user_prompt)
            messages.append(user_message)
        if "assistant_prompt" in kwargs:
            assistant_message = create_messages(
                role="assistant", text=kwargs.get("assistant_prompt")
            )
            messages.append(assistant_message)

        return messages

    def call_api(
        self, messages: List, wait: int = 5, max_retires: int = 3, **kwargs
    ) -> requests.Response:
        """
        Calls the OpenAI API, including retry logic in case of failure.

        Args:
            messages (List): Messages to be sent in the request
            wait (int, optional): Waiting time between attempts. Defaults to 5.
            max_retires (int, optional): Maximum retry attempts. Defaults to 3.

        Returns:
            requests.Response: API response with content from OpenAI.
        """
        logger.debug(
            f"Calling API {self.deployment} at {time.strftime('%H:%M:%S', time.localtime())}"
        )
        time.sleep(wait)
        attempts = 1
        if self.deployment == "gpt4-vision":
            while attempts <= max_retires:
                try:
                    headers = {
                        "Content-Type": "application/json",
                        "api-key": self.api_key,
                    }

                    payload = {
                        "messages": messages,
                        "temperature": self.temperature,
                        "max_tokens": self.max_tokens,
                        "top_p": self.top_p,
                    }

                    # with open("payload.json", "w") as f:
                    #     json.dump(payload, f, indent=4)

                    response = requests.post(
                        self.endpoint, headers=headers, json=payload
                    )
                    response.raise_for_status()
                    return response.json()["choices"][0]["message"]["content"]
                except requests.RequestException:
                    logger.exception("API HTTP error")
                    attempts += 1
                    logger.debug("Retrying in 20 seconds...")
                    time.sleep(20)
        elif self.deployment in ["gpt4", "gpt-4-613", "gpt-4-32k", "gpt35"]:
            while attempts <= max_retires:
                try:
                    response = self.client.chat.completions.create(
                        model=self.deployment,
                        messages=messages,
                        temperature=self.temperature,
                        max_tokens=self.max_tokens,
                        top_p=self.top_p,
                    )
                    return response.choices[0].message.content
                except Exception as e:
                    logger.exception(f"Error: {e}")
                    attempts += 1
                    logger.debug("Retrying in 20 seconds...")
                    time.sleep(20)

        else:
            pass

    def info(self):
        """Displays the agent's configuration."""
        print(
            f"""Deployment Name: {self.deployment}
                Endpoint: {self.endpoint}
                API Key: {self.api_key}
                Temperature: {self.temperature}
                Max Token: {self.max_tokens}
                Top P: {self.top_p}
                """
        )

    def __deepcopy__(self, memodict={}):
        cpyobj = OpenAIAgent(
            deployment=self.deployment,
            endpoint=self.endpoint,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            top_p=self.top_p,
        )  # create a new OpenAIAgent

        return cpyobj

    def __dict__(self):
        return {
            "deployment": self.deployment,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
        }
