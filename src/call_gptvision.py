import base64
import os
import sys
from pathlib import Path

import requests

if Path(__file__).parents[1] not in sys.path:
    sys.path.append(Path(__file__).parents[1].as_posix())
import ast
import json
import re
import time
from pathlib import Path

import cv2
import pandas as pd
import pytesseract
from credentials import get_openai_key
from pdf2image import convert_from_path


def extract_filename(file_path):
    # Get the base name (filename with extension)
    filename_with_extension = os.path.basename(file_path)
    # Split the filename by '.' and get the first part (filename without extension)
    filename = filename_with_extension.split(".")[0]
    return filename


def convert_pdf_to_images(pdf_path, output_folder):
    filename = extract_filename(pdf_path)
    # Ensure the output folder exists, create if it doesn't
    os.makedirs(output_folder, exist_ok=True)

    # Convert PDF to a list of images
    images = convert_from_path(
        pdf_path, poppler_path="E:\\claryt\\poppler-23.11.0\\Library\\bin"
    )

    # Save each image as a separate file
    for i, image in enumerate(images):
        image_path = f"{output_folder}/{filename}_p{i+1}.jpg"
        image.save(image_path, "JPEG")


def get_files_from_folder(folder_path, extensions=[".pdf", ".jpg", ".png", ".jpeg"]):
    file_paths = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if any(file.lower().endswith(ext) for ext in extensions):
                file_paths.append(os.path.join(root, file))
    return file_paths


def extract_list_from_string(s):
    try:
        # Find all substrings that look like lists
        potential_lists = re.findall(r"\[.*?\]", s)

        for potential_list in potential_lists:
            try:
                # Attempt to evaluate each potential list
                evaluated_list = ast.literal_eval(potential_list)
                if isinstance(evaluated_list, list):
                    return evaluated_list
            except (ValueError, SyntaxError):
                # If evaluation fails, move to the next potential list
                continue
    except Exception as e:
        print(f"Error: {e}")

    return None


def clean_json_string(input_str):
    # Find the index of the first opening curly brace
    start_index = input_str.find("{")
    # Find the index of the last closing curly brace
    end_index = input_str.rfind("}")

    # Extract the JSON part, including the curly braces
    cleaned_json = input_str[start_index : end_index + 1]

    return cleaned_json


def convert_to_json(data_str):
    """
    Convert a JSON-like string to a JSON object.

    Parameters:
    - data_str: A string in JSON format.

    Returns:
    - A JSON object if the string is valid JSON; otherwise, it returns an error message.
    """
    try:
        # Parse the JSON string into a Python dictionary (JSON object)
        clean_json = clean_json_string(data_str)
        # data_json = json.dumps(clean_json)
        # data_json = json.loads(clean_json)
        return clean_json
    except json.JSONDecodeError as e:
        # Return the error message if an exception occurs
        return f"An error occurred: {e}"


def rotate_and_overwrite(imPath, center=None, scale=1.0):
    im = cv2.imread(str(imPath), cv2.IMREAD_COLOR)
    angle = 360 - int(
        re.search(r"(?<=Rotate: )\d+", pytesseract.image_to_osd(im)).group(0)
    )
    (h, w) = im.shape[:2]

    if center is None:
        center = (w / 2, h / 2)

    # Perform the rotation
    M = cv2.getRotationMatrix2D(center, angle, scale)
    rotated = cv2.warpAffine(im, M, (w, h))

    # Overwrite the original image with the rotated image
    cv2.imwrite(imPath, rotated)


def call_gptvision(
    system_prompt: str = "You are an AI assistant that helps people find information.",
    user_prompt: str = "Hello there.",
    file_path: str = "C:\\users",
    **kwargs,
) -> dict:
    """
    Call the API to generate the answer from GPT4 Vision
    Args:
        sys_prompt (str): the system prompt
        user_prompt (str): the user prompt
        image_path (str): full path to image attached (pdf or jpg)
        **kwargs (dict): all the other parameters required by the model

    Returns:
        dict: the result from the API plus all the input parameters
    """
    # Configuration
    GPT4V_KEY = get_openai_key()
    if not GPT4V_KEY:
        raise Exception("A key should be provided to invoke the endpoint")
    if not file_path:
        raise Exception("Provide a valid path to an image file.")

    # Check if pdf file, convert to images
    if file_path.lower().endswith(".pdf"):
        # Extract base name without extension and create a folder name based on it
        base_name = os.path.basename(file_path)
        folder_name = os.path.splitext(base_name)[0]
        output_folder = os.path.join(os.path.dirname(file_path), folder_name)

        convert_pdf_to_images(file_path, output_folder)

        # Encode all images in folder
        encoded_images = []

        # Iterate over all files in the output folder
        for file_name in os.listdir(output_folder):
            file_path = os.path.join(output_folder, file_name)

            # Check if the current file is an image (e.g., .jpg)
            if file_path.lower().endswith(".jpg"):
                # Rotate image if wrong orientation
                # rotate_and_overwrite(file_path)

                # Encode the image file
                encoded_image = base64.b64encode(open(file_path, "rb").read()).decode(
                    "utf-8"
                )
                encoded_images.append(encoded_image)

        # Generate JSON entries for each encoded image
        image_entries = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"},
            }
            for encoded_image in encoded_images
        ]

    else:
        try:
            # Rotate image if wrong orientation
            # rotate_and_overwrite(file_path)

            encoded_image = base64.b64encode(open(file_path, "rb").read()).decode(
                "utf-8"
            )

            # Generate JSON entries for each encoded image
            image_entries = [
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"},
                }
            ]

        except:
            raise SystemExit("Invalid file path.")

    headers = {
        "Content-Type": "application/json",
        "api-key": GPT4V_KEY,
    }

    # Payload for the request
    payload = {
        "messages": [
            {"role": "system", "content": [{"type": "text", "text": system_prompt}]},
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": user_prompt},
                    # Add all image entries here
                    *image_entries,
                ],
            },
        ],
        "temperature": 0,
        "top_p": 0,
        "max_tokens": 2000,
    }

    GPT4V_ENDPOINT = "https://petsure-openai-api.openai.azure.com/openai/deployments/gpt4-vision/chat/completions?api-version=2023-07-01-preview"

    # Send request
    try:
        response = requests.post(GPT4V_ENDPOINT, headers=headers, json=payload)
        response.raise_for_status()  # Will raise an HTTPError if the HTTP request returned an unsuccessful status code
    except requests.RequestException as e:
        raise SystemExit(f"Failed to make the request. Error: {e}")

    return response


if __name__ == "__main__":
    df = pd.read_excel(
        r"C:\Users\<USER>\Documents\Projects\OCR_inhouse\data\invoices_0.8_paddle_ocr_clean.xlsx"
    )

    # folder_path = "C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\100_invoices_0.8_gpt_ocr"
    folder_path = "C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\test"
    file_paths = get_files_from_folder(folder_path)

    params = {
        "temperature": 0,
        "max_tokens": 5000,
        "top_p": 0,
        "frequency_penalty": 0,
        "presence_penalty": 0,
        "stop": None,
    }

    # Loop through each file and process
    class_list = []
    filename_list = []
    json_list = []

    # Define Prompts
    system_prompt = "You are an AI assistant that helps to extract documents."

    user_prompt = """Please analyse the invoice and populate the provided JSON structure accordingly based on the list of extraction value provided. If information does not exist in the list of extraction value, return 'null' in the JSON.
                    Animal Details:
                    - Name and other details should be populated in the ["VetXMLClaim"]["InfoFromPolicyHolder"]["AnimalDetails"] section.
                    Vet Practice Details:
                    - Vet Name, Practice Name, Address, and other practice-related details should be populated in the ["VetXMLClaim"]["InfoFromVet"]["Vet"] section.

                    Invoice Details:
                    -  Invoice Amount, Invoice Date, and other related details should be populated in the ["VetXMLClaim"]["InfoFromVet"]["Conditions"]["Invoices"] section.
                    - Amount shown in invoice refers to TotalIncVAT unless being specified as AmountExVAT.
                    - The ["Conditions"]["Financial"][ "Invoices"]["TotalIncVat"] should be equal to the total of ["Conditions"]["Financial"][ "Invoices"]["Items"]["TotalIncVat"] in all items.
                    - If the invoices have different invoice number, split it into different list in"Invoices".



                    Invoice Line Item Details:
                    1. Double-check the spelling and details of each drug or treatment name to ensure accuracy.
                    2. Ensure the quantity, price, and any other related details match the invoice.
                    3. Item Descriptions, Item Amounts, and other related item details should be populated in the ["VetXMLClaim"]["InfoFromVet"]["Conditions"]["Invoices"]["Items"] section.
                    4. Note that it is possible to have a negative amount in item description as discount.
                    5. Extract the line item and amount accordingly from the invoice, if unclear return 'null', do not change the item description.

                    Document Metadata:
                    Extract metadata about the image and populate the ["VetXMLClaim"]["DocumentMetadata"] section. This should include information about the header, body, footer and other interesting aspects from the image.

                    Note that:
                    1. if invoice number is not present in the invoice, consider transaction no as invoice number
                    2. never take invoice number from the ETFPOS
                    3. if the information is unclear and not fully visible, return null without justification
                    4. if the document is cut off or crop, return null without justification
                    5. do not include comments with // or explanations in the JSON output
                    6. do not change the format of the given JSON
                    7. no justification is needed in the output





                    JSON
                    {
                    "VetXmlClaim": {
                        "InfoFromPolicyHolder": {
                            "AnimalDetails": {
                                "Name": null
                            }
                        },
                        "InfoFromVet": {
                            "Vet": {
                                "VetName": null,
                                "PracticeName": null,
                                "PracticeAddress": null,
                                "PracticeABN": null,
                                "PracticePhoneNumber": null,
                                "PracticeFaxNumber": null,
                                "PracticeEmailAddress": null,
                                "PracticeWebsite": null
                            },
                            "Conditions": [
                                {
                                    "Financial": {
                                        "TotalExVAT": null,
                                        "VAT": null,
                                        "TotalIncVat": null,
                                        "Invoices": [
                                            {
                                                "InvoiceNumber": null,
                                                "InvoiceDate": null,
                                                "TotalExVat": null,
                                                "VAT": null,
                                                "TotalIncVat": null,
                                                "Items": [
                                                    {
                                                        "TreatmentDate": null,
                                                        "ItemCode": null,
                                                        "ItemType": null,
                                                        "Sequence": null,
                                                        "Description": null,
                                                        "AmountExVAT": null,
                                                        "DiscountExVaT": null,
                                                        "VAT": null,
                                                        "Quantity": null,
                                                        "TotalIncVAT": null
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                }
                            ],
                            "DocumentMetadata": {}
                        }
                    }
                    }

                    List of Extraction value is"""

    ## Document extraction
    for file_path in file_paths:
        filename_path = Path(file_path).name
        filename = filename_path.split(".")[0]

        if "_p" in filename:
            continue

        filename_list.append(filename)

        if filename in df["filename"].values:
            extraction_values = df.loc[df["filename"] == filename, "Text"].values[0]
            user_prompt_final = user_prompt + extraction_values

        try:
            # Call the GPT-vision function and get the response
            response = call_gptvision(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                file_path=file_path,
                **params,
            )
            output_response = response.json()["choices"][0]["message"]["content"]

        except Exception as e:
            print(f"An error occurred while processing {file_path}: {e}")
            output_response = None

        # Extract the list from the classified response if it's not None
        if output_response is not None:
            extracted_json = convert_to_json(output_response)
            json_list.append(extracted_json)

        else:
            # If output_response is None, append None to Json_list
            json_list.append(None)

        print(f"File: {file_path}")
        print(f"JSON Output: {output_response}")

        time.sleep(60)  # Pause for 60 seconds (1 minute) after each response

    df_output = pd.DataFrame()
    df_output["FileName"] = filename_list
    df_output["JsonResponse"] = json_list

    print(df_output)

    df_output.to_excel(
        "C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_ocr_invoice_0.8.xlsx"
    )
