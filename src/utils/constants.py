"""
Definition of some constants
"""
from __future__ import annotations

from enum import Enum


class Status(str, Enum):
    """

    list the possible status of nodes, tasks, or pipelines

    """

    none = "NONE"
    start = "START"
    running = "RUNNING"
    fail = "FAIL"
    success = "SUCCESS"


class StorageType(str, Enum):
    BLOB = "azure.blob.storage"
    LOCAL = "local"
    KV = "azure.keyvault"
    SQL = "ms.sql.database"


class StorageAction(str, Enum):
    UPLOAD = "upload_to"
    DOWNLOAD = "download_from"


class FileFormat(str, Enum):
    DOCX = "docx"
    PDF = "pdf"
    JPG = "jpg"
    PNG = "png"
