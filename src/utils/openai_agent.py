import base64
from pathlib import Path


def encode_image(image_path: Path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def create_messages(role: str, text: str, images=None) -> dict:
    """
    Create a message object for the chatbot.

    :param str role: message role, either 'system' or 'user'
    :param str text: text message
    :param str | list images: path(s) to images to be processed, defaults to None
    :return dict: created message object
    """
    assert isinstance(role, str) and isinstance(
        text, str
    ), "Role and text should be strings"
    assert role in [
        "system",
        "user",
        "assistant",
    ], "Invalid role, should be either 'system', 'user' or 'assistant'"

    message = [{"type": "text", "text": text}]

    if images:
        assert isinstance(
            images, (str, list)
        ), "Images should be a string or a list of strings"

        if isinstance(images, str):
            images = [images]

        image_message_list = [
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/{Path(image).suffix[1:]};base64,{encode_image(image)}"
                },
            }
            for image in images
        ]

        message.extend(image_message_list)

    return {"role": role, "content": message}
