import re
from typing import List, <PERSON><PERSON>, Union

from fitz import Point, Rect


# BBOX
def update_block_numbers(
    blocks: List[List], reset_block_no: bool, last_block_no: int
) -> List[List]:
    """Update block numbers based on the reset_block_no flag."""
    if reset_block_no:
        for idx, block in enumerate(blocks, start=1):
            block[5] = idx
    else:
        for block in blocks:
            block[5] += last_block_no

    return blocks


def exclude_empty_lines(
    block: List[Union[float, str, int]],
) -> List[Union[float, str, int]]:
    """Remove empty lines at the end of a block and adjust its size."""
    text = block[4]
    num_lines = len(text.split("\n"))

    pattern = re.compile(r"((?:\n *)+)$")
    matches = pattern.findall(text)
    num_trailing_empty_lines = len(matches[0].split("\n")) - 1 if matches else 0

    if num_trailing_empty_lines > 1:
        block[4] = pattern.sub(r"\n", text)
        line_height = (block[3] - block[1]) / num_lines
        block[3] -= line_height * (num_trailing_empty_lines - 1)

    return block


def is_close(block1: Tuple, block2: Tuple, threshold: int = 5) -> bool:
    """Check if two blocks are close to each other."""
    rect1, rect2 = Rect(block1[:4]), Rect(block2[:4])

    if threshold > 0:
        for rect in (rect1, rect2):
            rect.x0 -= threshold
            rect.x1 += threshold
            rect.y0 -= threshold
            rect.y1 += threshold

    return rect1.intersects(rect2)


def merge_blocks(block1: Union[List, Tuple], block2: Union[List, Tuple]) -> List:
    """Merge two blocks into one."""
    return [
        min(block1[0], block2[0]),
        min(block1[1], block2[1]),
        max(block1[2], block2[2]),
        max(block1[3], block2[3]),
        f"{block1[4]}\n{block2[4]}",
        0,  # Temporary value, will be reset after merging
        block1[-1] if block1[-1] == block2[-1] else 2,
    ]


def add_padding(rect: Union[Tuple, List, Rect], padding: int = 2) -> Rect:
    """Add padding to a rectangle."""
    if not isinstance(rect, Rect):
        rect = Rect(rect)

    rect.x0 -= padding
    rect.y0 -= padding
    rect.x1 += padding
    rect.y1 += padding

    return rect


def get_text_point(rect_like: Tuple, x_offset: int = -18, y_offset: int = 10) -> Point:
    """Get the text point for a rectangle."""
    init_x, init_y = Rect(rect_like).tl
    return Point(init_x + x_offset, init_y + y_offset)
