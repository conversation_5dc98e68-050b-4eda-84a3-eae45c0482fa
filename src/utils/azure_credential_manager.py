from azure.identity import EnvironmentCredential


class AzureCredentialManager:
    """
    Manage credentials by providing methods to get and reset credentials based on the name provided.

    Methods:
        - get(name: str): Retrieves the credential associated with the given name. If the credential does not exist, a new one is created using EnvironmentCredential.
        - reset(name: str): Removes the credential associated with the given name from the manager.

    Attributes:
        - _credentials: A dictionary storing credentials with their respective names.
    """

    _credentials = {}
    # github-action
    # python-service-principal

    @classmethod
    def get(cls, name: str) -> EnvironmentCredential:
        if name not in cls._credentials:
            cls._credentials[name] = EnvironmentCredential()
        return cls._credentials.get(name)

    @classmethod
    def reset(cls, name: str):
        if name in cls._credentials:
            cls._credentials.pop(name)
        return cls.get(name)

    @classmethod
    def list(cls):
        print(list(cls._credentials.keys()))
