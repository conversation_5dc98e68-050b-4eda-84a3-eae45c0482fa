"""
the utility functions
"""
import difflib
import hashlib
import json
import os
import random
import string
import unicodedata
import zipfile
from pathlib import Path
from string import Formatter
from typing import Dict, List, Union

from loguru import logger

DEFAULT_FUZZY_MATCHING_THRESHOLD = 0.7
DEFAULT_FUZZY_MATCHING_EXTENSION = 5
DEFAULT_FUZZY_MATCHING_MAX_RESULT_RETURN = 10


def find_similar_substrings(
    full_string: Union[str, List[str]],
    query_string: str,
    threshold: float = DEFAULT_FUZZY_MATCHING_THRESHOLD,
    extension: int = DEFAULT_FUZZY_MATCHING_EXTENSION,
    max_result_return: int = DEFAULT_FUZZY_MATCHING_MAX_RESULT_RETURN,
):
    """
    Finds substrings in the full_string that are similar to the query string.

    Parameters:
    full_string (str): The large string in which to search.
    query_string (str): The substring to find similar substrings to.
    threshold (float): The minimum similarity score (0 to 1) to consider a match.
    extension (int): The extension length of query string to be included.
    max_result_return (int): The maximum number of results returned.

    Returns:
    list of tuples: Each tuple contains (substring, start_index, score).
    """
    assert (
        isinstance(threshold, float) and 0.0 <= threshold <= 1.0
    ), f"threshold={threshold} should be a float between 0.0 and 1.0."
    assert (
        isinstance(extension, int) and 0 <= extension
    ), f"extension={extension} should be an int and greater than 0."
    assert (
        isinstance(max_result_return, int) and 1 <= max_result_return
    ), f"max_result_return={max_result_return} should be an int and greater than 1."

    results = []
    if isinstance(full_string, str):
        full_string_list = [full_string]
    elif isinstance(full_string, List):
        full_string_list = full_string
    else:
        logger.exception(
            "full_string should be string or list of string. No results returned"
        )
        return results

    query_len = len(query_string)
    for full_string in full_string_list:
        full_string_len = len(full_string)

        min_query_len = max(1, query_len - extension)
        max_query_len = min(full_string_len, query_len + extension)

        for possible_query_len in range(min_query_len, max_query_len + 1):
            for i_sub_str_start in range(full_string_len - possible_query_len + 1):
                substring = full_string[
                    i_sub_str_start : i_sub_str_start + possible_query_len
                ]
                score = difflib.SequenceMatcher(None, query_string, substring).ratio()
                if score >= threshold:
                    results.append((substring, i_sub_str_start, score))

    max_result_return = min(max(1, max_result_return), len(results))
    results = sorted(results, key=lambda x: (x[-1], -len(x[0]), -x[1]), reverse=True)[
        :max_result_return
    ]

    return results


def get_template_vars(template_str: str) -> List[str]:
    """Get template variables from a template string."""
    variables = []
    formatter = Formatter()

    for _, variable_name, _, _ in formatter.parse(template_str):
        if variable_name:
            variables.append(variable_name)

    return variables


def generate_run_no(length) -> str:
    """
    Generates a random alphanumeric string of the given length.

    Args:
        length (int): The desired length of the generated string.

    Returns:
        str: A random sequence consisting of uppercase letters, lowercase letters, and digits.
    """
    # Create a sequence of letters (uppercase and lowercase) and digits
    characters = string.ascii_letters + string.digits
    # Generate a random sequence of the specified length
    random_sequence = "".join(random.choice(characters) for i in range(length))
    return random_sequence


def generate_file_id(file_name: str) -> str:
    """
    Generates a unique file identifier by computing the MD5 hash of the file name.

    Args:
        file_name (str): The name of the file to hash.

    Returns:
        str: The hexadecimal digest of the MD5 hash of the file name.
    """
    md5hash = hashlib.md5(file_name.encode())
    return md5hash.hexdigest()


def extract_json(text) -> Dict:
    """
    Attempts to extract a valid JSON object from the given text.

    Args:
        text (str): The text from which to extract the JSON object.

    Returns:
        Dict: The first valid JSON object found, or an empty dictionary if none is found.
    """
    pos = 0
    decoder = json.JSONDecoder()
    while True:
        match = text.find("{", pos)
        if match == -1:
            break
        try:
            result, index = decoder.raw_decode(text[match:])
            return result
            pos = match + index
        except ValueError:
            pos = match + 1
    return {}


def get_root_dir() -> Path:
    """
    Get the root directory of the project

    :return Path: the root directory
    """
    return Path(os.path.dirname(__file__)).parents[1]


def get_greek_letters() -> str:
    """Generate a string of Greek letters."""
    return "".join(
        chr(code_point)
        for code_point in range(0x0370, 0x03FF)
        if "GREEK" in unicodedata.name(chr(code_point), "")
    )


def unzip(
    source_file_path: str | Path, target_storage_folder_path: str | Path, override: bool
) -> None:
    source_file_path = Path(source_file_path)
    target_storage_folder_path = Path(target_storage_folder_path)

    assert (
        source_file_path.exists and source_file_path.is_file()
    ), f"source_file__path={source_file_path} is not a validated file."
    if not override:
        assert not target_storage_folder_path.exists(), f"target_storage_folder_path={target_storage_folder_path} exists. Provide a new extraction folder path."

    try:
        with zipfile.ZipFile(source_file_path, "r") as zip_ref:
            zip_ref.extractall(target_storage_folder_path)
    except Exception as e:
        raise e


def get_image_paths(target_dir: Path) -> List[str]:
    """Get the paths of the images in the specified directory."""
    image_paths = []
    for pattern in ("*.png", "*.jpg", "*.jpeg", "*.gif", "*.bmp"):
        image_paths.extend(Path(target_dir).glob(pattern))
    image_paths.sort()
    image_paths = [str(image_path) for image_path in image_paths]
    return image_paths
