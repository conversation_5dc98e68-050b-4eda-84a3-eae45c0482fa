"""
Base Store Class
"""
import json
import os
import pickle
import re
import traceback
from pathlib import Path
from typing import Any

import pandas as pd
import sqlalchemy as sa
from azure.keyvault.secrets import SecretClient
from azure.storage.blob import BlobServiceClient
from dotenv import load_dotenv
from loguru import logger
from sqlalchemy import URL, text

from src.utils.azure_credential_manager import AzureCredentialManager
from src.utils.constants import StorageType


class Storage(dict):
    _kv_clients = {}

    def __init__(self, type: StorageType, **argv) -> None:
        assert isinstance(type, StorageType)
        self.storage_type: StorageType = type

        self._client = None

        match type:
            case StorageType.BLOB:
                assert (
                    "connection_str" in argv
                ), "connection_str should be provided in function arguments when connecting to azure blob storage."
                connection_str = argv["connection_str"]

                try:
                    self._client = BlobServiceClient.from_connection_string(
                        connection_str
                    )
                except Exception:
                    logger.error(
                        "Initialising blob service client failed! Please check the provided connection string!"
                    )
            case StorageType.LOCAL:
                # don't need any client or local file system
                # asssign a str to it for consistency
                self._client = "local"
            case StorageType.KV:
                assert (
                    "kv_name" in argv
                ), "kv_name should be provided for a keyvault storage"
                kv_name = argv["kv_name"]

                if kv_name not in self._kv_clients:
                    KVUri = f"https://{kv_name}.vault.azure.net"
                    credential = AzureCredentialManager.get(
                        "github-action"
                    )  # TODO: manager different env
                    self._kv_clients[kv_name] = SecretClient(
                        vault_url=KVUri, credential=credential
                    )
                self._client = self._kv_clients[kv_name]
            case StorageType.SQL:
                assert "server" in argv
                assert "database" in argv

                load_dotenv()
                username = os.environ.get("DB_USERNAME")
                password = os.environ.get("DB_PASSWORD")

                assert (
                    username is not None and password is not None
                ), "Please set DB_USERNAME and DB_PASSWORD in .env file"

                params = {
                    "drivername": "mssql+pymssql",
                    "username": f"PETSURE\\{username}",
                    "password": password,
                    "host": argv["server"],
                    "port": "1433",
                    "database": argv["database"],
                }
                connection_url = URL.create(**params)

                try:
                    self._client = sa.create_engine(connection_url)
                except Exception as e:
                    logger.error(e)
                    logger.error(
                        "Initialising sql engine failed! Please check the provided db info!"
                    )
            case _:
                pass

        dict.__init__(
            self,
            storage_type=type,
        )

    def push(self, **argv) -> bool:
        assert self._client, "The storage object has not been initialised yet."

        match self.storage_type:
            case StorageType.BLOB:
                assert (
                    "container_name" in argv
                    and "blob_name" in argv
                    and "file_path" in argv
                ), "Please provide container_name, blob_name, and file_path in functin argument."
                container = argv["container_name"]
                blob = argv["blob_name"]
                file_path = argv["file_path"]

                return self._push_blob(
                    container,
                    blob,
                    file_path,
                    overwrite=argv["overwrite"] if "overwrite" in argv else True,
                )
            case StorageType.KV:
                assert (
                    "key_name" in argv and "key_value" in argv
                ), "Please provide the right value of key_name and key_value."
                key_name = argv["key_name"]
                key_value = argv["key_value"]

                self._push_kv(key_name, key_value)
            case StorageType.LOCAL:
                assert (
                    "object" in argv and "file_path" in argv
                ), "Please provide file_path and object as an function argument"
                obj = argv["object"]
                file_path: Path = argv["file_path"]
                use_pickle: bool = argv.get("pickle", False)
                assert isinstance(use_pickle, bool), "argument pickle should be a bool"

                self._push_local(obj, file_path, use_pickle)
            case StorageType.SQL:
                assert (
                    "dataframe" in argv and "table_name" in argv
                ), "Please provide dataframe and table_name in function argument."
                df: pd.DataFrame = argv["dataframe"]
                table_name: str = argv["table_name"]

                # make sure the table_name is a format of [DB].[schema].[table]
                assert re.match(
                    pattern=r"\w+\.\w+\.\w+", string=table_name
                ), "table_name should follow the pattern [DATABASE NAME].[SCHEMA].[TABLE NAME]"
                _, schema, table = table_name.split(".")

                return self._push_sql(df, table, schema)

    def _push_blob(
        self, container: str, blob: str, file_path: Path, overwrite: bool = True
    ) -> bool:
        try:
            container_client = self._client.get_container_client(container)
            with open(file=file_path, mode="rb") as data:
                logger.debug(
                    f"Pushing file [{file_path}] to blob container [{container}] as [{blob}]"
                )

                container_client.upload_blob(
                    name=blob,
                    data=data,
                    overwrite=overwrite,
                )

            return True
        except Exception:
            traceback.print_exc()
            return False

    def _push_kv(self, key_name, key_value):
        self._client.set_secret(key_name, key_value)

    def _push_local(self, object: Any, file_path: Path, use_pickle: bool):
        with open(file_path, "w") as f:
            if use_pickle:
                pickle.dump(object, f)
            elif file_path.suffix == ".json" and isinstance(object, dict):
                json.dump(object, f, indent=4)
            else:
                f.write(object)

    def _push_sql(self, dataframe, table, schema):
        try:
            dataframe.to_sql(
                name=table,
                con=self._client,
                schema=schema,
                if_exists="append",
                index=False,
            )
            return True
        except Exception:
            traceback.print_exc()
            return False

    def pull(self, **argv) -> Any:
        assert self._client, "The storage object has not been initialised yet."

        match self.storage_type:
            case StorageType.BLOB:
                assert (
                    "container_name" in argv
                    and "blob_name" in argv
                    and "download_dir" in argv
                ), "Pleae provide container, blob and download_dir in function argument."

                container = argv["container_name"]
                blob = argv["blob_name"]
                download_dir = argv["download_dir"]

                return self._pull_blob(container, blob, download_dir)
            case StorageType.LOCAL:
                assert (
                    "file_path" in argv
                ), "Please provide file_path as a function argument."
                file_path: Path = argv["file_path"]
                use_pickle: bool = argv.get("use_pickle", False)
                assert isinstance(use_pickle, bool), "use_pickle should be bool"

                self._pull_local(file_path, use_pickle)
            case StorageType.KV:
                assert (
                    "key_name" in argv
                ), "Please provide key_name as an fucntion argument."
                assert isinstance(self._client, SecretClient)
                key_name = argv["key_name"]

                return self._pull_kv(key_name)
            case StorageType.SQL:
                assert "query" in argv, "Please provide query as an function argument."
                query = text(argv["query"])

                return self._pull_sql(query)

    def _pull_blob(self, container: str, blob: str, download_dir: Path) -> Any:
        try:
            blob_client = self._client.get_blob_client(container=container, blob=blob)
            logger.debug(f"blob_client: {blob_client}")

            if not blob_client.exists():
                raise FileNotFoundError(
                    f"Blob [{blob}] does not exist in container [{container}]"
                )

            blob_name = Path(blob).name
            if not download_dir.is_dir():
                logger.debug(
                    f"download_dir not exists! Creating download directory: [{download_dir}]"
                )
                download_dir.mkdir(parents=True)

            with open(file=download_dir / blob_name, mode="wb") as blob_file:
                logger.debug(
                    f"Downloading [{blob_name}] from [{container}] to [{download_dir}]"
                )
                download_stream = blob_client.download_blob()
                blob_file.write(download_stream.readall())

            return download_dir / blob_name
        except Exception:
            traceback.print_exc()
            return None

    def _pull_kv(self, key_name: str) -> str:
        return self._client.get_secret(key_name).value

    def _pull_local(self, file_path: Path, use_pickle: bool) -> Any:
        with open(file_path, "r") as f:
            if use_pickle:
                return pickle.load(f)
            elif file_path.sufffix == ".json":
                return json.load(f, indent=4)
            else:
                return f.read()

    def _pull_sql(self, query) -> pd.DataFrame:
        try:
            with self._client.connect() as conn:
                df = pd.read_sql(
                    sql=query,
                    con=conn,
                )
                return df
        except Exception:
            traceback.print_exc()
            return None

    def remove(self, **argv) -> Any:
        assert self._client, "The storage object has not been initialised yet."

        match self.storage_type:
            case StorageType.KV:
                assert (
                    "key_name" in argv
                ), "Please provide key_name as an fucntion argument."
                self._client.begin_delete_secret(argv["key_name"])

    def __deepcopy__(self, memodict={}):
        # TODO create another instance as the result of deepcopy
        return self


if __name__ == "__main__":
    kv_storage = Storage(type=StorageType.KV, kv_name="PS-AI-keyvault")

    blob_storage = Storage(type=StorageType.BLOB)
