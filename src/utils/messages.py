import json
from enum import Enum

from ulid import ULID

Stage = Enum(
    "Stage",
    (
        "INITIALISING",
        "UNZIPPING",
        "PROCESSINGIMAGES",
        "PARSINGDOCUMENT",
        "GENERATINGCOMMENTS",
        "WRAPPINGUP",
    ),
)


def pack_message_response(
    filename: str,
    stage: Stage,
    status: str = "",
    percentage: str = "",
    prompt_id: int = -1,
    prompt_text: str = "",
    breach: bool = False,
    justification: str = "",
    recommendation: str = "",
    reference: str = "",
    index: str = "",
    risk_level: str = "None",
    confidence: int = 0,
    id: int = 0,
    rules: str = "",
    error: bool = False,
) -> dict:
    """
    Pack the data into a dictionary with the key 'message'.

    Parameters:
    - data: The data to be packed.

    Returns:
    - A dictionary with the key 'message'.
    """
    if id == 0:
        id = str(ULID())

    message = {}
    if prompt_id != -1:
        message = {
            "filename": filename,
            "stage": stage.value,
            "status": status,
            "percentage": percentage,
            "rule_id": prompt_id,
            "prompt_text": prompt_text,
            "breach": breach,
            "justification": justification,
            "recommendation": recommendation,
            "references": reference,
            "index": index,
            "risk_level": risk_level,
            "confidence": confidence,
            "error": error,
        }
    else:
        if rules:
            message = {
                "filename": filename,
                "stage": stage.value,
                "status": status,
                "rules": rules,
                "error": error,
            }
        else:
            message = {
                "filename": filename,
                "stage": stage.value,
                "status": status,
                "error": error,
            }

    message_str = json.dumps(message)

    return {"event": "message", "id": id, "retry": 3000, "data": message_str}


def return_and_log(
    filename: str, user_id: str, timestamp: str, **kwargs
) -> tuple[dict, dict]:
    response = pack_message_response(
        filename=filename,
        **kwargs,
    )
    record = {
        "FileName": filename,
        "Message": response["data"],
        "UserID": user_id,
        "Timestamp": timestamp,
    }
    return response, record
