import re
from string import punctuation

import pandas as pd
from gensim.models import FastText


# from src.service_provider_matching.preprocessing.preprocess import preprocess
def preprocess(text):
    """
    Preprocessing of Names, Addresses
    :param text: string
    :return: string
    """
    text = str(text)
    text = text.lower()
    text = "".join(c for c in text if c not in punctuation)
    text = "".join([x for x in text if x.isalnum() or x == " "])
    # Replace SP words/phrases
    text = re.sub(
        r"((?<=[a-z])(veterinary))", " veterinary", text
    )  # veterinary contained within strings
    text = re.sub(r"((veterinary)(?=[a-z]))", "veterinary ", text)
    text = re.sub(r"(\bveterinary\b)", "vet", text)
    text = re.sub(r"((?<=[a-z])(vet))", " vet", text)  # seperate "(character)vet"
    text = re.sub(r"(\bpty ltd\b)", "", text)
    text = re.sub(r"(\bclinic\b)", "", text)  # generic words
    text = re.sub(r"(\bcentre\b)", "", text)
    text = re.sub(" +", " ", text)  # remove double spaces
    text = text.lstrip().rstrip()
    # Replace Aus states
    text = re.sub(r"(australian capital territory)", "act", text)
    text = re.sub(r"(western australia)", "wa", text)
    text = re.sub(r"(south australia)", "sa", text)
    text = re.sub(r"(victoria)", "vic", text)
    text = re.sub(r"(new south wales)", "nsw", text)
    text = re.sub(r"(tasmania)", "tas", text)
    text = re.sub(r"(queensland)", "qld", text)
    text = re.sub(r"(northern territory)", "nt", text)
    # Replace street types
    text = re.sub(r"(\broad\b)", "rd", text)
    text = re.sub(r"(\bstreet\b)", "st", text)
    text = re.sub(r"(\bavenue\b)", "ave", text)
    text = re.sub(r"(\bclose\b)", "cl", text)
    text = re.sub(r"(\bcourt\b)", "ct", text)
    text = re.sub(r"(\blane\b)", "ln", text)
    text = re.sub(r"(\bparade\b)", "pde", text)
    text = re.sub(r"(\bterrace\b)", "tce", text)
    text = re.sub(r"(\bcrescent\b)", "crst", text)
    text = re.sub(r"(\bboulevard\b)", "blvd", text)
    text = re.sub(r"(\bplace\b)", "pl", text)
    text = re.sub(r"(\bdrive\b)", "dve", text)
    text = re.sub(r"(\bhighway\b)", "hwy", text)
    return text


# Read in Service Provider data
serv_prov = pd.read_csv(
    "/workspaces/OCR_in_house/data/ref_service_provider_updated.csv"
)
serv_prov = serv_prov.fillna("")
serv_prov["PostCode"] = serv_prov["PostCode"].astype(str)

# Concat to form full Address
serv_prov["Address"] = (
    serv_prov["Address"]
    + " "
    + serv_prov["City"]
    + " "
    + serv_prov["State"]
    + " "
    + serv_prov["PostCode"]
)
# Preprocessing
for field in ["ServiceProviderName", "Address"]:
    serv_prov[field] = serv_prov[field].apply(preprocess)

print("Begin training...")
for field in ["ServiceProviderName", "Address"]:
    # Extract list of lists
    key_list = serv_prov[field].values.tolist()
    key_list = [[val] for val in key_list]
    # Run FastText model
    FT_model = FastText(
        key_list,
        vector_size=50,
        window=5,
        min_count=1,
        workers=4,
        sg=1,
        epochs=100,
        bucket=20000,
    )
    FT_model.save("/workspaces/OCR_in_house/data/FT_model_updated_" + field)
    print(field + " model saved!")
