from gensim.models import Word2Vec
from preprocessing.preprocess import preprocess

# Only for FT model testing

# Dictionary to link between OCR data and list of Service Provider columns
sp_dict = {
    "Name": "ServiceProviderName",
    "Address": "Address",
    "abn": "ABN",
    "email": "Email",
    "web": "HomePage",
    "phone_home": "PhoneNo_Home",
    "phone_work": "PhoneNo_Work",
    "phone_mobile": "PhoneNo_Mobile",
    "fax": "FaxNo",
}

# Load FT models
FT_model = {}
for field in ["Name", "Address"]:
    FT_model[field] = Word2Vec.load("FT_model_" + sp_dict[field])
    print("FastText model loaded...", field)

field = "Name"
text = "ultra vets"

print(FT_model[field].wv.most_similar(preprocess(text), topn=5))
