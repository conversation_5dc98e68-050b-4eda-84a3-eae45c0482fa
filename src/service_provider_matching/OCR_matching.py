import ast

import numpy as np
import pandas as pd
from gensim.models import Word2Vec
from preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web

# Dictionary to link between OCR data and list of Service Provider columns
sp_dict = {
    "Name": "ServiceProviderName",
    "Address": "Address",
    "streetname_name": "ServiceProviderName",
    "suburb_name": "ServiceProviderName",
    "abn": "ABN",
    "email": "Email",
    "web": "HomePage",
    "phone_home": "PhoneNo_Home",
    "phone_work": "PhoneNo_Work",
    "phone_mobile": "PhoneNo_Mobile",
    "fax": "FaxNo",
}

# List of hyperparameters
top_n = 10  # Top number of fuzzy matches to keep for Name, Address
cos_l = 0.6  # Lower limit on cosine acceptance
A_log = 1.0  # Logarithmic amplitude for scaling ABN multi-matches
priority_dict = {  # Dictionary to link OCR fields to priority
    "Name": 1.0,
    "Address": 1.0,
    "streetname_name": 0.5,
    "suburb_name": 0.5,
    "abn": 1.0,
    "email": 1.0,
    "web": 0.25,
    "phone_home": 0.5,
    "phone_work": 0.5,
    "phone_mobile": 0.5,
    "fax": 0.5,
}

if __name__ == "__main__":
    ##########################
    # Load and preprocess data
    ##########################

    # Read in Service Provider raw data
    serv_prov_raw = pd.read_csv("data/serv_prov.csv")
    serv_prov = serv_prov_raw.copy()  # create copy of raw data
    serv_prov = serv_prov.fillna("")
    serv_prov["PostCode"] = serv_prov["PostCode"].astype(str)
    print("Service Provider data loaded...")

    # Load list of fields to check in iteration
    field_list = list(sp_dict.keys())
    priority_scores = [priority_dict[field] for field in field_list]

    # Preprocessing of Service Provider List
    serv_prov["Address"] = (
        serv_prov["Address"]
        + " "
        + serv_prov["City"]
        + " "
        + serv_prov["State"]
        + " "
        + serv_prov["PostCode"]
    )  # Concat fields to form full Address
    for field in [
        "ServiceProviderName",
        "Address",
        "ABN",
        "Email",
        "HomePage",
        "PhoneNo_Home",
        "PhoneNo_Work",
        "PhoneNo_Mobile",
        "FaxNo",
    ]:
        if field in ["ServiceProviderName", "Address"]:
            serv_prov[field] = serv_prov[field].apply(preprocess)
        elif field in ["Email", "HomePage"]:
            serv_prov[field] = serv_prov[field].apply(preprocess_web)
        elif field in [
            "PhoneNo_Home",
            "PhoneNo_Work",
            "PhoneNo_Mobile",
            "FaxNo",
            "PhoneNo_Home",
        ]:
            serv_prov[field] = serv_prov[field].apply(preprocess_numbers)

    # Read in OCR data after parsing
    ocr_data = pd.read_csv("preprocessing/dataaa1006_fixed.csv")
    print("OCR data loaded...")

    # Load FT models into dictionary
    FT_model = {}
    for field in ["Name", "Address"]:
        FT_model[field] = Word2Vec.load("FTmodels/FT_model_" + sp_dict[field])
        print("FastText model loaded...", field)

    #########################
    # Iteration over OCR data
    #########################

    results_list = []
    for i, row in ocr_data.iterrows():
        print("Begin Iteration...", i)
        # Prepare list of match index and cosine values
        match_index_list = []
        match_cosine_list = []

        # Begin iterating over field list
        for field in field_list:
            if row[field] is not np.NaN:
                # fields stored in lists (numerical)
                if field in ["abn", "phone_home", "phone_work", "phone_mobile", "fax"]:
                    query_list = [
                        preprocess_numbers(query)
                        for query in ast.literal_eval(row[field])
                    ]  # Convert string to list of integers
                    query_list = [query for query in query_list if query != 99999999]
                # fields stored in lists (non-numerical)
                elif field in ["email", "web"]:
                    query_list = [
                        preprocess_web(query) for query in ast.literal_eval(row[field])
                    ]  # Convert string to list of strings
                # fields stored in lists (requires Fuzzy Search)
                elif field in ["Name", "Address"]:
                    query_list = [
                        query for query in ast.literal_eval(row[field])
                    ]  # multiple strings in list
                    # flatten entire fuzzy match list
                    fuzzy_list = []
                    for query in query_list:
                        fuzzy_list += FT_model[field].wv.most_similar(query, topn=top_n)
                    cosine_dict = {
                        query: 1.0 for query in query_list if query
                    }  # Prepare dictionary of cosine value mappings
                    query_fuzzy_list = [
                        item[0] for item in fuzzy_list if item[1] >= cos_l
                    ]  # only keep items above threshold
                    # Add mappings for cosing values
                    cosine_fuzzy_dict = {
                        item[0]: item[1] for item in fuzzy_list if item[1] >= cos_l
                    }  # only keep items above threshold
                    # combine all mappings
                    cosine_dict = {**cosine_dict, **cosine_fuzzy_dict}
                    # combine all queries
                    query_list = query_list + query_fuzzy_list
                # Fields stored in list (does not require Fuzzy search)
                elif field in ["streetname_name", "suburb_name"]:
                    query_list = [
                        preprocess(query) for query in ast.literal_eval(row[field])
                    ]  # Convert string to list of strings
                # fields not stored in lists
                else:
                    query_list = [preprocess(row[field])]  # Put string in list

                # Build up index of matches
                if field in ["streetname_name", "suburb_name"] and len(query_list) > 0:
                    match_index = []
                    match_query = []
                    match_length = []
                    for query in query_list:  # Check all suburbs in query list
                        match_check = serv_prov[
                            sp_dict[field]
                        ].apply(
                            lambda x: query in x
                        )  # check if suburb or streetname in contained in list of SP names
                        match_index += serv_prov[
                            match_check
                        ].index.values.tolist()  # Append index list for each query
                        match_query += serv_prov[match_check][sp_dict[field]].tolist()
                elif field in ["abn"] and len(query_list) > 0:
                    match_index = []
                    match_query = []
                    match_length = []
                    for query in query_list:
                        match_check = serv_prov[sp_dict[field]].isin(query_list)
                        match_index += serv_prov[match_check].index.values.tolist()
                        match_query += serv_prov[match_check][sp_dict[field]].tolist()
                        match_length += [
                            len(match_index) for index in range(0, len(match_index))
                        ]  # Scaling of cosine score from number of matches returned
                else:
                    match_check = serv_prov[sp_dict[field]].isin(query_list)
                    match_index = serv_prov[match_check].index.values.tolist()
                    match_query = serv_prov[match_check][sp_dict[field]].tolist()
                # Map to cosine values
                if field in ["Name", "Address"]:
                    match_dict = dict(
                        zip(match_index, [cosine_dict[item] for item in match_query])
                    )
                    match_cosine = [match_dict[idx] for idx in match_index]
                elif field in ["abn"]:
                    match_cosine = [
                        1.0 / (A_log * np.log(match_length[idx]) + 1)
                        for idx in range(0, len(match_index))
                    ]
                else:
                    match_cosine = [1.0 for idx in range(0, len(match_index))]
                match_index_list.append(match_index)
                match_cosine_list.append(match_cosine)
            else:
                match_index_list.append([])
                match_cosine_list.append([])

        # Flatten entire match index list
        flat_index_list = [ind for sublist in match_index_list for ind in sublist]

        # Calculate counts, priority score and entities matched
        count_list = []
        for ind in set(flat_index_list):
            priority_score = 0
            fields_matched = []
            for n, match_index in enumerate(match_index_list):
                if ind in match_index:
                    cosine_score = match_cosine_list[n][match_index.index(ind)]
                    priority_score += priority_scores[n] * cosine_score
                    fields_matched.append(field_list[n])
            count_list.append(
                (ind, flat_index_list.count(ind), priority_score, fields_matched)
            )

        # Keep Top 5 best matches in descending order of priority score
        sorted_count_list = sorted(count_list, key=lambda tup: (tup[2]), reverse=True)[
            :5
        ]  # Sort by match count/ match priority?

        # Print best and second best matches to file
        if len(sorted_count_list) > 1:
            best_match_index = int([item[0] for item in sorted_count_list][0])
            best_match_count = int([item[1] for item in sorted_count_list][0])
            best_match_priority = [item[2] for item in sorted_count_list][0]
            best_match_fields = [item[3] for item in sorted_count_list][0]
            best_match_list = (
                serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
                .iloc[best_match_index]
                .values.tolist()
            )  # ,'Address','City','State','PostCode','PhoneNo_Home','PhoneNo_Work','PhoneNo_Mobile','FaxNo','Email','HomePage','ABN']].iloc[best_match_index].values.tolist()

            best_match_index_2 = int([item[0] for item in sorted_count_list][1])
            best_match_count_2 = int([item[1] for item in sorted_count_list][1])
            best_match_priority_2 = [item[2] for item in sorted_count_list][1]
            best_match_fields_2 = [item[3] for item in sorted_count_list][1]
            best_match_list_2 = (
                serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
                .iloc[best_match_index_2]
                .values.tolist()
            )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index_2].values.tolist()
        # Single Match
        elif len(sorted_count_list) == 1:
            best_match_index = int([item[0] for item in sorted_count_list][0])
            best_match_count = int([item[1] for item in sorted_count_list][0])
            best_match_priority = [item[2] for item in sorted_count_list][0]
            best_match_fields = [item[3] for item in sorted_count_list][0]
            best_match_list = (
                serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
                .iloc[best_match_index]
                .values.tolist()
            )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index].values.tolist()

            best_match_index_2 = None
            best_match_count_2 = 0
            best_match_priority_2 = 0
            best_match_fields_2 = None
            best_match_list_2 = [None]
        # No matches
        else:
            best_match_index = None
            best_match_count = 0
            best_match_priority = 0
            best_match_fields = None
            best_match_list = [None]

            best_match_index_2 = None
            best_match_count_2 = 0
            best_match_priority_2 = None
            best_match_fields_2 = None
            best_match_list_2 = [None]

        # export to list
        results_list.append(
            [row["FileName"]]
            + best_match_list
            + [best_match_fields, best_match_count, best_match_priority]
            + best_match_list_2
            + [best_match_fields_2, best_match_count_2, best_match_priority_2]
        )

    ###########################################
    # Write all results to dataframe and export
    ###########################################

    results_df = pd.DataFrame(
        results_list,
        columns=[
            "FileName",
            "Predicted Service Provider No",
            "Predicted Service Provider Name",
            "Matched Fields",
            "Number of Fields Matched",
            "Matched Priority Scores",
            "Predicted Service Provider No 2",
            "Predicted Service Provider Name 2",
            "Matched Fields 2",
            "Number of Fields Matched 2",
            "Matched Priority Scores 2",
        ],
    )

    # Join OCR fields
    merged_df = results_df.merge(ocr_data, on="FileName", how="inner")

    # Export results
    merged_df.to_csv("data/dataaa1706_results.csv")
    print("Results exported!")
