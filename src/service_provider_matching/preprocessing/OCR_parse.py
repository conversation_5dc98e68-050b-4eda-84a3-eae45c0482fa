import ast
import re

import pandas as pd
from preprocessing.preprocess import preprocess


def parse_phone(lst):
    """
    Phone numbers are extracted from the list-like OCR data
    :param lst: OCR token list
    :return: Phone Number list
    """
    lst = ast.literal_eval(lst)  # convert from string to list
    r = re.compile(
        r"^((\d{2})?\s?[0-9]{4}(\s|-)?[0-9]{4})$"
    )  # match phone numbers (incl. spaces, dashes, area code)
    ph_list = list(filter(r.match, lst))
    # Phone numbers seperated over list
    for i in range(0, len(lst)):
        if i > 0:
            if re.match(r"^\d{4}$", lst[i]) and re.match(
                r"^\d{4}$", lst[i - 1]
            ):  # match 8 digit number seperated in list
                ph_list.append("".join(lst[i - 1 : i + 1]))
            elif (
                re.match(r"^\d{3}$", lst[i])
                and re.match(r"^\d{3}$", lst[i - 1])
                and re.match(r"^\d{4}$", lst[i - 2])
            ):  # match 1300 number seperated in list
                ph_list.append("".join(lst[i - 2 : i + 1]))
    ph_list = list(set([ph for ph in ph_list]))

    return ph_list


def parse_ABN(lst):
    """
    ABNs are extracted from the list-like OCR data
    :param lst: OCR token list
    :return: ABN list
    """
    lst = ast.literal_eval(lst)  # convert from string to list
    r = re.compile("^([0-9]{10})$")  # match 10 digit numbers
    abn_lst = list(filter(r.match, lst))
    return abn_lst


def parse_email(lst):
    """
    Emails are extracted from the list-like OCR data
    :param lst: OCR token list
    :return: Email list
    """
    lst = ast.literal_eval(lst)  # convert from string to list
    r = re.compile(
        r"([a-zA-Z0-9+._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+)"
    )  # match email format
    em_lst = list(filter(r.match, lst))
    return em_lst


def parse_webpage(lst):
    """
    URLs are extracted from the list-like OCR data
    :param lst: OCR token list
    :return: URL list
    """
    lst = ast.literal_eval(lst)  # convert from string to list
    r = re.compile(
        r"(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})"
    )  # match url format
    wp_lst = list(filter(r.match, lst))
    return wp_lst


def parse_address(text):
    """
    Extracts multiple addresses from single string and puts in list
    :param text: Multiple address string
    :return: list of addresses from multiple in string
    """
    text = preprocess(text)
    text = re.split(r"(?<= \d{4})", text)  # get end of postcode
    text = [t for t in text if len(t) > 0]
    idx = 0
    while idx <= len(text) - 1:
        # Combine preceeding PO box with next string
        if "po box " in text[idx] and idx < len(text) - 1:
            text[idx] = text[idx] + text[idx + 1]
            text.pop(idx + 1)
        # Combine preceeding street number with next string
        elif re.match(r"(\d|shop|suites)? \d{4}", text[idx]) and idx < len(text) - 1:
            text[idx] = text[idx] + text[idx + 1]
            text.pop(idx + 1)
        # Combine previous string with state
        if re.match(" (vic|nsw|qld|nt|tas|sa)", text[idx]) and idx > 0:
            text[idx - 1] = text[idx - 1] + text[idx]
            text.pop(idx)
        # Combine 'cnr' with previous string
        elif re.match(" (cnr .*)", text[idx]) and idx > 0:
            text[idx - 1] = text[idx - 1] + text[idx]
            text.pop(idx)
        # Remove australia
        elif re.match(" (australia)", text[idx]) and idx > 0:
            text.pop(idx)
        idx += 1
    ad_lst = [t.lstrip().rstrip() for t in text if t != "nan"]
    return ad_lst


def parse_streetname(text):
    """
    Extract all streetnames from addresses
    :param text: Multiple Address string
    :return: list of streetnames from multiple in string
    """
    text = preprocess(text)
    lst = text.split(" ")
    sb_list = []
    start = 0
    stop = 0
    for i in range(0, len(lst)):
        if i > 0:
            if re.match(r"(\b(\d{1,4})\b)", lst[i]):  # match street number
                start = i
            elif re.match(
                r"(\b(st|ave|rd|dve|blvd|hwy|ln|ct|cl|pde|pl|crst|tce)\b)", lst[i]
            ):  # match street type
                stop = i
            if stop - start > 0 and stop - start < 5:
                sb_list.append(" ".join(lst[start + 1 : stop]))
                start = 0
                stop = 0
    sb_list = list(
        set([sb for sb in sb_list if len(sb) > 4 and sb != "park"])
    )  # exclude "park"
    return sb_list


def parse_suburb(text):
    """
    Extract all suburbs from addresses
    :param text: Multiple Address string
    :return: list of suburbs from multiple in string
    """
    text = preprocess(text)
    lst = text.split(" ")
    sb_list = []
    start = 0
    stop = 0
    for i in range(0, len(lst)):
        if i > 0:
            if re.match(
                r"(\b(st|ave|rd|dve|blvd|hwy|ln|ct|cl|pde|pl|crst|tce)\b)", lst[i]
            ):  # match street type
                start = i
            elif (
                re.match(r"(\d{1,4})", lst[i])
                and re.match(r"(box)", lst[i - 1])
                and re.match(r"(po)", lst[i - 2])
            ):  # optional match for PO BOX
                start = i
            elif re.match(r"\b(nsw|act|vic|tas|sa|qld|nt|wa)\b", lst[i]):  # match state
                stop = i
            if stop - start > 0 and stop - start < 5:
                sb_list.append(" ".join(lst[start + 1 : stop]))
                start = 0
                stop = 0
    sb_list = list(set([sb for sb in sb_list if len(sb) > 4]))
    return sb_list


def parse_postcode(text):
    """
    Extract all postcodes from addresses
    :param text: Multiple Address string
    :return: list of postcodes from multiple in string
    """
    text = preprocess(text)
    lst = text.split(" ")
    pc_list = []
    for i in range(0, len(lst)):
        if i > 0 and re.match(r"\d{4}", lst[i]):
            pc_list.append(lst[i])
    return pc_list


def parse_name(lst):
    """
    Extract vet provider name from OCR tokens
    :param lst: OCR token list
    :return: vet provider string
    """
    lst = ast.literal_eval(lst)  # convert from string to list
    lst = lst[0:10]  # search only start of list
    lst = [preprocess(text) for text in lst]
    idx_stp = -1
    for word in [
        "vet",
        "hospital",
        "clinic",
        "clinics",
        "surgery",
        "surgeons",
        "practice",
        "services",
        "centre",
        "specialists",
    ]:
        for i, token in enumerate(lst):
            if word in token:
                idx_stp = max(idx_stp, i)
    return [" ".join(lst[0 : idx_stp + 1])]


def append_sub(name, suburbs):
    """
    Appends suburb to vet name
    :param name: Name of vet provider (string)
    :param suburbs: Suburb list
    :return: Name of vet provider + suburb
    """
    name = preprocess(name)
    if name is not None and len(name) > 0:
        return [name] + [name + " " + sb for sb in suburbs if sb not in name]
    else:
        return []


if __name__ == "__main__":
    # Read in OCR data
    ocr_data = pd.read_csv("../data/dataaa1006.csv")
    ocr_data["Name"] = ocr_data["Name"].fillna("")
    ocr_data = ocr_data.fillna("[]")

    # Dictionary to link between OCR data and list of Service Provider columns
    sp_dict = {
        "Name": "ServiceProviderName",
        "Address": "Address",
        "streetname_name": "ServiceProviderName",
        "suburb_name": "ServiceProviderName",
        "abn": "ABN",
        "email": "Email",
        "web": "HomePage",
        "phone_home": "PhoneNo_Home",
        "phone_work": "PhoneNo_Work",
        "phone_mobile": "PhoneNo_Mobile",
        "fax": "FaxNo",
    }

    # Parse Address as list
    ocr_data.rename(columns={"Address": "Address_string"}, inplace=True)
    ocr_data["Address"] = ocr_data["Address_string"].apply(parse_address)

    # Parse suburb/streetname as list
    ocr_data["streetname_name"] = ocr_data["Address"].apply(parse_streetname)
    ocr_data["suburb_name"] = ocr_data["Address"].apply(parse_suburb)
    ocr_data["postcode_name"] = ocr_data["Address"].apply(parse_postcode)

    # Parse additional names from ocr column
    ocr_data.rename(columns={"Name": "Name_string"}, inplace=True)
    ocr_data["Name"] = ocr_data[["Name_string", "suburb_name"]].apply(
        lambda x: append_sub(x["Name_string"], x["suburb_name"]), axis=1
    )  # combines suburb with SP name to also check
    ocr_data["Name"] = ocr_data["Name"] + ocr_data["ocr"].apply(parse_name)
    ocr_data["Name"] = ocr_data["Name"].apply(
        lambda x: list(set([l for l in x if l != ""]))
    )

    # Extract ALL phone numbers from OCR list
    ocr_data["phone"] = ocr_data["ocr"].apply(parse_phone)
    ocr_data["phone_home"] = ocr_data["phone"]
    ocr_data["phone_work"] = ocr_data["phone"]
    ocr_data["phone_mobile"] = ocr_data["phone"]
    ocr_data["fax"] = ocr_data["phone"]

    # Add ABN to existing list
    ocr_data["abn"] = ocr_data["abn"].apply(lambda x: ast.literal_eval(x)) + ocr_data[
        "ocr"
    ].apply(parse_ABN)
    ocr_data["abn"] = ocr_data["abn"].apply(
        lambda x: list(set(x))
    )  # Remove if already exists

    # Add Email to existing list
    ocr_data["email"] = ocr_data["email"].apply(
        lambda x: ast.literal_eval(x)
    ) + ocr_data["ocr"].apply(parse_email)
    ocr_data["email"] = ocr_data["email"].apply(
        lambda x: list(set(x))
    )  # Remove if already exists

    # Add Webpage to existing list
    ocr_data["web"] = ocr_data["web"].apply(lambda x: ast.literal_eval(x)) + ocr_data[
        "ocr"
    ].apply(parse_webpage)
    ocr_data["web"] = ocr_data["web"].apply(
        lambda x: list(set(x))
    )  # Remove if already exists

    # Get counts for each field that could be extracted
    field_list = list(sp_dict.keys())
    for field in field_list:
        ocr_data[field + " Count"] = ocr_data[field].apply(lambda x: len(x))
    ocr_data["Total_count"] = ocr_data[[field + " Count" for field in field_list]].sum(
        axis=1
    )  # Get total count

    # Export new data
    ocr_data.to_csv("dataaa1006_fixed.csv")
    print("OCR data exported!")
