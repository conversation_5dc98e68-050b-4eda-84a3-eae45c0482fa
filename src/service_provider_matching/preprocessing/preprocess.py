import re
from string import punctuation

import numpy as np


# Functions for preprocessing data
def preprocess(text):
    """
    Preprocessing of Names, Addresses
    :param text: string
    :return: string
    """
    text = str(text)
    text = text.lower()
    text = "".join(c for c in text if c not in punctuation)
    text = "".join([x for x in text if x.isalnum() or x == " "])
    # Replace SP words/phrases
    text = re.sub(
        r"((?<=[a-z])(veterinary))", " veterinary", text
    )  # veterinary contained within strings
    text = re.sub(r"((veterinary)(?=[a-z]))", "veterinary ", text)
    text = re.sub(r"(\bveterinary\b)", "vet", text)
    text = re.sub(r"((?<=[a-z])(vet))", " vet", text)  # seperate "(character)vet"
    text = re.sub(r"(\bpty ltd\b)", "", text)
    text = re.sub(r"(\bclinic\b)", "", text)  # generic words
    text = re.sub(r"(\bcentre\b)", "", text)
    text = re.sub(" +", " ", text)  # remove double spaces
    text = text.lstrip().rstrip()
    # Replace Aus states
    text = re.sub(r"(australian capital territory)", "act", text)
    text = re.sub(r"(western australia)", "wa", text)
    text = re.sub(r"(south australia)", "sa", text)
    text = re.sub(r"(victoria)", "vic", text)
    text = re.sub(r"(new south wales)", "nsw", text)
    text = re.sub(r"(tasmania)", "tas", text)
    text = re.sub(r"(queensland)", "qld", text)
    text = re.sub(r"(northern territory)", "nt", text)
    # Replace street types
    text = re.sub(r"(\broad\b)", "rd", text)
    text = re.sub(r"(\bstreet\b)", "st", text)
    text = re.sub(r"(\bavenue\b)", "ave", text)
    text = re.sub(r"(\bclose\b)", "cl", text)
    text = re.sub(r"(\bcourt\b)", "ct", text)
    text = re.sub(r"(\blane\b)", "ln", text)
    text = re.sub(r"(\bparade\b)", "pde", text)
    text = re.sub(r"(\bterrace\b)", "tce", text)
    text = re.sub(r"(\bcrescent\b)", "crst", text)
    text = re.sub(r"(\bboulevard\b)", "blvd", text)
    text = re.sub(r"(\bplace\b)", "pl", text)
    text = re.sub(r"(\bdrive\b)", "dve", text)
    text = re.sub(r"(\bhighway\b)", "hwy", text)
    return text


def preprocess_web(text):
    """
    Preprocessing of webpages, emails
    :param text: string
    :return: string
    """
    text = str(text)
    text = text.lower()
    text = text.strip()
    text = "".join(c for c in text if c not in punctuation)
    # Replace start of webpages, end of emails
    text = re.sub(r"^(http(s)?www|www)", "", text)
    text = re.sub(r"(com(au)?)", "", text)
    return text


def preprocess_numbers(text):
    """
    Preprocessing of ABN, all phone numbers
    :param text: object
    :return: integer
    """
    text = str(text)
    text = "".join(c for c in text if c not in punctuation)
    text = text.replace(" ", "")
    text = "".join([c for c in text if c.isnumeric()])
    if len(text) > 8 and text[0:2] in ["02", "03", "07", "08"]:  # leading area code
        return int(text[2:])
    elif len(text) > 8:
        return int(text)  # for ABN, 1300 numbers
    elif len(text) == 0:
        return 99999999
    elif text == np.NaN:  # For phone numbers in OCR
        return 99999999
    else:
        return int(text)


if __name__ == "__main__":
    print(
        [
            preprocess_numbers(a)
            for a in ["9569 4262", "02 7563 4774", "037563 4774", "1300 234 234"]
        ]
    )
