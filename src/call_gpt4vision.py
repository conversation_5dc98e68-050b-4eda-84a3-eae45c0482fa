import os
import sys
import requests
import base64
from pathlib import Path
if Path(__file__).parents[1] not in sys.path:
    sys.path.append(Path(__file__).parents[1].as_posix())
from credentials import get_openai_key
from pdf2image import convert_from_path
import time 
from PIL import Image
import ast
import re
import pandas as pd
from pathlib import Path
import base64
import cv2
import pytesseract
import json

## Extract file name from document path
def extract_filename(file_path):
    # Get the base name (filename with extension)
    filename_with_extension = os.path.basename(file_path)

    # Split the filename by '.' and get the first part (filename without extension)
    filename = filename_with_extension.split('.')[0]
    return filename

## Convert PDF file to jpg image
def convert_pdf_to_images(pdf_path, output_folder):
    filename = extract_filename(pdf_path)

    # Ensure the output folder exists, create if it doesn't
    os.makedirs(output_folder, exist_ok=True)
    
    # Convert PDF to a list of images
    images = convert_from_path(pdf_path,poppler_path= 'E:\\claryt\\poppler-23.11.0\\Library\\bin') 

    # Save each image as a separate file
    for i, image in enumerate(images):
        image_path = f'{output_folder}/{filename}_p{i+1}.jpg'
        image.save(image_path, 'JPEG')

## Retrieve all the files from a specified folder
def get_files_from_folder(folder_path, extensions=['.pdf', '.jpg','.png','.jpeg']):
    file_paths = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if any(file.lower().endswith(ext) for ext in extensions):
                file_paths.append(os.path.join(root, file))
    return file_paths

## Extract the Python list from the string returned by gpt-4 vision
def extract_list_from_string(s):
    try:
        # Find all substrings that look like lists
        potential_lists = re.findall(r'\[.*?\]', s)
        
        for potential_list in potential_lists:
            try:
                # Attempt to evaluate each potential list
                evaluated_list = ast.literal_eval(potential_list)
                if isinstance(evaluated_list, list):
                    return evaluated_list
                
            except (ValueError, SyntaxError):
                # If evaluation fails, move to the next potential list
                continue

    except Exception as e:
        print(f"Error: {e}")

    return None

## Extract and clean the output returned by gpt-4 vision to a proper JSON format
def clean_json_string(input_str):
    # Find the index of the first opening curly brace
    start_index = input_str.find('{')
    # Find the index of the last closing curly brace
    end_index = input_str.rfind('}')

    # Extract the JSON part, including the curly braces
    cleaned_json = input_str[start_index:end_index + 1]

    return cleaned_json

## Convert string into formatted JSON object
def convert_to_json(data_str):
    """
    Convert a JSON-like string to a JSON object.
    
    Parameters:
    - data_str: A string in JSON format.
    
    Returns:
    - A JSON object if the string is valid JSON; otherwise, it returns an error message.
    """
    try:
        # Parse the JSON string into a Python dictionary (JSON object)
        clean_json = clean_json_string(data_str) 
        return clean_json
    
    except json.JSONDecodeError as e:
        # Return the error message if an exception occurs
        return f"An error occurred: {e}"

## Rotate the image to correct orientation
def rotate_and_overwrite(imPath, center=None, scale=1.0):
    im = cv2.imread(str(imPath), cv2.IMREAD_COLOR)
    angle = 360 - int(re.search('(?<=Rotate: )\d+', pytesseract.image_to_osd(im)).group(0))
    (h, w) = im.shape[:2]

    if center is None:
        center = (w / 2, h / 2)

    # Perform the rotation
    M = cv2.getRotationMatrix2D(center, angle, scale)
    rotated = cv2.warpAffine(im, M, (w, h))

    # Overwrite the original image with the rotated image
    cv2.imwrite(imPath, rotated)

## Main Function : Call the gpt-4 vision API
def call_gptvision(
    system_prompt: str = "You are an AI assistant that helps people find information.",
    user_prompt: str = "Hello there.",
    file_path: str = "C:\\users",
    **kwargs,
) -> dict:
    """
    Call the API to generate the answer from GPT4 Vision
    Args:
        sys_prompt (str): the system prompt
        user_prompt (str): the user prompt
        image_path (str): full path to image attached (pdf or jpg)
        **kwargs (dict): all the other parameters required by the model

    Returns:
        dict: the result from the API plus all the input parameters
    """
    # Configuration
    GPT4V_KEY = get_openai_key()
    if not GPT4V_KEY:
        raise Exception("A key should be provided to invoke the endpoint")
    if not file_path:
        raise Exception("Provide a valid path to an image file.")
    
    # Check if pdf file, convert to images
    if file_path.lower().endswith('.pdf'):
        
        # Extract base name without extension and create a folder name based on it
        base_name = os.path.basename(file_path)
        folder_name = os.path.splitext(base_name)[0]
        output_folder = os.path.join(os.path.dirname(file_path), folder_name)

        convert_pdf_to_images(file_path, output_folder)

        # Encode all images in folder
        encoded_images = []
        
        # Iterate over all files in the output folder
        for file_name in os.listdir(output_folder):
            file_path = os.path.join(output_folder, file_name)
            
            # Check if the current file is an image (e.g., .jpg)
            if file_path.lower().endswith('.jpg') :
                # Rotate image if wrong orientation
                # rotate_and_overwrite(file_path)
                with open(file_path, 'rb') as image_file:

                    # Encode the image file
                    encoded_image = base64.b64encode(image_file.read(file_path)).decode('utf-8')
                    encoded_images.append(encoded_image)
        
        # Generate JSON entries for each encoded image
        image_entries = [
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encoded_image}"
                }
            }
            for encoded_image in encoded_images
        ]
    
    else:
        try:
            # Rotate image if wrong orientation
            # rotate_and_overwrite(file_path)

            encoded_image = base64.b64encode(open(file_path, 'rb').read()).decode('utf-8')

            # Generate JSON entries for each encoded image
            image_entries = [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{encoded_image}"
                    }
                }
            ]

        except:
            raise SystemExit(f"Invalid file path.")

    headers = {
        "Content-Type": "application/json",
        "api-key": GPT4V_KEY,
    }

    # Payload for the request
    payload = {
    "messages": [
        {
        "role": "system",
        "content": [
            {
            "type": "text",
            "text": system_prompt
            }
        ]
        },
        {
        "role": "user",
        "content": [
            {
            "type": "text",
            "text": user_prompt
            },
            # Add all image entries here
            *image_entries
        ]
        }
        ],
        "temperature": 0,
        "top_p": 0,
        "max_tokens": 2000
        }

    GPT4V_ENDPOINT = "https://petsure-openai-api.openai.azure.com/openai/deployments/gpt4-vision/chat/completions?api-version=2023-07-01-preview"

    # Send request
    try:
        response = requests.post(GPT4V_ENDPOINT, headers=headers, json=payload)
        response.raise_for_status()  # Will raise an HTTPError if the HTTP request returned an unsuccessful status code
    except requests.RequestException as e:
        raise SystemExit(f"Failed to make the request. Error: {e}")
    
    return response

if __name__ == "__main__":

    # Define Prompts
    system_prompt = "You are an AI assistant that helps to extract information from documents."

    user_prompt = """Please extract the relevant data from the invoice and populate the provided JSON structure accordingly.
        Animal Details:
        -  Name and other details should be populated in the ["VetXMLClaim"]["InfoFromPolicyHolder"]["AnimalDetails"] section.
        Vet Practice Details:
        -  Vet Name, Practice Name, Address, and other practice-related details should be populated in the ["VetXMLClaim"]["InfoFromVet"]["Vet"] section.

        Invoice Details:
        -  Invoice Amount, Invoice Date, and other related details should be populated in the ["VetXMLClaim"]["InfoFromVet"]["Conditions"]["Invoices"] section.
        -  Amount shown in invoice refers to TotalIncVAT unless being specified as AmountExVAT.
        -  The ["Conditions"]["Financial"][ "Invoices"]["TotalIncVat"] should be equal to the total of ["Conditions"]["Financial"][ "Invoices"]["Items"]["TotalIncVat"] in all items.

        
        Invoice Line Item Details:
        1. Double-check the spelling and details of each drug or treatment name to ensure accuracy.
        2. Ensure the quantity, price, and any other related details match the invoice.
        3. Item Descriptions, Item Amounts, and other related item details should be populated in the ["VetXMLClaim"]["InfoFromVet"]["Conditions"]["Invoices"]["Items"] section.
        4. Note that it is possible to have a negative amount in item description as discount.
        5. Extract the line item and amount accordingly from the invoice, if unclear return 'null', do not change the item description.
        
        Document Metadata:
        Extract metadata about the image and populate the ["VetXMLClaim"]["DocumentMetadata"] section. This should include information about the header, body, footer and other interesting aspects from the image.
        
        Note that:
        1. If invoice number is not present in the invoice, consider transaction no as invoice number
        2. Never take invoice number from the ETFPOS
        3. If the information is unclear and not fully visible, return null without justification
        4. If the document is cut off or crop, return null without justification
        5. Do not include comments with // or explanations in the JSON output
        6. Do not change the format of the given JSON
        7. No justification is needed in the output

        JSON

        {
            "VetXmlClaim": {
                "InfoFromPolicyHolder": {
                    "AnimalDetails": {
                        "Name": null
                    }
                },
                "InfoFromVet": {
                    "Vet": {
                        "VetName": null,
                        "PracticeName": null,
                        "PracticeAddress": null,
                        "PracticeABN": null,
                        "PracticePhoneNumber": null,
                        "PracticeFaxNumber": null,
                        "PracticeEmailAddress": null,
                        "PracticeWebsite": null
                    },
                    "Conditions": [
                        {
                            "Financial": {
                                "TotalExVAT": null,
                                "VAT": null,
                                "TotalIncVat": null,
                                "Invoices": [
                                    {
                                        "InvoiceNumber": null,
                                        "InvoiceDate": null,
                                        "TotalExVat": null,
                                        "VAT": null,
                                        "TotalIncVat": null,
                                        "Items": [
                                            {
                                                "TreatmentDate": null,
                                                "ItemCode": null,
                                                "ItemType": null,
                                                "Sequence": null,
                                                "Description": null,
                                                "AmountExVAT": null,
                                                "DiscountExVaT": null,
                                                "VAT": null,
                                                "Quantity": null,
                                                "TotalIncVAT": null
                                            }
                                        ]
                                    }
                                ]
                            }
                        }
                    ],
                    "DocumentMetadata": {}
                }
            }
        }"""

    ## Specify the folder path which stored the samples to be parsed into the API
    folder_path = "C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\100_tabular_error_extraction" 

    ## List out all the files' path in the folder 
    file_paths = get_files_from_folder(folder_path)

    ## Specify the parameter
    params = {
        "temperature": 0, 
        "max_tokens": 2000,
        "top_p": 0,
        "frequency_penalty": 0,
        "presence_penalty": 0,
        "stop" : None
    }

    # Loop through each file and process
    class_list = []
    filename_list =[]
    json_list = []


    ## Layout Analysis and Document Extraction via gpt-4 vision
    for file_path in file_paths:

        filename = filename_list.append(Path(file_path).name)

        try:
            # Call the GPT-vision function and get the response
            response = call_gptvision(system_prompt=system_prompt, user_prompt=user_prompt, file_path=file_path, **params)
            output_response = response.json()["choices"][0]["message"]["content"]

        except Exception as e:
            print(f"An error occurred while processing {file_path}: {e}")
            output_response = None

        # Extract the list from the classified response if it's not None
        if output_response is not None:
            extracted_json =convert_to_json(output_response)
            json_list.append(extracted_json)
      
        else:
            # If output_response is None, append None to Json_list
            json_list.append(None)

        print(f"File: {file_path}")
        print('params:', params)
        print(f"JSON Output: {output_response}")
        
        # Pause for 60 seconds (1 minute) after each response
        time.sleep(60)  

    # Append result to dataframe
    df = pd.DataFrame()

    # Create a column which records the file name
    df['FileName'] = filename_list

    # Create a column which records the Json Response
    df['JsonResponse'] = json_list
    print(df)
    
    # Export to excel spreadsheet for further analysis
    df.to_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_tabular_error_pdf.xlsx')