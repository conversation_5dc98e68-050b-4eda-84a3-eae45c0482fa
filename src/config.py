"""
Configuration file for the OCR processing pipeline.
"""
from pathlib import Path
import os

# Root directory
ROOTDIR = Path("/home/<USER>/repos/OCR_in_house")

# Sample prefix
SAMPLE_PREFIX = "test1000"

# Data locations
DATA_FOLDER = ROOTDIR / f"data/samples/{SAMPLE_PREFIX}_samples_DI"
RAW_DATA_FILE = ROOTDIR / f"data/input/{SAMPLE_PREFIX}_samples_DI.csv"
DI_OUTPUT_DATA_FOLDER = ROOTDIR / f"data/samples/{SAMPLE_PREFIX}_samples_DI_res"
UPM_GROUND_TRUTH_PATH = ROOTDIR / f"data/sot/{SAMPLE_PREFIX}_SourceOfTruth.xlsx"
PADDLEOCR_RES_PATH = ROOTDIR / f"data/paddleocr/{SAMPLE_PREFIX}_samples_paddleocr.csv"
TRUUTH_PATH = ROOTDIR / f"data/truuth/{SAMPLE_PREFIX}_samples_truuth.csv"
RULE_RES_OUTPUT_UPM_PATH = ROOTDIR / f"data/result/upm/{SAMPLE_PREFIX}_samples_DI_evaluation_upm.xlsx"
RULE_RES_OUTPUT_TRUUTH_PATH = ROOTDIR / f"data/result/truuth/{SAMPLE_PREFIX}_samples_DI_evaluation_truuth.xlsx"
SERVICE_PROVIDER_CSV_PATH = ROOTDIR / f"data/ref_service_provider_updated.csv"

# Database configuration
DB_SERVER = "*********"
DB_NAME = "BIA"

# Azure Document Intelligence configuration
MAX_RETRY = 3
