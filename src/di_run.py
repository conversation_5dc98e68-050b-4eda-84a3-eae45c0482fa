import os
import json
import time
import pickle as pk
from pathlib import Path
from loguru import logger
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.exceptions import HttpResponseError
from azure.core.credentials import AzureKeyCredential

def run_document_intelligence(
    input_directory: str,
    output_directory: str,
    model_id: str = "prebuilt-invoice",
    max_retry: int = 3,
):
    """
    Analyzes documents in an input directory using Azure Document Intelligence
    and saves the results to an output directory.

    This function first checks if the output directory already exists.
    - If it does not exist, it creates the directory and processes each document.
    - If it already exists, the entire analysis process is skipped.

    Args:
        input_directory (str): The path to the folder containing documents to analyze.
        output_directory (str): The path to the folder where results will be saved.
        model_id (str, optional): The model ID to use for analysis.
                                  Defaults to "prebuilt-invoice".
        max_retry (int, optional): The maximum number of retries for a single file
                                   in case of a service error. Defaults to 3.
    """
    output_path = Path(output_directory)
    input_path = Path(input_directory)

    # Initialize Azure Document Intelligence client
    endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPONT")
    key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
    doc_analysis_client = DocumentAnalysisClient(
        endpoint=endpoint, credential=AzureKeyCredential(key)
    )

    # The main condition: only run if the output directory doesn't exist.
    if not output_path.exists():
        logger.info(f"Output directory '{output_path}' not found. Creating and starting analysis.")
        os.makedirs(output_path)

        all_results = []
        doc_list = sorted(os.listdir(input_path))
        total_docs = len(doc_list)

        for i, file_name in enumerate(doc_list):
            retry_num = 0
            file_path = input_path / file_name
            file_stem = file_path.stem
            
            logger.info(f"Processing document {i+1}/{total_docs}: '{file_name}'")

            while retry_num < max_retry:
                try:
                    with open(file_path, "rb") as f:
                        poller = doc_analysis_client.begin_analyze_document(model_id=model_id, document=f)
                        result = poller.result()
                    
                    result_dict = result.to_dict()
                    
                    # Save individual results
                    with open(output_path / f"{file_stem}.pk", "wb") as fout:
                        pk.dump(result_dict, fout)
                    with open(output_path / f"{file_stem}.json", "w") as fout:
                        json.dump(result_dict, fout, indent=4, default=str)

                    # Add to the main list and log success
                    all_results.append({"file_path": file_name, "invoice": result_dict})
                    logger.success(f"Successfully processed and saved '{file_name}'.")
                    time.sleep(1) # Small delay to avoid hitting rate limits on rapid calls
                    break  # Exit retry loop on success

                except HttpResponseError as hre:
                    logger.warning(f"Attempt {retry_num + 1}/{max_retry} failed for '{file_name}' with HTTP error: {hre}. Retrying...")
                    retry_num += 1
                    time.sleep(5 * (retry_num)) # Exponential backoff
                except Exception as e:
                    logger.exception(f"An unexpected error occurred while processing '{file_name}': {e}")
                    break # Exit retry loop on non-retriable error
            
            if retry_num == max_retry:
                logger.error(f"Failed to process '{file_name}' after {max_retry} attempts.")

        # Save the aggregated list of all results
        logger.info(f"Saving aggregated results for {len(all_results)} documents.")
        with open(output_path / "ans.pk", "wb") as fout:
            pk.dump(all_results, fout)
        with open(output_path / "ans.json", "w") as fout:
            json.dump(all_results, fout, indent=4, default=str)
        
        logger.info("Document intelligence analysis complete.")

    else:
        # This block runs if the directory already exists.
        logger.info(f"Output directory '{output_path}' already exists. Skipping analysis.")