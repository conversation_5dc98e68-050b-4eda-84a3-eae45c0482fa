import json
import os
from abc import ABC
from pathlib import Path
from typing import Dict, Optional, Union

from dotenv import load_dotenv
from loguru import logger

from src.utils.storage import Storage, StorageType


class StateManager(ABC):
    """
    The StateManager stores all the running state including
    the environmental variables, running parameters, running results, etc

    Args:
        ABC (_type_): _description_

    Returns:
        StateManager: StateManager instance with running state

    Example:

    from src.workflow.state_manager import StateManager
    state_manager = StateManager(state={}, env_config={})

    """

    state: Dict
    env_config: Dict

    def __init__(
        self,
        state: Optional[Dict],
        env_config: Dict[str, Union[str, int, float]] | None = None,
    ):
        """
        init the StateManager

        Args:
            state (Optional[Dict]): the initial state for running parameters
            env_config (Dict[str, Union[str, int, float]] | None, optional): the extra environment variables to be set. Defaults to None.
        """
        assert state is None or isinstance(
            state, Dict
        ), "state should be in Dict format."
        assert env_config is None or isinstance(
            env_config, Dict
        ), "env_config should be in Dict format."

        self.state = state or {}
        self.env_config = env_config or {}

        # load .env file
        load_dotenv()

        # load running environment
        kv_name = self.state.get("kv_name", "PS-AI-keyvault")
        self.kv_client = Storage(type=StorageType.KV, kv_name=kv_name)._client

        # insert and override using provided env_config params
        for key, value in self.env_config.items():
            os.environ[key] = value

        # specific environment variable setting for OPENAI related ones
        # if any(
        #     env_key.upper().startswith("OPENAI") for env_key in self.env_config.keys()
        # ):
        try:
            import openai

            open_ai_key = self.kv_client.get_secret("openai-endpoint-key").value
            openai.api_type = os.getenv("OPENAI_API_TYPE")

            openai.api_base = os.getenv("OPENAI_API_BASE")
            openai.api_version = os.getenv("OPENAI_API_VERSION")
            openai.api_key = open_ai_key

            os.environ["OPENAI_API_KEY"] = openai.api_key
            os.environ["AZURE_OPENAI_ENDPOINT"] = openai.api_base
            os.environ["OPENAI_API_VERSION"] = openai.api_version
        except TypeError:
            logger.warning(
                f"No OpenAI related environment has been set up in the .env file and env_config {env_config}."
            )
        except Exception as e:
            logger.exception(f"Unknown Exception. Exception Information: {e}")

    def set(self, key: str, value: Dict) -> None:
        """
        set a key value pair in the state

        Args:
            key (str): state key
            value (Dict): the value
        """
        self.state[key] = value

    def update(self, state: Dict[str, Dict]) -> None:
        # update the state with the provided dictionary
        self.state.update(state)

    def get(self, key: str) -> Dict:
        # get the value of the key, return {} if not exists
        return self.state.get(key, {})

    def get_state(self):
        # get a copy of the state
        return self.state.copy()

    def set_state(self, state):
        # reset the state
        self.state = state

    def dump(self) -> str:
        # dump the state into json format
        return json.dumps(self.state, indent=4)

    @classmethod
    def load(cls, path: str | Path) -> None:
        # load the dumped file and reconstruct the StateManager.
        try:
            path = Path(path)
            assert path.exists() and path.is_file
            with open(path, "r") as fin:
                state = json.load(fin)
            return StateManager(state=state)
        except ValueError as ve:
            logger.error(
                f"Loading path {path} can not be converted into a Path. Error message: {ve}"
            )
        except AssertionError as ae:
            logger.error(
                f"Loading path {path} is not a valid file. Error message: {ae}"
            )

        return None
