import os
import sys
import pandas as pd
from pathlib import Path

os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

module = '.'
sys.path.insert(1, module)

from scripts.sql_engine import *

current_directory = os.path.dirname(os.path.abspath(__file__))

# Execute SQL query to extract claims related data if not found
if not os.path.exists(os.path.join(current_directory,'claims_path.csv')):
    df = execute_sql_file_to_dataframe(
                                    sql_file=os.path.join(current_directory,'claim_document_path.sql'), 
                                    server_name='PS-PRD-AZS-DWH1',
                                    database_name='BIA'
                                    )
    
    df["ContainerName"] = df["DocumentPath"].apply(lambda x: str(Path(x).parent))
    df["DocumentFile"] = df["DocumentPath"].apply(lambda x: str(Path(x).name))

    df.to_csv(os.path.join(current_directory,'claims_path.csv'))
    print(f"Saved CSV to {os.path.join(current_directory,'claims_path.csv')}")