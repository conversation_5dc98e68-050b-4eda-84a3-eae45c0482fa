import csv

from fuzzywuzzy import process


class ServiceProviderMatcher:
    def __init__(self, csv_file_path):
        """
        Initialize the matcher with a CSV file of service providers

        CSV file should have columns: 'name' and 'address'

        Args:
            csv_file_path (str): Path to the CSV file containing service providers
        """
        self.providers = []

        # Read the CSV file
        with open(csv_file_path, "r", encoding="utf-8") as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                # Combine name and address for more comprehensive matching
                # full_provider_info = f"{row['ServiceProviderName']} - {row['Address']} - {row['City']} - {row['State']} - {row['PostCode']}"

                # Only use name for matching
                full_provider_info = row["ServiceProviderName"]
                self.providers.append(full_provider_info)

    def find_closest_match(self, query, threshold=70):
        """
        Find the closest match for a given service provider query

        Args:
            query (str): Name or address of the service provider to match
            threshold (int, optional): Minimum score to consider a match. Defaults to 70.

        Returns:
            tuple: (best_match, match_score) or (None, 0) if no match above threshold
        """
        # Use process.extractOne for finding the single best match
        result = process.extractOne(query, self.providers)

        # Check if the match meets the threshold
        if result[1] >= threshold:
            return result

        return None, 0


def main():
    # Example usage
    try:
        # Create matcher with a CSV file of service providers
        matcher = ServiceProviderMatcher(
            "/workspaces/OCR_in_house/data/ref_service_provider_updated.csv"  ## path to the csv file
        )

        # Example queries
        queries = [
            "JOHNSTON ST VET CLINIC",
        ]

        # Test matches
        for query in queries:
            match, score = matcher.find_closest_match(query)
            if match:
                print(f"Query: {query}")
                print(f"Best Match: {match}")
                print(f"Match Score: {score}")
            else:
                print(f"No close match found for: {query}")
            print()

    except FileNotFoundError:
        print(
            "Error: service_providers.csv file not found. Please create the CSV file."
        )
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()
