import os

from azureml.core import Workspace
from azureml.core.authentication import ServicePrincipalAuthentication
from dotenv import load_dotenv
from sqlalchemy import URL, create_engine, engine


def get_key_vault():
    subscription_id = "b15ae5d0-8f07-4cfb-aca3-508d38e9d983"
    resource_group = "t-to-tstar-rg"
    workspace = "t-to-tstar"
    service_principal_id = "e34d636b-b1a1-4ec9-894d-3250d863b9e1"
    service_principal_password = "U1/saLIG2eHM:A2@k]dMSf71.cUA4JbP"

    svc_pr = ServicePrincipalAuthentication(
        tenant_id="ce9f1d01-56a1-4c06-bf68-2cfbf51ff729",
        service_principal_id=service_principal_id,
        service_principal_password=service_principal_password,
    )
    ws = Workspace(
        subscription_id=subscription_id,
        resource_group=resource_group,
        workspace_name=workspace,
        auth=svc_pr,
    )
    return ws.get_default_keyvault()


class Engine:
    _instance = None

    load_dotenv()

    def __init__(self):
        pass

    @classmethod
    def get_engine(cls, *args, **kwargs) -> engine.base.Engine:
        if not cls._instance:
            username = os.environ.get("DB_USERNAME")
            password = os.environ.get("DB_PASSWORD")

            params = {
                "drivername": "mssql+pymssql",
                "username": f"PETSURE\\{username}",
                "password": password,
                "host": kwargs["server"],
                "port": "1433",
                "database": kwargs["database"],
            }
            connection_url = URL.create(**params)
            cls._instance = create_engine(connection_url)
            print(username)
            print(kwargs["server"])
            print(kwargs["database"])
        # print(password)
        return cls._instance

    @classmethod
    def get_engine2(cls, *args, **kwargs) -> engine.base.Engine:
        if not cls._instance:
            username = "SVC_BCS_UAT_PRDT"
            password = "p*oiVrNjP7vD9JTBEcZm"

            params = {
                "drivername": "mssql+pymssql",
                "username": f"PETSURE\\{username}",
                "password": password,
                "host": kwargs["server"],
                "port": "1433",
                "database": kwargs["database"],
            }
            connection_url = URL.create(**params)
            cls._instance = create_engine(connection_url)
            print(username)
            print(kwargs["server"])
            print(kwargs["database"])
        # print(password)
        return cls._instance

    @classmethod
    def disconnect(cls):
        if cls._instance:
            cls._instance.dispose()
            cls._instance = None
            print("Engine disconnected successfully")
        else:
            print("No active engine connection to disconnect")


if __name__ == "__main__":
    engine = Engine.get_engine(server="*********", database="FinanceDB")
    engine.connect()
# usage
# engine = Engine.get_engine(server="xxx", database="xxx")
