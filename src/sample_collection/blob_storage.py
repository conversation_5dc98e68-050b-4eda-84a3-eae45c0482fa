import os
from pathlib import Path

import fitz  # PyMuPDF
from azure.storage.blob import (
    BlobServiceClient,
)
from dotenv import load_dotenv
from loguru import logger
from paddleocr import PaddleOCR
from scripts.sql_engine import *

load_dotenv()


# blob storage download
def blob_download(container_name: str, file_name: str, dest_path: str):
    source_key = os.getenv("AZURE_OCR_BLOB_STORAGE_KEY")
    source_account_url = os.getenv("AZURE_OCR_BLOB_STORAGE_ENDPONT")
    source_blob_service_client = BlobServiceClient(
        account_url=source_account_url, credential=source_key
    )

    # Create download function
    def download(
        blob_service_client: BlobServiceClient,
        container_name: str,
        file_name: str,
        dest_file_path,
    ):
        container_client = blob_service_client.get_container_client(container_name)
        blob_client = container_client.get_blob_client(file_name)
        with open(dest_file_path, "wb") as f:
            f.write(blob_client.download_blob().readall())

    # Ensure the directory exists
    directory = os.path.dirname(dest_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)

    try:
        download(source_blob_service_client, container_name, file_name, dest_path)
        logger.info(f"file downloaded to {dest_path}")
        # copy2(dest_path, f"../data/tmp_{time.time_ns()}.pdf")
    except Exception as e:
        logger.exception(e)


def extract_consultation_notes(file_path: str) -> str:
    # file_path = Path(file_path)
    file_suffix = Path(file_path).suffix

    if file_suffix == ".pdf":
        # Open the PDF file
        pdf_document = fitz.open(file_path)

        # Get the number of pages
        number_of_pages = pdf_document.page_count
        logger.info(f"File {file_path} has {number_of_pages} pages")
        ocr = PaddleOCR(use_angle_cls=True, lang="en", page_num=number_of_pages)

        result = ocr.ocr(file_path, cls=True)

    elif file_suffix in [".png", ".jpg", ".jpeg"]:
        ocr = PaddleOCR(use_angle_cls=True, lang="en")
        result = ocr.ocr(file_path, cls=True)

    else:
        raise TypeError("the file type should be pdf, png, jpg, or jpeg")

    if result:
        txts = []
        for idx in range(len(result)):
            tmp_txt = []
            try:
                for line in result[idx]:
                    tmp_txt.append(line[1][0])
            except Exception as e:
                logger.exception(e)
                tmp_txt.append("")
            txts.append(" ".join(tmp_txt))

        return "\n".join(txts)
    return ""
