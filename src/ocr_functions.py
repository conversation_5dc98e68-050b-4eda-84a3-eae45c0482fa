"""
OCR functions for document processing.
"""
import os
import json
import pickle as pk
import time
from pathlib import Path
from typing import List, Dict
import fitz  # PyMuPDF
from paddleocr import PaddleOCR
from fitz import FileDataError
from loguru import logger
import pandas as pd
from src.config import MAX_RETRY

def paddleocr_extract_consultation_notes(file_path: str) -> str:
    """
    Extracts text from a PDF or image file using PaddleOCR, handling broken files.

    Args:
        file_path: The full path to the file.

    Returns:
        A string containing the extracted text, or an empty string if the
        file is broken, unsupported, or contains no text.
    """
    file_suffix = Path(file_path).suffix.lower()
    logger.info(f"Processing {file_path}")

    # Initialize result to None
    result = None

    try:
        if file_suffix in [".pdf", ".PDF"]:
            # Open the PDF file
            pdf_document = fitz.open(file_path)

            # Get the number of pages
            number_of_pages = pdf_document.page_count
            pdf_document.close() # Close the document after getting page count
            logger.info(f"File {file_path} has {number_of_pages} pages")

            # Initialize and run OCR
            ocr = PaddleOCR(use_angle_cls=True, lang="en", page_num=number_of_pages)
            result = ocr.ocr(file_path, cls=True)

        elif file_suffix in [".png", ".jpg", ".jpeg"]:
            # Initialize and run OCR
            ocr = PaddleOCR(use_angle_cls=True, lang="en")
            result = ocr.ocr(file_path, cls=True)

        else:
            # Raise a TypeError for unsupported file formats
            raise TypeError(f"Unsupported file type: {file_suffix}")

    except Exception as e:
        # Catch exceptions from broken/corrupted files (e.g., from fitz.open or ocr.ocr)
        logger.error(f"Could not process broken or corrupted file: {file_path}. Error: {e}")
        return "" # Return empty string for broken files

    # Process and sort the OCR results if extraction was successful
    if result and result[0] is not None:
        def get_sort_key(item):
            # Sort by top-left y coordinate, then x coordinate
            top_left_y = item[0][0][1]
            top_left_x = item[0][0][0]
            return top_left_y, top_left_x

        sorted_pages = []
        for page in result:
            if page:
                # Sort lines within each page
                sorted_page = sorted(page, key=get_sort_key)
                sorted_pages.append(sorted_page)

        if not sorted_pages:
            logger.error(f"No content extracted from {file_path}")
            return ""

        # Combine text from all pages
        all_text = []
        for page in sorted_pages:
            page_text = []
            for line in page:
                # line[1][0] contains the text part of the OCR result
                if line and len(line) > 1 and len(line[1]) > 0:
                    page_text.append(line[1][0])
            all_text.append(" ".join(page_text))

        return "\n".join(all_text)

    # Return an empty string if no result was generated
    logger.warning(f"No text detected in {file_path}")
    return ""

def load_paddleocr_res(pcr_res_path: str , data_folder: str) -> List[Dict]:
    try:
        df = pd.read_csv(pcr_res_path)
        df[["content"]] = df[["content"]].fillna(value="")
        return df.to_dict(orient="records")
    except FileNotFoundError:
        # extract all the text content using paddleOCR
        ocr_result = []
        for file_path in sorted(os.listdir(data_folder)):
            try:
                consultation_note = paddleocr_extract_consultation_notes(os.path.join(data_folder, file_path))
            except TypeError:
                consultation_note = ""
            ocr_result.append({
                "file_path": file_path,
                "content": consultation_note
            })
        pd.DataFrame(ocr_result).to_csv(pcr_res_path, index=False)
        return ocr_result

def load_document_intelligence_res(res_data_folder: str, raw_data_folder: str) -> List[Dict]:
    try:
        with open(os.path.join(res_data_folder, "ans.pk"), "rb") as fin:
            ans = pk.load(fin)
    except FileNotFoundError:
        original_file_path_list = sorted(os.listdir(raw_data_folder))
        original_file_path_dict = {}
        for p in original_file_path_list:
            original_file_path_dict[str(Path(p).stem)] = p
        ans = []
        for file_path in sorted(os.listdir(res_data_folder)):
            if file_path.endswith(".json"):
                with open(os.path.join(res_data_folder, file_path), "r") as fin:
                    ans.append(
                        {
                            "file_path": original_file_path_dict[str(Path(file_path).stem)],
                            "invoice": json.load(fin),
                            }
                    )
    return ans