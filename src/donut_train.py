import gc
from pathlib import Path

import evaluate
import numpy as np
import torch
from datasets import load_dataset
from icecream import ic
from loguru import logger
from transformers import (
    DonutProcessor,
    Trainer,
    TrainingArguments,
    VisionEncoderDecoderConfig,
    VisionEncoderDecoderModel,
)

from src.donut_prepare import prepare_dataset

# Variables ###############################################################################################
ROOT_DIR = Path(__file__).parent
DATA_DIR = ROOT_DIR / "data"

IMAGE_SIZE = [600, 800]
CLS_TOKEN = "<s_doctriage>"
LABELS = ["invoice", "medical_record", "other"]
CLASS_TOKEN = "<s_class>"


# Functions ###############################################################################################
def add_tokens(list_of_tokens: list[str]):
    newly_added_num = processor.tokenizer.add_tokens(list_of_tokens)
    if newly_added_num > 0:
        model.decoder.resize_token_embeddings(len(processor.tokenizer))


def collate_fn(dataset):
    pixel_values = torch.cat([processor(example["image"], return_tensors="pt").pixel_values for example in dataset])
    labels_dict = [example["ground_truth"]["gt_parse"] for example in dataset]
    labels = []
    for item in labels_dict:
        assert isinstance(item, dict), "ground_truth should be a dictionary"
        assert "class" in item, 'ground_truth should have a key named "class"'
        labels.append(
            processor.tokenizer(
                [f"<s_class>{item['class']}<s_class/>"],
                add_special_tokens=True,
                return_tensors="pt",
                max_length=8,
                padding="max_length",
                truncation=True,
            ).input_ids
        )
    return {
        "pixel_values": pixel_values,
        "labels": torch.concat(labels),
    }


def compute_metrics(eval_pred):
    predictions, labels = eval_pred.predictions[0], eval_pred.label_ids
    # predictions = np.argmax(logits, axis=-1)
    acc = evaluate.load("accuracy").compute(predictions=predictions[:, 2], references=labels[:, 2])
    logger.info(f"{acc}")
    return acc


def preprocess_logits_for_metrics(logits, labels):
    """
    Original Trainer may have a memory leak.
    This is a workaround to avoid storing too many tensors that are not needed.
    """
    pred_ids = torch.argmax(logits[0], dim=-1)
    return pred_ids, labels


def train(
    processor: DonutProcessor,
    model: VisionEncoderDecoderModel,
    training_args: TrainingArguments,
    train_set: torch.utils.data.Dataset,
    test_set: torch.utils.data.Dataset,
    collate_fn: callable,
):
    logger.info("Training starts...")
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model.to(device)

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_set,
        eval_dataset=test_set,
        compute_metrics=compute_metrics,
        preprocess_logits_for_metrics=preprocess_logits_for_metrics,
        data_collator=collate_fn,
        tokenizer=processor,
    )

    mem_free, _ = torch.cuda.mem_get_info(device + ":0")
    if mem_free / 1024**3 < 12:
        gc.collect()
        torch.cuda.empty_cache()
    trainer.train()


if __name__ == "__main__":
    logger.add("../logs/donut_train.log", rotation="3 MB")

    # Prepare dataset
    prepare_dataset()

    # Load dataset
    logger.info("Loading dataset...")
    csp_dataset = load_dataset("imagefolder", data_dir="../data/csp_dataset", drop_labels=False)

    # Split into train and test
    train_set = csp_dataset["train"].shuffle(seed=1234)
    test_set = csp_dataset["test"]
    logger.info("Done.")

    # Load VisionEncoderDecoderConfig, Processor and Model
    logger.info("Loading model and processor...")
    config = VisionEncoderDecoderConfig.from_pretrained("nielsr/donut-base", num_labels=3)
    config.encoder.image_size = IMAGE_SIZE
    config.decoder.max_length = 8
    processor = DonutProcessor.from_pretrained("nielsr/donut-base")
    config.pad_token_id = processor.tokenizer.pad_token_id
    config.decoder_start_token_id = processor.tokenizer.convert_tokens_to_ids(["<s_doctriage>"])[0]
    model = VisionEncoderDecoderModel.from_pretrained("nielsr/donut-base", config=config)
    processor.image_processor.size = IMAGE_SIZE[::-1]
    processor.image_processor.do_align_long_axis = False

    additional_tokens = [CLS_TOKEN] + LABELS + [CLASS_TOKEN, "/".join([CLASS_TOKEN[:-1], CLASS_TOKEN[-1]])]
    add_tokens(additional_tokens)

    training_args = TrainingArguments(
        output_dir="output",
        evaluation_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        metric_for_best_model="eval_accuracy",
        per_device_train_batch_size=4,
        per_device_eval_batch_size=4,
        remove_unused_columns=False,
        logging_dir="logs",
        num_train_epochs=10,
    )
    logger.info("Done.")

    train(
        processor=processor,
        model=model,
        training_args=training_args,
        train_set=train_set,
        test_set=test_set,
        collate_fn=collate_fn,
    )
