"""
Business rules for OCR processing.
"""
from pathlib import Path
import re
from typing import List, Dict, Tuple
from copy import deepcopy
import fitz
from loguru import logger


# Extraction Rules
abn_extract_regex = r"(?:\d *){11}"
abn_extract_regex1 = r"\d{2}-\d{3}-\d{3}-\d{3}"

def validate_abn(nums: List[int]) -> bool:
    if len(nums) != 11:
        return False
    if not all(isinstance(x, int) for x in nums):
        return False
    if any(x>9 for x in nums):
        return False
    if any(x<0 for x in nums):
        return False

    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))
    return s%89 == 0

def get_abn(info: Dict) -> List[str]:
    content = info["content"]
    matches = re.findall(abn_extract_regex, content)
    matches1 = re.findall(abn_extract_regex1, content)
    ans = []
    for match in matches + matches1:
        match_num = []
        for c in match:
            try:
                int_c = int(c)
                match_num.append(int_c)
            except ValueError:
                continue
        if validate_abn(match_num):
            ans.append(match_num)

    ans = list({"".join([str(x) for x in abn]) for abn in ans})
    info["ABN"] = ans
    return info

def if_empty_fields(info: Dict) -> Tuple[bool, str]:
    # CHANGELOG: 04 Nov 2024 update the service provider empty field checking
    # CHANGELOG: 04 Nov 2024 reconstruct the empty fields check and messages
    messages = []

    invoice_no = info["invoice_no"]
    if_invoice_no_empty = len(invoice_no.strip()) == 0
    if if_invoice_no_empty:
        messages.append("Invoice Number not extracted")

    service_provider = info["service_provider"].lower()
    service_provider_address = info["service_provider_address"].lower()
    abn = info["ABN"]
    if_service_provider_empty = False
    if  len(service_provider.strip())==0 or service_provider.startswith("dr.") or service_provider.startswith("dr "):
        # service provider extraction empty
        # service provider extraction could be wrong that leads to no fuzzy matching result
        if not (abn or service_provider_address):
        # no abn is extracted from the invoice content
        # no address extracted by DI
            if_service_provider_empty = True
            messages.append("Service provider name not extracted")

    invoice_date = info["invoice_date"]
    if_invoice_date_empty = len(invoice_date.strip()) == 0
    if if_invoice_date_empty:
        messages.append("Invoice date not extracted")

    total_amount = info["invoice_total"]
    if_total_amount_empty = len(str(total_amount).strip()) == 0
    if if_total_amount_empty:
        messages.append("Invoice total not extracted")


    activate = len(messages) > 0
    if activate:
        return activate, f"FALL OUT: {'|'.join(messages)}"
    return activate, ""

from datetime import datetime
def if_date_in_future(info: Dict) -> Tuple[bool, str]:
    invoice_date = info["invoice_date"]
    try:
        # Convert the date string to a date object
        date = datetime.strptime(invoice_date, '%Y-%m-%d').date()
        # Get today's date
        today = datetime.today().date()
        # Check if the date is in the future
        activate = date > today
        messages = ["", "FALL OUT: Date in Future"]
        return activate, messages[activate]
    except ValueError:
        return True, "FALL OUT: Date Format Error"

def if_invoice_no_len_fit(info: Dict) -> Tuple[bool, str]:
    invoice_no = info["invoice_no"]
    activate = len(invoice_no) <= 4 or len(invoice_no) >= 20
    messages = ["", "FALL OUT: Invoice Len"]
    return activate, messages[activate]

from PIL import Image
def if_file_broken(path: str) -> Tuple[bool, str]:
    def check_pdf(filepath):
        try:
            with fitz.open(filepath) as doc:
                doc.load_page(0)  # Try loading the first page
            return True
        except FileNotFoundError as fnfe:
            filepath = filepath[:-3]+"PDF"
            with fitz.open(filepath) as doc:
                doc.load_page(0)  # Try loading the first page
            return True
        except Exception as e:
            logger.exception(f"PDF error: {e}")
            return False

    def check_image(filepath):
        try:
            with Image.open(filepath) as img:
                img.verify()  # Verifies image integrity
            return True
        except FileNotFoundError as fnfe:
            file_suffix = Path(filepath).suffix
            filepath = filepath[:-len(file_suffix)] + str(file_suffix).upper()
            with Image.open(filepath) as img:
                img.verify()  # Verifies image integrity
            return True
        except Exception as e:
            logger.exception(f"JPG error: {e}")
            return False
    suffix = str(Path(path).suffix)

    if suffix.lower() == ".pdf":
        activate = not check_pdf(path)
    elif suffix.lower() in [".jpeg", ".jpg", ".png", ".bmp", ".tiff"]:
        activate = not check_image(path)
    else:
        activate = True
    messages = ["", "FALL OUT: Broken File or Invalid File"]
    return activate, messages[activate]

def if_empty_treatment(info: Dict) -> Tuple[bool, str]:
    treatments = info["treatments"]
    activate = len(treatments) == 0
    messages = ["", "FALL OUT: Zero Treatment Line"]
    return activate, messages[activate]

def if_negative_invoice_total(info: Dict) -> Tuple[bool, str]:
    invoice_total = info["invoice_total"]
    activate = invoice_total < 0
    messages = ["", "FALL OUT: Negative Invoice Total"]
    return activate, messages[activate]

def if_negative_treatment_amount(info: Dict) -> Tuple[bool, str]:
    treatments = info["treatments"]

    # CHANGELOG: 06 NOV 2024 add acceptance rules for discount and rounding which are in the treatment lines
    activate = False
    for treatment in treatments:
        # acceptable amount
        if treatment["amount"] >= 0:
            continue
        else:
            if if_diff_invoice_total_sum_treatment_amount(info)[0]:
                activate = True
                break
            else:
                continue
        # # rounding acceptance # TODO to be further refined for the rounding mapping
        # if treatment["treatment"].lower().strip() == "rounding" and -0.05 < treatment["amount"] < 0:
        #     continue

        # # discount acceptance # TODO to be further refined for the discount mapping
        # elif treatment["treatment"].lower().strip().startswith("discount"):
        #     continue

        # # fallout
        # elif treatment["amount"] < 0:
        #     activate = True
        #     break
    # activate = any(treatment["amount"] < 0 for treatment in treatments)
    messages = ["", "FALL OUT: Negative Treatment Amount"]
    return activate, messages[activate]

def if_diff_invoice_total_sum_treatment_amount(info: Dict) -> Tuple[bool, str]:
    # CHANGELOG: 4/11/2024 update the accept_rounding from 0.01 to 0.05
    accept_rounding = 0.05
    treatments = info["treatments"]
    treatment_total = sum([treatment["amount"] if treatment["amount"] else 0. for treatment in treatments ])
    invoice_total = info["invoice_total"]

    activate = abs(round(invoice_total,2) - round(treatment_total, 2)) >= accept_rounding
    messages = ["", "FALL OUT: Invoice total does not match with line totals"]
    # CHANGELOG: 04 NOV 2024 lift invoice total confidence if invoice total == sum(treatment amount)
    if not activate:
        info["invoice_total_conf"] = min(1.0, info["invoice_total_conf"] + 0.3)
    return activate, messages[activate]

def if_over_conf_threshold(info: Dict, conf_threshold: float = 0.8) -> Tuple[bool, str]:
    conf = min(info["invoice_no_conf"], info["invoice_date_conf"], info["invoice_total_conf"])
    activate = conf < conf_threshold
    messages = ["", "FALL OUT: Low Confidence"]
    return activate, messages[activate]

# get shipping Pet Chemist Online
def get_shipping_pet_chemist_online(content: str) -> float:
    # get related text snippet
    # Regex to match the full section containing payment information
    section_pattern = r"Delivery Method:.*?(?:Discount|$)"  # Matches from "Delivery Method" to "Discount" or end of text

    # Extract the full section with payment information
    section_match = re.search(section_pattern, content, re.DOTALL)
    # extract $
    try:
        section_text = section_match.group()  # Extract the matched section

        # Money regex to extract all amounts from this section
        money_pattern = r"\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)"
        amounts = re.findall(money_pattern, section_text)
        # adjust which one
        return float(amounts[-1])
    except Exception:
        return None
    return None

def get_shipping(info: Dict) -> Dict:
    abn = info["ABN"]
    content = info["content"]
    # Pet Chemist Online
    if "***********" in abn:
        shipping = get_shipping_pet_chemist_online(content)
        if shipping:
            invoice_date = info["invoice_date"]
            invoice_date_conf = info["invoice_date_conf"]
            info["treatments"].append({
                "treatment_date": invoice_date,
                "treatment": "shipping",
                "amount": shipping,
                "treatment_date_conf": invoice_date_conf,
                "treatment_conf": 1.0,
                "amount_conf": 1.0,
                "treatmentline_conf": invoice_date_conf})
    return info

# Post-Process Rules
from copy import deepcopy

def postprocess_treatmentline(info: Dict) -> Tuple[Dict, str]:
    ans = deepcopy(info)
    # CHANGELOG: 06 NOV 2024 update the payment related treatment line removal logic
    # CHANGELOG: 06 NOV 2024 update the discount related treatment line removal logic    
    treatments = ans["treatments"]
    treatment_total = sum([treatment["amount"] if treatment["amount"] else 0. for treatment in treatments ])
    invoice_total = ans["invoice_total"]

    activate = False
    message = ["", "POSTPROCESS: Treatment Line Curation"]

    tmp = []
    for treatment in treatments:
        # CHANGELOG: add .strip() to remove space string
        desc = treatment["treatment"].strip().lower()

        # "" in treatment line gets ignored
        if len(desc) == 0:
            activate = True
            message[1] = "POSTPROCESS: Empty Treatment Line"
            continue

        # # 0 in treatment line amount gets ignored
        # if treatment["amount"] == 0:
        #     activate = True
        #     message[1] = "POSTPROCESS: Zero Treatment Amount"
        #     continue
        
        # # asking Matt and Andrew for raw t containing "eftpos" as substring or not
        # if "eftpos" in desc:
        #     activate = True
        #     message[1] = "POSTPROCESS: EFTPOS in Treatment Line"
        #     continue
    
        # # "payment" or "eftpos" in treatment line gets ignored
        # #  payment surcharge should be reserved
        # if "payment" in desc:
        #     if not ("surcharge" in desc and treatment_total == invoice_total):
        #         activate = True
        #         message[1] = "POSTPROCESS: Payment in Treatment Line"
        #         continue
        
        tmp.append(treatment)

    ans["treatments"] = tmp

    return ans, message[activate]


def find_similar_substring(content: str, target: str, max_diff: int = 2):
    """
    Find a substring in the content that has the same length as the target substring,
    with only one or two different characters or digits.

    Args:
        content (str): The string to search within.
        target (str): The substring to compare with.
        max_diff (int): Maximum number of allowed differences. Default is 2.

    Returns:
        list: A list of matching substrings that differ by at most max_diff characters.
    """
    target_len = len(target)
    result = []

    # Loop through content to get every possible substring of the same length as target
    for i in range(len(content) - target_len + 1):
        sub_str = content[i:i + target_len]
        if i-1 >= 0 and content[i-1].isalnum():
            continue
        if i + target_len < len(content) and content[i + target_len].isalnum():
            continue

        # Count how many characters are different between sub_str and target
        # di_invoice_no: 1012538, invoice_no_fuzzy_res: ['.00 58.', '. 1/21 ', '1012540'
        diff_count = 0
        for a, b in zip(sub_str.lower(), target.lower()):
            if a!=b:
                if a.isalnum() and b.isalnum():
                    diff_count += 1
                else:
                    diff_count += max_diff+1
                    break
                # if b.isalnum() and not a.isalnum():
                #     diff_count += max_diff
                #     break
                # elif not b.isalnum():
                #     diff_count += max_diff
                #     break
        # If the difference is within the allowed limit, add to the result
        if diff_count <= max_diff:
            result.append(sub_str)

    return list(set(result))

def postprocess_invoice_no(info: Dict) -> Tuple[Dict, str]:
    ans = deepcopy(info)
    activate = False
    message = ["", "POSTPROCESS: Invoice No Curation"]
    
    invoice_no = ans["invoice_no"]
    content = ans["content"]
    paddle_ocr_content = ans["paddleocr_content"]

    # CHANGELOG: 05 NOV 2024 add . into the stip set
    invoice_no = invoice_no.strip("#) .")
    ans["invoice_no"] = invoice_no

    # later fall out
    if len(invoice_no)<=4 or len(invoice_no)>=20:
        return ans, ""
    #  if invoice no and receipt no both appear, ask gpt (choose invocie no)
    if "invoice no" in content.lower() and "receipt no" in content.lower():
        return ans,"POSTPROCESS: Invoice No Receipt No both Appearance. Need GPT Verification"
    
    invoice_no_fuzzy_res = find_similar_substring(paddle_ocr_content, invoice_no, max_diff=2)
    if len(invoice_no_fuzzy_res) == 0:
        return ans, ""
    elif len(invoice_no_fuzzy_res) == 1:
        ans["invoice_no"] = invoice_no_fuzzy_res[0]
        return ans, f"POSTPROCESS: Invoice No Replaced by PaddleOCR. Original: {invoice_no}"
    else:
        return ans, f"POSTPROCESS: Multi Fuzzy Invoice No Extracted by PaddleOCR. Need GPT Verification. Fuzzy: {invoice_no_fuzzy_res}"


# CHANGELOG: 06 NOV 2024 add gst for each item for certain service providers
def if_extra_gst_service_provider(info: Dict) -> bool:
    # TODO to be continued when samples got
    extra_gst_servie_provider_abn_set = {"***********", # Pet Chemist Online
                                         "***********", # Perth Animal Eye Hospital
                                         "***********", # Melbourne Veterinary Specialist Centre-Essendon
                                         "***********", # Hamilton Hill Veterinary Hospital
                                         "***********", # Animal Eye Care
                                         "***********", # Pets At Peace
                                         "***********", # Dermatology for Animals
                                         "***********", # Walk-In Clinic for Animals
                                         }
    abn = info["ABN"]
    return len(extra_gst_servie_provider_abn_set & set(abn)) > 0

def postprocess_extra_gst_adjustment(info: Dict) -> Tuple[Dict, str]:
    ans = deepcopy(info)

    if not if_extra_gst_service_provider(ans):
        return ans, ""

    treatments = ans["treatments"]

    tmp = []
    for treatment in treatments:
        adjust_treatment = deepcopy(treatment)
        adjust_treatment["amount"] = round(treatment["amount"] * 1.1, 2)
        tmp.append(adjust_treatment)

    ans["treatments"] = tmp

    return ans, "POSTPROCESS: Treatment Line GST Adjustment"


# FallOut Rule Set
fallout_rule_set = [if_empty_fields, if_date_in_future, if_invoice_no_len_fit,  
                    if_empty_treatment, if_negative_invoice_total, 
                    if_negative_treatment_amount, if_diff_invoice_total_sum_treatment_amount, if_over_conf_threshold]
# Extraction Rule Set
extraction_rule_set = [get_abn, get_shipping]
# PostProcess Rule Set
postprocess_rule_set = [postprocess_treatmentline, postprocess_invoice_no, postprocess_extra_gst_adjustment]

def run_rules(data: Dict, fallout_rule_set: List=fallout_rule_set, extraction_rule_set: List = extraction_rule_set, postprocess_rule_set: List=postprocess_rule_set):
    ans = {}
    for k, v in data.items():
        logger.info(k)
        tmp = []
        for invoice in v:
            invoice = deepcopy(invoice)
            notes = []

            # run extraction rule
            for erule in extraction_rule_set:
                invoice = erule(invoice)

            # run postprocess rule
            for pprule in postprocess_rule_set:
                invoice, message = pprule(invoice)
                notes.append(message)

            # run fall out rule
            for frule in fallout_rule_set:
                activate, message = frule(invoice)
                if activate:
                    notes.append(message)
            invoice["rule_res"] = " ".join(notes)
            tmp.append(invoice)
        ans[k] = tmp
    return ans