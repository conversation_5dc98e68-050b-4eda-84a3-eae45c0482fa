import json
import os
import random
import shutil
from pathlib import Path

import fitz
from PIL import Image
from icecream import ic
from loguru import logger
from sklearn.model_selection import train_test_split

logger.add("../logs/donut_prepare.log", rotation="3 MB")

# Variables
ROOT_DIR = Path(__file__).parents[1]
DATA_DIR = ROOT_DIR / "data"

LABELS = ["invoice", "medical_record", "other"]

random.seed(1234)


# Functions
def convert_to_jpg(data_path: Path):
    """
    Convert pdf, png and jpeg to jpg

    Args:
        data_path (Path): the target folder

    Returns:
        None
    """
    logger.info(f"Converting images in {data_path} to .jpg...")

    data_path = Path(data_path)  # Ensure path is a Path object

    # Assume images are categorized in folders with class names
    categories = [p for p in data_path.iterdir() if p.is_dir()]

    for category in categories:
        for file_path in category.iterdir():  # Use Path.iterdir() for iteration
            if file_path.suffix.lower() == ".pdf":
                doc = fitz.open(file_path)
                for page in doc:
                    pix = page.get_pixmap(dpi=200)
                    output_file = data_path / f"{file_path.stem}_{page.number}.jpg"  # Save directly as .jpg
                    pix.save(output_file)
                os.remove(file_path)  # Use Path.unlink() if you prefer
            elif file_path.suffix.lower() in [".png", ".jpeg"]:
                img = Image.open(file_path).convert("RGB")
                output_file = file_path.with_suffix(".jpg")
                img.save(output_file)
                if file_path.suffix.lower() != ".jpg":  # Check to avoid deleting the source if it's already .jpg
                    os.remove(file_path)
            elif file_path.suffix.lower() == ".jpg":  # Make sure the all jpg files are in RGB mode
                img = Image.open(file_path)
                if img.mode != "RGB":
                    img = img.convert("RGB")
                img.save(file_path)


def partition_dataset(data_path: Path, test_size: float):
    """
    Partition dataset into train and test sets

    Args:
        data_path (Path): path to the data folder
        test_size (float): ratio of test dataset to the whole dataset

    Returns:
        None
    """
    logger.info(f"Partitioning dataset in {data_path} into train and test sets...")
    data_path = Path(data_path)

    # Assume images are categorized in folders with class names
    categories = [p for p in data_path.iterdir() if p.is_dir()]

    for category in categories:
        images = list(category.glob("*.jpg"))
        if images:
            train, test = train_test_split(images, test_size=test_size)
            # Create train/test directories if they don't exist
            train_dir = data_path.parent / "csp_dataset" / "train" / category.name
            test_dir = data_path.parent / "csp_dataset" / "test" / category.name
            train_dir.mkdir(parents=True, exist_ok=True)
            test_dir.mkdir(parents=True, exist_ok=True)

            for file in train:
                shutil.move(str(file), str(train_dir / file.name))
            logger.info(f"Moved {len(train)} images to {train_dir}.")
            for file in test:
                shutil.move(str(file), str(test_dir / file.name))
            logger.info(f"Moved {len(test)} images to {test_dir}.")
        else:
            logger.info(f"No images found in {category}.")


def generate_metadata(data_path: Path):
    """
    Generate metadata.jsonl for a given path

    Args:
        data_path (Path): the target path, must be in one of the label folder

    Returns:
        None
    """
    logger.info(f"Generating metadata.jsonl for {data_path}...")

    data_path = Path(data_path)

    # Write metadata to a JSON file
    with (data_path / "metadata.jsonl").open("w") as metafile:
        for image_file in data_path.glob("*.jpg"):
            item = {"file_name": image_file.name, "ground_truth": {"gt_parse": {"class": data_path.name}}}
            line = json.dumps(item)
            metafile.write(line + "\n")

    num_lines = sum(1 for _ in open(data_path / "metadata.jsonl"))
    num_jpgs = len(list(data_path.glob("*.jpg")))

    assert (
        num_lines == num_jpgs
    ), f"Number of lines in metadata.jsonl {num_lines} does not match the number of images {num_jpgs}."


def prepare_dataset(test_ratio=0.2):
    """
    Prepare dataset for training and testing

    Args:
        test_ratio (float, optional): the ratio of test set. Defaults to 0.2.
    Returns:
        None
    """

    unclassified = DATA_DIR / "unclassified"

    try:
        # Convert pdf, png and jpeg to jpg
        convert_to_jpg(unclassified)

        # Partition dataset into train and test sets
        partition_dataset(unclassified, test_ratio)

        for split in ["train", "test"]:
            # Assume images are categorized in folders with class names
            categories = [p for p in (DATA_DIR / "csp_dataset" / split).iterdir() if p.is_dir()]
            for category in categories:
                if (category / "metadata.jsonl").exists():
                    num_lines = sum(1 for _ in open(category / "metadata.jsonl"))
                    num_jpgs = len(list(category.glob("*.jpg")))
                    if num_lines == num_jpgs:
                        logger.info(f"Dataset {category} has been prepared with {num_jpgs} samples.")
                    else:
                        logger.info(f"Updating dataset {category}...")
                        generate_metadata(category)
                else:
                    generate_metadata(category)

    except Exception as e:
        logger.exception("An error occurred during dataset preparation.")


if __name__ == "__main__":
    prepare_dataset()
