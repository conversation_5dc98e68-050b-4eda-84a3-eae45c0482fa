"""
Utility functions for OCR processing.
"""
from typing import List
import <PERSON><PERSON>htein
from loguru import logger
from pathlib import Path

def find_similar_substring(text: str, substring: str, max_diff: int = 2) -> List[str]:
    """
    Find substrings in text that are similar to the given substring.
    
    Args:
        text: The text to search in
        substring: The substring to search for
        max_diff: Maximum Levenshtein distance allowed
        
    Returns:
        List of similar substrings found
    """
    if not text or not substring:
        return []
    
    words = text.split()
    similar_substrings = []
    
    for i in range(len(words)):
        for j in range(i, min(i + 5, len(words))):
            candidate = " ".join(words[i:j+1])
            distance = Levenshtein.distance(candidate.lower(), substring.lower())
            if distance <= max_diff:
                similar_substrings.append(candidate)
    
    return similar_substrings

def setup_logging():
    """Configure logging for the application."""
    from loguru import logger
    import sys
    
    # Remove default handler
    logger.remove()
    
    # Add a handler for stdout with a specific format
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # Add a file handler
    logger.add(
        "logs/ocr_pipeline.log",
        rotation="10 MB",
        retention="1 week",
        level="DEBUG"
    )

def setup_custom_logging():
    """Setup custom logging with timestamped log file."""
    from datetime import datetime

    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # Create timestamped log filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = logs_dir / f"ocr_pipeline_truuth_{timestamp}.log"

    # Remove default handler
    logger.remove()

    # Add console handler
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )

    # Add file handler with timestamp
    logger.add(
        log_filename,
        rotation="10 MB",
        retention="1 week",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    )

    logger.info(f"Logging initialized. Log file: {log_filename}")
    return log_filename