%load_ext autoreload
%autoreload 2
import pandas as pd
from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient
from datetime import datetime, timedelta
import sys
import os
from pathlib import Path


# Adjust the path to properly include the src directory
module_path = os.path.abspath(os.path.join(".."))  # Go up two levels to reach project root
if module_path not in sys.path:
    sys.path.append(module_path)

from src.sample_collection.sql_engine import *
from dotenv import load_dotenv

# Explicitly load the .env file
load_dotenv()
ROOTDIR = Path("/home/<USER>/repos/OCR_in_house")

# sample prefix
sample_prefix = "test"
# Data location
DATA_FOLDER = ROOTDIR / f"data/samples/{sample_prefix}_samples_DI"
RAW_DATA_FILE = ROOTDIR / f"data/input/{sample_prefix}_samples_DI.csv"
OUTPUT_DATA_FOLDER = ROOTDIR / f"data/di/{sample_prefix}_samples_DI_res"
UPM_GROUND_TRUTH_PATH = ROOTDIR / f"data/sot/{sample_prefix}_SourceOfTruth.xlsx"
PADDLEOCR_RES_PATH = ROOTDIR / f"data/paddleocr/{sample_prefix}_samples_paddleocr.csv"
TRUUTH_PATH = ROOTDIR / f"data/truuth/{sample_prefix}_samples_truuth.csv"



# engine = Engine.disconnect()
engine = Engine.get_engine(server="10.3.0.90", database="BIA")

samples_di = pd.read_csv(RAW_DATA_FILE)

def load_claim_doc_path(df, engine):
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""

        SELECT
            c.ClaimNumber,
            d.*
        FROM [COS].[dbo].[Document] d
            LEFT JOIN [COS].[dbo].[Claim] c
            ON d.ClaimRefNumber = c.ClaimRefNumber
        WHERE [DocumentPath] LIKE '%csp%'
            AND [DocumentType] = 'ClaimInvoice'
            AND c.ClaimNumber
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_sot

doc_path = load_claim_doc_path(samples_di, engine)

# Separate document container and document file
doc_path['DocContainer'] = doc_path['DocumentPath'].str.split(pat="/", n=1, expand = True)[0]
doc_path['DocFile'] = doc_path['DocumentPath'].str.split(pat="/", n=1, expand = True)[1]



# define credentials
    
source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='

source_account_name = 'p3storageprod'  #'p3storagearchive'

sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,
                                     resource_types=ResourceTypes(
                                         service=True, container=True, object=True),
permission=AccountSasPermissions(read=True),
                                     expiry=datetime.utcnow() + timedelta(hours=1))

source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)

# Create download function
def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):
        container_client = blob_service_client.get_container_client(container_name)
        blob_client = container_client.get_blob_client(file_name)
        with open(dest_path, "wb") as f:
            f.write(blob_client.download_blob().readall())

# Create output directory if it doesn't exist
if not Path(DATA_FOLDER).exists():
    os.makedirs(DATA_FOLDER)

# Download documents from blob storage to local folder called pdf             
for idx in doc_path.index:
    path =  DATA_FOLDER +'/' + doc_path['DocFile'][idx]
    download(source_blob_service_client,doc_path['DocContainer'].iloc[idx],doc_path['DocFile'].iloc[idx],path)

def load_claim_truuth(df, engine):
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"].astype(str).apply(lambda x: x.split("-")[0]).unique().tolist()
        )

        sql_query_policy = rf"""

        SELECT
            c.ClaimNumber,
            cc.CustomerId,
            cc.ClaimPolicyNumber,
            cc.CosReference,
            cc.OCRDocumentConfidence,
            cc.OCRResponseObject

        FROM [CSP_ODS_PROD].[dbo].[CustomerClaim] cc
            LEFT JOIN [COS].[dbo].[Claim] c
            ON cc.COSReference = c.ClaimRefNumber COLLATE DATABASE_DEFAULT
        WHERE c.ClaimNumber
                IN ('{claim_numbers}')
                AND cc.OCRReferenceId IS NOT NULL
        """
        df_truuth = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_truuth

df_truuth = load_claim_truuth(samples_di, engine)

len(df_truuth)

df_truuth.to_csv(TRUUTH_PATH, index=False)

df_truuth.head()

def load_upm_sot(df, engine):
    if not df.empty:
        claim_numbers = "','".join(
            df["ClaimNo"]
            .astype(str)
            .apply(lambda x: x.split("-")[0])
            .unique()
            .tolist()
        )

        sql_query_policy = rf"""

        SELECT
            t.ClaimNo,
            t.InvoiceNo,
            s.ServiceProviderName,
            s.ServiceProviderNo,
            ci.DateInvoice,
            t.DateTreatment,
            t.TreatmentDrugRaw AS TreatmentDrugDescription,
            t.AmountInclVat,
            SUM(t.AmountInclVat) OVER (PARTITION BY t.InvoiceNo) AS InvoiceAmount

        FROM [PS-PRD-AZS-DWH1].[BIA].[model].[XML-ClaimTreatment] t
            LEFT JOIN [BIA].[model].[XML-ClaimPhysical] p
                ON t.ClaimNo = p.ClaimNo
            LEFT JOIN [BIA].[dbo].[Ref_VetServiceProvider] s
                ON p.ServiceProviderNo = s.ServiceProviderNo
            LEFT JOIN [BIA].[model].[XML-ClaimInvoice] ci
                ON t.InvoiceNo = ci.InvoiceNo AND t.ClaimNo = ci.ClaimNo

        WHERE t.ClaimNo
                IN ('{claim_numbers}')
        """
        df_sot = pd.read_sql(sql_query_policy, engine)

    # Return both DataFrames
    return df_sot

df_sot = load_upm_sot(samples_di, engine)

len(df_sot)

# Show SOT without invoice no and invoice date from XML
len(df_sot["InvoiceNo"].unique())

df_sot.to_excel(UPM_GROUND_TRUTH_PATH, index=False)

# import libraries 
from loguru import logger
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import HttpResponseError
import glob
import time

# set `<your-endpoint>` and `<your-key>` variables with the values from the Azure portal
endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPONT")
key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
document_analysis_client = DocumentAnalysisClient(
        endpoint=endpoint, credential=AzureKeyCredential(key)
    )

# Create output directory if it doesn't exist
if not Path(OUTPUT_DATA_FOLDER).exists():
    os.makedirs(OUTPUT_DATA_FOLDER)
    
MAX_RETRY = 3
ans = []
doc_path_list = sorted(os.listdir(DATA_FOLDER))

import json
import pickle as pk

for file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:
    retry_num = 0

    file_stem = str(Path(file_path).stem)
    while retry_num < MAX_RETRY:
        logger.info(f"Precessing {file_path}... Retried {retry_num} times")
        try:
            with open(os.path.join(DATA_FOLDER, file_path), "rb") as f:
                poller = document_analysis_client.begin_analyze_document(model_id="prebuilt-invoice", document=f)
                invoices = poller.result()
                invoice_dict = invoices.to_dict()
                ans.append(
                    {
                        "file_path": file_path,
                        "invoice": invoice_dict,
                        }
                    )
                time.sleep(5)

                # dumpt to pk file
                with open(os.path.join(OUTPUT_DATA_FOLDER, f"{file_stem}.pk"), "wb") as fout:
                    pk.dump(invoice_dict, fout)
                # dumpt to json file
                with open(os.path.join(OUTPUT_DATA_FOLDER, f"{file_stem}.json"), "w") as fout:
                    json.dump(invoice_dict, fout, indent=4, default=str)

                break
        except HttpResponseError as hre:
            logger.exception(hre)
            retry_num += 1
            time.sleep(5)
        except Exception as e:
            logger.exception(e)
            break

    if ans and ans[-1]["file_path"] == file_path:
        # process succeed
        logger.info(f"file {file_path} processed SUCCESS")
    else:
        # process failed
        logger.exception(f"file {file_path} processed FAIL")

# dumpt to pk file
with open(os.path.join(OUTPUT_DATA_FOLDER, "ans.pk"), "wb") as fout:
    pk.dump(ans, fout)
# dumpt to json file
with open(os.path.join(OUTPUT_DATA_FOLDER, "ans.json"), "w") as fout:
    json.dump(ans, fout, indent=4, default=str)

# package import
import json
import pickle as pk
import os
import glob
import time
from dotenv import load_dotenv
from loguru import logger
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
from tqdm import tqdm
import fitz  # PyMuPDF
from paddleocr import PaddleOCR, draw_ocr
from fitz import FileDataError


load_dotenv()

pd.set_option("display.max_columns", None)


# load Azure Document Intelligence results from OUTPUT_DATA_FOLDER
def load_document_intelligence_res(res_data_folder: str = OUTPUT_DATA_FOLDER, raw_data_folder: str = DATA_FOLDER) -> List[Dict]:
    try:
        with open(os.path.join(res_data_folder, "ans.pk"), "rb") as fin:
            ans = pk.load(fin)
    except FileNotFoundError:
        original_file_path_list = sorted(os.listdir(raw_data_folder))
        original_file_path_dict = {}
        for p in original_file_path_list:
            original_file_path_dict[str(Path(p).stem)] = p
        ans = []
        for file_path in sorted(os.listdir(res_data_folder)):
            if file_path.endswith(".json"):
                with open(os.path.join(res_data_folder, file_path), "r") as fin:
                    ans.append(
                        {
                            "file_path": original_file_path_dict[str(Path(file_path).stem)],
                            "invoice": json.load(fin),
                            }
                    )
    return ans

document_intelligence_res = load_document_intelligence_res(OUTPUT_DATA_FOLDER)
len(document_intelligence_res)

# CHANGELOG: 04 NOV 2024 add function to verify whether the content contains numbers and puncs only
import string
def is_numbers_and_punctuation(s):
    allowed_chars = set(string.digits + string.punctuation + " ")
    return all(char in allowed_chars for char in s)

# parse Azure Document Intelligence results from ans
def parse_document_intelligence_res(data_info: List[Dict] | Dict) -> Dict:
    if isinstance(data_info, Dict):
        data_info = [data_info]

    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)

    ans = {}
    for data in tqdm(data_info):
        file_path = data["file_path"]
        invoice = data["invoice"]
        content = invoice["content"]
        invoice_info = []
        for document in invoice["documents"]:
            service_provider = document["fields"].get("VendorName", {}).get("value", "") + " " + document["fields"].get("VendorAddressRecipient", {}).get("value", "")
            service_provider = service_provider.strip()
            service_provider_conf = 0.
            service_provider_count = int(document["fields"].get("VendorName", {}).get("value", "") != "") + int(document["fields"].get("VendorAddressRecipient", {}).get("value", "") != "")
            service_provider_conf = (document["fields"].get("VendorName", {}).get("confidence", 0.) or 0.) + (document["fields"].get("VendorAddressRecipient", {}).get("confidence", 0.) or 0.)
            if service_provider_conf > 0.:
                service_provider_conf /= service_provider_count
            # CHANGELOG: 04 NOV 2024 add service provider address extraction for service provider field fuzzy matching preparation
            service_provider_address = ""
            service_provider_address_value = document["fields"].get("VendorAddress", {}).get("value", {})
            service_provider_address_exist = service_provider_address_value.get("street_address", "") and (service_provider_address_value.get("postal_code", "") or service_provider_address_value.get("suburb", "") or service_provider_address_value.get("city", "")) 
            service_provider_address_content = document["fields"].get("VendorAddress", {}).get("content", "").replace("\t", " ").replace("\n", " ")
            if service_provider_address_exist:
                service_provider_address = service_provider_address_content


            invoice_no = document["fields"].get("InvoiceId", {}).get("value", "")
            invoice_date = document["fields"].get("InvoiceDate", {}).get("value", "") or ""
            if not isinstance(invoice_date, str):
                invoice_date = invoice_date.isoformat()
            invoice_total_dict = document["fields"].get("InvoiceTotal", {}).get("value", {}) or {}
            invoice_total = invoice_total_dict.get("amount", -1)

            invoice_no_conf = document["fields"].get("InvoiceId", {}).get("confidence", 0.) or 0.
            invoice_date_conf = document["fields"].get("InvoiceDate", {}).get("confidence", 0.) or 0.
            invoice_total_conf = document["fields"].get("InvoiceTotal", {}).get("confidence", 0.) or 0.


            treatments = []
            cur_treatment_date = invoice_date
            cur_treatment_date_conf = invoice_date_conf

            for item in document["fields"].get("Items", {}).get("value", []):
                item_conf = item.get("confidence", 0.) or 0.

                treatment_date = item.get("value", {}).get("Date", {}).get("value", cur_treatment_date) or ""
                if not isinstance(treatment_date, str):
                    treatment_date = treatment_date.isoformat()
                treatment_date_conf = item.get("value", {}).get("Date", {}).get("confidence", cur_treatment_date_conf)
                if treatment_date_conf is None:
                    treatment_date_conf = item_conf

                if not treatment_date:
                    treatment_date = cur_treatment_date
                    treatment_date_conf = cur_treatment_date_conf
                cur_treatment_date = treatment_date
                cur_treatment_date_conf = treatment_date_conf

                desc = item.get("value", {}).get("Description", {}).get("content", "")
                product = item.get("value", {}).get("ProductCode", {}).get("content", "")
                # CHANGELOG: 04 NOV 2024 ignore product if it only contains numbers and puncs.
                if is_numbers_and_punctuation(product.strip()):
                     product = ""
                desc_conf = item.get("value", {}).get("Description", {}).get("confidence", item_conf) or item_conf
                product_conf = item.get("value", {}).get("ProductCode", {}).get("confidence", item_conf) or item_conf
                desc_conf = (desc_conf * int(desc!="") + product_conf * int(product!=""))/(int(desc!="") + int(product!="")+1e-7)
                desc = product + " " + desc
                desc = desc.strip()

                # CHANGELOG: 01 NOV 2024 default amount change from -1 to 0. This treatment line would be removed later during post process as amount  == 0
                amount_dict = item.get("value", {}).get("Amount", {}).get("value", {}) or {}
                amount = amount_dict.get("amount", 0)
                amount_conf = item.get("value", {}).get("Amount", {}).get("confidence", 0.)
                if amount_conf is None:
                    amount_conf = item_conf


                treatments.append({"treatment_date": treatment_date, 
                                   "treatment": desc, 
                                   "amount": amount, 
                                   "treatment_date_conf": treatment_date_conf,
                                   "treatment_conf": desc_conf, 
                                   "amount_conf": amount_conf, 
                                   "treatmentline_conf": item_conf})

            if not invoice_date:
                invoice_date = cur_treatment_date
                invoice_date_conf = cur_treatment_date_conf

            if not isinstance(invoice_date, str):
                invoice_date = invoice_date.isoformat()

            invoice_info.append(
                {
                    "service_provider": service_provider,
                    "service_provider_address": service_provider_address,
                    "content": content,
                    "invoice_no": invoice_no,
                    "invoice_date": invoice_date,
                    "invoice_total": invoice_total,
                    "service_provider_conf": service_provider_conf,
                    "invoice_no_conf": invoice_no_conf,
                    "invoice_date_conf": invoice_date_conf,
                    "invoice_total_conf": invoice_total_conf,
                    "treatments": treatments
                }
            )
            
        ans[file_path] = invoice_info


    return ans

document_intelligence_parsed_res = parse_document_intelligence_res(document_intelligence_res)

# check whether PaddleOCR results exists
def paddleocr_extract_consultation_notes(file_path: str) -> str:
    """
    Extracts text from a PDF or image file using PaddleOCR, handling broken files.

    Args:
        file_path: The full path to the file.

    Returns:
        A string containing the extracted text, or an empty string if the
        file is broken, unsupported, or contains no text.
    """
    file_suffix = Path(file_path).suffix.lower()
    logger.info(f"Processing {file_path}")

    # Initialize result to None
    result = None

    try:
        if file_suffix in [".pdf", ".PDF"]:
            # Open the PDF file
            pdf_document = fitz.open(file_path)

            # Get the number of pages
            number_of_pages = pdf_document.page_count
            pdf_document.close() # Close the document after getting page count
            logger.info(f"File {file_path} has {number_of_pages} pages")

            # Initialize and run OCR
            ocr = PaddleOCR(use_angle_cls=True, lang="en", page_num=number_of_pages)
            result = ocr.ocr(file_path, cls=True)

        elif file_suffix in [".png", ".jpg", ".jpeg"]:
            # Initialize and run OCR
            ocr = PaddleOCR(use_angle_cls=True, lang="en")
            result = ocr.ocr(file_path, cls=True)

        else:
            # Raise a TypeError for unsupported file formats
            raise TypeError(f"Unsupported file type: {file_suffix}")

    except Exception as e:
        # Catch exceptions from broken/corrupted files (e.g., from fitz.open or ocr.ocr)
        logger.error(f"Could not process broken or corrupted file: {file_path}. Error: {e}")
        return "" # Return empty string for broken files

    # Process and sort the OCR results if extraction was successful
    if result and result[0] is not None:
        def get_sort_key(item):
            # Sort by top-left y coordinate, then x coordinate
            top_left_y = item[0][0][1]
            top_left_x = item[0][0][0]
            return top_left_y, top_left_x

        sorted_pages = []
        for page in result:
            if page:
                # Sort lines within each page
                sorted_page = sorted(page, key=get_sort_key)
                sorted_pages.append(sorted_page)

        if not sorted_pages:
            logger.error(f"No content extracted from {file_path}")
            return ""

        # Combine text from all pages
        all_text = []
        for page in sorted_pages:
            page_text = []
            for line in page:
                # line[1][0] contains the text part of the OCR result
                if line and len(line) > 1 and len(line[1]) > 0:
                    page_text.append(line[1][0])
            all_text.append(" ".join(page_text))

        return "\n".join(all_text)

    # Return an empty string if no result was generated
    logger.warning(f"No text detected in {file_path}")
    return ""

def load_paddleocr_res(pcr_res_path: str = PADDLEOCR_RES_PATH, data_folder: str = DATA_FOLDER):
    try:
        df = pd.read_csv(pcr_res_path)
        df[["content"]] = df[["content"]].fillna(value="")
        return df.to_dict(orient="records")
    except FileNotFoundError:
        # extract all the text content using paddleOCR
        ocr_result = []
        for file_path in sorted(os.listdir(data_folder)):
            try:
                consultation_note = paddleocr_extract_consultation_notes(os.path.join(data_folder, file_path))
            except TypeError:
                consultation_note = ""
            ocr_result.append({
                "file_path": file_path,
                "content": consultation_note
            })
        pd.DataFrame(ocr_result).to_csv(pcr_res_path, index=False)
        return ocr_result

paddleocr_res = load_paddleocr_res(pcr_res_path=PADDLEOCR_RES_PATH, data_folder=DATA_FOLDER)
paddleocr_info_dict = {item["file_path"].lower(): item for item in paddleocr_res}

# add paddleocr_extraction to document intelligence results
tmp = {}
for k, v in document_intelligence_parsed_res.items():
    paddleocr_content = paddleocr_info_dict[k.lower()]["content"]
    tmp_invoice = []
    for invoice in v:
        invoice["paddleocr_content"] = paddleocr_content
        tmp_invoice.append(invoice)
    tmp[k] = tmp_invoice
document_intelligence_parsed_res = tmp
del tmp

# FallOut Rules
def if_empty_fields(info: Dict) -> Tuple[bool, str]:
    # CHANGELOG: 04 Nov 2024 update the service provider empty field checking
    # CHANGELOG: 04 Nov 2024 reconstruct the empty fields check and messages
    messages = []

    invoice_no = info["invoice_no"]
    if_invoice_no_empty = len(invoice_no.strip()) == 0
    if if_invoice_no_empty:
        messages.append("Invoice Number not extracted")

    service_provider = info["service_provider"].lower()
    service_provider_address = info["service_provider_address"].lower()
    abn = info["ABN"]
    if_service_provider_empty = False
    if  len(service_provider.strip())==0 or service_provider.startswith("dr.") or service_provider.startswith("dr "):
        # service provider extraction empty
        # service provider extraction could be wrong that leads to no fuzzy matching result
        if not (abn or service_provider_address):
        # no abn is extracted from the invoice content
        # no address extracted by DI
            if_service_provider_empty = True
            messages.append("Service provider name not extracted")

    invoice_date = info["invoice_date"]
    if_invoice_date_empty = len(invoice_date.strip()) == 0
    if if_invoice_date_empty:
        messages.append("Invoice date not extracted")

    total_amount = info["invoice_total"]
    if_total_amount_empty = len(str(total_amount).strip()) == 0
    if if_total_amount_empty:
        messages.append("Invoice total not extracted")


    activate = len(messages) > 0
    if activate:
        return activate, f"FALL OUT: {'|'.join(messages)}"
    return activate, ""

from datetime import datetime
def if_date_in_future(info: Dict) -> Tuple[bool, str]:
    invoice_date = info["invoice_date"]
    try:
        # Convert the date string to a date object
        date = datetime.strptime(invoice_date, '%Y-%m-%d').date()
        # Get today's date
        today = datetime.today().date()
        # Check if the date is in the future
        activate = date > today
        messages = ["", "FALL OUT: Date in Future"]
        return activate, messages[activate]
    except ValueError:
        return True, "FALL OUT: Date Format Error"

def if_invoice_no_len_fit(info: Dict) -> Tuple[bool, str]:
    invoice_no = info["invoice_no"]
    activate = len(invoice_no) <= 4 or len(invoice_no) >= 20
    messages = ["", "FALL OUT: Invoice Len"]
    return activate, messages[activate]

from PIL import Image
def if_file_broken(path: str) -> Tuple[bool, str]:
    def check_pdf(filepath):
        try:
            with fitz.open(filepath) as doc:
                doc.load_page(0)  # Try loading the first page
            return True
        except FileNotFoundError as fnfe:
            filepath = filepath[:-3]+"PDF"
            with fitz.open(filepath) as doc:
                doc.load_page(0)  # Try loading the first page
            return True
        except Exception as e:
            logger.exception(f"PDF error: {e}")
            return False

    def check_image(filepath):
        try:
            with Image.open(filepath) as img:
                img.verify()  # Verifies image integrity
            return True
        except FileNotFoundError as fnfe:
            file_suffix = Path(filepath).suffix
            filepath = filepath[:-len(file_suffix)] + str(file_suffix).upper()
            with Image.open(filepath) as img:
                img.verify()  # Verifies image integrity
            return True
        except Exception as e:
            logger.exception(f"JPG error: {e}")
            return False
    suffix = str(Path(path).suffix)

    if suffix.lower() == ".pdf":
        activate = not check_pdf(path)
    elif suffix.lower() in [".jpeg", ".jpg", ".png", ".bmp", ".tiff"]:
        activate = not check_image(path)
    else:
        activate = True
    messages = ["", "FALL OUT: Broken File or Invalid File"]
    return activate, messages[activate]

def if_empty_treatment(info: Dict) -> Tuple[bool, str]:
    treatments = info["treatments"]
    activate = len(treatments) == 0
    messages = ["", "FALL OUT: Zero Treatment Line"]
    return activate, messages[activate]

def if_negative_invoice_total(info: Dict) -> Tuple[bool, str]:
    invoice_total = info["invoice_total"]
    activate = invoice_total < 0
    messages = ["", "FALL OUT: Negative Invoice Total"]
    return activate, messages[activate]

def if_negative_treatment_amount(info: Dict) -> Tuple[bool, str]:
    treatments = info["treatments"]

    # CHANGELOG: 06 NOV 2024 add acceptance rules for discount and rounding which are in the treatment lines
    activate = False
    for treatment in treatments:
        # acceptable amount
        if treatment["amount"] >= 0:
            continue
        else:
            if if_diff_invoice_total_sum_treatment_amount(info)[0]:
                activate = True
                break
            else:
                continue
        # # rounding acceptance # TODO to be further refined for the rounding mapping
        # if treatment["treatment"].lower().strip() == "rounding" and -0.05 < treatment["amount"] < 0:
        #     continue

        # # discount acceptance # TODO to be further refined for the discount mapping
        # elif treatment["treatment"].lower().strip().startswith("discount"):
        #     continue

        # # fallout
        # elif treatment["amount"] < 0:
        #     activate = True
        #     break
    # activate = any(treatment["amount"] < 0 for treatment in treatments)
    messages = ["", "FALL OUT: Negative Treatment Amount"]
    return activate, messages[activate]

def if_diff_invoice_total_sum_treatment_amount(info: Dict) -> Tuple[bool, str]:
    # CHANGELOG: 4/11/2024 update the accept_rounding from 0.01 to 0.05
    accept_rounding = 0.05
    treatments = info["treatments"]
    treatment_total = sum([treatment["amount"] if treatment["amount"] else 0. for treatment in treatments ])
    invoice_total = info["invoice_total"]

    activate = abs(round(invoice_total,2) - round(treatment_total, 2)) >= accept_rounding
    messages = ["", "FALL OUT: Invoice total does not match with line totals"]
    # CHANGELOG: 04 NOV 2024 lift invoice total confidence if invoice total == sum(treatment amount)
    if not activate:
        info["invoice_total_conf"] = min(1.0, info["invoice_total_conf"] + 0.3)
    return activate, messages[activate]

def if_over_conf_threshold(info: Dict, conf_threshold: float = 0.8) -> Tuple[bool, str]:
    conf = min(info["invoice_no_conf"], info["invoice_date_conf"], info["invoice_total_conf"])
    activate = conf < conf_threshold
    messages = ["", "FALL OUT: Low Confidence"]
    return activate, messages[activate]

# Extraction Rules
import re 
abn_extract_regex = r"(?:\d *){11}"
abn_extract_regex1 = r"\d{2}-\d{3}-\d{3}-\d{3}"

def validate_abn(nums: List[int]) -> bool:
    if len(nums) != 11:
        return False
    if not all(isinstance(x, int) for x in nums):
        return False
    if any(x>9 for x in nums):
        return False
    if any(x<0 for x in nums):
        return False

    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))
    return s%89 == 0


def get_abn(info: Dict) -> List[str]:
    content = info["content"]
    matches = re.findall(abn_extract_regex, content)
    matches1 = re.findall(abn_extract_regex1, content)
    ans = []
    for match in matches + matches1:
        match_num = []
        for c in match:
            try:
                int_c = int(c)
                match_num.append(int_c)
            except ValueError:
                continue
        if validate_abn(match_num):
            ans.append(match_num)

    ans = list({"".join([str(x) for x in abn]) for abn in ans})
    info["ABN"] = ans
    return info


# TODO
import re
# get phone number 
def extract_phone_number(content: str) -> List[str]:
    # phone number extraction
    phone_number_regex = r"\+?61[- ]?\d{1,2}[- ]?\d{4}[- ]?\d{4}"
    matches = re.findall(phone_number_regex, content) 
    return matches
    

# get email 
def extract_email(content: str) -> List[str]:
    try: 
        # email extraction with sender name
        pattern = r"([a-zA-Z\s]+)?\s*<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})>"
        matches = re.findall(pattern, content)
    except Exception:
        # Email regex only
        pattern = r"([a-zA-Z\s]+)?\s*<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})>"
        matches = re.findall(pattern, content)
    return matches
# get weblink
def extract_weblink(content: str) -> List[str]:
    # weblink extraction
    weblink_regex = r"(http|ftp|https)://([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?"
    matches = re.findall(weblink_regex, content)
    return matches
    
# get shipping Pet Chemist Online
def get_shipping_pet_chemist_online(content: str) -> float:
    # get related text snippet
    # Regex to match the full section containing payment information
    section_pattern = r"Delivery Method:.*?(?:Discount|$)"  # Matches from "Delivery Method" to "Discount" or end of text

    # Extract the full section with payment information
    section_match = re.search(section_pattern, content, re.DOTALL)
    # extract $
    try:
        section_text = section_match.group()  # Extract the matched section

        # Money regex to extract all amounts from this section
        money_pattern = r"\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)"
        amounts = re.findall(money_pattern, section_text)
        # adjust which one
        return float(amounts[-1])
    except Exception:
        return None
    return None

def get_shipping(info: Dict) -> Dict:
    abn = info["ABN"]
    content = info["content"]
    # Pet Chemist Online
    if "***********" in abn:
        shipping = get_shipping_pet_chemist_online(content)
        if shipping:
            invoice_date = info["invoice_date"]
            invoice_date_conf = info["invoice_date_conf"]
            info["treatments"].append({
                "treatment_date": invoice_date,
                "treatment": "shipping",
                "amount": shipping,
                "treatment_date_conf": invoice_date_conf,
                "treatment_conf": 1.0,
                "amount_conf": 1.0,
                "treatmentline_conf": invoice_date_conf})
    return info


# Post-Process Rules
from copy import deepcopy

def postprocess_treatmentline(info: Dict) -> Tuple[Dict, str]:
    ans = deepcopy(info)
    # CHANGELOG: 06 NOV 2024 update the payment related treatment line removal logic
    # CHANGELOG: 06 NOV 2024 update the discount related treatment line removal logic    
    treatments = ans["treatments"]
    treatment_total = sum([treatment["amount"] if treatment["amount"] else 0. for treatment in treatments ])
    invoice_total = ans["invoice_total"]

    activate = False
    message = ["", "POSTPROCESS: Treatment Line Curation"]

    tmp = []
    for treatment in treatments:
        # CHANGELOG: add .strip() to remove space string
        desc = treatment["treatment"].strip().lower()

        # "" in treatment line gets ignored
        if len(desc) == 0:
            activate = True
            message[1] = "POSTPROCESS: Empty Treatment Line"
            continue

        # 0 in treatment line amount gets ignored
        if treatment["amount"] == 0:
            activate = True
            message[1] = "POSTPROCESS: Zero Treatment Amount"
            continue
        
        # asking Matt and Andrew for raw t containing "eftpos" as substring or not
        if "eftpos" in desc:
            activate = True
            message[1] = "POSTPROCESS: EFTPOS in Treatment Line"
            continue
    
        # "payment" or "eftpos" in treatment line gets ignored
        #  payment surcharge should be reserved
        if "payment" in desc:
            if not ("surcharge" in desc and treatment_total == invoice_total):
                activate = True
                message[1] = "POSTPROCESS: Payment in Treatment Line"
                continue
        
        tmp.append(treatment)

    ans["treatments"] = tmp

    return ans, message[activate]


def find_similar_substring(content: str, target: str, max_diff: int = 2):
    """
    Find a substring in the content that has the same length as the target substring,
    with only one or two different characters or digits.

    Args:
        content (str): The string to search within.
        target (str): The substring to compare with.
        max_diff (int): Maximum number of allowed differences. Default is 2.

    Returns:
        list: A list of matching substrings that differ by at most max_diff characters.
    """
    target_len = len(target)
    result = []

    # Loop through content to get every possible substring of the same length as target
    for i in range(len(content) - target_len + 1):
        sub_str = content[i:i + target_len]
        if i-1 >= 0 and content[i-1].isalnum():
            continue
        if i + target_len < len(content) and content[i + target_len].isalnum():
            continue

        # Count how many characters are different between sub_str and target
        # di_invoice_no: 1012538, invoice_no_fuzzy_res: ['.00 58.', '. 1/21 ', '1012540'
        diff_count = 0
        for a, b in zip(sub_str.lower(), target.lower()):
            if a!=b:
                if a.isalnum() and b.isalnum():
                    diff_count += 1
                else:
                    diff_count += max_diff+1
                    break
                # if b.isalnum() and not a.isalnum():
                #     diff_count += max_diff
                #     break
                # elif not b.isalnum():
                #     diff_count += max_diff
                #     break
        # If the difference is within the allowed limit, add to the result
        if diff_count <= max_diff:
            result.append(sub_str)

    return list(set(result))

def postprocess_invoice_no(info: Dict) -> Tuple[Dict, str]:
    ans = deepcopy(info)
    activate = False
    message = ["", "POSTPROCESS: Invoice No Curation"]
    
    invoice_no = ans["invoice_no"]
    content = ans["content"]
    paddle_ocr_content = ans["paddleocr_content"]

    # CHANGELOG: 05 NOV 2024 add . into the stip set
    invoice_no = invoice_no.strip("#) .")
    ans["invoice_no"] = invoice_no

    # later fall out
    if len(invoice_no)<=4 or len(invoice_no)>=20:
        return ans, ""
    #  if invoice no and receipt no both appear, ask gpt (choose invocie no)
    if "invoice no" in content.lower() and "receipt no" in content.lower():
        return ans,"POSTPROCESS: Invoice No Receipt No both Appearance. Need GPT Verification"
    
    invoice_no_fuzzy_res = find_similar_substring(paddle_ocr_content, invoice_no, max_diff=2)
    if len(invoice_no_fuzzy_res) == 0:
        return ans, ""
    elif len(invoice_no_fuzzy_res) == 1:
        ans["invoice_no"] = invoice_no_fuzzy_res[0]
        return ans, f"POSTPROCESS: Invoice No Replaced by PaddleOCR. Original: {invoice_no}"
    else:
        return ans, f"POSTPROCESS: Multi Fuzzy Invoice No Extracted by PaddleOCR. Need GPT Verification. Fuzzy: {invoice_no_fuzzy_res}"


# CHANGELOG: 06 NOV 2024 add gst for each item for certain service providers
def if_extra_gst_service_provider(info: Dict) -> bool:
    # TODO to be continued when samples got
    extra_gst_servie_provider_abn_set = {"***********", # Pet Chemist Online
                                         "***********", # Perth Animal Eye Hospital
                                         "***********", # Melbourne Veterinary Specialist Centre-Essendon
                                         "***********", # Hamilton Hill Veterinary Hospital
                                         "***********", # Animal Eye Care
                                         "***********", # Pets At Peace
                                         "***********", # Dermatology for Animals
                                         "***********", # Walk-In Clinic for Animals
                                         }
    abn = info["ABN"]
    return len(extra_gst_servie_provider_abn_set & set(abn)) > 0

def postprocess_extra_gst_adjustment(info: Dict) -> Tuple[Dict, str]:
    ans = deepcopy(info)

    if not if_extra_gst_service_provider(ans):
        return ans, ""

    treatments = ans["treatments"]

    tmp = []
    for treatment in treatments:
        adjust_treatment = deepcopy(treatment)
        adjust_treatment["amount"] = round(treatment["amount"] * 1.1, 2)
        tmp.append(adjust_treatment)

    ans["treatments"] = tmp

    return ans, "POSTPROCESS: Treatment Line GST Adjustment"

# FallOut Rule Set
fallout_rule_set = [if_empty_fields, if_date_in_future, if_invoice_no_len_fit,  
                    if_empty_treatment, if_negative_invoice_total, 
                    if_negative_treatment_amount, if_diff_invoice_total_sum_treatment_amount, if_over_conf_threshold]
# Extraction Rule Set
extraction_rule_set = [get_abn, get_shipping]
# PostProcess Rule Set
postprocess_rule_set = [postprocess_treatmentline, postprocess_invoice_no, postprocess_extra_gst_adjustment]

def run_rules(data: Dict, fallout_rule_set: List=fallout_rule_set, extraction_rule_set: List = extraction_rule_set, postprocess_rule_set: List=postprocess_rule_set):
    ans = {}
    for k, v in data.items():
        logger.info(k)
        tmp = []
        for invoice in v:
            invoice = deepcopy(invoice)
            notes = []

            # run extraction rule
            for erule in extraction_rule_set:
                invoice = erule(invoice)

            # run postprocess rule
            for pprule in postprocess_rule_set:
                invoice, message = pprule(invoice)
                notes.append(message)

            # run fall out rule
            for frule in fallout_rule_set:
                activate, message = frule(invoice)
                if activate:
                    notes.append(message)
            invoice["rule_res"] = " ".join(notes)
            tmp.append(invoice)
        ans[k] = tmp
    return ans

document_intelligence_rule_res = run_rules(document_intelligence_parsed_res)

len(document_intelligence_rule_res)

with open(f"../data/OCR_in_house/samples/{sample_prefix}_samples_DI_rule.json", "w") as fout:
    json.dump(document_intelligence_rule_res, fout, indent=4, default=str)

import ast
from fuzzywuzzy import fuzz
import numpy as np
import pandas as pd
from gensim.models import Word2Vec
from src.service_provider_matching.preprocessing.preprocess import preprocess, preprocess_numbers, preprocess_web

# config
# Dictionary to link between OCR data and list of Service Provider columns
sp_dict = {
    "Name": "ServiceProviderName",
    "Address": "Address",
    # "streetname_name": "ServiceProviderName",
    # "suburb_name": "ServiceProviderName",
    "abn": "ABN",
    "email": "Email",
    "web": "HomePage",
    "phone_home": "PhoneNo_Home",
    "phone_work": "PhoneNo_Work",
    "phone_mobile": "PhoneNo_Mobile",
    "fax": "FaxNo",
}

# List of hyperparameters
top_n = 10  # Top number of fuzzy matches to keep for Name, Address
cos_l = 0.5  # Lower limit on cosine acceptance
A_log = 1.0  # Logarithmic amplitude for scaling ABN multi-matches
priority_dict = {  # Dictionary to link OCR fields to priority
    "Name": 1.0,
    "Address": 1.0,
    # "streetname_name": 0.5,
    # "suburb_name": 0.5,
    "abn": 1.0,
    "email": 1.0,
    "web": 0.25,
    "phone_home": 0.5,
    "phone_work": 0.5,
    "phone_mobile": 0.5,
    "fax": 0.5,
}

##########################
# Load and preprocess data
##########################

# Read in Service Provider raw data
# serv_prov_raw = pd.read_csv("/workspaces/OCR_in_house/data/OCR_in_house/data/ref_service_provider.csv")
serv_prov_raw = pd.read_csv(ROOTDIR/ "data/ref_service_provider_updated.csv")
serv_prov_raw = serv_prov_raw[serv_prov_raw['Is_Blocked']==0].reset_index(drop=True)
serv_prov = serv_prov_raw.copy()  # create copy of raw data
serv_prov = serv_prov.fillna("")
serv_prov["PostCode"] = serv_prov["PostCode"].astype(str)
print("Service Provider data loaded...")

# Load list of fields to check in iteration
field_list = list(sp_dict.keys())
priority_scores = [priority_dict[field] for field in field_list]

# Preprocessing of Service Provider List
serv_prov["Address"] = (
    serv_prov["Address"]
    + " "
    + serv_prov["City"]
    + " "
    + serv_prov["State"]
    + " "
    + serv_prov["PostCode"]
)  # Concat fields to form full Address
for field in [
    "ServiceProviderName",
    "Address",
    "ABN",
    "Email",
    "HomePage",
    "PhoneNo_Home",
    "PhoneNo_Work",
    "PhoneNo_Mobile",
    "FaxNo",
]:
    if field in ["ServiceProviderName", "Address"]:
        serv_prov[field] = serv_prov[field].apply(preprocess)
    elif field in ["Email", "HomePage"]:
        serv_prov[field] = serv_prov[field].apply(preprocess_web)
    elif field in [
        "PhoneNo_Home",
        "PhoneNo_Work",
        "PhoneNo_Mobile",
        "FaxNo",
        "PhoneNo_Home",
    ]:
        serv_prov[field] = serv_prov[field].apply(preprocess_numbers)
    elif field in ['ABN']:
        serv_prov[field] = serv_prov[field].apply(lambda x: int(x) if x != "" and pd.notnull(x) else x)

import re

abn_extract_regex = r"(?:\d *){11}"
abn_extract_regex1 = r"\d{2}-\d{3}-\d{3}-\d{3}"

def validate_abn(nums: List[int]) -> bool:
    if len(nums) != 11:
        return False
    if not all(isinstance(x, int) for x in nums):
        return False
    if any(x>9 for x in nums):
        return False
    if any(x<0 for x in nums):
        return False

    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))
    return s%89 == 0


def extract_abn(info: Dict) -> List[str]:
    content = info["content"]
    matches = re.findall(abn_extract_regex, content)
    matches1 = re.findall(abn_extract_regex1, content)
    ans = []
    for match in matches + matches1:
        match_num = []
        for c in match:
            try:
                int_c = int(c)
                match_num.append(int_c)
            except ValueError:
                continue
        if validate_abn(match_num):
            ans.append(match_num)

    ans = list({"".join([str(x) for x in abn]) for abn in ans})
    return ans
    # info["ABN"] = ans
    # return info

# get phone number 
def extract_fax_number(content: str) -> List[str]:
    # work landline extraction
    phone_number_regex = r"\(0\d{1,2}\) \d{4} \d{4}"
    matches = re.findall(phone_number_regex, content)
    return matches

def extract_fax_number(content: str) -> List[str]:
    # Regex to capture Australian phone numbers including:
    # 1. Landlines like (02) 9876 5432
    # 2. Mobile numbers like 0412 345 678
    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.
    # Exclude international format like +61
    
    phone_number_regex = r"(\(0\d{1,2}\) \d{4} \d{4})|(\(04\d{2}\) \d{3} \d{3})|(\d{4} \d{3} \d{3})|13\d{4}|1300 \d{3} \d{3}|1800 \d{3} \d{3}"
    
    # Extract all matches
    matches = re.findall(phone_number_regex, content)
    
    # Flatten the list and remove any empty strings
    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]
    
    return flattened_matches

def extract_phone_number(content: str) -> List[str]:
    # Regex to capture Australian phone numbers including:
    # 1. Landlines like (02) 9876 5432
    # 2. Mobile numbers like 0412 345 678
    # 3. Special numbers like 1300 123 456, 1800 123 456, 13 1234, etc.
    # Exclude international format like +61
    
    phone_number_regex = r"(\(0\d{1,2}\) \d{4} \d{4})|(\(04\d{2}\) \d{3} \d{3})|(\d{4} \d{3} \d{3})|13\d{4}|1300 \d{3} \d{3}|1800 \d{3} \d{3}"
    
    # Extract all matches
    matches = re.findall(phone_number_regex, content)
    
    # Flatten the list and remove any empty strings
    flattened_matches = [match[0] or match[1] or match[2] or match[3] or match[4] for match in matches if any(match)]
    
    return flattened_matches

def extract_mobile_number(content: str) -> List[str]:
    # phone number extraction
    phone_number_regex = r"\+?61[- ]?\d{1,2}[- ]?\d{4}[- ]?\d{4}"
    matches = re.findall(phone_number_regex, content) 
    return matches

def extract_email(content: str) -> List[str]:
    # Replace newlines with spaces to normalize text
    content = content.replace("\n", " ")
    
    # Improved regex to capture emails after a colon or space
    pattern = r"(?<=[:\s])([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(?!\S)"
    
    matches = re.findall(pattern, content)
    
    return matches

# get weblink
def extract_weblink(content: str) -> List[str]:
    # weblink extraction
    weblink_regex = r"(http|ftp|https)://([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?"
    matches = re.findall(weblink_regex, content)
    return matches

# prepare service provider info
def prepare_service_provider_info(info: Dict) -> Dict:
    service_provider_info = {}
    content = info["content"]
    service_provider_info["phone_home"] = extract_phone_number(content)
    service_provider_info["phone_work"] = service_provider_info["phone_home"]
    service_provider_info["fax"] = service_provider_info["phone_home"]
    service_provider_info["phone_mobile"] = extract_mobile_number(content)
    service_provider_info["email"] = extract_email(content)
    service_provider_info["web"] = extract_weblink(content)
    service_provider_info["abn"] = extract_abn(info)
    service_provider_info["Name"] = info["service_provider"]
    service_provider_info["Address"] = info["service_provider_address"]
    # service_provider_info["streetname_name"] = info["service_provider_streetname_name"]
    # service_provider_info["suburb_name"] = info["service_provider_suburb_name"]
    return service_provider_info

def fuzzy_match_service_provider(service_provider_info: Dict):
    # Prepare list of match index and cosine values
    match_index_list = []
    match_cosine_list = []

    # Begin iterating over field list
    for field in field_list:
        if (service_provider_info.get(field, None) is not None or service_provider_info[field] is not np.NaN) and service_provider_info.get(field, ""):
            # fields stored in lists (numerical)
            if field in ["abn", "phone_home", "phone_work", "phone_mobile", "fax"]:
                query_list = [
                    preprocess_numbers(query)
                    for query in service_provider_info[field]
                ]  # Convert string to list of integers
                query_list = [query for query in query_list if query != 99999999]
                # print(query_list)
            # fields stored in lists (non-numerical)
            elif field in ["email", "web"]:
                query_list = [
                    preprocess_web(query) for query in service_provider_info[field]
                ]  # Convert string to list of strings
            # # fields stored in lists (requires Fuzzy Search)
            # elif field in ["Name", "Address"]:
            #     query_list = [
            #         query for query in [service_provider_info[field]]
            #     ]  # multiple strings in list
            #     # print(query_list)
            #     # flatten entire fuzzy match list
            #     fuzzy_list = []
            #     for query in query_list:
            #         fuzzy_list += FT_model[field].wv.most_similar(query, topn=top_n)
            #     cosine_dict = {
            #         query: 1.0 for query in query_list if query
            #     }  # Prepare dictionary of cosine value mappings
            #     query_fuzzy_list = [
            #         item[0] for item in fuzzy_list if item[1] >= cos_l
            #     ]  # only keep items above threshold
            #     # print(fuzzy_list)
            #     # Add mappings for cosing values
            #     cosine_fuzzy_dict = {
            #         item[0]: item[1] for item in fuzzy_list if item[1] >= cos_l
            #     }  # only keep items above threshold
            #     # combine all mappings
            #     cosine_dict = {**cosine_dict, **cosine_fuzzy_dict}
            #     # combine all queries
            #     query_list = query_list + query_fuzzy_list
            #     print(query)
            #     print(cosine_dict)
            #     print(query_list)
                      # fields stored in lists (requires Fuzzy Search)
            elif field in ["Name", "Address"]:
                query_list = [
                    query for query in [service_provider_info[field]]
                ]  # multiple strings in list
                
                # Use fuzzywuzzy for fuzzy matching
                fuzzy_list = []
                for query in query_list:
                    # Perform fuzzy matching with each item in the field_list
                    for field_item in serv_prov[sp_dict[field]]:
                        similarity_score = fuzz.ratio(query.lower(), field_item.lower())
                        # print(query.lower(),field_item.lower(),similarity_score)
                        if similarity_score >= cos_l * 100:  # Check if the score exceeds the threshold
                            fuzzy_list.append((field_item, similarity_score))
                            # print(fuzzy_list)
                
                # Sort by similarity score in descending order
                fuzzy_list = sorted(fuzzy_list, key=lambda x: x[1], reverse=True)

                # Get the top N results based on similarity score
                query_fuzzy_list = [item[0] for item in fuzzy_list[:top_n]]
                cosine_dict = {
                    item[0]: item[1] / 100.0 for item in fuzzy_list[:top_n]
                }  # Prepare dictionary of cosine value mappings
                
                # Combine the original query with the fuzzy results
                query_list = query_list + query_fuzzy_list
                # print(query)
                # print(cosine_dict)
                # print(query_list)
                
            # Fields stored in list (does not require Fuzzy search)
            # elif field in ["streetname_name", "suburb_name"]:
            #     query_list = [
            #         preprocess(query) for query in [service_provider_info[field]]
            #     ]  # Convert string to list of strings
            # fields not stored in lists
            else:
                query_list = [preprocess(service_provider_info[field])]  # Put string in list

            # # Build up index of matches
            # if field in ["streetname_name", "suburb_name"] and len(query_list) > 0:
            #     match_index = []
            #     match_query = []
            #     match_length = []
            #     for query in query_list:  # Check all suburbs in query list
            #         match_check = serv_prov[
            #             sp_dict[field]
            #         ].apply(
            #             lambda x: query in x
            #         )  # check if suburb or streetname in contained in list of SP names
            #         match_index += serv_prov[
            #             match_check
            #         ].index.values.tolist()  # Append index list for each query
            #         match_query += serv_prov[match_check][sp_dict[field]].tolist()
            # print(query_list)
            if field in ["abn"] and len(query_list) > 0:
                match_index = []
                match_query = []
                match_length = []
                for query in query_list:
                    match_check = serv_prov[sp_dict[field]].isin(query_list)
                    match_index += serv_prov[match_check].index.values.tolist()
                    match_query += serv_prov[match_check][sp_dict[field]].tolist()
                    match_length += [
                        len(match_index) for index in range(0, len(match_index))
                    ]  # Scaling of cosine score from number of matches returned
                    # print(match_query)
            else:
                match_check = serv_prov[sp_dict[field]].isin(query_list)
                match_index = serv_prov[match_check].index.values.tolist()
                match_query = serv_prov[match_check][sp_dict[field]].tolist()
                # print(match_check,match_index,match_query)
            
            # Map to cosine values
            if field in ["Name", "Address"]:
                match_dict = dict(
                    zip(match_index, [cosine_dict[item] for item in match_query])
                )
                match_cosine = [match_dict[idx] for idx in match_index]
            # elif field in ["abn"]:
            #     # print('abn')
            #     match_cosine = [
            #         1.0 / (A_log * np.log(match_length[idx]) + 1)
            #         for idx in range(0, len(match_index))
            #     ]
                # print(match_cosine)
            else:
                match_cosine = [1.0 for idx in range(0, len(match_index))]
            match_index_list.append(match_index)
            match_cosine_list.append(match_cosine)
        else:
            match_index_list.append([])
            match_cosine_list.append([])

    # Flatten entire match index list
    flat_index_list = [ind for sublist in match_index_list for ind in sublist]

    # Calculate counts, priority score and entities matched
    count_list = []
    for ind in set(flat_index_list):
        priority_score = 0
        fields_matched = []
        for n, match_index in enumerate(match_index_list):
            if ind in match_index:
                cosine_score = match_cosine_list[n][match_index.index(ind)]
                priority_score += priority_scores[n] * cosine_score
                fields_matched.append(field_list[n])
        count_list.append(
            (ind, flat_index_list.count(ind), priority_score, fields_matched)
        )

    # Keep Top 5 best matches in descending order of priority score
    sorted_count_list = sorted(count_list, key=lambda tup: (tup[2], len(tup[3])), reverse=True)[
        :5
    ]  # Sort by match count/ match priority?
    # print(sorted_count_list)
    # print(sorted_count_list)

    # Print best and second best matches to file

    # Initialize the second-best match variables
    best_match_count_2 = 0
    best_match_priority_2 = 0
    best_match_fields_2 = None
    best_match_list_2 = [None]

    if len(sorted_count_list) > 1:
        best_match_index = int([item[0] for item in sorted_count_list][0])
        best_match_count = int([item[1] for item in sorted_count_list][0])
        best_match_priority = [item[2] for item in sorted_count_list][0]
        best_match_fields = [item[3] for item in sorted_count_list][0]
        best_match_list = (
            serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
            .iloc[best_match_index]
            .values.tolist()
        )

        # Check for a tie in priority score and resolve by field match count
        best_match_index_2 = int([item[0] for item in sorted_count_list][1])
        best_match_fields_2 = [item[3] for item in sorted_count_list][1]
        best_match_priority_2 = [item[2] for item in sorted_count_list][1]
        best_match_list_2 = (
            serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
            .iloc[best_match_index_2]
            .values.tolist()
        )

        # If priority scores are the same, compare the number of fields matched
        if best_match_priority == best_match_priority_2:
            if len(best_match_fields) < len(best_match_fields_2):
                best_match_index_2 = int([item[0] for item in sorted_count_list][1])
                best_match_fields_2 = [item[3] for item in sorted_count_list][1]
                best_match_list_2 = (
                    serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
                    .iloc[best_match_index_2]
                    .values.tolist()
                )
            # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index_2].values.tolist()
    
    # Single Match
    elif len(sorted_count_list) == 1:
        best_match_index = int([item[0] for item in sorted_count_list][0])
        best_match_count = int([item[1] for item in sorted_count_list][0])
        best_match_priority = [item[2] for item in sorted_count_list][0]
        best_match_fields = [item[3] for item in sorted_count_list][0]
        best_match_list = (
            serv_prov[["ServiceProviderNo", "ServiceProviderName"]]
            .iloc[best_match_index]
            .values.tolist()
        )  # , 'Address', 'City', 'State', 'PostCode', 'PhoneNo_Home','PhoneNo_Work', 'PhoneNo_Mobile', 'FaxNo', 'Email', 'HomePage', 'ABN']].iloc[best_match_index].values.tolist()

        best_match_index_2 = None
        best_match_count_2 = 0
        best_match_priority_2 = 0
        best_match_fields_2 = None
        best_match_list_2 = [None]
    # No matches
    else:
        best_match_index = None
        best_match_count = 0
        best_match_priority = 0
        best_match_fields = None
        best_match_list = ["",""]

        best_match_index_2 = None
        best_match_count_2 = 0
        best_match_priority_2 = None
        best_match_fields_2 = None
        best_match_list_2 = [None]

    # export to list
    return {
        "best_match_list": best_match_list,
        "best_match_evidence": (best_match_fields, best_match_count, best_match_priority),
        "best_match_list2": best_match_list_2,
        "best_match_evidence2": (best_match_fields_2, best_match_count_2, best_match_priority_2),
        "list" : sorted_count_list
    }

for docfile, info in document_intelligence_rule_res.items():
    service_provider_info = prepare_service_provider_info(info[0])
    service_provider_info.update(fuzzy_match_service_provider(service_provider_info))
    info[0]["service_provider_info"] = service_provider_info
    info[0]["service_provider_fm"] = info[0]["service_provider_info"]['best_match_list'][1]
    info[0]["service_provider_no_fm"] = info[0]["service_provider_info"]['best_match_list'][0]

# prepare claimno file name mapping
def map_claimno_docfile(df: pd.DataFrame = doc_path) -> Tuple[Dict, Dict]:
    claimno2docfile = {}
    docfile2claimno = {}

    for claimno, docfile in zip(df["ClaimNumber"], df["DocFile"]):
        docfile = docfile.lower()
        if claimno not in claimno2docfile:
            claimno2docfile[claimno] = docfile.lower()

        if docfile not in docfile2claimno:
            docfile2claimno[docfile.lower()] = claimno

    return claimno2docfile, docfile2claimno
claimno2docfile, docfile2claimno = map_claimno_docfile(doc_path)

len(claimno2docfile), len(docfile2claimno)

import json
from typing import Dict, Tuple, List

a = json.loads(df_truuth['OCRResponseObject'].iloc[0])

a

import pandas as pd
import json
from typing import Dict, Any, Optional, List

def flatten_ocr_response(df: pd.DataFrame) -> pd.DataFrame:
    """
    Flatten the OCRResponseObject column into separate columns.
    Creates one row per treatment item, with multiple rows per original record if there are multiple invoices/treatments.
    
    Args:
        df: DataFrame containing 'OCRResponseObject', 'ClaimNumber', and 'OCRDocumentConfidence' columns
        
    Returns:
        DataFrame with flattened OCR response fields as separate columns
    """
    count = 0
    
    def extract_ocr_fields(ocr_obj: Any) -> List[Dict[str, Any]]:
        """Extract fields from a single OCR response object, returning a list of records for each treatment item."""
        
        # Initialize base fields that are common across all treatment items
        base_fields = {
            'ServiceProvider': None,
            'ServiceProviderNo': None,
            'ServiceProvider_conf': None,
            'TotalAmount': None,
            'TotalAmount_conf': None,
            'Truuth_Error': None
        }
        
        # Initialize treatment-specific fields
        treatment_fields_template = {
            'InvoiceDate': None,
            'InvoiceDate_conf': None,
            'InvoiceNumber': None,
            'InvoiceNumber_conf': None,
            'TreatmentDate': None,
            'TreatmentDate_conf': None,
            'TreatmentDescription': None,
            'TreatmentDescription_conf': None,
            'TreatmentAmount': None,
            'TreatmentAmount_conf': None,
        }
        
        try:
            # Convert to dict if it's a string
            if isinstance(ocr_obj, str):
                ocr_data = json.loads(ocr_obj)
            elif isinstance(ocr_obj, dict):
                ocr_data = ocr_obj
            else:
                # Return single record with all None values if can't parse
                return [{**base_fields, **treatment_fields_template}]
            
            # Navigate through the nested structure
            vet_xml_claim = ocr_data.get('VetXmlClaim', {})
            info_from_vet = vet_xml_claim.get('InfoFromVet', {})
            vet_info = info_from_vet.get('Vet', {})
            
            # Extract ServiceProvider fields (common across all records)
            base_fields['ServiceProvider'] = vet_info.get('PracticeName')
            base_fields['ServiceProviderNo'] = vet_info.get('PracticeId')
            base_fields['ServiceProvider_conf'] = vet_info.get('PracticeNameConfidence')
            
            # Extract error information (common across all records)
            document_info = vet_xml_claim.get('DocumentInfo', {})
            error_codes = document_info.get('ErrorCode', [])
            if error_codes:
                # Combine all error descriptions
                error_descriptions = [error.get('Description', '') for error in error_codes if error.get('Description')]
                base_fields['Truuth_Error'] = '; '.join(error_descriptions) if error_descriptions else None
            
            # Extract invoice and treatment details from Conditions
            conditions = info_from_vet.get('Conditions', [])
            
            all_treatment_records = []
            
            if conditions:
                # Loop through all conditions
                for condition in conditions:
                    financial = condition.get('Financial', {})
                    
                    # Extract total amount (at condition level)
                    base_fields['TotalAmount'] = financial.get('TotalIncVat')
                    base_fields['TotalAmount_conf'] = financial.get('TotalIncVatConfidence')
                    
                    # Loop through all invoices
                    invoices = financial.get('Invoices', [])
                    for invoice in invoices:
                        invoice_number = invoice.get('InvoiceNumber')
                        invoice_number_conf = invoice.get('InvoiceNumberConfidence')
                        invoice_date = invoice.get('Date')
                        invoice_date_conf = invoice.get('DateConfidence')
                        
                        # Loop through all treatment items in this invoice
                        items = invoice.get('Items', [])
                        if items:
                            for item in items:
                                treatment_record = {
                                    **base_fields,
                                    'InvoiceNumber': invoice_number,
                                    'InvoiceNumber_conf': invoice_number_conf,
                                    'InvoiceDate': invoice_date,
                                    'InvoiceDate_conf': invoice_date_conf,
                                    'TreatmentDate': item.get('TreatmentDate'),
                                    'TreatmentDate_conf': item.get('TreatmentDateConfidence'),
                                    'TreatmentDescription': item.get('Description'),
                                    'TreatmentDescription_conf': item.get('DescriptionConfidence'),
                                    'TreatmentAmount': item.get('TotalIncVAT'),
                                    'TreatmentAmount_conf': item.get('TotalIncVATConfidence'),
                                }
                                all_treatment_records.append(treatment_record)
                        else:
                            # If no items, still create a record with invoice info
                            treatment_record = {
                                **base_fields,
                                **treatment_fields_template,
                                'InvoiceNumber': invoice_number,
                                'InvoiceNumber_conf': invoice_number_conf,
                                'InvoiceDate': invoice_date,
                                'InvoiceDate_conf': invoice_date_conf,
                            }
                            all_treatment_records.append(treatment_record)
            
            # If no records were created, return a single record with base fields
            if not all_treatment_records:
                all_treatment_records = [{**base_fields, **treatment_fields_template}]
            
            return all_treatment_records
            
        except (json.JSONDecodeError, KeyError, TypeError, IndexError) as e:
            # If any error occurs during parsing, return single record with None values
            print(count)
            print(f"Error parsing OCR response: {e}")
            return [{**base_fields, **treatment_fields_template}]
    count += 1
    # Process each row and collect all treatment records
    all_records = []
    
    for idx, row in df.iterrows():
        # Extract OCR fields (returns list of treatment records)
        treatment_records = extract_ocr_fields(row['OCRResponseObject'])
        
        # Add the original row data to each treatment record
        for treatment_record in treatment_records:
            combined_record = {
                'ClaimNumber': row['ClaimNumber'],
                'OCRDocumentConfidence': row['OCRDocumentConfidence'],
                **treatment_record
            }
            all_records.append(combined_record)
    
    # Create DataFrame from all records
    result_df = pd.DataFrame(all_records)
    
    # Define column order
    column_order = [
        'ClaimNumber',
        'OCRDocumentConfidence',
        'ServiceProvider',
        'ServiceProviderNo',
        'ServiceProvider_conf',
        'InvoiceDate',
        'InvoiceDate_conf',
        'InvoiceNumber',
        'InvoiceNumber_conf',
        'TreatmentDate',
        'TreatmentDate_conf',
        'TreatmentDescription',
        'TreatmentDescription_conf',
        'TreatmentAmount',
        'TreatmentAmount_conf',
        'TotalAmount',
        'TotalAmount_conf',
        'Truuth_Error'
    ]
    
    return result_df[column_order]

# Example usage:
# df_flattened = flatten_ocr_response(df)
# print(f"Original DataFrame shape: {df.shape}")
# print(f"Flattened DataFrame shape: {df_flattened.shape}")
# print(df_flattened.head())

df_truuth_processed = flatten_ocr_response(df_truuth)
# df_truuth_processed.head()

def parse_truuth_info(df: pd.DataFrame) -> Dict:
    ans = {}

    for info in df.to_dict(orient="records"):
        claim_no = info["ClaimNumber"]
        ocr_confidence = info["OCRDocumentConfidence"]
        service_provider = info["ServiceProvider"]
        service_provider_no = info["ServiceProviderNo"]
        service_provider_conf = info["ServiceProvider_conf"]
        invoice_date = info["InvoiceDate"]
        invoice_date_conf = info["InvoiceDate_conf"]
        invoice_no = info["InvoiceNumber"]
        invoice_no_conf = info["InvoiceNumber_conf"]
        treatment_date = info["TreatmentDate"]
        treatment_date_conf = info["TreatmentDate_conf"]
        treatment_description = info["TreatmentDescription"]
        treatment_description_conf = info["TreatmentDescription_conf"]
        treatment_amount = info["TreatmentAmount"]
        treatment_amount_conf = info["TreatmentAmount_conf"]
        total_amount = info["TotalAmount"]
        total_amount_conf = info["TotalAmount_conf"]
        truuth_error = info["Truuth_Error"]
 
        if claim_no not in ans:
            ans[claim_no] = {
                "claim_no": claim_no,
                "ocr_confidence": ocr_confidence,
                "service_provider": service_provider,
                "service_provider_no": service_provider_no,
                "service_provider_conf": service_provider_conf,
                "invoice_date": invoice_date,
                "invoice_date_conf": invoice_date_conf,
                "invoice_no": invoice_no,
                "invoice_no_conf": invoice_no_conf,
                "total_amount": total_amount,
                "total_amount_conf": total_amount_conf,
                "truuth_error": truuth_error,

                "treatments": [
                    {
                        "treatment_date": treatment_date,
                        "treatment_date_conf": treatment_date_conf,
                        "treatment_description": treatment_description,
                        "treatment_description_conf": treatment_description_conf,
                        "treatment_amount": treatment_amount,
                        "treatment_amount_conf": treatment_amount_conf,
                    }
                ]
            }
        else:
            ans[claim_no]["treatments"].append(
                {
                    "treatment_date": treatment_date,
                        "treatment_date_conf": treatment_date_conf,
                        "treatment_description": treatment_description,
                        "treatment_description_conf": treatment_description_conf,
                        "treatment_amount": treatment_amount,
                        "treatment_amount_conf": treatment_amount_conf
                }
            )
    return ans

truuth_info_dict = parse_truuth_info(df_truuth_processed)

def gather_all_info_truuth_di(di_res_dict: Dict,
                    truuth_info_dict: Dict,
                    paddleocr_res_dict: Dict,
                    claimno2docfile: Dict,
                    docfile2claimno: Dict):

    summary_list = []

    # Threshold for considering treatments as similar
    TREATMENT_SIMILARITY_THRESHOLD = 0.7

    # files processed by Azure Document Intelligence
    for k, v in di_res_dict.items():
        docfile = k
        invoices = v

        mapping_claimno = docfile2claimno[docfile.lower()]

        if not mapping_claimno:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
            continue

        logger.info(f"Gather infomation: {mapping_claimno} {docfile}")

        paddleocr_content = paddleocr_res_dict[docfile.lower()]["content"]

        if len(invoices) > 1:
            print(k, mapping_claimno, len(invoices))
            print("-"*40)
        
        # Check if the claim number exists in truuth_res_dict
        if mapping_claimno not in truuth_info_dict:
            logger.warning(f"Claim number {mapping_claimno} not found in Truuth data. Processing with empty Truuth data.")
            # Create empty Truuth info with default values
            truuth_info = {
                "claim_no": mapping_claimno,
                "invoice_no": "",
                "invoice_date": None,
                "invoice_total": 0,
                "service_provider": "",
                "service_provider_no": "",
                "service_provider_evidence": "",
                "service_provider2": "",
                "service_provider_no2": "",
                "service_provider_evidence2": "",
                "treatments": [
                    {
                        "date_treatment": "",
                        "treatment": "",
                        "amount": "",
                    }
                ]
            }
        else:
            truuth_info = truuth_info_dict[mapping_claimno]
            
        for invoice in invoices:
            # check invoice no
            di_invoice_no = invoice["invoice_no"]
            truuth_invoice_no = str(truuth_info["invoice_no"])
            di_invoice_no_correct = int(di_invoice_no == truuth_invoice_no)
    
            # check service provider
            di_service_provider = invoice["service_provider_fm"]
            di_service_provider_no = invoice["service_provider_no_fm"]
            di_service_provider_evidence = invoice["service_provider_evidence"]
            di_service_provider2 = invoice["service_provider_fm2"]
            di_service_provider_no2 = invoice["service_provider_no_fm2"]
            di_service_provider_evidence2 = invoice["service_provider_evidence2"]
            di_service_provider_address = invoice["service_provider_address"]
            truuth_service_provider = truuth_info["service_provider"]
            truuth_service_provider_no = truuth_info["service_provider_no"]


            di_service_provider_correct = int(di_service_provider_no == truuth_service_provider_no)
            
            # check invoice date TODO cannot check Truuth results now 15OCT24
            di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
            if truuth_info["invoice_date"]:
                print(truuth_info["invoice_date"])
                parsed_date = datetime.fromisoformat(truuth_info["invoice_date"])
                truuth_invoice_date = parsed_date.date().isoformat()
            else:
                truuth_invoice_date = ""
            # if truuth_info["invoice_date"] != truuth_info["invoice_date"].date().isoformat():
            #     print("invoice_date mismatch", k, mapping_claimno)
            di_invoice_date_correct = int(di_invoice_date == truuth_invoice_date)

            # check total amount ### TODO
            di_total_amount = invoice["invoice_total"]
            truuth_total_amount = truuth_info["total_amount"]  
            # if stat_info.get("invoice_total_truuth", -1) != truuth_info.get("invoice_total", None):
            #     print("invoice_total mismatch", k, mapping_claimno)
            di_total_amount_correct = int(abs(di_total_amount - truuth_total_amount) < 0.05)

            summary = {
                "claimno": mapping_claimno,
                "docfile": docfile,
                # "paddleocr_content": paddleocr_content,

                "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),

                "di_invoice_no": di_invoice_no,
                "truuth_invoice_no": truuth_invoice_no,
                "di_invoice_no_correct": di_invoice_no_correct,
                "di_invoice_no_conf": invoice["invoice_no_conf"],
                "truuth_invoice_no_conf": truuth_info.get("invoice_no_conf", 0.0),

                "di_service_provider": di_service_provider,
                "di_service_provider_conf": invoice["service_provider_conf"],
                "di_service_provider_no": di_service_provider_no,
                "di_service_provider_evidence": di_service_provider_evidence,
                "truuth_service_provider": truuth_service_provider,
                "truuth_service_provider_conf": truuth_info.get("service_provider_conf", 0.0),
                "di_service_provider_correct": di_service_provider_correct,
                "di_service_provider_fm2": di_service_provider2,
                "di_service_provider_no_fm2": di_service_provider2,
                "di_service_provider_evidence2": di_service_provider_evidence2,
                # "di_service_provider_address": di_service_provider_address
                # "di_abn": invoice["ABN"],

                "di_invoice_date": di_invoice_date,
                "truuth_invoice_date": truuth_invoice_date,
                "di_invoice_date_correct": di_invoice_date_correct,
                "di_invoice_date_conf": invoice["invoice_date_conf"],
                "truuth_invoice_date_conf": truuth_info.get("invoice_date_conf", 0.0),

                "di_total_amount": di_total_amount,
                "truuth_total_amount": truuth_total_amount,
                "di_total_amount_correct": di_total_amount_correct,
                "di_total_amount_conf": invoice["invoice_total_conf"],
                "truuth_total_amount_conf": truuth_info.get("total_amount_conf", 0.0),

                "rule_res": invoice["rule_res"],
                "if_fallout": int("FALL OUT" in invoice["rule_res"]),
                "truuth_conf" : truuth_info.get("ocr_confidence",0),
                "truuth_error" : truuth_info.get("truuth_error",0)
            }

            sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
            sorted_truuth_treatments = sorted(truuth_info["treatments"], key=lambda x: x["amount"])
            treatments = []
            di_pointer, truuth_pointer = 0, 0
            while di_pointer < len(sorted_di_treatments) or truuth_pointer < len(sorted_truuth_treatments):
                try:
                    di_treatment = sorted_di_treatments[di_pointer]
                except IndexError:
                    assert di_pointer == len(sorted_di_treatments)
                    di_treatment = {}
                try:
                    truuth_treatment = sorted_truuth_treatments[truuth_pointer]
                except IndexError:
                    assert truuth_pointer == len(sorted_truuth_treatments)
                    truuth_treatment = {}
                # logger.info(di_treatment)

                # logger.info(upm_treatment)
                if not di_treatment and not truuth_treatment:
                    logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
                    logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
                    logger.error(f"sorted_truuth_treatments: {sorted_truuth_treatments}")
                    logger.error(f"di_pointer: {di_pointer}, truuth_pointer: {truuth_pointer}")
                    break

                if not di_treatment:
                    if truuth_treatment["treatment_date"]:
                        parsed_date = datetime.fromisoformat(truuth_treatment["treatment_date"])
                        truuth_treatment_date = parsed_date.date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "truuth_treatment_date": truuth_treatment_date,
                        "truuth_treatment": truuth_treatment["treatment_description"],
                        "truuth_amount": truuth_treatment["treatment_amount"],
                        "truuth_treatment_date_conf": truuth_treatment["treatment_date_conf"],
                        "truuth_amount_conf": truuth_treatment["treatment_amount_conf"],
                        "truuth_treatment_conf": truuth_treatment["treatment_description_conf"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : ""
                    })
                    truuth_pointer += 1
                    continue

                if not truuth_treatment:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "truuth_treatment_date": "",
                        "truuth_treatment": "",
                        "truuth_amount": "",
                        "truuth_treatment_date_conf": "",
                        "truuth_amount_conf": "",
                        "truuth_treatment_conf": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0
                    })
                    di_pointer += 1
                    continue

                if abs(di_treatment["amount"] - truuth_treatment["amount"]) < 0.05:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]
                    if truuth_treatment["treatment_date"]:
                        parsed_date = datetime.fromisoformat(truuth_treatment["treatment_date"])
                        truuth_treatment_date = parsed_date.date().isoformat()
                    di_treatment_date_correct = int(di_treatment_date == truuth_treatment_date)

                    # Add treatment text similarity calculation
                    treatment_similarity = get_text_similarity(di_treatment["treatment"], truuth_treatment["treatment_description"])
                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "truuth_treatment_date": truuth_treatment_date,
                        "truuth_treatment": truuth_treatment["treatment_description"],
                        "truuth_amount": truuth_treatment["treatment_amount"],
                        "truuth_treatment_date_conf": truuth_treatment["treatment_date_conf"],
                        "truuth_amount_conf": truuth_treatment["treatment_amount_conf"],
                        "truuth_treatment_conf": truuth_treatment["treatment_description_conf"],

                        "di_treatment_amount_correct" : 1,
                        "di_treatment_date_correct" : di_treatment_date_correct,
                        "di_treatment_desc_correct": di_treatment_text_correct,
                        "di_treatment_similarity": treatment_similarity
                    })
                    di_pointer += 1
                    truuth_pointer += 1
                elif di_treatment["amount"] < truuth_treatment["treatment_amount"]:

                    di_treatment_date = parsed_date.date().isoformat()
                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "truuth_treatment_date": "",
                        "truuth_treatment": "",
                        "truuth_amount": "",
                        "truuth_treatment_date_conf": "",
                        "truuth_amount_conf": "",
                        "truuth_treatment_conf": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0,
                        "di_treatment_desc_correct": 0,
                        "di_treatment_similarity": 0
                    })
                    di_pointer += 1
                else:
                    if truuth_treatment["treatment_date"]:
                        parsed_date = datetime.fromisoformat(truuth_treatment["treatment_date"])
                        truuth_treatment_date = parsed_date.date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "truuth_treatment_date": truuth_treatment_date,
                        "truuth_treatment": truuth_treatment["treatment_description"],
                        "truuth_amount": truuth_treatment["treatment_amount"],
                        "truuth_treatment_date_conf": truuth_treatment["treatment_date_conf"],
                        "truuth_amount_conf": truuth_treatment["treatment_amount_conf"],
                        "truuth_treatment_conf": truuth_treatment["treatment_description_conf"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : "",
                        "di_treatment_desc_correct": "",
                        "di_treatment_similarity": ""
                    })

                    truuth_pointer += 1
         
            # If there are treatments, loop through each one and create a flat record 
            # by combining the invoice summary with the treatment details.
            if treatments:
                for treatment_item in treatments:
                    row = summary.copy()
                    row.update(treatment_item)
                    summary_list.append(row)
            # If there are no treatments, add the main summary record as is.
            # When converted to a DataFrame, treatment columns will be empty (NaN).
            else:
                summary_list.append(summary)

    summary_list = sorted(summary_list, key=lambda x: (x["claimno"], x["docfile"]))
    return summary_list

truuth_info_dict.keys()

truuth_info_dict['C8781990']

len(df_truuth_processed['ClaimNumber'].unique())

df_truuth_processed[df_truuth_processed['ClaimNumber'] == 'C8782179']

df_truuth_processed

# load UPM ground truth information from UPM_GROUND_TRUTH_PATH
upm_truth_df = pd.read_excel(UPM_GROUND_TRUTH_PATH)
# [['Claim No', 'Invoice No', 'Service Provider Name',
#     'Date Invoice', 'Date Treatment','Treatment Drug Code','Treatment Drug Description', 'Amount Inc Vat',]]
# 'Amount Claimed (UPM)']] #
upm_truth_df.head()

# parse upm_truth_df 
def parse_upm_info(df: pd.DataFrame) -> Dict:
    ans = {}

    for info in df.to_dict(orient="records"):
        claim_no = info["ClaimNo"]
        invoice_no = info["InvoiceNo"]
        service_provider = info["ServiceProviderName"]
        service_provider_no = info["ServiceProviderNo"]
        date_treatment = info["DateTreatment"]
        treatment = info["TreatmentDrugDescription"]
        # treatment_code = info["TreatmentDrugCode"]
        amount = info["AmountInclVat"]
        invoice_date = info.get("DateInvoice", "")
        invoice_total = info.get("InvoiceAmount", 0.)

        if claim_no not in ans:
            ans[claim_no] = {
                "claim_no": claim_no,
                "invoice_no": invoice_no,
                "invoice_date": invoice_date,
                "invoice_total": invoice_total,
                "service_provider": service_provider,
                "service_provider_no": service_provider_no,
                "treatments": [
                    {
                        "date_treatment": date_treatment,
                        "treatment": treatment,
                        # "treatment_code": treatment_code,
                        "amount": amount,
                    }
                ]
            }
        else:
            ans[claim_no]["treatments"].append(
                {
                    "date_treatment": date_treatment,
                    "treatment": treatment,
                    # "treatment_code": treatment_code,
                    "amount": amount,
                }
            )
    return ans

upm_info_dict = parse_upm_info(upm_truth_df)

def filter_di_res_dict(di_res_dict: Dict, 
                      upm_res_dict: Dict,
                      docfile2claimno: Dict) -> Dict:
    """
    Remove entries from di_res_dict where the corresponding claim numbers don't exist in upm_res_dict.
    
    Args:
        di_res_dict: Dictionary mapping docfiles to DI results
        upm_res_dict: Dictionary mapping claim numbers to UPM results
        docfile2claimno: Dictionary mapping docfiles to claim numbers
        
    Returns:
        Filtered di_res_dict with only entries that have matching claim numbers in upm_res_dict
    """
    filtered_di_res_dict = {}
    removed_count = 0
    
    for docfile, di_results in di_res_dict.items():
        # Get the claim number for this docfile
        claim_no = docfile2claimno.get(docfile.lower())
        
        # Skip if no claim number mapping exists
        if not claim_no:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Removing from results.")
            removed_count += 1
            continue
            
        # Check if the claim number exists in upm_res_dict
        if claim_no in upm_res_dict:
            filtered_di_res_dict[docfile] = di_results
        else:
            logger.warning(f"Claim number {claim_no} (docfile: {docfile}) not found in UPM data. Removing from results.")
            removed_count += 1
            
    logger.info(f"Removed {removed_count} entries from di_res_dict that had no matching claim numbers in upm_res_dict.")
    logger.info(f"Original di_res_dict size: {len(di_res_dict)}, Filtered di_res_dict size: {len(filtered_di_res_dict)}")
    
    return filtered_di_res_dict

filtered_di_res_dict = filter_di_res_dict(document_intelligence_rule_res, upm_info_dict, docfile2claimno)


# TODO combine the rule processed results with UPM data (Clary raw)
# Import for text similarity matching
from difflib import SequenceMatcher

def get_text_similarity(text1, text2):
    """Calculate similarity ratio between two strings"""
    if not text1 or not text2:
        return 0.0
    return SequenceMatcher(None, str(text1).lower(), str(text2).lower()).ratio()


    
def gather_all_info(di_res_dict: Dict = document_intelligence_rule_res, 
                   
                    upm_res_dict: Dict = upm_info_dict, 
                    paddleocr_res_dict: Dict = paddleocr_info_dict, 
                    claimno2docfile: Dict = claimno2docfile, 
                    docfile2claimno: Dict = docfile2claimno):

    summary_list = []

    # Threshold for considering treatments as similar
    TREATMENT_SIMILARITY_THRESHOLD = 0.7

    # files are not processed by Azure Document Intelligence
    # for docfile in set(docfile2claimno.keys()) - set(di_res_dict.keys()):
    #     mapping_claimno = docfile2claimno[docfile.lower()]
    #     stat_info = stat_res_dict[mapping_claimno]

    #     ocr_conf = stat_info.get("doc_conf", 0.) or 0.
    #     try:
    #         activate, message = if_file_broken(os.path.join(f"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix}_samples_DI", docfile))
    #     except FileNotFoundError:
    #         activate, message = if_file_broken(os.path.join(f"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix}_samples_DI", docfile.lower()))
    #     summary = {
    #             "claimno": mapping_claimno,
    #             "docfile": docfile,
    #             "ocr_conf": ocr_conf,
    #             "rule_res": message}
    #     summary_list.append(summary)

    # files processed by Azure Document Intelligence
    for k, v in di_res_dict.items():
        docfile = k

        # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',
        # 'invoice_no': '1/928537',
        # 'invoice_date': datetime.date(2024, 9, 22),
        # 'invoice_total': 2634.57,
        # 'service_provider_conf': 0.888,
        # 'invoice_no_conf': 0.94,
        # 'invoice_date_conf': 0.941,
        # 'invoice_total_conf': 0.93,
        # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',
        #     'amount': 190.0,
        #     'treatment_conf': 0.888,
        #     'amount_conf': 0.889,
        #     'treatmentline_conf': 0.85},
        invoices = v

        mapping_claimno = docfile2claimno[docfile.lower()]

        if not mapping_claimno:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
            continue

        logger.info(f"Gather infomation: {mapping_claimno} {docfile}")

        paddleocr_content = paddleocr_res_dict[docfile.lower()]["content"]

        if len(invoices) > 1:
            print(k, mapping_claimno, len(invoices))
            print("-"*40)

        # "claim_no": claim_no,
        # "invoice_no": invoice_no,
        # "invoice_date": invoice_date,
        # "invoice_total": invoice_total,
        # "service_provider": service_provider,
        # "service_provider_address": service_provider_address,
        # "treatments": [
        #     {
        #         "date_treatment": date_treatment,
        #         "treatment": treatment,
        #         "amount": amount,
        #     }
        # ]

        # "claim_no"
        # "doc_conf"
        # "invoice_total_ocr"
        # "invoice_total_upm"
        # "invoice_total_diff"
        # "invoice_count_ocr"
        # "invoice_count_upm"
        # "invoice_count_diff"
        # "is_invoice_num_diff"
        # "treatment_count_ocr"
        # "treatment_count_upm"
        # "treatment_count_diff"
        # "invoice_no"
        # "invoice_date"
        # "service_provider_ocr"
        # "service_provider_upm"
        # "is_service_provider_diff"
        # "amount"
        # "doc_file"
        
        # Check if the claim number exists in upm_res_dict
        if mapping_claimno not in upm_res_dict:
            logger.warning(f"Claim number {mapping_claimno} not found in UPM data. Processing with empty UPM data.")
            # Create empty UPM info with default values
            upm_info = {
                "claim_no": mapping_claimno,
                "invoice_no": "",
                "invoice_date": None,
                "invoice_total": 0,
                "service_provider": "",
                "service_provider_no": "",
                "treatments": []
            }
        else:
            upm_info = upm_res_dict[mapping_claimno]
            
        for invoice in invoices:
            # check invoice no
            di_invoice_no = invoice["invoice_no"]
            upm_invoice_no = str(upm_info["invoice_no"])
            di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)
      

            # check service provider
            di_service_provider = invoice["service_provider"]
            di_service_provider_address = invoice["service_provider_address"]
            upm_service_provider = upm_info["service_provider"]
     
    
            di_service_provider_correct = int(di_service_provider == upm_service_provider)
            

            # check invoice date TODO cannot check Truuth results now 15OCT24
            di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
            if upm_info["invoice_date"]:
                print(upm_info["invoice_date"])
                upm_invoice_date = upm_info["invoice_date"].date().isoformat()
            else:
                upm_invoice_date = ""
            # if upm_info["invoice_date"] != upm_info["invoice_date"].date().isoformat():
            #     print("invoice_date mismatch", k, mapping_claimno)
            di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)


            # check total amount ### TODO
            di_total_amount = invoice["invoice_total"]
            upm_total_amount = upm_info["invoice_total"]  
            # if stat_info.get("invoice_total_upm", -1) != upm_info.get("invoice_total", None):
            #     print("invoice_total mismatch", k, mapping_claimno)
            di_total_amount_correct = int(di_total_amount == upm_total_amount)


            summary = {
                "claimno": mapping_claimno,
                "docfile": docfile,
                "paddleocr_content": paddleocr_content,

                "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),

                "di_invoice_no": di_invoice_no,
                "upm_invoice_no": upm_invoice_no,
                "di_invoice_no_correct": di_invoice_no_correct,
                "di_invoice_no_conf": invoice["invoice_no_conf"],

                "di_service_provider": di_service_provider,
                "di_service_provider_address": di_service_provider_address,
                "upm_service_provider": upm_service_provider,
                "di_service_provider_correct": di_service_provider_correct,
        
                "di_service_provider_conf": invoice["service_provider_conf"],
                "di_abn": invoice["ABN"],

                "di_invoice_date": di_invoice_date,
                "upm_invoice_date": upm_invoice_date,
                "di_invoice_date_correct": di_invoice_date_correct,
                "di_invoice_date_conf": invoice["invoice_date_conf"],

                "di_total_amount": di_total_amount,
          
                "di_total_amount_correct": di_total_amount_correct,
         
                "di_total_amount_conf": invoice["invoice_total_conf"],

                "rule_res": invoice["rule_res"],
                "if_fallout": int("FALL OUT" in invoice["rule_res"])
            }

            # DI treatment 
            # "treatments": [{'treatment_date': '2024-09-17',
            # 'treatment': ' Veterinary Consultation & Examination: Health Assessment',
            # 'amount': 104.0,
            # 'treatment_date_conf': 0.947,
            # 'treatment_conf': 0.8899999110000089,
            # 'amount_conf': 0.888,
            # 'treatmentline_conf': 0.914},]
            # UPM treatment
            # "treatments": [
            #     {
            #         "date_treatment": date_treatment,
            #         "treatment": treatment,
            #         "amount": amount,
            #     }
            # ]
            sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
            sorted_upm_treatments = sorted(upm_info["treatments"], key=lambda x: x["amount"])
            treatments = []
            di_pointer, upm_pointer = 0, 0
            while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):
                try:
                    di_treatment = sorted_di_treatments[di_pointer]
                except IndexError:
                    assert di_pointer == len(sorted_di_treatments)
                    di_treatment = {}
                try:
                    upm_treatment = sorted_upm_treatments[upm_pointer]
                except IndexError:
                    assert upm_pointer == len(sorted_upm_treatments)
                    upm_treatment = {}
                # logger.info(di_treatment)

                # logger.info(upm_treatment)
                if not di_treatment and not upm_treatment:
                    logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
                    logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
                    logger.error(f"sorted_upm_treatments: {sorted_upm_treatments}")
                    logger.error(f"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}")
                    break

                if not di_treatment:
                    if upm_treatment["date_treatment"]:
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "upm_treatment_date": upm_treatment_date,
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : ""


                    })
                    upm_pointer += 1
                    continue

                if not upm_treatment:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],


                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0

                    })
                    di_pointer += 1
                    continue

                if (di_treatment["amount"] == upm_treatment["amount"]) or (di_treatment["amount"] - upm_treatment["amount"]<0.05):
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]
                    if upm_treatment["date_treatment"]:
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    di_treatment_date_correct = int(di_treatment_date == upm_treatment_date )

                    # Add treatment text similarity calculation
                    treatment_similarity = get_text_similarity(di_treatment["treatment"], upm_treatment["treatment"])
                    di_treatment_text_correct = int(treatment_similarity >= TREATMENT_SIMILARITY_THRESHOLD)

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],


                        "upm_treatment_date": upm_treatment["date_treatment"],
                        "upm_treatment": upm_treatment_date,
                        "upm_amount": upm_treatment["amount"],

                        "di_treatment_amount_correct" : 1,
                        "di_treatment_date_correct" : di_treatment_date_correct,
                        "di_treatment_desc_correct": di_treatment_text_correct,
                        "di_reatment_similarity": treatment_similarity


                    })
                    di_pointer += 1
                    upm_pointer += 1
                elif di_treatment["amount"] < upm_treatment["amount"]:
                    di_treatment_date = di_treatment["treatment_date"].isoformat() if not isinstance(di_treatment["treatment_date"], str) else di_treatment["treatment_date"]

                    treatments.append({
                        "di_treatment_date": di_treatment_date,
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],
                        "di_treatment_date_conf": di_treatment["treatment_date_conf"],
                        "di_treatment_conf": di_treatment["treatment_conf"],
                        "di_amount_conf": di_treatment["amount_conf"],

                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",

                        "di_treatment_amount_correct" : 0,
                        "di_treatment_date_correct" : 0,
                        "di_treatment_desc_correct": 0,
                        "di_treatment_similarity": 0

                    })
                    di_pointer += 1
                else:
                    if upm_treatment["date_treatment"]:
                        upm_treatment_date = upm_treatment["date_treatment"].date().isoformat()
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",
                        "di_treatment_date_conf": "",
                        "di_treatment_conf": "",
                        "di_amount_conf": "",

                        "upm_treatment_date": upm_treatment_date,
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                        "di_treatment_amount_correct" : "",
                        "di_treatment_date_correct" : "",
                        "di_treatment_desc_correct": "",
                        "di_treatment_similarity": ""


                    })

                    upm_pointer += 1
            summary["treatments"] = treatments
            summary_list.append(summary)
    summary_list = sorted(summary_list, key=lambda x: (x["claimno"], x["docfile"]))
    return summary_list


data_gather_res = gather_all_info(di_res_dict=filtered_di_res_dict,
                                  #stat_res_dict=stat_info_dict, 
                                  upm_res_dict=upm_info_dict,
                                  paddleocr_res_dict=paddleocr_info_dict, 
                                  claimno2docfile=claimno2docfile, 
                                  docfile2claimno=docfile2claimno)

len(data_gather_res)

# initial output
data_output = []
for info in data_gather_res:
    tmp = deepcopy(info)
    try:
        tmp.pop("treatments", None)
        for treatment in info["treatments"]:
            tmp.update(treatment)
            tmp_output = deepcopy(tmp)
            data_output.append(tmp_output)
            del tmp_output
    except:
        data_output.append(tmp)

len(data_output)

from openpyxl import load_workbook

# RULE_RES_OUTPUT_PATH = f"/workspaces/OCR_in_house/data/{sample_prefix}_samples_DI_rule_res_conf.xlsx"

RULE_RES_OUTPUT_PATH = ROOTDIR/f"data/result/{sample_prefix}_samples_DI_evaluation_raw.xlsx"

try:
    # book = load_workbook(RULE_RES_OUTPUT_PATH)
    excel_writer = pd.ExcelWriter(RULE_RES_OUTPUT_PATH, mode="a")
    num_pre_rule_res = len(pd.ExcelFile(RULE_RES_OUTPUT_PATH).sheet_names)
    # excel_writer.book = book

    logger.info(f"Number of sheets: {num_pre_rule_res}")
    pd.DataFrame(data_output).to_excel(excel_writer, sheet_name=f"Sheet{num_pre_rule_res+1}", index=False)
    excel_writer.close()
except FileNotFoundError:
    pd.DataFrame(data_output).to_excel(RULE_RES_OUTPUT_PATH, index=False)

# TODO combine the rule processed results with UPM data
def gather_all_info(di_res_dict: Dict , 
                 
                    upm_res_dict: Dict , 
                    paddleocr_res_dict: Dict, 
                    claimno2docfile: Dict, 
                    docfile2claimno: Dict):

    summary_list = []

    # Threshold for considering treatments as similar
    TREATMENT_SIMILARITY_THRESHOLD = 0.7


    
    # files processed by Azure Document Intelligence
    for k, v in di_res_dict.items():
        docfile = k
        
        # [{'service_provider': 'Baldivis Vet Hospital Pty Ltd',
        # 'invoice_no': '1/928537',
        # 'invoice_date': datetime.date(2024, 9, 22),
        # 'invoice_total': 2634.57,
        # 'service_provider_conf': 0.888,
        # 'invoice_no_conf': 0.94,
        # 'invoice_date_conf': 0.941,
        # 'invoice_total_conf': 0.93,
        # 'treatments': [{'treatment': 'Consult 19:00 - 23:30',
        #     'amount': 190.0,
        #     'treatment_conf': 0.888,
        #     'amount_conf': 0.889,
        #     'treatmentline_conf': 0.85},
        invoices = v

        mapping_claimno = docfile2claimno[docfile.lower()]

        if not mapping_claimno:
            logger.warning(f"No claim number mapping found for docfile: {docfile}. Skipping.")
            continue

        logger.info(f"Gather infomation: {mapping_claimno} {docfile}")

        paddleocr_content = paddleocr_res_dict[docfile.lower()]["content"]

        if len(invoices) > 1:
            print(k, mapping_claimno, len(invoices))
            print("-"*40)

        # "claim_no": claim_no,
        # "invoice_no": invoice_no,
        # "invoice_date": invoice_date,
        # "invoice_total": invoice_total,
        # "service_provider": service_provider,
        # "service_provider_address": service_provider_address,
        # "treatments": [
        #     {
        #         "date_treatment": date_treatment,
        #         "treatment": treatment,
        #         "amount": amount,
        #     }
        # ]
        upm_info = upm_res_dict[mapping_claimno]
        # "claim_no"
        # "doc_conf"
        # "invoice_total_ocr"
        # "invoice_total_upm"
        # "invoice_total_diff"
        # "invoice_count_ocr"
        # "invoice_count_upm"
        # "invoice_count_diff"
        # "is_invoice_num_diff"
        # "treatment_count_ocr"
        # "treatment_count_upm"
        # "treatment_count_diff"
        # "invoice_no"
        # "invoice_date"
        # "service_provider_ocr"
        # "service_provider_upm"
        # "is_service_provider_diff"
        # "amount"
        # "doc_file"
        stat_info = stat_res_dict[mapping_claimno]

        ocr_conf = stat_info["doc_conf"]
        for invoice in invoices:
            # check invoice no
            di_invoice_no = invoice["invoice_no"]
            upm_invoice_no = str(upm_info["invoice_no"])
            if stat_info["is_invoice_num_diff"] == 1:
                ocr_invoice_no = ""
            else:
                ocr_invoice_no = upm_invoice_no
            di_invoice_no_correct = int(di_invoice_no == upm_invoice_no)
            ocr_invoice_no_correct = int(ocr_invoice_no == upm_invoice_no)

            # check service provider
            di_service_provider = invoice["service_provider"]
            di_service_provider_address = invoice["service_provider_address"]
            upm_service_provider = stat_info["service_provider_upm"]
            if upm_info["service_provider"] != upm_service_provider:
                print("service_provider mismatch",k, mapping_claimno)
            ocr_service_provider = stat_info["service_provider_ocr"]
            is_service_provider_diff = stat_info["is_service_provider_diff"]
            di_service_provider_correct = int(di_service_provider == upm_service_provider)
            ocr_service_provider_correct = int(is_service_provider_diff == 0)

            # check invoice date TODO cannot check Truuth results now 15OCT24
            di_invoice_date = invoice["invoice_date"].isoformat() if not isinstance(invoice["invoice_date"], str) else invoice["invoice_date"]
            upm_invoice_date = stat_info["invoice_date"]
            if upm_info["invoice_date"] and upm_invoice_date != upm_info["invoice_date"].date().isoformat():
                print("invoice_date mismatch", k, mapping_claimno)
            di_invoice_date_correct = int(di_invoice_date == upm_invoice_date)
            # TODO
            ocr_invoice_date_correct = 1

            # check total amount
            di_total_amount = invoice["invoice_total"]
            upm_total_amount = stat_info["invoice_total_upm"]
            ocr_total_amount = stat_info["invoice_total_ocr"]
            if upm_total_amount != upm_info["invoice_total"]:
                print("invoice_total mismatch", k, mapping_claimno)
            di_total_amount_correct = int(di_total_amount == upm_total_amount)
            ocr_total_amount_correct = int(ocr_total_amount == upm_total_amount)

            summary = {
                "claimno": mapping_claimno,
                "docfile": docfile,
                "paddleocr_content": paddleocr_content,
                "ocr_conf": ocr_conf,
                "di_conf": min(invoice["invoice_no_conf"], invoice["invoice_date_conf"], invoice["invoice_total_conf"]),

                "di_invoice_no": di_invoice_no,
                "upm_invoice_no": upm_invoice_no,
                "di_invoice_no_correct": di_invoice_no_correct,
                "ocr_invoice_no_correct": ocr_invoice_no_correct,
                "di_invoice_no_conf": invoice["invoice_no_conf"],

                "di_service_provider": di_service_provider,
                "di_service_provider_address": di_service_provider_address,
                "upm_service_provider": upm_service_provider,
                "ocr_service_provider": ocr_service_provider,
                "di_service_provider_correct": di_service_provider_correct,
                "ocr_service_provider_correct": ocr_service_provider_correct,
                "di_service_provider_conf": invoice["service_provider_conf"],
                "di_abn": invoice["ABN"],

                "di_invoice_date": di_invoice_date,
                "upm_invoice_date": upm_invoice_date,
                "di_invoice_date_correct": di_invoice_date_correct,
                "ocr_invoice_date_correct": ocr_invoice_date_correct,
                "di_invoice_date_conf": invoice["invoice_date_conf"],

                "di_total_amount": di_total_amount,
                "upm_total_amount": upm_total_amount,
                "ocr_total_amount": ocr_total_amount,
                "di_total_amount_correct": di_total_amount_correct,
                "ocr_total_amount_correct": ocr_total_amount_correct,
                "di_total_amount_conf": invoice["invoice_total_conf"],

                "rule_res": invoice["rule_res"],
                "if_fallout": int("FALL OUT" in invoice["rule_res"])
            }

            # DI treatment 
            # "treatments": [{'treatment_date': '2024-09-17',
            # 'treatment': ' Veterinary Consultation & Examination: Health Assessment',
            # 'amount': 104.0,
            # 'treatment_date_conf': 0.947,
            # 'treatment_conf': 0.8899999110000089,
            # 'amount_conf': 0.888,
            # 'treatmentline_conf': 0.914},]
            # UPM treatment
            # "treatments": [
            #     {
            #         "date_treatment": date_treatment,
            #         "treatment": treatment,
            #         "amount": amount,
            #     }
            # ]
            sorted_di_treatments = sorted(invoice["treatments"], key=lambda x: x["amount"])
            sorted_upm_treatments = sorted(upm_info["treatments"], key=lambda x: x["amount"])
            treatments = []
            di_pointer, upm_pointer = 0, 0
            while di_pointer < len(sorted_di_treatments) or upm_pointer < len(sorted_upm_treatments):
                try:
                    di_treatment = sorted_di_treatments[di_pointer]
                except IndexError:
                    assert di_pointer == len(sorted_di_treatments)
                    di_treatment = {}
                try:
                    upm_treatment = sorted_upm_treatments[upm_pointer]
                except IndexError:
                    assert upm_pointer == len(sorted_upm_treatments)
                    upm_treatment = {}
                # logger.info(di_treatment)

                # logger.info(upm_treatment)
                if not di_treatment and not upm_treatment:
                    logger.error(f"ClaimNo: {mapping_claimno}, File:{docfile}, has Abnormal treatments.")
                    logger.error(f"sorted_di_treatments: {sorted_di_treatments}")
                    logger.error(f"sorted_upm_treatments: {sorted_upm_treatments}")
                    logger.error(f"di_pointer: {di_pointer}, upm_pointer: {upm_pointer}")
                    break

                if not di_treatment:
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",

                        "upm_treatment_date": upm_treatment["date_treatment"],
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                    })
                    upm_pointer += 1
                    continue

                if not upm_treatment:
                    treatments.append({
                        "di_treatment_date": di_treatment["treatment_date"],
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],

                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",

                    })
                    di_pointer += 1
                    continue

                if di_treatment["amount"] == upm_treatment["amount"]:
                    treatments.append({
                        "di_treatment_date": di_treatment["treatment_date"],
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],

                        "upm_treatment_date": upm_treatment["date_treatment"],
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                    })
                    di_pointer += 1
                    upm_pointer += 1
                elif di_treatment["amount"] < upm_treatment["amount"]:
                    treatments.append({
                        "di_treatment_date": di_treatment["treatment_date"],
                        "di_treatment": di_treatment["treatment"],
                        "di_amount": di_treatment["amount"],

                        "upm_treatment_date": "",
                        "upm_treatment": "",
                        "upm_amount": "",

                    })
                    di_pointer += 1
                else:
                    treatments.append({
                        "di_treatment_date": "",
                        "di_treatment": "",
                        "di_amount": "",

                        "upm_treatment_date": upm_treatment["date_treatment"],
                        "upm_treatment": upm_treatment["treatment"],
                        "upm_amount": upm_treatment["amount"],

                    })
                    upm_pointer += 1
            summary["treatments"] = treatments
            summary_list.append(summary)
    return summary_list