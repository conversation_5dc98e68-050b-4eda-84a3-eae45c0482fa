{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "input_file_path = Path(\"/workspaces/OCR_in_house/data/OCR_in_house/res/1000_samples_DI_rule_res.xlsx\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.read_excel(input_file_path)\n", "df = df.fillna(\"\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def replace_linebreak2space(text):\n", "    return text.replace(\"\\n\", \" \").replace(\"\\r\", \" \")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["claimno                                  object\n", "docfile                                  object\n", "Truuth_ocr_conf                          object\n", "same_claimno                              int64\n", "if_fallout                                int64\n", "rule_res                                 object\n", "paddleocr_content                        object\n", "di_conf                                 float64\n", "di_invoice_no                            object\n", "upm_invoice_no                           object\n", "di_invoice_no_correct                     int64\n", "ocr_invoice_no_correct                    int64\n", "di_invoice_no_conf                      float64\n", "di_service_provider                      object\n", "di_service_provider_address              object\n", "di_ServiceProviderNo                     object\n", "di_ServiceProviderName                   object\n", "upm_service_provider                     object\n", "ocr_service_provider                     object\n", "di_service_provider_correct               int64\n", "ocr_service_provider_correct              int64\n", "di_service_provider_conf                float64\n", "di_abn                                   object\n", "di_invoice_date                          object\n", "upm_invoice_date                         object\n", "di_invoice_date_correct                   int64\n", "ocr_invoice_date_correct                  int64\n", "di_invoice_date_conf                    float64\n", "di_total_amount                         float64\n", "upm_total_amount                         object\n", "ocr_total_amount                        float64\n", "di_total_amount_correct                   int64\n", "ocr_total_amount_correct                  int64\n", "di_total_amount_conf                    float64\n", "di_treatment_date                        object\n", "di_treatment                             object\n", "di_amount                                object\n", "upm_treatment_date               datetime64[ns]\n", "upm_treatment                            object\n", "upm_amount                               object\n", "human_invoice_no_verify                  object\n", "human_service_provider_verify            object\n", "human_invoice_date_verify                object\n", "human_invoice_total_verify               object\n", "human_treatment_date_verify              object\n", "human_treatment_verify                   object\n", "human_treatment_amount_verify            object\n", "PASS                                     object\n", "NOTE                                     object\n", "human_pass + not_fall_out                object\n", "Rules                                    object\n", "MP_pred                                  object\n", "dtype: object"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dtypes"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-01-14 02:46:11.353\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to claimno\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to docfile\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.366\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to Truuth_ocr_conf\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.373\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to rule_res\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.389\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to paddleocr_content\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.394\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_invoice_no\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.400\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to upm_invoice_no\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.406\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_service_provider\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.413\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_service_provider_address\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.421\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_ServiceProviderNo\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.430\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_ServiceProviderName\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.437\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to upm_service_provider\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.443\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to ocr_service_provider\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.449\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_abn\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.454\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_invoice_date\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.459\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to upm_invoice_date\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.466\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to upm_total_amount\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.471\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_treatment_date\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.478\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_treatment\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.484\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to di_amount\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.490\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to upm_treatment\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.499\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to upm_amount\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.505\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_invoice_no_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.511\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_service_provider_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.516\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_invoice_date_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.522\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_invoice_total_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.528\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_treatment_date_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.534\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_treatment_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.540\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_treatment_amount_verify\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.546\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to PASS\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.551\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to NOTE\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.557\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to human_pass + not_fall_out\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.562\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to Rules\u001b[0m\n", "\u001b[32m2025-01-14 02:46:11.567\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mApplied to MP_pred\u001b[0m\n"]}], "source": ["from loguru import logger\n", "for col in df.columns:\n", "    if df[col].dtype != \"object\":\n", "        continue\n", "    try:\n", "        df[col] = df[col].apply(lambda x: str(x).replace(\"\\n\", \" \").replace(\"\\r\", \" \"))\n", "        logger.info(f\"Applied to {col}\")\n", "    except:\n", "        logger.warning(f\"Failed to apply to {col}\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON><PERSON><PERSON> 4mg (10) ( Ondansetron)'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"di_treatment\"][6]"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["df.to_excel(input_file_path, index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}