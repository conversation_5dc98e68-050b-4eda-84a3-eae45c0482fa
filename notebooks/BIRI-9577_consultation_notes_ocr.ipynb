{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BIRI-9577_consultaion_notes_ocr\n", "\n", "## Intro\n", "<PERSON> mentioned he needs some support to extract the consultation notes from the submitted pdf instead of using the text in the XML file.\n", "\n", "## Step\n", "* data information extraction from db\n", "    - input: claim no\n", "    - output: claim no, container_name, blob_name. in csv format\n", "\n", "    - Need to be done in SQL, linux does not have access to PROD. \n", "\n", "* pdf file download from blob storage\n", "    - input: credential, container_name, blob_name, download_dir,\n", "    - output: pdf file in the download_dir\n", "\n", "* ~~data transformation, transform pdf into images~~ \n", "    - ~~ optional: paddle ocr may support pdf extraction. need to test ~~\n", "    - ~~ input: pdf file location, target folder ~~\n", "    - ~~ output: image files in the target folder ~~\n", "\n", "* oct extraction\n", "    - input: image folder\n", "    - output: concatenated string via PaddleOCR\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Package Versions\n", "azure-storage-blob == 12.19.0\n", "\n", "loguru==0.7.2\n", "\n", "paddleocr == 2.7.0.3\n", "\n", "paddlepaddle == 2.6.0\n", "\n", "pandas==2.2.2\n", "\n", "python-dotenv==1.0.1\n", "\n", "python==3.10.15"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import time\n", "from typing import Any, Union, Tuple, Dict\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from loguru import logger\n", "from shutil import copy2\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "# blob storage download\n", "def blob_download(container_name: str, file_name: str, dest_path: str, override: False):\n", "    source_key = os.getenv(\"AZURE_OCR_BLOB_STORAGE_KEY\")\n", "\n", "    source_account_url = os.getenv(\"AZURE_OCR_BLOB_STORAGE_ENDPONT\")\n", "\n", "    source_blob_service_client = BlobServiceClient(account_url=source_account_url, credential=source_key)\n", "\n", "    # Create download function\n", "    def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "            container_client = blob_service_client.get_container_client(container_name)\n", "            blob_client = container_client.get_blob_client(file_name)\n", "            with open(dest_path, \"wb\") as f:\n", "                f.write(blob_client.download_blob().readall())\n", "\n", "    assert override or not os.path.exists(dest_path)\n", "\n", "    try:\n", "        download(source_blob_service_client, container_name, file_name, dest_path)\n", "        logger.info(f\"file downloaded to {dest_path}\")\n", "        copy2(dest_path, f\"../data/tmp_{time.time_ns()}.pdf\")\n", "    except Exception as e:\n", "        logger.exception(e)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import glob\n", "import fitz  # PyMuPDF\n", "from paddleocr import PaddleOCR, draw_ocr\n", "from fitz import FileDataError\n", "\n", "def extract_consultation_notes(file_path: str) -> str:\n", "    # file_path = Path(file_path)\n", "    file_suffix = Path(file_path).suffix\n", "\n", "    if file_suffix == \".pdf\":\n", "        # Open the PDF file\n", "        pdf_document = fitz.open(file_path)\n", "\n", "        # Get the number of pages\n", "        number_of_pages = pdf_document.page_count\n", "        logger.info(f\"File {file_path} has {number_of_pages} pages\")\n", "        ocr = PaddleOCR(use_angle_cls=True, lang=\"en\", page_num=number_of_pages)\n", "\n", "        result = ocr.ocr(file_path, cls=True)\n", "\n", "    elif file_suffix in [\".png\", \".jpg\", \".jpeg\"]:\n", "        ocr = PaddleOCR(use_angle_cls=True, lang=\"en\")\n", "        result = ocr.ocr(file_path, cls=True)\n", "\n", "    else:\n", "        raise TypeError(\"the file type should be pdf, png, jpg, or jpeg\")\n", "\n", "    if result:\n", "        txts = []\n", "        for idx in range(len(result)):\n", "            tmp_txt = []\n", "            try:\n", "                for line in result[idx]:\n", "                    tmp_txt.append(line[1][0])\n", "            except Exception as e:\n", "                logger.exception(e)\n", "                tmp_txt.append(\"\")\n", "            txts.append(\" \".join(tmp_txt))\n", "\n", "        return \"\\n\".join(txts)\n", "    return \"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OCR from CSV Files with Blob Storage Document Path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["consultaion_note_info_path = \"../data/BIRI-9577_sample_data.csv\"\n", "assert os.path.exists(consultaion_note_info_path)\n", "df = pd.read_csv(consultaion_note_info_path)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df[\"DocContainer\"] = df[\"DocumentPath\"].apply(lambda x: str(Path(x).parent))\n", "df[\"DocFile\"] = df[\"DocumentPath\"].apply(lambda x: str(Path(x).name))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df[\"unique_identifier\"] = df[\"ClaimNo\"].astype(str) + \"-\" + df[\"InvoiceLineNo\"].astype(str)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>DocumentPath</th>\n", "      <th>DocumentType</th>\n", "      <th>DocContainer</th>\n", "      <th>DocFile</th>\n", "      <th>unique_identifier</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>cosservice-prod-claims-********/f8274842-5b6f-...</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-********</td>\n", "      <td>f8274842-5b6f-4aef-b9aa-62f0f0c85128.pdf</td>\n", "      <td>********-20000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>cosservice-prod-claims-********/1574227f-41fe-...</td>\n", "      <td>application/pdf</td>\n", "      <td>cosservice-prod-claims-********</td>\n", "      <td>1574227f-41fe-46e9-8774-def39107a8e9.pdf</td>\n", "      <td>********-20000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>cosservice-prod-claims-********/bb6831f7-dcf0-...</td>\n", "      <td>application/pdf</td>\n", "      <td>cosservice-prod-claims-********</td>\n", "      <td>bb6831f7-dcf0-48bc-800e-be4b23ef5831.pdf</td>\n", "      <td>********-20000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>cosservice-prod-claims-********/3742fd45-938c-...</td>\n", "      <td>application/pdf</td>\n", "      <td>cosservice-prod-claims-********</td>\n", "      <td>3742fd45-938c-4389-8b77-dfa3050b4549.pdf</td>\n", "      <td>********-20000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>cosservice-prod-claims-********/07a45bf7-b472-...</td>\n", "      <td>application/pdf</td>\n", "      <td>cosservice-prod-claims-********</td>\n", "      <td>07a45bf7-b472-465f-bb9a-7142c691b5d0.pdf</td>\n", "      <td>********-20000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ClaimNo  InvoiceLineNo                                       DocumentPath  \\\n", "0  ********          20000  cosservice-prod-claims-********/f8274842-5b6f-...   \n", "1  ********          20000  cosservice-prod-claims-********/1574227f-41fe-...   \n", "2  ********          20000  cosservice-prod-claims-********/bb6831f7-dcf0-...   \n", "3  ********          20000  cosservice-prod-claims-********/3742fd45-938c-...   \n", "4  ********          20000  cosservice-prod-claims-********/07a45bf7-b472-...   \n", "\n", "      DocumentType                     DocContainer  \\\n", "0              pdf  cosservice-prod-claims-********   \n", "1  application/pdf  cosservice-prod-claims-********   \n", "2  application/pdf  cosservice-prod-claims-********   \n", "3  application/pdf  cosservice-prod-claims-********   \n", "4  application/pdf  cosservice-prod-claims-********   \n", "\n", "                                    DocFile unique_identifier  \n", "0  f8274842-5b6f-4aef-b9aa-62f0f0c85128.pdf    ********-20000  \n", "1  1574227f-41fe-46e9-8774-def39107a8e9.pdf    ********-20000  \n", "2  bb6831f7-dcf0-48bc-800e-be4b23ef5831.pdf    ********-20000  \n", "3  3742fd45-938c-4389-8b77-dfa3050b4549.pdf    ********-20000  \n", "4  07a45bf7-b472-465f-bb9a-7142c691b5d0.pdf    ********-20000  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["61"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ans = []"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["61"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ans)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:03:08.808\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:03:08.811\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m20\u001b[0m - \u001b[31m\u001b[1mcannot open broken document\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7db7ae881000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7db7ae6bd3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7db7ae881000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7db7ac0e1ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7db7ae7e8dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7db7abf357e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7db7abf26950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7db7ae7e8dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7db7ad7a8310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7db7abf26950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7db7ad7a9e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7db7ad9897e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7db7ac09c280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7db7abf26f20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.<PERSON>ame(b'1ba89029-56f'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'ca423b735887'...64B)>, <zmq.<PERSON>ame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7db7abf26f20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7db0418c9930>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 8, 5, 3, 8, 744000, tzinfo=tzutc()), 'msg_id': '120a1ab5-fdb7-47d9-8319-94f88...\n", "                                  │       └ [b'1ba89029-56fd-4e1e-9834-7ffdde094ecd']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7db7abf26680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7db0418c99a0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7db7ac0cb0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('from fitz import FileDataError\\n\\nCLAIM_NO_COL = \"unique_identifier\"\\nCONTAINER_NAME_COL = \"DocContainer\"\\nFILE_NAME_COL = ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7db7acbf9ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7db0418c9460>\n", "             └ <function _pseudo_sync_runner at 0x7db7acbe57e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7db0418c9460>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_10991/3467404600.py'\n", "                       │    │             │        └ [<ast.ImportFrom object at 0x7db0418c3730>, <ast.Assign object at 0x7db0418c2470>, <ast.Assign object at 0x7db0418c3670>, <as...\n", "                       │    │             └ <ast.Module object at 0x7db0418c1ba0>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7db7acbfa170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7db6e0fc0490, execution_count=11 error_before_exec=None error_in_exec=None info=<ExecutionInfo obj...\n", "             │    │        └ <code object <module> at 0x7db7314e8f50, file \"/tmp/ipykernel_10991/3467404600.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7db7acbfa200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "         │         │    └ <property object at 0x7db7acbec810>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "         └ <code object <module> at 0x7db7314e8f50, file \"/tmp/ipykernel_10991/3467404600.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_10991/\u001b[0m\u001b[32m\u001b[1m3467404600.py\u001b[0m\", line \u001b[33m16\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mconsultation_note\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mextract_consultation_notes\u001b[0m\u001b[1m(\u001b[0m\u001b[1mDEST_PATH\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m                    │                          └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp.pdf'\u001b[0m\n", "    \u001b[36m                    └ \u001b[0m\u001b[36m\u001b[1m<function extract_consultation_notes at 0x7db77c5ac550>\u001b[0m\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_10991/\u001b[0m\u001b[32m\u001b[1m2857776881.py\u001b[0m\", line \u001b[33m11\u001b[0m, in \u001b[35mextract_consultation_notes\u001b[0m\n", "    \u001b[1mpdf_document\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mfitz\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mopen\u001b[0m\u001b[1m(\u001b[0m\u001b[1mfile_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m               │    │    └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp.pdf'\u001b[0m\n", "    \u001b[36m               │    └ \u001b[0m\u001b[36m\u001b[1m<class 'fitz.fitz.Document'>\u001b[0m\n", "    \u001b[36m               └ \u001b[0m\u001b[36m\u001b[1m<module 'fitz' from '/usr/local/lib/python3.10/site-packages/fitz/__init__.py'>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/fitz/fitz.py\", line 3876, in __init__\n", "    _fitz.Document_swiginit(self, _fitz.new_Document(filename, stream, filetype, rect, width, height, fontsize))\n", "    │     │                 │     │     │            │         │       │         │     │      │       └ 11\n", "    │     │                 │     │     │            │         │       │         │     │      └ 0\n", "    │     │                 │     │     │            │         │       │         │     └ 0\n", "    │     │                 │     │     │            │         │       │         └ None\n", "    │     │                 │     │     │            │         │       └ None\n", "    │     │                 │     │     │            │         └ None\n", "    │     │                 │     │     │            └ '../data/tmp.pdf'\n", "    │     │                 │     │     └ <built-in function new_Document>\n", "    │     │                 │     └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "    │     │                 └ Document('../data/tmp.pdf')\n", "    │     └ <built-in function Document_swiginit>\n", "    └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "\n", "\u001b[31m\u001b[1mfitz.fitz.FileDataError\u001b[0m:\u001b[1m cannot open broken document\u001b[0m\n", "\u001b[32m2024-10-08 05:03:09.020\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:03:09.026\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 22 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:03:09] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=22, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:03:12] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.3203768730163574\n", "[2024/10/08 05:03:12] ppocr DEBUG: cls num  : 76, elapsed : 0.20218443870544434\n", "[2024/10/08 05:03:16] ppocr DEBUG: rec_res num  : 76, elapsed : 4.522339820861816\n", "[2024/10/08 05:03:16] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.2059953212738037\n", "[2024/10/08 05:03:17] ppocr DEBUG: cls num  : 56, elapsed : 0.1402738094329834\n", "[2024/10/08 05:03:21] ppocr DEBUG: rec_res num  : 56, elapsed : 4.049954652786255\n", "[2024/10/08 05:03:21] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.19898009300231934\n", "[2024/10/08 05:03:21] ppocr DEBUG: cls num  : 58, elapsed : 0.12697172164916992\n", "[2024/10/08 05:03:25] ppocr DEBUG: rec_res num  : 58, elapsed : 3.8050906658172607\n", "[2024/10/08 05:03:25] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.2004389762878418\n", "[2024/10/08 05:03:25] ppocr DEBUG: cls num  : 55, elapsed : 0.16838645935058594\n", "[2024/10/08 05:03:29] ppocr DEBUG: rec_res num  : 55, elapsed : 3.963373899459839\n", "[2024/10/08 05:03:29] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.20473217964172363\n", "[2024/10/08 05:03:30] ppocr DEBUG: cls num  : 53, elapsed : 0.14048409461975098\n", "[2024/10/08 05:03:33] ppocr DEBUG: rec_res num  : 53, elapsed : 3.6215980052948\n", "[2024/10/08 05:03:33] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.181685209274292\n", "[2024/10/08 05:03:34] ppocr DEBUG: cls num  : 57, elapsed : 0.14045381546020508\n", "[2024/10/08 05:03:38] ppocr DEBUG: rec_res num  : 57, elapsed : 4.5162034034729\n", "[2024/10/08 05:03:38] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.18332147598266602\n", "[2024/10/08 05:03:38] ppocr DEBUG: cls num  : 55, elapsed : 0.13482284545898438\n", "[2024/10/08 05:03:42] ppocr DEBUG: rec_res num  : 55, elapsed : 4.0298144817352295\n", "[2024/10/08 05:03:43] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.1753852367401123\n", "[2024/10/08 05:03:43] ppocr DEBUG: cls num  : 58, elapsed : 0.12342596054077148\n", "[2024/10/08 05:03:47] ppocr DEBUG: rec_res num  : 58, elapsed : 4.633683681488037\n", "[2024/10/08 05:03:48] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.19063901901245117\n", "[2024/10/08 05:03:48] ppocr DEBUG: cls num  : 55, elapsed : 0.1526050567626953\n", "[2024/10/08 05:03:52] ppocr DEBUG: rec_res num  : 55, elapsed : 4.1918675899505615\n", "[2024/10/08 05:03:52] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.16924500465393066\n", "[2024/10/08 05:03:52] ppocr DEBUG: cls num  : 14, elapsed : 0.05126643180847168\n", "[2024/10/08 05:03:53] ppocr DEBUG: rec_res num  : 14, elapsed : 1.1983656883239746\n", "[2024/10/08 05:03:54] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.18593811988830566\n", "[2024/10/08 05:03:54] ppocr DEBUG: cls num  : 39, elapsed : 0.1086268424987793\n", "[2024/10/08 05:03:56] ppocr DEBUG: rec_res num  : 39, elapsed : 2.0713658332824707\n", "[2024/10/08 05:03:56] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.2048628330230713\n", "[2024/10/08 05:03:56] ppocr DEBUG: cls num  : 98, elapsed : 0.22923707962036133\n", "[2024/10/08 05:04:01] ppocr DEBUG: rec_res num  : 98, elapsed : 4.6358442306518555\n", "[2024/10/08 05:04:01] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.19070148468017578\n", "[2024/10/08 05:04:01] ppocr DEBUG: cls num  : 41, elapsed : 0.11912727355957031\n", "[2024/10/08 05:04:04] ppocr DEBUG: rec_res num  : 41, elapsed : 2.8870484828948975\n", "[2024/10/08 05:04:04] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.23688530921936035\n", "[2024/10/08 05:04:05] ppocr DEBUG: cls num  : 192, elapsed : 0.4244110584259033\n", "[2024/10/08 05:04:13] ppocr DEBUG: rec_res num  : 192, elapsed : 8.273770570755005\n", "[2024/10/08 05:04:13] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.21822524070739746\n", "[2024/10/08 05:04:13] ppocr DEBUG: cls num  : 119, elapsed : 0.2952859401702881\n", "[2024/10/08 05:04:19] ppocr DEBUG: rec_res num  : 119, elapsed : 5.248173236846924\n", "[2024/10/08 05:04:19] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.18941259384155273\n", "[2024/10/08 05:04:19] ppocr DEBUG: cls num  : 61, elapsed : 0.18919730186462402\n", "[2024/10/08 05:04:21] ppocr DEBUG: rec_res num  : 61, elapsed : 2.312324047088623\n", "[2024/10/08 05:04:22] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.18465757369995117\n", "[2024/10/08 05:04:22] ppocr DEBUG: cls num  : 40, elapsed : 0.08661961555480957\n", "[2024/10/08 05:04:24] ppocr DEBUG: rec_res num  : 40, elapsed : 1.9538986682891846\n", "[2024/10/08 05:04:24] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.21639060974121094\n", "[2024/10/08 05:04:24] ppocr DEBUG: cls num  : 167, elapsed : 0.3435063362121582\n", "[2024/10/08 05:04:30] ppocr DEBUG: rec_res num  : 167, elapsed : 6.149990558624268\n", "[2024/10/08 05:04:31] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.21239900588989258\n", "[2024/10/08 05:04:31] ppocr DEBUG: cls num  : 115, elapsed : 0.30062174797058105\n", "[2024/10/08 05:04:36] ppocr DEBUG: rec_res num  : 115, elapsed : 4.804705619812012\n", "[2024/10/08 05:04:36] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.2526881694793701\n", "[2024/10/08 05:04:37] ppocr DEBUG: cls num  : 232, elapsed : 0.5220131874084473\n", "[2024/10/08 05:04:46] ppocr DEBUG: rec_res num  : 232, elapsed : 9.395733833312988\n", "[2024/10/08 05:04:46] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.20814299583435059\n", "[2024/10/08 05:04:46] ppocr DEBUG: cls num  : 95, elapsed : 0.24117493629455566\n", "[2024/10/08 05:04:52] ppocr DEBUG: rec_res num  : 95, elapsed : 5.764638900756836\n", "[2024/10/08 05:04:52] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.19590044021606445\n", "[2024/10/08 05:04:53] ppocr DEBUG: cls num  : 72, elapsed : 0.16106820106506348\n", "[2024/10/08 05:04:56] ppocr DEBUG: rec_res num  : 72, elapsed : 3.8407936096191406\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:04:57.477\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:04:57.485\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 37 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:04:57] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=37, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:05:00] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.28079724311828613\n", "[2024/10/08 05:05:00] ppocr DEBUG: cls num  : 51, elapsed : 0.15739130973815918\n", "[2024/10/08 05:05:06] ppocr DEBUG: rec_res num  : 51, elapsed : 5.484133958816528\n", "[2024/10/08 05:05:06] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.18355751037597656\n", "[2024/10/08 05:05:06] ppocr DEBUG: cls num  : 108, elapsed : 0.23475265502929688\n", "[2024/10/08 05:05:11] ppocr DEBUG: rec_res num  : 108, elapsed : 4.938259124755859\n", "[2024/10/08 05:05:11] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.15866398811340332\n", "[2024/10/08 05:05:11] ppocr DEBUG: cls num  : 40, elapsed : 0.10663127899169922\n", "[2024/10/08 05:05:13] ppocr DEBUG: rec_res num  : 40, elapsed : 1.8472681045532227\n", "[2024/10/08 05:05:13] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.1558835506439209\n", "[2024/10/08 05:05:13] ppocr DEBUG: cls num  : 11, elapsed : 0.05232548713684082\n", "[2024/10/08 05:05:16] ppocr DEBUG: rec_res num  : 11, elapsed : 2.1332058906555176\n", "[2024/10/08 05:05:16] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.17334294319152832\n", "[2024/10/08 05:05:16] ppocr DEBUG: cls num  : 45, elapsed : 0.1149754524230957\n", "[2024/10/08 05:05:26] ppocr DEBUG: rec_res num  : 45, elapsed : 10.153195858001709\n", "[2024/10/08 05:05:26] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.16454768180847168\n", "[2024/10/08 05:05:26] ppocr DEBUG: cls num  : 11, elapsed : 0.059618234634399414\n", "[2024/10/08 05:05:28] ppocr DEBUG: rec_res num  : 11, elapsed : 2.183610677719116\n", "[2024/10/08 05:05:29] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.15917348861694336\n", "[2024/10/08 05:05:29] ppocr DEBUG: cls num  : 6, elapsed : 0.016383647918701172\n", "[2024/10/08 05:05:30] ppocr DEBUG: rec_res num  : 6, elapsed : 0.9546992778778076\n", "[2024/10/08 05:05:30] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.22621822357177734\n", "[2024/10/08 05:05:30] ppocr DEBUG: cls num  : 45, elapsed : 0.11692214012145996\n", "[2024/10/08 05:05:38] ppocr DEBUG: rec_res num  : 45, elapsed : 8.15176248550415\n", "[2024/10/08 05:05:38] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.16817545890808105\n", "[2024/10/08 05:05:38] ppocr DEBUG: cls num  : 21, elapsed : 0.04792904853820801\n", "[2024/10/08 05:05:43] ppocr DEBUG: rec_res num  : 21, elapsed : 4.751304626464844\n", "[2024/10/08 05:05:43] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.15909409523010254\n", "[2024/10/08 05:05:43] ppocr DEBUG: cls num  : 13, elapsed : 0.07195639610290527\n", "[2024/10/08 05:05:46] ppocr DEBUG: rec_res num  : 13, elapsed : 2.3459670543670654\n", "[2024/10/08 05:05:46] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.16775870323181152\n", "[2024/10/08 05:05:46] ppocr DEBUG: cls num  : 21, elapsed : 0.07151651382446289\n", "[2024/10/08 05:05:50] ppocr DEBUG: rec_res num  : 21, elapsed : 4.380408048629761\n", "[2024/10/08 05:05:51] ppocr DEBUG: dt_boxes num : 0, elapsed : 0.303300142288208\n", "[2024/10/08 05:05:51] ppocr DEBUG: cls num  : 0, elapsed : 0\n", "[2024/10/08 05:05:51] ppocr DEBUG: rec_res num  : 0, elapsed : 2.384185791015625e-06\n", "[2024/10/08 05:05:51] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.30908203125\n", "[2024/10/08 05:05:51] ppocr DEBUG: cls num  : 76, elapsed : 0.17636442184448242\n", "[2024/10/08 05:05:55] ppocr DEBUG: rec_res num  : 76, elapsed : 4.19045352935791\n", "[2024/10/08 05:05:55] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.19335389137268066\n", "[2024/10/08 05:05:56] ppocr DEBUG: cls num  : 56, elapsed : 0.14170384407043457\n", "[2024/10/08 05:06:00] ppocr DEBUG: rec_res num  : 56, elapsed : 3.928417205810547\n", "[2024/10/08 05:06:00] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.19266009330749512\n", "[2024/10/08 05:06:00] ppocr DEBUG: cls num  : 58, elapsed : 0.145249605178833\n", "[2024/10/08 05:06:04] ppocr DEBUG: rec_res num  : 58, elapsed : 3.798495292663574\n", "[2024/10/08 05:06:04] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.18894457817077637\n", "[2024/10/08 05:06:04] ppocr DEBUG: cls num  : 55, elapsed : 0.14219260215759277\n", "[2024/10/08 05:06:08] ppocr DEBUG: rec_res num  : 55, elapsed : 3.7604892253875732\n", "[2024/10/08 05:06:08] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.18095707893371582\n", "[2024/10/08 05:06:08] ppocr DEBUG: cls num  : 53, elapsed : 0.1344304084777832\n", "[2024/10/08 05:06:12] ppocr DEBUG: rec_res num  : 53, elapsed : 3.4990522861480713\n", "[2024/10/08 05:06:12] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.1924896240234375\n", "[2024/10/08 05:06:12] ppocr DEBUG: cls num  : 57, elapsed : 0.15352272987365723\n", "[2024/10/08 05:06:16] ppocr DEBUG: rec_res num  : 57, elapsed : 4.460058212280273\n", "[2024/10/08 05:06:17] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.20058631896972656\n", "[2024/10/08 05:06:17] ppocr DEBUG: cls num  : 55, elapsed : 0.16921162605285645\n", "[2024/10/08 05:06:21] ppocr DEBUG: rec_res num  : 55, elapsed : 4.582896709442139\n", "[2024/10/08 05:06:22] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.20651030540466309\n", "[2024/10/08 05:06:22] ppocr DEBUG: cls num  : 58, elapsed : 0.172074556350708\n", "[2024/10/08 05:06:27] ppocr DEBUG: rec_res num  : 58, elapsed : 4.730159521102905\n", "[2024/10/08 05:06:27] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.20291996002197266\n", "[2024/10/08 05:06:27] ppocr DEBUG: cls num  : 55, elapsed : 0.16638398170471191\n", "[2024/10/08 05:06:31] ppocr DEBUG: rec_res num  : 55, elapsed : 4.351732015609741\n", "[2024/10/08 05:06:32] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.1799945831298828\n", "[2024/10/08 05:06:32] ppocr DEBUG: cls num  : 14, elapsed : 0.041932106018066406\n", "[2024/10/08 05:06:33] ppocr DEBUG: rec_res num  : 14, elapsed : 1.0842499732971191\n", "[2024/10/08 05:06:33] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.1822519302368164\n", "[2024/10/08 05:06:33] ppocr DEBUG: cls num  : 39, elapsed : 0.11149168014526367\n", "[2024/10/08 05:06:35] ppocr DEBUG: rec_res num  : 39, elapsed : 2.027785062789917\n", "[2024/10/08 05:06:35] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.20418024063110352\n", "[2024/10/08 05:06:35] ppocr DEBUG: cls num  : 98, elapsed : 0.20427584648132324\n", "[2024/10/08 05:06:39] ppocr DEBUG: rec_res num  : 98, elapsed : 4.0180394649505615\n", "[2024/10/08 05:06:40] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.18131422996520996\n", "[2024/10/08 05:06:40] ppocr DEBUG: cls num  : 41, elapsed : 0.10927748680114746\n", "[2024/10/08 05:06:42] ppocr DEBUG: rec_res num  : 41, elapsed : 2.6874306201934814\n", "[2024/10/08 05:06:43] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.2380669116973877\n", "[2024/10/08 05:06:43] ppocr DEBUG: cls num  : 192, elapsed : 0.40369462966918945\n", "[2024/10/08 05:06:52] ppocr DEBUG: rec_res num  : 192, elapsed : 8.654825448989868\n", "[2024/10/08 05:06:52] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.2236936092376709\n", "[2024/10/08 05:06:52] ppocr DEBUG: cls num  : 119, elapsed : 0.3155851364135742\n", "[2024/10/08 05:06:58] ppocr DEBUG: rec_res num  : 119, elapsed : 5.513834476470947\n", "[2024/10/08 05:06:58] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.217759370803833\n", "[2024/10/08 05:06:58] ppocr DEBUG: cls num  : 61, elapsed : 0.18915295600891113\n", "[2024/10/08 05:07:01] ppocr DEBUG: rec_res num  : 61, elapsed : 3.0298619270324707\n", "[2024/10/08 05:07:01] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.2037208080291748\n", "[2024/10/08 05:07:02] ppocr DEBUG: cls num  : 40, elapsed : 0.13411831855773926\n", "[2024/10/08 05:07:04] ppocr DEBUG: rec_res num  : 40, elapsed : 2.3945939540863037\n", "[2024/10/08 05:07:04] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.24781394004821777\n", "[2024/10/08 05:07:05] ppocr DEBUG: cls num  : 167, elapsed : 0.44553136825561523\n", "[2024/10/08 05:07:12] ppocr DEBUG: rec_res num  : 167, elapsed : 7.223820209503174\n", "[2024/10/08 05:07:12] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.23082780838012695\n", "[2024/10/08 05:07:13] ppocr DEBUG: cls num  : 115, elapsed : 0.31867289543151855\n", "[2024/10/08 05:07:18] ppocr DEBUG: rec_res num  : 115, elapsed : 5.049811601638794\n", "[2024/10/08 05:07:18] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.27025723457336426\n", "[2024/10/08 05:07:18] ppocr DEBUG: cls num  : 232, elapsed : 0.6292164325714111\n", "[2024/10/08 05:07:28] ppocr DEBUG: rec_res num  : 232, elapsed : 9.875042915344238\n", "[2024/10/08 05:07:29] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.19434666633605957\n", "[2024/10/08 05:07:29] ppocr DEBUG: cls num  : 95, elapsed : 0.22379684448242188\n", "[2024/10/08 05:07:34] ppocr DEBUG: rec_res num  : 95, elapsed : 5.247190237045288\n", "[2024/10/08 05:07:34] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.18297457695007324\n", "[2024/10/08 05:07:34] ppocr DEBUG: cls num  : 72, elapsed : 0.15100836753845215\n", "[2024/10/08 05:07:38] ppocr DEBUG: rec_res num  : 72, elapsed : 3.298146963119507\n", "[2024/10/08 05:07:38] ppocr DEBUG: dt_boxes num : 116, elapsed : 0.2389230728149414\n", "[2024/10/08 05:07:38] ppocr DEBUG: cls num  : 116, elapsed : 0.24855446815490723\n", "[2024/10/08 05:07:43] ppocr DEBUG: rec_res num  : 116, elapsed : 4.733449697494507\n", "[2024/10/08 05:07:43] ppocr DEBUG: dt_boxes num : 122, elapsed : 0.1976768970489502\n", "[2024/10/08 05:07:43] ppocr DEBUG: cls num  : 122, elapsed : 0.30101561546325684\n", "[2024/10/08 05:07:48] ppocr DEBUG: rec_res num  : 122, elapsed : 4.826465845108032\n", "[2024/10/08 05:07:48] ppocr DEBUG: dt_boxes num : 19, elapsed : 0.17711853981018066\n", "[2024/10/08 05:07:49] ppocr DEBUG: cls num  : 19, elapsed : 0.08214902877807617\n", "[2024/10/08 05:07:49] ppocr DEBUG: rec_res num  : 19, elapsed : 0.8904256820678711\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:07:50.519\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:07:50.524\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 11 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:07:50] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=11, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:07:53] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.22023534774780273\n", "[2024/10/08 05:07:53] ppocr DEBUG: cls num  : 51, elapsed : 0.14169573783874512\n", "[2024/10/08 05:07:58] ppocr DEBUG: rec_res num  : 51, elapsed : 5.40762734413147\n", "[2024/10/08 05:07:58] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.17904281616210938\n", "[2024/10/08 05:07:59] ppocr DEBUG: cls num  : 108, elapsed : 0.22556853294372559\n", "[2024/10/08 05:08:04] ppocr DEBUG: rec_res num  : 108, elapsed : 5.681619644165039\n", "[2024/10/08 05:08:05] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.2039644718170166\n", "[2024/10/08 05:08:05] ppocr DEBUG: cls num  : 40, elapsed : 0.11916279792785645\n", "[2024/10/08 05:08:07] ppocr DEBUG: rec_res num  : 40, elapsed : 2.260251760482788\n", "[2024/10/08 05:08:07] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.18557095527648926\n", "[2024/10/08 05:08:07] ppocr DEBUG: cls num  : 11, elapsed : 0.051603078842163086\n", "[2024/10/08 05:08:10] ppocr DEBUG: rec_res num  : 11, elapsed : 2.3338117599487305\n", "[2024/10/08 05:08:10] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.19791030883789062\n", "[2024/10/08 05:08:10] ppocr DEBUG: cls num  : 45, elapsed : 0.12270975112915039\n", "[2024/10/08 05:08:22] ppocr DEBUG: rec_res num  : 45, elapsed : 11.623799324035645\n", "[2024/10/08 05:08:22] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.24109935760498047\n", "[2024/10/08 05:08:22] ppocr DEBUG: cls num  : 11, elapsed : 0.06422090530395508\n", "[2024/10/08 05:08:24] ppocr DEBUG: rec_res num  : 11, elapsed : 2.571697235107422\n", "[2024/10/08 05:08:25] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.21586990356445312\n", "[2024/10/08 05:08:25] ppocr DEBUG: cls num  : 6, elapsed : 0.015569925308227539\n", "[2024/10/08 05:08:26] ppocr DEBUG: rec_res num  : 6, elapsed : 1.012988805770874\n", "[2024/10/08 05:08:26] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.17064738273620605\n", "[2024/10/08 05:08:26] ppocr DEBUG: cls num  : 45, elapsed : 0.12040519714355469\n", "[2024/10/08 05:08:34] ppocr DEBUG: rec_res num  : 45, elapsed : 7.723239183425903\n", "[2024/10/08 05:08:34] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.16795992851257324\n", "[2024/10/08 05:08:34] ppocr DEBUG: cls num  : 21, elapsed : 0.0564570426940918\n", "[2024/10/08 05:08:39] ppocr DEBUG: rec_res num  : 21, elapsed : 5.021556377410889\n", "[2024/10/08 05:08:39] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.19780659675598145\n", "[2024/10/08 05:08:39] ppocr DEBUG: cls num  : 13, elapsed : 0.0695183277130127\n", "[2024/10/08 05:08:42] ppocr DEBUG: rec_res num  : 13, elapsed : 2.63723087310791\n", "[2024/10/08 05:08:42] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.18591618537902832\n", "[2024/10/08 05:08:42] ppocr DEBUG: cls num  : 21, elapsed : 0.07024741172790527\n", "[2024/10/08 05:08:47] ppocr DEBUG: rec_res num  : 21, elapsed : 4.863275051116943\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:08:48.074\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:08:48.078\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:08:48] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:08:50] ppocr DEBUG: dt_boxes num : 116, elapsed : 0.2562410831451416\n", "[2024/10/08 05:08:51] ppocr DEBUG: cls num  : 116, elapsed : 0.3845853805541992\n", "[2024/10/08 05:08:57] ppocr DEBUG: rec_res num  : 116, elapsed : 6.426742315292358\n", "[2024/10/08 05:08:57] ppocr DEBUG: dt_boxes num : 122, elapsed : 0.24158716201782227\n", "[2024/10/08 05:08:58] ppocr DEBUG: cls num  : 122, elapsed : 0.36256861686706543\n", "[2024/10/08 05:09:03] ppocr DEBUG: rec_res num  : 122, elapsed : 4.931078910827637\n", "[2024/10/08 05:09:03] ppocr DEBUG: dt_boxes num : 19, elapsed : 0.17573332786560059\n", "[2024/10/08 05:09:03] ppocr DEBUG: cls num  : 19, elapsed : 0.07651901245117188\n", "[2024/10/08 05:09:04] ppocr DEBUG: rec_res num  : 19, elapsed : 0.8429763317108154\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:09:04.674\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:09:04.678\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m20\u001b[0m - \u001b[31m\u001b[1mcannot open broken document\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7db7ae881000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7db7ae6bd3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7db7ae881000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7db7ac0e1ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7db7ae7e8dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7db7abf357e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7db7abf26950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7db7ae7e8dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7db7ad7a8310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7db7abf26950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7db7ad7a9e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7db7ad9897e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...9B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7db7ac09c280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7db7abf26f20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.<PERSON>ame(b'1ba89029-56f'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'ca423b735887'...64B)>, <zmq.<PERSON>ame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7db7abf26f20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7db0418c9930>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 8, 5, 3, 8, 744000, tzinfo=tzutc()), 'msg_id': '120a1ab5-fdb7-47d9-8319-94f88...\n", "                                  │       └ [b'1ba89029-56fd-4e1e-9834-7ffdde094ecd']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7db7abf26680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7db0418c99a0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7db7ac0cb0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('from fitz import FileDataError\\n\\nCLAIM_NO_COL = \"unique_identifier\"\\nCONTAINER_NAME_COL = \"DocContainer\"\\nFILE_NAME_COL = ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7db7acbf9ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7db0418c9460>\n", "             └ <function _pseudo_sync_runner at 0x7db7acbe57e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7db0418c9460>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_10991/3467404600.py'\n", "                       │    │             │        └ [<ast.ImportFrom object at 0x7db0418c3730>, <ast.Assign object at 0x7db0418c2470>, <ast.Assign object at 0x7db0418c3670>, <as...\n", "                       │    │             └ <ast.Module object at 0x7db0418c1ba0>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7db7acbfa170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7db6e0fc0490, execution_count=11 error_before_exec=None error_in_exec=None info=<ExecutionInfo obj...\n", "             │    │        └ <code object <module> at 0x7db7314e8f50, file \"/tmp/ipykernel_10991/3467404600.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7db7acbfa200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "         │         │    └ <property object at 0x7db7acbec810>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7db7a97a4070>\n", "         └ <code object <module> at 0x7db7314e8f50, file \"/tmp/ipykernel_10991/3467404600.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_10991/\u001b[0m\u001b[32m\u001b[1m3467404600.py\u001b[0m\", line \u001b[33m16\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mconsultation_note\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mextract_consultation_notes\u001b[0m\u001b[1m(\u001b[0m\u001b[1mDEST_PATH\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m                    │                          └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp.pdf'\u001b[0m\n", "    \u001b[36m                    └ \u001b[0m\u001b[36m\u001b[1m<function extract_consultation_notes at 0x7db77c5ac550>\u001b[0m\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_10991/\u001b[0m\u001b[32m\u001b[1m2857776881.py\u001b[0m\", line \u001b[33m11\u001b[0m, in \u001b[35mextract_consultation_notes\u001b[0m\n", "    \u001b[1mpdf_document\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mfitz\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mopen\u001b[0m\u001b[1m(\u001b[0m\u001b[1mfile_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m               │    │    └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp.pdf'\u001b[0m\n", "    \u001b[36m               │    └ \u001b[0m\u001b[36m\u001b[1m<class 'fitz.fitz.Document'>\u001b[0m\n", "    \u001b[36m               └ \u001b[0m\u001b[36m\u001b[1m<module 'fitz' from '/usr/local/lib/python3.10/site-packages/fitz/__init__.py'>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/fitz/fitz.py\", line 3876, in __init__\n", "    _fitz.Document_swiginit(self, _fitz.new_Document(filename, stream, filetype, rect, width, height, fontsize))\n", "    │     │                 │     │     │            │         │       │         │     │      │       └ 11\n", "    │     │                 │     │     │            │         │       │         │     │      └ 0\n", "    │     │                 │     │     │            │         │       │         │     └ 0\n", "    │     │                 │     │     │            │         │       │         └ None\n", "    │     │                 │     │     │            │         │       └ None\n", "    │     │                 │     │     │            │         └ None\n", "    │     │                 │     │     │            └ '../data/tmp.pdf'\n", "    │     │                 │     │     └ <built-in function new_Document>\n", "    │     │                 │     └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "    │     │                 └ Document('../data/tmp.pdf')\n", "    │     └ <built-in function Document_swiginit>\n", "    └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "\n", "\u001b[31m\u001b[1mfitz.fitz.FileDataError\u001b[0m:\u001b[1m cannot open broken document\u001b[0m\n", "\u001b[32m2024-10-08 05:09:04.770\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:09:04.775\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 22 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:09:04] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=22, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:09:08] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.42748141288757324\n", "[2024/10/08 05:09:08] ppocr DEBUG: cls num  : 76, elapsed : 0.2599067687988281\n", "[2024/10/08 05:09:13] ppocr DEBUG: rec_res num  : 76, elapsed : 4.902327537536621\n", "[2024/10/08 05:09:13] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.2461402416229248\n", "[2024/10/08 05:09:14] ppocr DEBUG: cls num  : 56, elapsed : 0.18775010108947754\n", "[2024/10/08 05:09:18] ppocr DEBUG: rec_res num  : 56, elapsed : 4.667114973068237\n", "[2024/10/08 05:09:18] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.20499610900878906\n", "[2024/10/08 05:09:19] ppocr DEBUG: cls num  : 58, elapsed : 0.14517688751220703\n", "[2024/10/08 05:09:23] ppocr DEBUG: rec_res num  : 58, elapsed : 4.471259593963623\n", "[2024/10/08 05:09:23] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.21344614028930664\n", "[2024/10/08 05:09:24] ppocr DEBUG: cls num  : 55, elapsed : 0.19101548194885254\n", "[2024/10/08 05:09:28] ppocr DEBUG: rec_res num  : 55, elapsed : 4.823081731796265\n", "[2024/10/08 05:09:29] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.2278275489807129\n", "[2024/10/08 05:09:29] ppocr DEBUG: cls num  : 53, elapsed : 0.18661808967590332\n", "[2024/10/08 05:09:33] ppocr DEBUG: rec_res num  : 53, elapsed : 4.316694021224976\n", "[2024/10/08 05:09:33] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.23755693435668945\n", "[2024/10/08 05:09:34] ppocr DEBUG: cls num  : 57, elapsed : 0.1637425422668457\n", "[2024/10/08 05:09:38] ppocr DEBUG: rec_res num  : 57, elapsed : 4.194429874420166\n", "[2024/10/08 05:09:38] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.1889197826385498\n", "[2024/10/08 05:09:38] ppocr DEBUG: cls num  : 55, elapsed : 0.14243698120117188\n", "[2024/10/08 05:09:42] ppocr DEBUG: rec_res num  : 55, elapsed : 4.184198379516602\n", "[2024/10/08 05:09:42] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.18306326866149902\n", "[2024/10/08 05:09:43] ppocr DEBUG: cls num  : 58, elapsed : 0.13514304161071777\n", "[2024/10/08 05:09:47] ppocr DEBUG: rec_res num  : 58, elapsed : 4.376949787139893\n", "[2024/10/08 05:09:47] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.18956804275512695\n", "[2024/10/08 05:09:47] ppocr DEBUG: cls num  : 55, elapsed : 0.13767242431640625\n", "[2024/10/08 05:09:51] ppocr DEBUG: rec_res num  : 55, elapsed : 3.602177143096924\n", "[2024/10/08 05:09:51] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.18391823768615723\n", "[2024/10/08 05:09:51] ppocr DEBUG: cls num  : 14, elapsed : 0.05608177185058594\n", "[2024/10/08 05:09:52] ppocr DEBUG: rec_res num  : 14, elapsed : 1.1854631900787354\n", "[2024/10/08 05:09:53] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.18320608139038086\n", "[2024/10/08 05:09:53] ppocr DEBUG: cls num  : 39, elapsed : 0.10736870765686035\n", "[2024/10/08 05:09:54] ppocr DEBUG: rec_res num  : 39, elapsed : 1.741060495376587\n", "[2024/10/08 05:09:55] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.18225622177124023\n", "[2024/10/08 05:09:55] ppocr DEBUG: cls num  : 98, elapsed : 0.21829485893249512\n", "[2024/10/08 05:09:59] ppocr DEBUG: rec_res num  : 98, elapsed : 3.867276191711426\n", "[2024/10/08 05:09:59] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.16290783882141113\n", "[2024/10/08 05:09:59] ppocr DEBUG: cls num  : 41, elapsed : 0.10389828681945801\n", "[2024/10/08 05:10:01] ppocr DEBUG: rec_res num  : 41, elapsed : 2.3927295207977295\n", "[2024/10/08 05:10:02] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.2101585865020752\n", "[2024/10/08 05:10:02] ppocr DEBUG: cls num  : 192, elapsed : 0.3721599578857422\n", "[2024/10/08 05:10:08] ppocr DEBUG: rec_res num  : 192, elapsed : 6.068926095962524\n", "[2024/10/08 05:10:08] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.18041539192199707\n", "[2024/10/08 05:10:08] ppocr DEBUG: cls num  : 119, elapsed : 0.2382655143737793\n", "[2024/10/08 05:10:12] ppocr DEBUG: rec_res num  : 119, elapsed : 3.874502420425415\n", "[2024/10/08 05:10:12] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.1678156852722168\n", "[2024/10/08 05:10:13] ppocr DEBUG: cls num  : 61, elapsed : 0.14220404624938965\n", "[2024/10/08 05:10:15] ppocr DEBUG: rec_res num  : 61, elapsed : 2.2180614471435547\n", "[2024/10/08 05:10:15] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.1587083339691162\n", "[2024/10/08 05:10:15] ppocr DEBUG: cls num  : 40, elapsed : 0.0812985897064209\n", "[2024/10/08 05:10:17] ppocr DEBUG: rec_res num  : 40, elapsed : 1.753554105758667\n", "[2024/10/08 05:10:17] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.20133209228515625\n", "[2024/10/08 05:10:17] ppocr DEBUG: cls num  : 167, elapsed : 0.3299431800842285\n", "[2024/10/08 05:10:22] ppocr DEBUG: rec_res num  : 167, elapsed : 4.942395448684692\n", "[2024/10/08 05:10:23] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.18242144584655762\n", "[2024/10/08 05:10:23] ppocr DEBUG: cls num  : 115, elapsed : 0.23273324966430664\n", "[2024/10/08 05:10:26] ppocr DEBUG: rec_res num  : 115, elapsed : 3.4739420413970947\n", "[2024/10/08 05:10:26] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.2176673412322998\n", "[2024/10/08 05:10:27] ppocr DEBUG: cls num  : 232, elapsed : 0.42995762825012207\n", "[2024/10/08 05:10:34] ppocr DEBUG: rec_res num  : 232, elapsed : 6.847355604171753\n", "[2024/10/08 05:10:34] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.17712759971618652\n", "[2024/10/08 05:10:34] ppocr DEBUG: cls num  : 95, elapsed : 0.19301414489746094\n", "[2024/10/08 05:10:39] ppocr DEBUG: rec_res num  : 95, elapsed : 4.4015607833862305\n", "[2024/10/08 05:10:39] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.16815447807312012\n", "[2024/10/08 05:10:39] ppocr DEBUG: cls num  : 72, elapsed : 0.13157343864440918\n", "[2024/10/08 05:10:42] ppocr DEBUG: rec_res num  : 72, elapsed : 2.8634347915649414\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:10:42.644\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:10:42.652\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 37 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:10:42] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=37, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:10:45] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.25744152069091797\n", "[2024/10/08 05:10:45] ppocr DEBUG: cls num  : 51, elapsed : 0.14248919486999512\n", "[2024/10/08 05:10:50] ppocr DEBUG: rec_res num  : 51, elapsed : 4.793907403945923\n", "[2024/10/08 05:10:50] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.16930699348449707\n", "[2024/10/08 05:10:50] ppocr DEBUG: cls num  : 108, elapsed : 0.2082524299621582\n", "[2024/10/08 05:10:54] ppocr DEBUG: rec_res num  : 108, elapsed : 4.2434892654418945\n", "[2024/10/08 05:10:55] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.14790797233581543\n", "[2024/10/08 05:10:55] ppocr DEBUG: cls num  : 40, elapsed : 0.09732437133789062\n", "[2024/10/08 05:10:56] ppocr DEBUG: rec_res num  : 40, elapsed : 1.635025978088379\n", "[2024/10/08 05:10:56] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13582301139831543\n", "[2024/10/08 05:10:57] ppocr DEBUG: cls num  : 11, elapsed : 0.03892040252685547\n", "[2024/10/08 05:10:58] ppocr DEBUG: rec_res num  : 11, elapsed : 1.783576250076294\n", "[2024/10/08 05:10:58] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14974212646484375\n", "[2024/10/08 05:10:59] ppocr DEBUG: cls num  : 45, elapsed : 0.09343743324279785\n", "[2024/10/08 05:11:07] ppocr DEBUG: rec_res num  : 45, elapsed : 8.531914710998535\n", "[2024/10/08 05:11:07] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.14004802703857422\n", "[2024/10/08 05:11:07] ppocr DEBUG: cls num  : 11, elapsed : 0.0431210994720459\n", "[2024/10/08 05:11:09] ppocr DEBUG: rec_res num  : 11, elapsed : 1.8260102272033691\n", "[2024/10/08 05:11:09] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.13707995414733887\n", "[2024/10/08 05:11:09] ppocr DEBUG: cls num  : 6, elapsed : 0.011863946914672852\n", "[2024/10/08 05:11:10] ppocr DEBUG: rec_res num  : 6, elapsed : 0.8168277740478516\n", "[2024/10/08 05:11:10] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.16359567642211914\n", "[2024/10/08 05:11:10] ppocr DEBUG: cls num  : 45, elapsed : 0.09748387336730957\n", "[2024/10/08 05:11:17] ppocr DEBUG: rec_res num  : 45, elapsed : 6.294941663742065\n", "[2024/10/08 05:11:17] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13975214958190918\n", "[2024/10/08 05:11:17] ppocr DEBUG: cls num  : 21, elapsed : 0.04287362098693848\n", "[2024/10/08 05:11:21] ppocr DEBUG: rec_res num  : 21, elapsed : 3.9164087772369385\n", "[2024/10/08 05:11:21] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13707423210144043\n", "[2024/10/08 05:11:21] ppocr DEBUG: cls num  : 13, elapsed : 0.05011916160583496\n", "[2024/10/08 05:11:23] ppocr DEBUG: rec_res num  : 13, elapsed : 1.9721264839172363\n", "[2024/10/08 05:11:23] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.14465022087097168\n", "[2024/10/08 05:11:23] ppocr DEBUG: cls num  : 21, elapsed : 0.058655500411987305\n", "[2024/10/08 05:11:27] ppocr DEBUG: rec_res num  : 21, elapsed : 3.6015307903289795\n", "[2024/10/08 05:11:27] ppocr DEBUG: dt_boxes num : 0, elapsed : 0.27164149284362793\n", "[2024/10/08 05:11:27] ppocr DEBUG: cls num  : 0, elapsed : 0\n", "[2024/10/08 05:11:27] ppocr DEBUG: rec_res num  : 0, elapsed : 1.430511474609375e-06\n", "[2024/10/08 05:11:27] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.2706105709075928\n", "[2024/10/08 05:11:28] ppocr DEBUG: cls num  : 76, elapsed : 0.16161775588989258\n", "[2024/10/08 05:11:31] ppocr DEBUG: rec_res num  : 76, elapsed : 3.535330295562744\n", "[2024/10/08 05:11:31] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.1685347557067871\n", "[2024/10/08 05:11:31] ppocr DEBUG: cls num  : 56, elapsed : 0.12352538108825684\n", "[2024/10/08 05:11:35] ppocr DEBUG: rec_res num  : 56, elapsed : 3.184863328933716\n", "[2024/10/08 05:11:35] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.16986346244812012\n", "[2024/10/08 05:11:35] ppocr DEBUG: cls num  : 58, elapsed : 0.12719321250915527\n", "[2024/10/08 05:11:38] ppocr DEBUG: rec_res num  : 58, elapsed : 3.129547119140625\n", "[2024/10/08 05:11:38] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.1677999496459961\n", "[2024/10/08 05:11:38] ppocr DEBUG: cls num  : 55, elapsed : 0.12264776229858398\n", "[2024/10/08 05:11:41] ppocr DEBUG: rec_res num  : 55, elapsed : 3.081393241882324\n", "[2024/10/08 05:11:42] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.1650533676147461\n", "[2024/10/08 05:11:42] ppocr DEBUG: cls num  : 53, elapsed : 0.11607098579406738\n", "[2024/10/08 05:11:45] ppocr DEBUG: rec_res num  : 53, elapsed : 2.8883190155029297\n", "[2024/10/08 05:11:45] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.1683027744293213\n", "[2024/10/08 05:11:45] ppocr DEBUG: cls num  : 57, elapsed : 0.1363070011138916\n", "[2024/10/08 05:11:48] ppocr DEBUG: rec_res num  : 57, elapsed : 3.349334478378296\n", "[2024/10/08 05:11:48] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.17321205139160156\n", "[2024/10/08 05:11:49] ppocr DEBUG: cls num  : 55, elapsed : 0.12527990341186523\n", "[2024/10/08 05:11:52] ppocr DEBUG: rec_res num  : 55, elapsed : 3.520153045654297\n", "[2024/10/08 05:11:52] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.1676464080810547\n", "[2024/10/08 05:11:52] ppocr DEBUG: cls num  : 58, elapsed : 0.12865686416625977\n", "[2024/10/08 05:11:56] ppocr DEBUG: rec_res num  : 58, elapsed : 3.612091064453125\n", "[2024/10/08 05:11:56] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.16869783401489258\n", "[2024/10/08 05:11:56] ppocr DEBUG: cls num  : 55, elapsed : 0.12774896621704102\n", "[2024/10/08 05:12:00] ppocr DEBUG: rec_res num  : 55, elapsed : 3.321882963180542\n", "[2024/10/08 05:12:00] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.15401601791381836\n", "[2024/10/08 05:12:00] ppocr DEBUG: cls num  : 14, elapsed : 0.036403656005859375\n", "[2024/10/08 05:12:01] ppocr DEBUG: rec_res num  : 14, elapsed : 0.926011323928833\n", "[2024/10/08 05:12:01] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.16007685661315918\n", "[2024/10/08 05:12:01] ppocr DEBUG: cls num  : 39, elapsed : 0.09391927719116211\n", "[2024/10/08 05:12:03] ppocr DEBUG: rec_res num  : 39, elapsed : 1.5147569179534912\n", "[2024/10/08 05:12:03] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.17750024795532227\n", "[2024/10/08 05:12:03] ppocr DEBUG: cls num  : 98, elapsed : 0.18384528160095215\n", "[2024/10/08 05:12:06] ppocr DEBUG: rec_res num  : 98, elapsed : 3.369433641433716\n", "[2024/10/08 05:12:06] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.16230440139770508\n", "[2024/10/08 05:12:07] ppocr DEBUG: cls num  : 41, elapsed : 0.09731721878051758\n", "[2024/10/08 05:12:09] ppocr DEBUG: rec_res num  : 41, elapsed : 2.2010879516601562\n", "[2024/10/08 05:12:09] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.2098844051361084\n", "[2024/10/08 05:12:09] ppocr DEBUG: cls num  : 192, elapsed : 0.3483729362487793\n", "[2024/10/08 05:12:15] ppocr DEBUG: rec_res num  : 192, elapsed : 5.973447799682617\n", "[2024/10/08 05:12:15] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.1874086856842041\n", "[2024/10/08 05:12:16] ppocr DEBUG: cls num  : 119, elapsed : 0.23543763160705566\n", "[2024/10/08 05:12:19] ppocr DEBUG: rec_res num  : 119, elapsed : 3.7677903175354004\n", "[2024/10/08 05:12:20] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.16827178001403809\n", "[2024/10/08 05:12:20] ppocr DEBUG: cls num  : 61, elapsed : 0.13619446754455566\n", "[2024/10/08 05:12:22] ppocr DEBUG: rec_res num  : 61, elapsed : 2.059791088104248\n", "[2024/10/08 05:12:22] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.1625967025756836\n", "[2024/10/08 05:12:22] ppocr DEBUG: cls num  : 40, elapsed : 0.0959932804107666\n", "[2024/10/08 05:12:24] ppocr DEBUG: rec_res num  : 40, elapsed : 1.7135307788848877\n", "[2024/10/08 05:12:24] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.2014012336730957\n", "[2024/10/08 05:12:24] ppocr DEBUG: cls num  : 167, elapsed : 0.3242502212524414\n", "[2024/10/08 05:12:29] ppocr DEBUG: rec_res num  : 167, elapsed : 4.787588357925415\n", "[2024/10/08 05:12:29] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.1864466667175293\n", "[2024/10/08 05:12:30] ppocr DEBUG: cls num  : 115, elapsed : 0.23197698593139648\n", "[2024/10/08 05:12:33] ppocr DEBUG: rec_res num  : 115, elapsed : 3.4673826694488525\n", "[2024/10/08 05:12:33] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.226792573928833\n", "[2024/10/08 05:12:34] ppocr DEBUG: cls num  : 232, elapsed : 0.4434690475463867\n", "[2024/10/08 05:12:41] ppocr DEBUG: rec_res num  : 232, elapsed : 6.933307647705078\n", "[2024/10/08 05:12:41] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.18404507637023926\n", "[2024/10/08 05:12:41] ppocr DEBUG: cls num  : 95, elapsed : 0.19486427307128906\n", "[2024/10/08 05:12:46] ppocr DEBUG: rec_res num  : 95, elapsed : 4.4493324756622314\n", "[2024/10/08 05:12:46] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.17795419692993164\n", "[2024/10/08 05:12:46] ppocr DEBUG: cls num  : 72, elapsed : 0.1379551887512207\n", "[2024/10/08 05:12:49] ppocr DEBUG: rec_res num  : 72, elapsed : 2.9148783683776855\n", "[2024/10/08 05:12:49] ppocr DEBUG: dt_boxes num : 116, elapsed : 0.2337656021118164\n", "[2024/10/08 05:12:49] ppocr DEBUG: cls num  : 116, elapsed : 0.23752093315124512\n", "[2024/10/08 05:12:53] ppocr DEBUG: rec_res num  : 116, elapsed : 3.934967279434204\n", "[2024/10/08 05:12:54] ppocr DEBUG: dt_boxes num : 122, elapsed : 0.17374515533447266\n", "[2024/10/08 05:12:54] ppocr DEBUG: cls num  : 122, elapsed : 0.22847914695739746\n", "[2024/10/08 05:12:58] ppocr DEBUG: rec_res num  : 122, elapsed : 4.500578165054321\n", "[2024/10/08 05:12:58] ppocr DEBUG: dt_boxes num : 19, elapsed : 0.1394786834716797\n", "[2024/10/08 05:12:58] ppocr DEBUG: cls num  : 19, elapsed : 0.06074094772338867\n", "[2024/10/08 05:12:59] ppocr DEBUG: rec_res num  : 19, elapsed : 0.8219447135925293\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:13:00.280\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:13:00.286\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 11 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:13:00] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=11, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:13:02] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.2027730941772461\n", "[2024/10/08 05:13:02] ppocr DEBUG: cls num  : 51, elapsed : 0.13369107246398926\n", "[2024/10/08 05:13:07] ppocr DEBUG: rec_res num  : 51, elapsed : 5.030945777893066\n", "[2024/10/08 05:13:08] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.18325376510620117\n", "[2024/10/08 05:13:08] ppocr DEBUG: cls num  : 108, elapsed : 0.24405336380004883\n", "[2024/10/08 05:13:13] ppocr DEBUG: rec_res num  : 108, elapsed : 5.124405384063721\n", "[2024/10/08 05:13:13] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.1604604721069336\n", "[2024/10/08 05:13:13] ppocr DEBUG: cls num  : 40, elapsed : 0.107452392578125\n", "[2024/10/08 05:13:15] ppocr DEBUG: rec_res num  : 40, elapsed : 1.7453205585479736\n", "[2024/10/08 05:13:15] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.1441962718963623\n", "[2024/10/08 05:13:15] ppocr DEBUG: cls num  : 11, elapsed : 0.04183340072631836\n", "[2024/10/08 05:13:17] ppocr DEBUG: rec_res num  : 11, elapsed : 1.8690967559814453\n", "[2024/10/08 05:13:17] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.15126514434814453\n", "[2024/10/08 05:13:17] ppocr DEBUG: cls num  : 45, elapsed : 0.09767603874206543\n", "[2024/10/08 05:13:26] ppocr DEBUG: rec_res num  : 45, elapsed : 8.571313381195068\n", "[2024/10/08 05:13:26] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13977408409118652\n", "[2024/10/08 05:13:26] ppocr DEBUG: cls num  : 11, elapsed : 0.04349708557128906\n", "[2024/10/08 05:13:28] ppocr DEBUG: rec_res num  : 11, elapsed : 1.8330395221710205\n", "[2024/10/08 05:13:28] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.136091947555542\n", "[2024/10/08 05:13:28] ppocr DEBUG: cls num  : 6, elapsed : 0.012763738632202148\n", "[2024/10/08 05:13:29] ppocr DEBUG: rec_res num  : 6, elapsed : 0.810117244720459\n", "[2024/10/08 05:13:29] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14879655838012695\n", "[2024/10/08 05:13:29] ppocr DEBUG: cls num  : 45, elapsed : 0.09183287620544434\n", "[2024/10/08 05:13:36] ppocr DEBUG: rec_res num  : 45, elapsed : 6.444259166717529\n", "[2024/10/08 05:13:36] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.14040541648864746\n", "[2024/10/08 05:13:36] ppocr DEBUG: cls num  : 21, elapsed : 0.04482889175415039\n", "[2024/10/08 05:13:40] ppocr DEBUG: rec_res num  : 21, elapsed : 3.9433553218841553\n", "[2024/10/08 05:13:40] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.14020466804504395\n", "[2024/10/08 05:13:40] ppocr DEBUG: cls num  : 13, elapsed : 0.05037546157836914\n", "[2024/10/08 05:13:42] ppocr DEBUG: rec_res num  : 13, elapsed : 1.9650774002075195\n", "[2024/10/08 05:13:42] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13910174369812012\n", "[2024/10/08 05:13:42] ppocr DEBUG: cls num  : 21, elapsed : 0.05696463584899902\n", "[2024/10/08 05:13:46] ppocr DEBUG: rec_res num  : 21, elapsed : 3.6197354793548584\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:13:46.749\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:13:46.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:13:46] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:13:48] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.18917131423950195\n", "[2024/10/08 05:13:48] ppocr DEBUG: cls num  : 25, elapsed : 0.07441353797912598\n", "[2024/10/08 05:13:49] ppocr DEBUG: rec_res num  : 25, elapsed : 0.9252521991729736\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:13:50.307\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:13:50.309\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 4 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:13:50] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=4, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:13:52] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.1603531837463379\n", "[2024/10/08 05:13:52] ppocr DEBUG: cls num  : 51, elapsed : 0.1178138256072998\n", "[2024/10/08 05:13:57] ppocr DEBUG: rec_res num  : 51, elapsed : 4.495964527130127\n", "[2024/10/08 05:13:57] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.14106392860412598\n", "[2024/10/08 05:13:57] ppocr DEBUG: cls num  : 23, elapsed : 0.06266212463378906\n", "[2024/10/08 05:13:58] ppocr DEBUG: rec_res num  : 23, elapsed : 0.9277863502502441\n", "[2024/10/08 05:13:58] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.14056730270385742\n", "[2024/10/08 05:13:58] ppocr DEBUG: cls num  : 13, elapsed : 0.04806113243103027\n", "[2024/10/08 05:14:00] ppocr DEBUG: rec_res num  : 13, elapsed : 1.8178510665893555\n", "[2024/10/08 05:14:00] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.14447736740112305\n", "[2024/10/08 05:14:00] ppocr DEBUG: cls num  : 25, elapsed : 0.05530428886413574\n", "[2024/10/08 05:14:01] ppocr DEBUG: rec_res num  : 25, elapsed : 0.8840184211730957\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:14:01.742\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:14:01.744\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:14:01] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:14:03] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.1961197853088379\n", "[2024/10/08 05:14:04] ppocr DEBUG: cls num  : 51, elapsed : 0.1428234577178955\n", "[2024/10/08 05:14:08] ppocr DEBUG: rec_res num  : 51, elapsed : 4.5039496421813965\n", "[2024/10/08 05:14:08] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.1402575969696045\n", "[2024/10/08 05:14:08] ppocr DEBUG: cls num  : 23, elapsed : 0.06159067153930664\n", "[2024/10/08 05:14:09] ppocr DEBUG: rec_res num  : 23, elapsed : 0.938129186630249\n", "[2024/10/08 05:14:09] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13930034637451172\n", "[2024/10/08 05:14:09] ppocr DEBUG: cls num  : 13, elapsed : 0.04531741142272949\n", "[2024/10/08 05:14:11] ppocr DEBUG: rec_res num  : 13, elapsed : 1.8070883750915527\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:14:12.074\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:14:12.076\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:14:12] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:14:14] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.18884706497192383\n", "[2024/10/08 05:14:14] ppocr DEBUG: cls num  : 25, elapsed : 0.07258462905883789\n", "[2024/10/08 05:14:15] ppocr DEBUG: rec_res num  : 25, elapsed : 0.9110944271087646\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:14:15.585\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:14:15.587\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 4 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:14:15] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=4, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:14:17] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.15602707862854004\n", "[2024/10/08 05:14:17] ppocr DEBUG: cls num  : 51, elapsed : 0.12606072425842285\n", "[2024/10/08 05:14:22] ppocr DEBUG: rec_res num  : 51, elapsed : 4.502425909042358\n", "[2024/10/08 05:14:22] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.14251232147216797\n", "[2024/10/08 05:14:22] ppocr DEBUG: cls num  : 23, elapsed : 0.06294655799865723\n", "[2024/10/08 05:14:23] ppocr DEBUG: rec_res num  : 23, elapsed : 0.9532144069671631\n", "[2024/10/08 05:14:23] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.138838529586792\n", "[2024/10/08 05:14:23] ppocr DEBUG: cls num  : 13, elapsed : 0.04529881477355957\n", "[2024/10/08 05:14:25] ppocr DEBUG: rec_res num  : 13, elapsed : 1.7929675579071045\n", "[2024/10/08 05:14:25] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.1396634578704834\n", "[2024/10/08 05:14:25] ppocr DEBUG: cls num  : 25, elapsed : 0.05292797088623047\n", "[2024/10/08 05:14:26] ppocr DEBUG: rec_res num  : 25, elapsed : 0.8556926250457764\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:14:26.961\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:14:26.963\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:14:26] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:14:29] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.19777941703796387\n", "[2024/10/08 05:14:29] ppocr DEBUG: cls num  : 51, elapsed : 0.14152264595031738\n", "[2024/10/08 05:14:33] ppocr DEBUG: rec_res num  : 51, elapsed : 4.481452465057373\n", "[2024/10/08 05:14:33] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.141693115234375\n", "[2024/10/08 05:14:33] ppocr DEBUG: cls num  : 23, elapsed : 0.05995583534240723\n", "[2024/10/08 05:14:34] ppocr DEBUG: rec_res num  : 23, elapsed : 0.9383354187011719\n", "[2024/10/08 05:14:35] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13831233978271484\n", "[2024/10/08 05:14:35] ppocr DEBUG: cls num  : 13, elapsed : 0.0453333854675293\n", "[2024/10/08 05:14:36] ppocr DEBUG: rec_res num  : 13, elapsed : 1.7902309894561768\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:14:37.349\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:14:37.352\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 6 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:14:37] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=6, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:14:39] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.19968605041503906\n", "[2024/10/08 05:14:39] ppocr DEBUG: cls num  : 51, elapsed : 0.13642191886901855\n", "[2024/10/08 05:14:44] ppocr DEBUG: rec_res num  : 51, elapsed : 4.496072292327881\n", "[2024/10/08 05:14:44] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.14362430572509766\n", "[2024/10/08 05:14:44] ppocr DEBUG: cls num  : 29, elapsed : 0.07440471649169922\n", "[2024/10/08 05:14:45] ppocr DEBUG: rec_res num  : 29, elapsed : 1.095304012298584\n", "[2024/10/08 05:14:45] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.1426694393157959\n", "[2024/10/08 05:14:45] ppocr DEBUG: cls num  : 28, elapsed : 0.07207274436950684\n", "[2024/10/08 05:14:50] ppocr DEBUG: rec_res num  : 28, elapsed : 4.554884910583496\n", "[2024/10/08 05:14:50] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.16091132164001465\n", "[2024/10/08 05:14:50] ppocr DEBUG: cls num  : 87, elapsed : 0.1726698875427246\n", "[2024/10/08 05:14:54] ppocr DEBUG: rec_res num  : 87, elapsed : 3.5929412841796875\n", "[2024/10/08 05:14:54] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.1600325107574463\n", "[2024/10/08 05:14:54] ppocr DEBUG: cls num  : 87, elapsed : 0.16866159439086914\n", "[2024/10/08 05:14:58] ppocr DEBUG: rec_res num  : 87, elapsed : 3.5980536937713623\n", "[2024/10/08 05:14:58] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.14248347282409668\n", "[2024/10/08 05:14:58] ppocr DEBUG: cls num  : 33, elapsed : 0.06683063507080078\n", "[2024/10/08 05:14:59] ppocr DEBUG: rec_res num  : 33, elapsed : 1.2231502532958984\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:00.086\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:00.088\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:00] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:02] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.1977558135986328\n", "[2024/10/08 05:15:02] ppocr DEBUG: cls num  : 33, elapsed : 0.10492825508117676\n", "[2024/10/08 05:15:03] ppocr DEBUG: rec_res num  : 33, elapsed : 1.2028861045837402\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:03.954\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:03.956\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:03] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:06] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.15682506561279297\n", "[2024/10/08 05:15:06] ppocr DEBUG: cls num  : 51, elapsed : 0.11226344108581543\n", "[2024/10/08 05:15:10] ppocr DEBUG: rec_res num  : 51, elapsed : 4.504783630371094\n", "[2024/10/08 05:15:10] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.1457505226135254\n", "[2024/10/08 05:15:10] ppocr DEBUG: cls num  : 29, elapsed : 0.07767534255981445\n", "[2024/10/08 05:15:12] ppocr DEBUG: rec_res num  : 29, elapsed : 1.1468250751495361\n", "[2024/10/08 05:15:12] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.14583492279052734\n", "[2024/10/08 05:15:12] ppocr DEBUG: cls num  : 28, elapsed : 0.0748753547668457\n", "[2024/10/08 05:15:16] ppocr DEBUG: rec_res num  : 28, elapsed : 4.578533172607422\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:17.313\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:17.315\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:17] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:19] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.21225690841674805\n", "[2024/10/08 05:15:19] ppocr DEBUG: cls num  : 87, elapsed : 0.21748828887939453\n", "[2024/10/08 05:15:23] ppocr DEBUG: rec_res num  : 87, elapsed : 3.727614641189575\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:23.800\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:23.801\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:23] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:25] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.1691129207611084\n", "[2024/10/08 05:15:26] ppocr DEBUG: cls num  : 87, elapsed : 0.19211459159851074\n", "[2024/10/08 05:15:29] ppocr DEBUG: rec_res num  : 87, elapsed : 3.6985082626342773\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:30.215\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:30.218\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 6 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:30] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=6, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:32] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.15721774101257324\n", "[2024/10/08 05:15:32] ppocr DEBUG: cls num  : 51, elapsed : 0.1176602840423584\n", "[2024/10/08 05:15:37] ppocr DEBUG: rec_res num  : 51, elapsed : 4.528423309326172\n", "[2024/10/08 05:15:37] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.14485955238342285\n", "[2024/10/08 05:15:37] ppocr DEBUG: cls num  : 29, elapsed : 0.07708978652954102\n", "[2024/10/08 05:15:38] ppocr DEBUG: rec_res num  : 29, elapsed : 1.113816738128662\n", "[2024/10/08 05:15:38] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.14433026313781738\n", "[2024/10/08 05:15:38] ppocr DEBUG: cls num  : 28, elapsed : 0.07124781608581543\n", "[2024/10/08 05:15:43] ppocr DEBUG: rec_res num  : 28, elapsed : 4.5797107219696045\n", "[2024/10/08 05:15:43] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.16068601608276367\n", "[2024/10/08 05:15:43] ppocr DEBUG: cls num  : 87, elapsed : 0.17447423934936523\n", "[2024/10/08 05:15:47] ppocr DEBUG: rec_res num  : 87, elapsed : 3.594599485397339\n", "[2024/10/08 05:15:47] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.1597754955291748\n", "[2024/10/08 05:15:47] ppocr DEBUG: cls num  : 87, elapsed : 0.1711726188659668\n", "[2024/10/08 05:15:51] ppocr DEBUG: rec_res num  : 87, elapsed : 3.6062862873077393\n", "[2024/10/08 05:15:51] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.14433503150939941\n", "[2024/10/08 05:15:51] ppocr DEBUG: cls num  : 33, elapsed : 0.06625199317932129\n", "[2024/10/08 05:15:52] ppocr DEBUG: rec_res num  : 33, elapsed : 1.2121880054473877\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:52.953\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:52.954\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:52] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:55] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.1973438262939453\n", "[2024/10/08 05:15:55] ppocr DEBUG: cls num  : 33, elapsed : 0.11119651794433594\n", "[2024/10/08 05:15:56] ppocr DEBUG: rec_res num  : 33, elapsed : 1.214674711227417\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:15:56.745\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:15:56.747\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:15:56] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:15:58] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.15726017951965332\n", "[2024/10/08 05:15:58] ppocr DEBUG: cls num  : 51, elapsed : 0.1183023452758789\n", "[2024/10/08 05:16:03] ppocr DEBUG: rec_res num  : 51, elapsed : 4.50139045715332\n", "[2024/10/08 05:16:03] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.14250969886779785\n", "[2024/10/08 05:16:03] ppocr DEBUG: cls num  : 29, elapsed : 0.07833003997802734\n", "[2024/10/08 05:16:04] ppocr DEBUG: rec_res num  : 29, elapsed : 1.1128630638122559\n", "[2024/10/08 05:16:04] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.1434767246246338\n", "[2024/10/08 05:16:05] ppocr DEBUG: cls num  : 28, elapsed : 0.0743095874786377\n", "[2024/10/08 05:16:09] ppocr DEBUG: rec_res num  : 28, elapsed : 4.599325895309448\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:16:10.057\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:16:10.059\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:16:10] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:16:12] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.212693452835083\n", "[2024/10/08 05:16:12] ppocr DEBUG: cls num  : 87, elapsed : 0.2156522274017334\n", "[2024/10/08 05:16:16] ppocr DEBUG: rec_res num  : 87, elapsed : 3.6899020671844482\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 05:16:16.469\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mblob_download\u001b[0m:\u001b[36m28\u001b[0m - \u001b[1mfile downloaded to ../data/tmp.pdf\u001b[0m\n", "\u001b[32m2024-10-08 05:16:16.471\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 05:16:16] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 05:16:18] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.16674518585205078\n", "[2024/10/08 05:16:18] ppocr DEBUG: cls num  : 87, elapsed : 0.18893694877624512\n", "[2024/10/08 05:16:22] ppocr DEBUG: rec_res num  : 87, elapsed : 3.7210755348205566\n"]}], "source": ["from fitz import FileDataError\n", "\n", "CLAIM_NO_COL = \"unique_identifier\"\n", "CONTAINER_NAME_COL = \"DocContainer\"\n", "FILE_NAME_COL = \"DocFile\"\n", "DEST_PATH = \"../data/tmp.pdf\"\n", "\n", "num_res = len(ans)\n", "for i, (claim_no, container_name, file_name) in enumerate(zip(df[CLAIM_NO_COL], df[CONTAINER_NAME_COL], df[FILE_NAME_COL])):\n", "\n", "    if i < num_res:\n", "        continue\n", "    try:\n", "        blob_download(container_name = container_name, file_name = file_name, dest_path = DEST_PATH, override=True)\n", "\n", "        consultation_note = extract_consultation_notes(DEST_PATH)\n", "        ans.append({\"claim_no\": claim_no, \"notes\": consultation_note})\n", "    except FileDataError as e:\n", "        ans.append({\"claim_no\": claim_no, \"notes\": \"\"})\n", "        logger.exception(e)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["OUPUT_PATH = \"BIRI-9577_sample_data_res.csv\"\n", "pd.DataFrame.from_records(ans).to_csv(OUPUT_PATH, index=False)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["86"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ans)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OCR from Downloaded Files"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "tmp_file_paths = sorted(glob.glob(\"../data/tmp_*.pdf\"))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["['../data/tmp_1728358794590598391.pdf',\n", " '../data/tmp_1728359299651144298.pdf',\n", " '../data/tmp_1728359332772430795.pdf',\n", " '../data/tmp_1728359357866615603.pdf',\n", " '../data/tmp_1728359360017098653.pdf',\n", " '../data/tmp_1728359802577778653.pdf',\n", " '../data/tmp_1728359810265835963.pdf',\n", " '../data/tmp_1728359816259676277.pdf',\n", " '../data/tmp_1728359821004391841.pdf',\n", " '../data/tmp_1728359824497426297.pdf',\n", " '../data/tmp_1728359827875457555.pdf',\n", " '../data/tmp_1728360275998309428.pdf',\n", " '../data/tmp_1728360279052637451.pdf',\n", " '../data/tmp_1728360728120006267.pdf',\n", " '../data/tmp_1728360731616237755.pdf',\n", " '../data/tmp_1728360745568210309.pdf',\n", " '../data/tmp_1728360758187188715.pdf',\n", " '../data/tmp_1728360777381055806.pdf',\n", " '../data/tmp_1728360781096689802.pdf',\n", " '../data/tmp_1728360798413197554.pdf',\n", " '../data/tmp_1728360811577189849.pdf',\n", " '../data/tmp_1728360816162944897.pdf',\n", " '../data/tmp_1728360836959981392.pdf',\n", " '../data/tmp_1728360842283307909.pdf',\n", " '../data/tmp_1728360846266896431.pdf',\n", " '../data/tmp_1728361068174702947.pdf',\n", " '../data/tmp_1728361283869843610.pdf',\n", " '../data/tmp_1728361288621470598.pdf',\n", " '../data/tmp_1728361292941674973.pdf',\n", " '../data/tmp_1728361295524991749.pdf',\n", " '../data/tmp_1728361299503367995.pdf',\n", " '../data/tmp_1728361303353265905.pdf',\n", " '../data/tmp_1728361330038973326.pdf',\n", " '../data/tmp_1728361366615518989.pdf',\n", " '../data/tmp_1728361374198111744.pdf',\n", " '../data/tmp_1728361383864253631.pdf',\n", " '../data/tmp_1728361456976142804.pdf',\n", " '../data/tmp_1728361461370688130.pdf',\n", " '../data/tmp_1728361465694732187.pdf',\n", " '../data/tmp_1728361471822130504.pdf',\n", " '../data/tmp_1728361518557612666.pdf',\n", " '../data/tmp_1728361528632511290.pdf',\n", " '../data/tmp_1728361533599153485.pdf',\n", " '../data/tmp_1728361548311574399.pdf',\n", " '../data/tmp_1728361560920590629.pdf',\n", " '../data/tmp_1728361565383007384.pdf',\n", " '../data/tmp_1728361580001980963.pdf',\n", " '../data/tmp_1728361592473576505.pdf',\n", " '../data/tmp_1728361596949873233.pdf',\n", " '../data/tmp_1728361610321005329.pdf',\n", " '../data/tmp_1728361614151305363.pdf',\n", " '../data/tmp_1728361626325266131.pdf',\n", " '../data/tmp_1728361631753363190.pdf',\n", " '../data/tmp_1728361646495149250.pdf',\n", " '../data/tmp_1728361658295442704.pdf',\n", " '../data/tmp_1728361669097749111.pdf',\n", " '../data/tmp_1728361673391345026.pdf',\n", " '../data/tmp_1728361686318879462.pdf',\n", " '../data/tmp_1728361691318168993.pdf',\n", " '../data/tmp_1728361706671013978.pdf',\n", " '../data/tmp_1728361719766970303.pdf',\n", " '../data/tmp_1728363788809218978.pdf',\n", " '../data/tmp_1728363789023089552.pdf',\n", " '../data/tmp_1728363897479381212.pdf',\n", " '../data/tmp_1728364070522910129.pdf',\n", " '../data/tmp_1728364128077205417.pdf',\n", " '../data/tmp_1728364144675626646.pdf',\n", " '../data/tmp_1728364144772847933.pdf',\n", " '../data/tmp_1728364242645582554.pdf',\n", " '../data/tmp_1728364380285098292.pdf',\n", " '../data/tmp_1728364426751104374.pdf',\n", " '../data/tmp_1728364430308329861.pdf',\n", " '../data/tmp_1728364441743275500.pdf',\n", " '../data/tmp_1728364452075121300.pdf',\n", " '../data/tmp_1728364455586199399.pdf',\n", " '../data/tmp_1728364466962156195.pdf',\n", " '../data/tmp_1728364477350767671.pdf',\n", " '../data/tmp_1728364500087572467.pdf',\n", " '../data/tmp_1728364503955259097.pdf',\n", " '../data/tmp_1728364517314206481.pdf',\n", " '../data/tmp_1728364523800907644.pdf',\n", " '../data/tmp_1728364530216738827.pdf',\n", " '../data/tmp_1728364552953775126.pdf',\n", " '../data/tmp_1728364556746380284.pdf',\n", " '../data/tmp_1728364570058616358.pdf',\n", " '../data/tmp_1728364576470284886.pdf']"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["tmp_file_paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ans = []"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:49:57.322\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728363897479381212.pdf has 37 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:49:57] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=37, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:49:59] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.1990654468536377\n", "[2024/10/08 23:49:59] ppocr DEBUG: cls num  : 51, elapsed : 0.10676813125610352\n", "[2024/10/08 23:50:04] ppocr DEBUG: rec_res num  : 51, elapsed : 4.667799472808838\n", "[2024/10/08 23:50:04] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.1620347499847412\n", "[2024/10/08 23:50:04] ppocr DEBUG: cls num  : 108, elapsed : 0.18353819847106934\n", "[2024/10/08 23:50:08] ppocr DEBUG: rec_res num  : 108, elapsed : 4.086017370223999\n", "[2024/10/08 23:50:09] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.14037466049194336\n", "[2024/10/08 23:50:09] ppocr DEBUG: cls num  : 40, elapsed : 0.08808279037475586\n", "[2024/10/08 23:50:10] ppocr DEBUG: rec_res num  : 40, elapsed : 1.569810390472412\n", "[2024/10/08 23:50:10] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.1301724910736084\n", "[2024/10/08 23:50:10] ppocr DEBUG: cls num  : 11, elapsed : 0.03537893295288086\n", "[2024/10/08 23:50:12] ppocr DEBUG: rec_res num  : 11, elapsed : 1.738516092300415\n", "[2024/10/08 23:50:12] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14202451705932617\n", "[2024/10/08 23:50:12] ppocr DEBUG: cls num  : 45, elapsed : 0.08302497863769531\n", "[2024/10/08 23:50:21] ppocr DEBUG: rec_res num  : 45, elapsed : 8.299002885818481\n", "[2024/10/08 23:50:21] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.1324937343597412\n", "[2024/10/08 23:50:21] ppocr DEBUG: cls num  : 11, elapsed : 0.039749860763549805\n", "[2024/10/08 23:50:23] ppocr DEBUG: rec_res num  : 11, elapsed : 1.7654893398284912\n", "[2024/10/08 23:50:23] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.12791848182678223\n", "[2024/10/08 23:50:23] ppocr DEBUG: cls num  : 6, elapsed : 0.010713577270507812\n", "[2024/10/08 23:50:24] ppocr DEBUG: rec_res num  : 6, elapsed : 0.775766134262085\n", "[2024/10/08 23:50:24] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14424633979797363\n", "[2024/10/08 23:50:24] ppocr DEBUG: cls num  : 45, elapsed : 0.08451724052429199\n", "[2024/10/08 23:50:30] ppocr DEBUG: rec_res num  : 45, elapsed : 6.102142572402954\n", "[2024/10/08 23:50:30] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13609552383422852\n", "[2024/10/08 23:50:30] ppocr DEBUG: cls num  : 21, elapsed : 0.039276123046875\n", "[2024/10/08 23:50:34] ppocr DEBUG: rec_res num  : 21, elapsed : 3.8019449710845947\n", "[2024/10/08 23:50:34] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13129401206970215\n", "[2024/10/08 23:50:34] ppocr DEBUG: cls num  : 13, elapsed : 0.043961524963378906\n", "[2024/10/08 23:50:36] ppocr DEBUG: rec_res num  : 13, elapsed : 1.8857696056365967\n", "[2024/10/08 23:50:36] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13327693939208984\n", "[2024/10/08 23:50:36] ppocr DEBUG: cls num  : 21, elapsed : 0.051894187927246094\n", "[2024/10/08 23:50:40] ppocr DEBUG: rec_res num  : 21, elapsed : 3.512302875518799\n", "[2024/10/08 23:50:40] ppocr DEBUG: dt_boxes num : 0, elapsed : 0.2656285762786865\n", "[2024/10/08 23:50:40] ppocr DEBUG: cls num  : 0, elapsed : 0\n", "[2024/10/08 23:50:40] ppocr DEBUG: rec_res num  : 0, elapsed : 1.430511474609375e-06\n", "[2024/10/08 23:50:40] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.2610177993774414\n", "[2024/10/08 23:50:40] ppocr DEBUG: cls num  : 76, elapsed : 0.1467294692993164\n", "[2024/10/08 23:50:44] ppocr DEBUG: rec_res num  : 76, elapsed : 3.3877220153808594\n", "[2024/10/08 23:50:44] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.16480135917663574\n", "[2024/10/08 23:50:44] ppocr DEBUG: cls num  : 56, elapsed : 0.11495280265808105\n", "[2024/10/08 23:50:47] ppocr DEBUG: rec_res num  : 56, elapsed : 3.1557486057281494\n", "[2024/10/08 23:50:47] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.16391587257385254\n", "[2024/10/08 23:50:47] ppocr DEBUG: cls num  : 58, elapsed : 0.11445331573486328\n", "[2024/10/08 23:50:51] ppocr DEBUG: rec_res num  : 58, elapsed : 3.0791683197021484\n", "[2024/10/08 23:50:51] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.1638929843902588\n", "[2024/10/08 23:50:51] ppocr DEBUG: cls num  : 55, elapsed : 0.11351680755615234\n", "[2024/10/08 23:50:54] ppocr DEBUG: rec_res num  : 55, elapsed : 3.0709280967712402\n", "[2024/10/08 23:50:54] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.16019845008850098\n", "[2024/10/08 23:50:54] ppocr DEBUG: cls num  : 53, elapsed : 0.10654544830322266\n", "[2024/10/08 23:50:57] ppocr DEBUG: rec_res num  : 53, elapsed : 2.8677000999450684\n", "[2024/10/08 23:50:57] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.16709518432617188\n", "[2024/10/08 23:50:57] ppocr DEBUG: cls num  : 57, elapsed : 0.1334538459777832\n", "[2024/10/08 23:51:01] ppocr DEBUG: rec_res num  : 57, elapsed : 3.2838473320007324\n", "[2024/10/08 23:51:01] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.16389036178588867\n", "[2024/10/08 23:51:01] ppocr DEBUG: cls num  : 55, elapsed : 0.11543941497802734\n", "[2024/10/08 23:51:04] ppocr DEBUG: rec_res num  : 55, elapsed : 3.375149965286255\n", "[2024/10/08 23:51:05] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.16093778610229492\n", "[2024/10/08 23:51:05] ppocr DEBUG: cls num  : 58, elapsed : 0.11701297760009766\n", "[2024/10/08 23:51:08] ppocr DEBUG: rec_res num  : 58, elapsed : 3.504974603652954\n", "[2024/10/08 23:51:08] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.1614069938659668\n", "[2024/10/08 23:51:08] ppocr DEBUG: cls num  : 55, elapsed : 0.12223148345947266\n", "[2024/10/08 23:51:12] ppocr DEBUG: rec_res num  : 55, elapsed : 3.1830878257751465\n", "[2024/10/08 23:51:12] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.15001726150512695\n", "[2024/10/08 23:51:12] ppocr DEBUG: cls num  : 14, elapsed : 0.03623795509338379\n", "[2024/10/08 23:51:13] ppocr DEBUG: rec_res num  : 14, elapsed : 0.8901987075805664\n", "[2024/10/08 23:51:13] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.15781736373901367\n", "[2024/10/08 23:51:13] ppocr DEBUG: cls num  : 39, elapsed : 0.09115839004516602\n", "[2024/10/08 23:51:14] ppocr DEBUG: rec_res num  : 39, elapsed : 1.495147705078125\n", "[2024/10/08 23:51:15] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.17797303199768066\n", "[2024/10/08 23:51:15] ppocr DEBUG: cls num  : 98, elapsed : 0.1808488368988037\n", "[2024/10/08 23:51:18] ppocr DEBUG: rec_res num  : 98, elapsed : 3.3158204555511475\n", "[2024/10/08 23:51:18] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.1575031280517578\n", "[2024/10/08 23:51:18] ppocr DEBUG: cls num  : 41, elapsed : 0.09882259368896484\n", "[2024/10/08 23:51:21] ppocr DEBUG: rec_res num  : 41, elapsed : 2.1859230995178223\n", "[2024/10/08 23:51:21] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.209244966506958\n", "[2024/10/08 23:51:21] ppocr DEBUG: cls num  : 192, elapsed : 0.33056116104125977\n", "[2024/10/08 23:51:27] ppocr DEBUG: rec_res num  : 192, elapsed : 5.986372470855713\n", "[2024/10/08 23:51:27] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.18210268020629883\n", "[2024/10/08 23:51:28] ppocr DEBUG: cls num  : 119, elapsed : 0.2323448657989502\n", "[2024/10/08 23:51:31] ppocr DEBUG: rec_res num  : 119, elapsed : 3.7208797931671143\n", "[2024/10/08 23:51:31] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.16245818138122559\n", "[2024/10/08 23:51:32] ppocr DEBUG: cls num  : 61, elapsed : 0.13271474838256836\n", "[2024/10/08 23:51:34] ppocr DEBUG: rec_res num  : 61, elapsed : 2.1557374000549316\n", "[2024/10/08 23:51:34] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.15770721435546875\n", "[2024/10/08 23:51:34] ppocr DEBUG: cls num  : 40, elapsed : 0.09002685546875\n", "[2024/10/08 23:51:36] ppocr DEBUG: rec_res num  : 40, elapsed : 1.7415544986724854\n", "[2024/10/08 23:51:36] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.204115629196167\n", "[2024/10/08 23:51:36] ppocr DEBUG: cls num  : 167, elapsed : 0.3247342109680176\n", "[2024/10/08 23:51:41] ppocr DEBUG: rec_res num  : 167, elapsed : 5.011733531951904\n", "[2024/10/08 23:51:42] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.18340349197387695\n", "[2024/10/08 23:51:42] ppocr DEBUG: cls num  : 115, elapsed : 0.2317659854888916\n", "[2024/10/08 23:51:45] ppocr DEBUG: rec_res num  : 115, elapsed : 3.3307716846466064\n", "[2024/10/08 23:51:45] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.21175360679626465\n", "[2024/10/08 23:51:46] ppocr DEBUG: cls num  : 232, elapsed : 0.4147765636444092\n", "[2024/10/08 23:51:53] ppocr DEBUG: rec_res num  : 232, elapsed : 6.837332010269165\n", "[2024/10/08 23:51:53] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.17580342292785645\n", "[2024/10/08 23:51:53] ppocr DEBUG: cls num  : 95, elapsed : 0.1897141933441162\n", "[2024/10/08 23:51:57] ppocr DEBUG: rec_res num  : 95, elapsed : 4.340620040893555\n", "[2024/10/08 23:51:57] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.16650915145874023\n", "[2024/10/08 23:51:58] ppocr DEBUG: cls num  : 72, elapsed : 0.12651467323303223\n", "[2024/10/08 23:52:00] ppocr DEBUG: rec_res num  : 72, elapsed : 2.8159215450286865\n", "[2024/10/08 23:52:01] ppocr DEBUG: dt_boxes num : 116, elapsed : 0.21876907348632812\n", "[2024/10/08 23:52:01] ppocr DEBUG: cls num  : 116, elapsed : 0.21208643913269043\n", "[2024/10/08 23:52:05] ppocr DEBUG: rec_res num  : 116, elapsed : 3.7958180904388428\n", "[2024/10/08 23:52:05] ppocr DEBUG: dt_boxes num : 122, elapsed : 0.16419506072998047\n", "[2024/10/08 23:52:05] ppocr DEBUG: cls num  : 122, elapsed : 0.2196650505065918\n", "[2024/10/08 23:52:09] ppocr DEBUG: rec_res num  : 122, elapsed : 3.8918590545654297\n", "[2024/10/08 23:52:09] ppocr DEBUG: dt_boxes num : 19, elapsed : 0.13802337646484375\n", "[2024/10/08 23:52:09] ppocr DEBUG: cls num  : 19, elapsed : 0.05641579627990723\n", "[2024/10/08 23:52:10] ppocr DEBUG: rec_res num  : 19, elapsed : 0.6783261299133301\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:52:10.336\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m35\u001b[0m - \u001b[31m\u001b[1m'NoneType' object is not iterable\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7ecd6e8353f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7ecd6c22dab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7ecd6c07d7e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7ecd6d91c310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7ecd6d91de10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7ecd6dafd7e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7ecd6c1e8280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'c2507a73-fef'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'86c710e3e9ce'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7ec2fefdb4c0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 8, 23, 49, 57, 314000, tzinfo=tzutc()), 'msg_id': '5cdc92f5-f616-4da9-a94f-03...\n", "                                  │       └ [b'c2507a73-fef5-4a16-a9fa-3097e82c4297']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7ecd6c26a710>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7ec28aac84a0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7ecd6c2170a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('\\nfor tmp_file_path in tmp_file_paths[len(ans):]:\\n    try:\\n        tmp_res = extract_consultation_notes(tmp_file_path)\\n ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7ecd6cd65ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "             └ <function _pseudo_sync_runner at 0x7ecd6cd517e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_891/166541857.py'\n", "                       │    │             │        └ [<ast.For object at 0x7ec3045421d0>]\n", "                       │    │             └ <ast.Module object at 0x7ec304542320>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7ecd6cd66170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7ec304541270, execution_count=21 error_before_exec=None error_in_exec=None info=<ExecutionInfo obj...\n", "             │    │        └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7ecd6cd66200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         │         │    └ <property object at 0x7ecd6cd588b0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m166541857.py\u001b[0m\", line \u001b[33m3\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mtmp_res\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mextract_consultation_notes\u001b[0m\u001b[1m(\u001b[0m\u001b[1mtmp_file_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m          │                          └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp_1728363897479381212.pdf'\u001b[0m\n", "    \u001b[36m          └ \u001b[0m\u001b[36m\u001b[1m<function extract_consultation_notes at 0x7ec2ff049c60>\u001b[0m\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m578645121.py\u001b[0m\", line \u001b[33m32\u001b[0m, in \u001b[35mextract_consultation_notes\u001b[0m\n", "    \u001b[35m\u001b[1mfor\u001b[0m \u001b[1mline\u001b[0m \u001b[35m\u001b[1min\u001b[0m \u001b[1mresult\u001b[0m\u001b[1m[\u001b[0m\u001b[1midx\u001b[0m\u001b[1m]\u001b[0m\u001b[1m:\u001b[0m\n", "    \u001b[36m    │       │      └ \u001b[0m\u001b[36m\u001b[1m11\u001b[0m\n", "    \u001b[36m    │       └ \u001b[0m\u001b[36m\u001b[1m[[[[[140.0, 142.0], [1016.0, 142.0], [1016.0, 165.0], [140.0, 165.0]], ('I hereby agree that:1. All material submitted or tha...\u001b[0m\n", "    \u001b[36m    └ \u001b[0m\u001b[36m\u001b[1m[[[140.0, 686.0], [649.0, 686.0], [649.0, 709.0], [140.0, 709.0]], ('Small Animal SurgeryNSvsC Artarmon Vital Signs.', 0.9578...\u001b[0m\n", "\n", "\u001b[31m\u001b[1mTypeError\u001b[0m:\u001b[1m 'NoneType' object is not iterable\u001b[0m\n", "\u001b[32m2024-10-08 23:52:10.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364070522910129.pdf has 11 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:52:10] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=11, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:52:12] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.19333982467651367\n", "[2024/10/08 23:52:12] ppocr DEBUG: cls num  : 51, elapsed : 0.11333441734313965\n", "[2024/10/08 23:52:17] ppocr DEBUG: rec_res num  : 51, elapsed : 4.598994016647339\n", "[2024/10/08 23:52:17] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.1591341495513916\n", "[2024/10/08 23:52:17] ppocr DEBUG: cls num  : 108, elapsed : 0.18553853034973145\n", "[2024/10/08 23:52:21] ppocr DEBUG: rec_res num  : 108, elapsed : 4.114053726196289\n", "[2024/10/08 23:52:21] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.13899779319763184\n", "[2024/10/08 23:52:21] ppocr DEBUG: cls num  : 40, elapsed : 0.08965396881103516\n", "[2024/10/08 23:52:23] ppocr DEBUG: rec_res num  : 40, elapsed : 1.5468852519989014\n", "[2024/10/08 23:52:23] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13130903244018555\n", "[2024/10/08 23:52:23] ppocr DEBUG: cls num  : 11, elapsed : 0.0348663330078125\n", "[2024/10/08 23:52:25] ppocr DEBUG: rec_res num  : 11, elapsed : 1.7204177379608154\n", "[2024/10/08 23:52:25] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.1411738395690918\n", "[2024/10/08 23:52:25] ppocr DEBUG: cls num  : 45, elapsed : 0.08303093910217285\n", "[2024/10/08 23:52:33] ppocr DEBUG: rec_res num  : 45, elapsed : 8.264180421829224\n", "[2024/10/08 23:52:34] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13282179832458496\n", "[2024/10/08 23:52:34] ppocr DEBUG: cls num  : 11, elapsed : 0.039281368255615234\n", "[2024/10/08 23:52:35] ppocr DEBUG: rec_res num  : 11, elapsed : 1.749474048614502\n", "[2024/10/08 23:52:35] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.12915730476379395\n", "[2024/10/08 23:52:35] ppocr DEBUG: cls num  : 6, elapsed : 0.011260747909545898\n", "[2024/10/08 23:52:36] ppocr DEBUG: rec_res num  : 6, elapsed : 0.7700583934783936\n", "[2024/10/08 23:52:36] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14212369918823242\n", "[2024/10/08 23:52:36] ppocr DEBUG: cls num  : 45, elapsed : 0.08416128158569336\n", "[2024/10/08 23:52:43] ppocr DEBUG: rec_res num  : 45, elapsed : 6.073179483413696\n", "[2024/10/08 23:52:43] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.1311349868774414\n", "[2024/10/08 23:52:43] ppocr DEBUG: cls num  : 21, elapsed : 0.03761649131774902\n", "[2024/10/08 23:52:47] ppocr DEBUG: rec_res num  : 21, elapsed : 3.8167526721954346\n", "[2024/10/08 23:52:47] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13061022758483887\n", "[2024/10/08 23:52:47] ppocr DEBUG: cls num  : 13, elapsed : 0.04363894462585449\n", "[2024/10/08 23:52:49] ppocr DEBUG: rec_res num  : 13, elapsed : 1.8795828819274902\n", "[2024/10/08 23:52:49] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.1376025676727295\n", "[2024/10/08 23:52:49] ppocr DEBUG: cls num  : 21, elapsed : 0.05343031883239746\n", "[2024/10/08 23:52:52] ppocr DEBUG: rec_res num  : 21, elapsed : 3.4669740200042725\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:52:52.971\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364128077205417.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:52:53] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:52:54] ppocr DEBUG: dt_boxes num : 116, elapsed : 0.21033048629760742\n", "[2024/10/08 23:52:55] ppocr DEBUG: cls num  : 116, elapsed : 0.2366340160369873\n", "[2024/10/08 23:52:58] ppocr DEBUG: rec_res num  : 116, elapsed : 3.7188241481781006\n", "[2024/10/08 23:52:58] ppocr DEBUG: dt_boxes num : 122, elapsed : 0.1631908416748047\n", "[2024/10/08 23:52:59] ppocr DEBUG: cls num  : 122, elapsed : 0.21110820770263672\n", "[2024/10/08 23:53:02] ppocr DEBUG: rec_res num  : 122, elapsed : 3.771371364593506\n", "[2024/10/08 23:53:03] ppocr DEBUG: dt_boxes num : 19, elapsed : 0.13308334350585938\n", "[2024/10/08 23:53:03] ppocr DEBUG: cls num  : 19, elapsed : 0.05413413047790527\n", "[2024/10/08 23:53:03] ppocr DEBUG: rec_res num  : 19, elapsed : 0.6622087955474854\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:53:03.953\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m5\u001b[0m - \u001b[31m\u001b[1m../data/tmp_1728364144675626646.pdf\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7ecd6e8353f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7ecd6c22dab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7ecd6c07d7e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7ecd6d91c310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7ecd6d91de10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7ecd6dafd7e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7ecd6c1e8280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'c2507a73-fef'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'86c710e3e9ce'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7ec2fefdb4c0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 8, 23, 49, 57, 314000, tzinfo=tzutc()), 'msg_id': '5cdc92f5-f616-4da9-a94f-03...\n", "                                  │       └ [b'c2507a73-fef5-4a16-a9fa-3097e82c4297']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7ecd6c26a710>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7ec28aac84a0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7ecd6c2170a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('\\nfor tmp_file_path in tmp_file_paths[len(ans):]:\\n    try:\\n        tmp_res = extract_consultation_notes(tmp_file_path)\\n ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7ecd6cd65ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "             └ <function _pseudo_sync_runner at 0x7ecd6cd517e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_891/166541857.py'\n", "                       │    │             │        └ [<ast.For object at 0x7ec3045421d0>]\n", "                       │    │             └ <ast.Module object at 0x7ec304542320>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7ecd6cd66170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7ec304541270, execution_count=21 error_before_exec=None error_in_exec=None info=<ExecutionInfo obj...\n", "             │    │        └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7ecd6cd66200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         │         │    └ <property object at 0x7ecd6cd588b0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m166541857.py\u001b[0m\", line \u001b[33m3\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mtmp_res\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mextract_consultation_notes\u001b[0m\u001b[1m(\u001b[0m\u001b[1mtmp_file_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m          │                          └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp_1728364144675626646.pdf'\u001b[0m\n", "    \u001b[36m          └ \u001b[0m\u001b[36m\u001b[1m<function extract_consultation_notes at 0x7ec2ff049c60>\u001b[0m\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m578645121.py\u001b[0m\", line \u001b[33m11\u001b[0m, in \u001b[35mextract_consultation_notes\u001b[0m\n", "    \u001b[1mpdf_document\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mfitz\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mopen\u001b[0m\u001b[1m(\u001b[0m\u001b[1mfile_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m               │    │    └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp_1728364144675626646.pdf'\u001b[0m\n", "    \u001b[36m               │    └ \u001b[0m\u001b[36m\u001b[1m<class 'fitz.fitz.Document'>\u001b[0m\n", "    \u001b[36m               └ \u001b[0m\u001b[36m\u001b[1m<module 'fitz' from '/usr/local/lib/python3.10/site-packages/fitz/__init__.py'>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/fitz/fitz.py\", line 3876, in __init__\n", "    _fitz.Document_swiginit(self, _fitz.new_Document(filename, stream, filetype, rect, width, height, fontsize))\n", "    │     │                 │     │     │            │         │       │         │     │      │       └ 11\n", "    │     │                 │     │     │            │         │       │         │     │      └ 0\n", "    │     │                 │     │     │            │         │       │         │     └ 0\n", "    │     │                 │     │     │            │         │       │         └ None\n", "    │     │                 │     │     │            │         │       └ None\n", "    │     │                 │     │     │            │         └ None\n", "    │     │                 │     │     │            └ '../data/tmp_1728364144675626646.pdf'\n", "    │     │                 │     │     └ <built-in function new_Document>\n", "    │     │                 │     └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "    │     │                 └ Document('../data/tmp_1728364144675626646.pdf')\n", "    │     └ <built-in function Document_swiginit>\n", "    └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "\n", "\u001b[31m\u001b[1mfitz.fitz.FileDataError\u001b[0m:\u001b[1m cannot open broken document\u001b[0m\n", "\u001b[32m2024-10-08 23:53:04.011\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[31m\u001b[1mcannot open broken document\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7ecd6e8353f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7ecd6c22dab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7ecd6c07d7e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7ecd6d91c310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7ecd6d91de10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7ecd6dafd7e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7ecd6c1e8280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'c2507a73-fef'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'86c710e3e9ce'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7ec2fefdb4c0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 8, 23, 49, 57, 314000, tzinfo=tzutc()), 'msg_id': '5cdc92f5-f616-4da9-a94f-03...\n", "                                  │       └ [b'c2507a73-fef5-4a16-a9fa-3097e82c4297']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7ecd6c26a710>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7ec28aac84a0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7ecd6c2170a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('\\nfor tmp_file_path in tmp_file_paths[len(ans):]:\\n    try:\\n        tmp_res = extract_consultation_notes(tmp_file_path)\\n ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7ecd6cd65ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "             └ <function _pseudo_sync_runner at 0x7ecd6cd517e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_891/166541857.py'\n", "                       │    │             │        └ [<ast.For object at 0x7ec3045421d0>]\n", "                       │    │             └ <ast.Module object at 0x7ec304542320>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7ecd6cd66170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7ec304541270, execution_count=21 error_before_exec=None error_in_exec=None info=<ExecutionInfo obj...\n", "             │    │        └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7ecd6cd66200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         │         │    └ <property object at 0x7ecd6cd588b0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m166541857.py\u001b[0m\", line \u001b[33m3\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mtmp_res\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mextract_consultation_notes\u001b[0m\u001b[1m(\u001b[0m\u001b[1mtmp_file_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m          │                          └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp_1728364144675626646.pdf'\u001b[0m\n", "    \u001b[36m          └ \u001b[0m\u001b[36m\u001b[1m<function extract_consultation_notes at 0x7ec2ff049c60>\u001b[0m\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m578645121.py\u001b[0m\", line \u001b[33m11\u001b[0m, in \u001b[35mextract_consultation_notes\u001b[0m\n", "    \u001b[1mpdf_document\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mfitz\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mopen\u001b[0m\u001b[1m(\u001b[0m\u001b[1mfile_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m               │    │    └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp_1728364144675626646.pdf'\u001b[0m\n", "    \u001b[36m               │    └ \u001b[0m\u001b[36m\u001b[1m<class 'fitz.fitz.Document'>\u001b[0m\n", "    \u001b[36m               └ \u001b[0m\u001b[36m\u001b[1m<module 'fitz' from '/usr/local/lib/python3.10/site-packages/fitz/__init__.py'>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/fitz/fitz.py\", line 3876, in __init__\n", "    _fitz.Document_swiginit(self, _fitz.new_Document(filename, stream, filetype, rect, width, height, fontsize))\n", "    │     │                 │     │     │            │         │       │         │     │      │       └ 11\n", "    │     │                 │     │     │            │         │       │         │     │      └ 0\n", "    │     │                 │     │     │            │         │       │         │     └ 0\n", "    │     │                 │     │     │            │         │       │         └ None\n", "    │     │                 │     │     │            │         │       └ None\n", "    │     │                 │     │     │            │         └ None\n", "    │     │                 │     │     │            └ '../data/tmp_1728364144675626646.pdf'\n", "    │     │                 │     │     └ <built-in function new_Document>\n", "    │     │                 │     └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "    │     │                 └ Document('../data/tmp_1728364144675626646.pdf')\n", "    │     └ <built-in function Document_swiginit>\n", "    └ <module 'fitz._fitz' from '/usr/local/lib/python3.10/site-packages/fitz/_fitz.cpython-310-x86_64-linux-gnu.so'>\n", "\n", "\u001b[31m\u001b[1mfitz.fitz.FileDataError\u001b[0m:\u001b[1m cannot open broken document\u001b[0m\n", "\u001b[32m2024-10-08 23:53:04.025\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364144772847933.pdf has 22 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:53:04] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=22, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:53:06] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.27042698860168457\n", "[2024/10/08 23:53:06] ppocr DEBUG: cls num  : 76, elapsed : 0.1697394847869873\n", "[2024/10/08 23:53:09] ppocr DEBUG: rec_res num  : 76, elapsed : 3.323993682861328\n", "[2024/10/08 23:53:09] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.1555163860321045\n", "[2024/10/08 23:53:10] ppocr DEBUG: cls num  : 56, elapsed : 0.11455154418945312\n", "[2024/10/08 23:53:13] ppocr DEBUG: rec_res num  : 56, elapsed : 3.05114483833313\n", "[2024/10/08 23:53:13] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.15849995613098145\n", "[2024/10/08 23:53:13] ppocr DEBUG: cls num  : 58, elapsed : 0.1050410270690918\n", "[2024/10/08 23:53:16] ppocr DEBUG: rec_res num  : 58, elapsed : 3.0116982460021973\n", "[2024/10/08 23:53:16] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.15569710731506348\n", "[2024/10/08 23:53:16] ppocr DEBUG: cls num  : 55, elapsed : 0.11669158935546875\n", "[2024/10/08 23:53:19] ppocr DEBUG: rec_res num  : 55, elapsed : 2.970120429992676\n", "[2024/10/08 23:53:19] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.1549835205078125\n", "[2024/10/08 23:53:19] ppocr DEBUG: cls num  : 53, elapsed : 0.10531210899353027\n", "[2024/10/08 23:53:22] ppocr DEBUG: rec_res num  : 53, elapsed : 2.787976026535034\n", "[2024/10/08 23:53:22] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.1571352481842041\n", "[2024/10/08 23:53:22] ppocr DEBUG: cls num  : 57, elapsed : 0.1147148609161377\n", "[2024/10/08 23:53:26] ppocr DEBUG: rec_res num  : 57, elapsed : 3.1705050468444824\n", "[2024/10/08 23:53:26] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.15835905075073242\n", "[2024/10/08 23:53:26] ppocr DEBUG: cls num  : 55, elapsed : 0.11101865768432617\n", "[2024/10/08 23:53:29] ppocr DEBUG: rec_res num  : 55, elapsed : 3.3693346977233887\n", "[2024/10/08 23:53:29] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.15638113021850586\n", "[2024/10/08 23:53:30] ppocr DEBUG: cls num  : 58, elapsed : 0.10117816925048828\n", "[2024/10/08 23:53:33] ppocr DEBUG: rec_res num  : 58, elapsed : 3.4571523666381836\n", "[2024/10/08 23:53:33] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.15447449684143066\n", "[2024/10/08 23:53:33] ppocr DEBUG: cls num  : 55, elapsed : 0.11388254165649414\n", "[2024/10/08 23:53:36] ppocr DEBUG: rec_res num  : 55, elapsed : 3.1242125034332275\n", "[2024/10/08 23:53:37] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.14403748512268066\n", "[2024/10/08 23:53:37] ppocr DEBUG: cls num  : 14, elapsed : 0.04202985763549805\n", "[2024/10/08 23:53:37] ppocr DEBUG: rec_res num  : 14, elapsed : 0.8945963382720947\n", "[2024/10/08 23:53:38] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.1509406566619873\n", "[2024/10/08 23:53:38] ppocr DEBUG: cls num  : 39, elapsed : 0.08397102355957031\n", "[2024/10/08 23:53:39] ppocr DEBUG: rec_res num  : 39, elapsed : 1.4867000579833984\n", "[2024/10/08 23:53:39] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.16596698760986328\n", "[2024/10/08 23:53:40] ppocr DEBUG: cls num  : 98, elapsed : 0.17083740234375\n", "[2024/10/08 23:53:43] ppocr DEBUG: rec_res num  : 98, elapsed : 3.2537262439727783\n", "[2024/10/08 23:53:43] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.15103626251220703\n", "[2024/10/08 23:53:43] ppocr DEBUG: cls num  : 41, elapsed : 0.08876371383666992\n", "[2024/10/08 23:53:45] ppocr DEBUG: rec_res num  : 41, elapsed : 2.1450767517089844\n", "[2024/10/08 23:53:45] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.197066068649292\n", "[2024/10/08 23:53:46] ppocr DEBUG: cls num  : 192, elapsed : 0.3183720111846924\n", "[2024/10/08 23:53:52] ppocr DEBUG: rec_res num  : 192, elapsed : 5.82011079788208\n", "[2024/10/08 23:53:52] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.1744699478149414\n", "[2024/10/08 23:53:52] ppocr DEBUG: cls num  : 119, elapsed : 0.21704864501953125\n", "[2024/10/08 23:53:56] ppocr DEBUG: rec_res num  : 119, elapsed : 3.692387580871582\n", "[2024/10/08 23:53:56] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.15517377853393555\n", "[2024/10/08 23:53:56] ppocr DEBUG: cls num  : 61, elapsed : 0.12168240547180176\n", "[2024/10/08 23:53:58] ppocr DEBUG: rec_res num  : 61, elapsed : 2.018117666244507\n", "[2024/10/08 23:53:58] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.15148472785949707\n", "[2024/10/08 23:53:58] ppocr DEBUG: cls num  : 40, elapsed : 0.07310891151428223\n", "[2024/10/08 23:54:00] ppocr DEBUG: rec_res num  : 40, elapsed : 1.6621861457824707\n", "[2024/10/08 23:54:00] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.1880028247833252\n", "[2024/10/08 23:54:01] ppocr DEBUG: cls num  : 167, elapsed : 0.29179811477661133\n", "[2024/10/08 23:54:05] ppocr DEBUG: rec_res num  : 167, elapsed : 4.666409730911255\n", "[2024/10/08 23:54:05] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.17302346229553223\n", "[2024/10/08 23:54:06] ppocr DEBUG: cls num  : 115, elapsed : 0.21790051460266113\n", "[2024/10/08 23:54:09] ppocr DEBUG: rec_res num  : 115, elapsed : 3.2238919734954834\n", "[2024/10/08 23:54:09] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.20868921279907227\n", "[2024/10/08 23:54:09] ppocr DEBUG: cls num  : 232, elapsed : 0.38861870765686035\n", "[2024/10/08 23:54:16] ppocr DEBUG: rec_res num  : 232, elapsed : 6.563305139541626\n", "[2024/10/08 23:54:16] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.1716315746307373\n", "[2024/10/08 23:54:16] ppocr DEBUG: cls num  : 95, elapsed : 0.17539048194885254\n", "[2024/10/08 23:54:21] ppocr DEBUG: rec_res num  : 95, elapsed : 4.265773057937622\n", "[2024/10/08 23:54:21] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.1612703800201416\n", "[2024/10/08 23:54:21] ppocr DEBUG: cls num  : 72, elapsed : 0.12252950668334961\n", "[2024/10/08 23:54:24] ppocr DEBUG: rec_res num  : 72, elapsed : 2.7762303352355957\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:54:24.392\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364242645582554.pdf has 37 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:54:24] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=37, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:54:26] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.2516341209411621\n", "[2024/10/08 23:54:27] ppocr DEBUG: cls num  : 51, elapsed : 0.14031076431274414\n", "[2024/10/08 23:54:31] ppocr DEBUG: rec_res num  : 51, elapsed : 4.678151607513428\n", "[2024/10/08 23:54:31] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.16065263748168945\n", "[2024/10/08 23:54:32] ppocr DEBUG: cls num  : 108, elapsed : 0.19332432746887207\n", "[2024/10/08 23:54:36] ppocr DEBUG: rec_res num  : 108, elapsed : 4.123235702514648\n", "[2024/10/08 23:54:36] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.13998126983642578\n", "[2024/10/08 23:54:36] ppocr DEBUG: cls num  : 40, elapsed : 0.09012174606323242\n", "[2024/10/08 23:54:37] ppocr DEBUG: rec_res num  : 40, elapsed : 1.5603721141815186\n", "[2024/10/08 23:54:38] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13117671012878418\n", "[2024/10/08 23:54:38] ppocr DEBUG: cls num  : 11, elapsed : 0.035599708557128906\n", "[2024/10/08 23:54:39] ppocr DEBUG: rec_res num  : 11, elapsed : 1.71376633644104\n", "[2024/10/08 23:54:40] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14321184158325195\n", "[2024/10/08 23:54:40] ppocr DEBUG: cls num  : 45, elapsed : 0.08309340476989746\n", "[2024/10/08 23:54:48] ppocr DEBUG: rec_res num  : 45, elapsed : 8.220159769058228\n", "[2024/10/08 23:54:48] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.1314983367919922\n", "[2024/10/08 23:54:48] ppocr DEBUG: cls num  : 11, elapsed : 0.03912663459777832\n", "[2024/10/08 23:54:50] ppocr DEBUG: rec_res num  : 11, elapsed : 1.7504582405090332\n", "[2024/10/08 23:54:50] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.13224101066589355\n", "[2024/10/08 23:54:50] ppocr DEBUG: cls num  : 6, elapsed : 0.011052131652832031\n", "[2024/10/08 23:54:51] ppocr DEBUG: rec_res num  : 6, elapsed : 0.7707712650299072\n", "[2024/10/08 23:54:51] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14441776275634766\n", "[2024/10/08 23:54:51] ppocr DEBUG: cls num  : 45, elapsed : 0.08338475227355957\n", "[2024/10/08 23:54:57] ppocr DEBUG: rec_res num  : 45, elapsed : 6.028468370437622\n", "[2024/10/08 23:54:57] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13617324829101562\n", "[2024/10/08 23:54:57] ppocr DEBUG: cls num  : 21, elapsed : 0.038254737854003906\n", "[2024/10/08 23:55:01] ppocr DEBUG: rec_res num  : 21, elapsed : 3.77224063873291\n", "[2024/10/08 23:55:01] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13392353057861328\n", "[2024/10/08 23:55:01] ppocr DEBUG: cls num  : 13, elapsed : 0.0457301139831543\n", "[2024/10/08 23:55:03] ppocr DEBUG: rec_res num  : 13, elapsed : 1.8781917095184326\n", "[2024/10/08 23:55:03] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13789677619934082\n", "[2024/10/08 23:55:03] ppocr DEBUG: cls num  : 21, elapsed : 0.05393409729003906\n", "[2024/10/08 23:55:07] ppocr DEBUG: rec_res num  : 21, elapsed : 3.486095666885376\n", "[2024/10/08 23:55:07] ppocr DEBUG: dt_boxes num : 0, elapsed : 0.2679018974304199\n", "[2024/10/08 23:55:07] ppocr DEBUG: cls num  : 0, elapsed : 0\n", "[2024/10/08 23:55:07] ppocr DEBUG: rec_res num  : 0, elapsed : 1.430511474609375e-06\n", "[2024/10/08 23:55:07] ppocr DEBUG: dt_boxes num : 76, elapsed : 0.2584800720214844\n", "[2024/10/08 23:55:07] ppocr DEBUG: cls num  : 76, elapsed : 0.1462702751159668\n", "[2024/10/08 23:55:11] ppocr DEBUG: rec_res num  : 76, elapsed : 3.37247896194458\n", "[2024/10/08 23:55:11] ppocr DEBUG: dt_boxes num : 56, elapsed : 0.16167712211608887\n", "[2024/10/08 23:55:11] ppocr DEBUG: cls num  : 56, elapsed : 0.11211109161376953\n", "[2024/10/08 23:55:14] ppocr DEBUG: rec_res num  : 56, elapsed : 3.1174912452697754\n", "[2024/10/08 23:55:14] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.16642308235168457\n", "[2024/10/08 23:55:14] ppocr DEBUG: cls num  : 58, elapsed : 0.11788034439086914\n", "[2024/10/08 23:55:18] ppocr DEBUG: rec_res num  : 58, elapsed : 3.091993808746338\n", "[2024/10/08 23:55:18] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.16266202926635742\n", "[2024/10/08 23:55:18] ppocr DEBUG: cls num  : 55, elapsed : 0.11203885078430176\n", "[2024/10/08 23:55:21] ppocr DEBUG: rec_res num  : 55, elapsed : 3.083692789077759\n", "[2024/10/08 23:55:21] ppocr DEBUG: dt_boxes num : 53, elapsed : 0.1666707992553711\n", "[2024/10/08 23:55:21] ppocr DEBUG: cls num  : 53, elapsed : 0.12324953079223633\n", "[2024/10/08 23:55:24] ppocr DEBUG: rec_res num  : 53, elapsed : 2.880852222442627\n", "[2024/10/08 23:55:24] ppocr DEBUG: dt_boxes num : 57, elapsed : 0.16304302215576172\n", "[2024/10/08 23:55:24] ppocr DEBUG: cls num  : 57, elapsed : 0.1262807846069336\n", "[2024/10/08 23:55:28] ppocr DEBUG: rec_res num  : 57, elapsed : 3.280068874359131\n", "[2024/10/08 23:55:28] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.16360139846801758\n", "[2024/10/08 23:55:28] ppocr DEBUG: cls num  : 55, elapsed : 0.1213676929473877\n", "[2024/10/08 23:55:31] ppocr DEBUG: rec_res num  : 55, elapsed : 3.4618613719940186\n", "[2024/10/08 23:55:32] ppocr DEBUG: dt_boxes num : 58, elapsed : 0.16691160202026367\n", "[2024/10/08 23:55:32] ppocr DEBUG: cls num  : 58, elapsed : 0.11795401573181152\n", "[2024/10/08 23:55:35] ppocr DEBUG: rec_res num  : 58, elapsed : 3.480457067489624\n", "[2024/10/08 23:55:35] ppocr DEBUG: dt_boxes num : 55, elapsed : 0.1619880199432373\n", "[2024/10/08 23:55:36] ppocr DEBUG: cls num  : 55, elapsed : 0.11452603340148926\n", "[2024/10/08 23:55:39] ppocr DEBUG: rec_res num  : 55, elapsed : 3.229523181915283\n", "[2024/10/08 23:55:39] ppocr DEBUG: dt_boxes num : 14, elapsed : 0.15025734901428223\n", "[2024/10/08 23:55:39] ppocr DEBUG: cls num  : 14, elapsed : 0.033606767654418945\n", "[2024/10/08 23:55:40] ppocr DEBUG: rec_res num  : 14, elapsed : 0.8908119201660156\n", "[2024/10/08 23:55:40] ppocr DEBUG: dt_boxes num : 39, elapsed : 0.1566321849822998\n", "[2024/10/08 23:55:40] ppocr DEBUG: cls num  : 39, elapsed : 0.08504581451416016\n", "[2024/10/08 23:55:42] ppocr DEBUG: rec_res num  : 39, elapsed : 1.4858546257019043\n", "[2024/10/08 23:55:42] ppocr DEBUG: dt_boxes num : 98, elapsed : 0.17228317260742188\n", "[2024/10/08 23:55:42] ppocr DEBUG: cls num  : 98, elapsed : 0.1667191982269287\n", "[2024/10/08 23:55:45] ppocr DEBUG: rec_res num  : 98, elapsed : 3.2401816844940186\n", "[2024/10/08 23:55:45] ppocr DEBUG: dt_boxes num : 41, elapsed : 0.1574084758758545\n", "[2024/10/08 23:55:45] ppocr DEBUG: cls num  : 41, elapsed : 0.0906984806060791\n", "[2024/10/08 23:55:48] ppocr DEBUG: rec_res num  : 41, elapsed : 2.1607184410095215\n", "[2024/10/08 23:55:48] ppocr DEBUG: dt_boxes num : 192, elapsed : 0.2043921947479248\n", "[2024/10/08 23:55:48] ppocr DEBUG: cls num  : 192, elapsed : 0.3187596797943115\n", "[2024/10/08 23:55:54] ppocr DEBUG: rec_res num  : 192, elapsed : 5.7826011180877686\n", "[2024/10/08 23:55:54] ppocr DEBUG: dt_boxes num : 119, elapsed : 0.1803896427154541\n", "[2024/10/08 23:55:54] ppocr DEBUG: cls num  : 119, elapsed : 0.21654510498046875\n", "[2024/10/08 23:55:58] ppocr DEBUG: rec_res num  : 119, elapsed : 3.651834726333618\n", "[2024/10/08 23:55:58] ppocr DEBUG: dt_boxes num : 61, elapsed : 0.1626908779144287\n", "[2024/10/08 23:55:58] ppocr DEBUG: cls num  : 61, elapsed : 0.12474179267883301\n", "[2024/10/08 23:56:00] ppocr DEBUG: rec_res num  : 61, elapsed : 2.057892084121704\n", "[2024/10/08 23:56:01] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.15906977653503418\n", "[2024/10/08 23:56:01] ppocr DEBUG: cls num  : 40, elapsed : 0.09295940399169922\n", "[2024/10/08 23:56:02] ppocr DEBUG: rec_res num  : 40, elapsed : 1.6652123928070068\n", "[2024/10/08 23:56:02] ppocr DEBUG: dt_boxes num : 167, elapsed : 0.19761323928833008\n", "[2024/10/08 23:56:03] ppocr DEBUG: cls num  : 167, elapsed : 0.29388952255249023\n", "[2024/10/08 23:56:08] ppocr DEBUG: rec_res num  : 167, elapsed : 4.752637624740601\n", "[2024/10/08 23:56:08] ppocr DEBUG: dt_boxes num : 115, elapsed : 0.18048357963562012\n", "[2024/10/08 23:56:08] ppocr DEBUG: cls num  : 115, elapsed : 0.21124649047851562\n", "[2024/10/08 23:56:11] ppocr DEBUG: rec_res num  : 115, elapsed : 3.2984087467193604\n", "[2024/10/08 23:56:11] ppocr DEBUG: dt_boxes num : 232, elapsed : 0.2194967269897461\n", "[2024/10/08 23:56:12] ppocr DEBUG: cls num  : 232, elapsed : 0.4042038917541504\n", "[2024/10/08 23:56:18] ppocr DEBUG: rec_res num  : 232, elapsed : 6.5857954025268555\n", "[2024/10/08 23:56:19] ppocr DEBUG: dt_boxes num : 95, elapsed : 0.1718888282775879\n", "[2024/10/08 23:56:19] ppocr DEBUG: cls num  : 95, elapsed : 0.17990875244140625\n", "[2024/10/08 23:56:23] ppocr DEBUG: rec_res num  : 95, elapsed : 4.290677547454834\n", "[2024/10/08 23:56:23] ppocr DEBUG: dt_boxes num : 72, elapsed : 0.16612768173217773\n", "[2024/10/08 23:56:23] ppocr DEBUG: cls num  : 72, elapsed : 0.12277936935424805\n", "[2024/10/08 23:56:26] ppocr DEBUG: rec_res num  : 72, elapsed : 2.8182923793792725\n", "[2024/10/08 23:56:26] ppocr DEBUG: dt_boxes num : 116, elapsed : 0.22327518463134766\n", "[2024/10/08 23:56:27] ppocr DEBUG: cls num  : 116, elapsed : 0.20201921463012695\n", "[2024/10/08 23:56:30] ppocr DEBUG: rec_res num  : 116, elapsed : 3.7252004146575928\n", "[2024/10/08 23:56:31] ppocr DEBUG: dt_boxes num : 122, elapsed : 0.16444110870361328\n", "[2024/10/08 23:56:31] ppocr DEBUG: cls num  : 122, elapsed : 0.20838236808776855\n", "[2024/10/08 23:56:35] ppocr DEBUG: rec_res num  : 122, elapsed : 3.861790657043457\n", "[2024/10/08 23:56:35] ppocr DEBUG: dt_boxes num : 19, elapsed : 0.13341522216796875\n", "[2024/10/08 23:56:35] ppocr DEBUG: cls num  : 19, elapsed : 0.05336713790893555\n", "[2024/10/08 23:56:36] ppocr DEBUG: rec_res num  : 19, elapsed : 0.6900858879089355\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:56:36.065\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m35\u001b[0m - \u001b[31m\u001b[1m'NoneType' object is not iterable\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7ecd6e8353f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7ecd6e9fd000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7ecd6c22dab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7ecd6c07d7e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7ecd6e964dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7ecd6d91c310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7ecd6c26a9e0>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7ecd6d91de10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7ecd6dafd7e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...0B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7ecd6c1e8280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'c2507a73-fef'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'86c710e3e9ce'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7ecd6c26afb0>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7ec2fefdb4c0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 8, 23, 49, 57, 314000, tzinfo=tzutc()), 'msg_id': '5cdc92f5-f616-4da9-a94f-03...\n", "                                  │       └ [b'c2507a73-fef5-4a16-a9fa-3097e82c4297']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7ecd6c26a710>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7ec28aac84a0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7ecd6c2170a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('\\nfor tmp_file_path in tmp_file_paths[len(ans):]:\\n    try:\\n        tmp_res = extract_consultation_notes(tmp_file_path)\\n ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7ecd6cd65ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "             └ <function _pseudo_sync_runner at 0x7ecd6cd517e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7ec28aac8270>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_891/166541857.py'\n", "                       │    │             │        └ [<ast.For object at 0x7ec3045421d0>]\n", "                       │    │             └ <ast.Module object at 0x7ec304542320>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7ecd6cd66170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7ec304541270, execution_count=21 error_before_exec=None error_in_exec=None info=<ExecutionInfo obj...\n", "             │    │        └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7ecd6cd66200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         │         │    └ <property object at 0x7ecd6cd588b0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7ecd69968100>\n", "         └ <code object <module> at 0x7ecbd414e6b0, file \"/tmp/ipykernel_891/166541857.py\", line 1>\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m166541857.py\u001b[0m\", line \u001b[33m3\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mtmp_res\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mextract_consultation_notes\u001b[0m\u001b[1m(\u001b[0m\u001b[1mtmp_file_path\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m          │                          └ \u001b[0m\u001b[36m\u001b[1m'../data/tmp_1728364242645582554.pdf'\u001b[0m\n", "    \u001b[36m          └ \u001b[0m\u001b[36m\u001b[1m<function extract_consultation_notes at 0x7ec2ff049c60>\u001b[0m\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_891/\u001b[0m\u001b[32m\u001b[1m578645121.py\u001b[0m\", line \u001b[33m32\u001b[0m, in \u001b[35mextract_consultation_notes\u001b[0m\n", "    \u001b[35m\u001b[1mfor\u001b[0m \u001b[1mline\u001b[0m \u001b[35m\u001b[1min\u001b[0m \u001b[1mresult\u001b[0m\u001b[1m[\u001b[0m\u001b[1midx\u001b[0m\u001b[1m]\u001b[0m\u001b[1m:\u001b[0m\n", "    \u001b[36m    │       │      └ \u001b[0m\u001b[36m\u001b[1m11\u001b[0m\n", "    \u001b[36m    │       └ \u001b[0m\u001b[36m\u001b[1m[[[[[140.0, 142.0], [1016.0, 142.0], [1016.0, 165.0], [140.0, 165.0]], ('I hereby agree that:1. All material submitted or tha...\u001b[0m\n", "    \u001b[36m    └ \u001b[0m\u001b[36m\u001b[1m[[[140.0, 686.0], [649.0, 686.0], [649.0, 709.0], [140.0, 709.0]], ('Small Animal SurgeryNSvsC Artarmon Vital Signs.', 0.9578...\u001b[0m\n", "\n", "\u001b[31m\u001b[1mTypeError\u001b[0m:\u001b[1m 'NoneType' object is not iterable\u001b[0m\n", "\u001b[32m2024-10-08 23:56:36.090\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364380285098292.pdf has 11 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:56:36] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=11, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:56:38] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.19471001625061035\n", "[2024/10/08 23:56:38] ppocr DEBUG: cls num  : 51, elapsed : 0.11118936538696289\n", "[2024/10/08 23:56:43] ppocr DEBUG: rec_res num  : 51, elapsed : 4.631837368011475\n", "[2024/10/08 23:56:43] ppocr DEBUG: dt_boxes num : 108, elapsed : 0.16597700119018555\n", "[2024/10/08 23:56:43] ppocr DEBUG: cls num  : 108, elapsed : 0.18486642837524414\n", "[2024/10/08 23:56:47] ppocr DEBUG: rec_res num  : 108, elapsed : 4.094536304473877\n", "[2024/10/08 23:56:47] ppocr DEBUG: dt_boxes num : 40, elapsed : 0.1423187255859375\n", "[2024/10/08 23:56:47] ppocr DEBUG: cls num  : 40, elapsed : 0.09236836433410645\n", "[2024/10/08 23:56:49] ppocr DEBUG: rec_res num  : 40, elapsed : 1.578521490097046\n", "[2024/10/08 23:56:49] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13122296333312988\n", "[2024/10/08 23:56:49] ppocr DEBUG: cls num  : 11, elapsed : 0.03526473045349121\n", "[2024/10/08 23:56:51] ppocr DEBUG: rec_res num  : 11, elapsed : 1.7259361743927002\n", "[2024/10/08 23:56:51] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.14305973052978516\n", "[2024/10/08 23:56:51] ppocr DEBUG: cls num  : 45, elapsed : 0.08465862274169922\n", "[2024/10/08 23:56:59] ppocr DEBUG: rec_res num  : 45, elapsed : 8.23648476600647\n", "[2024/10/08 23:56:59] ppocr DEBUG: dt_boxes num : 11, elapsed : 0.13587689399719238\n", "[2024/10/08 23:56:59] ppocr DEBUG: cls num  : 11, elapsed : 0.03819990158081055\n", "[2024/10/08 23:57:01] ppocr DEBUG: rec_res num  : 11, elapsed : 1.752833604812622\n", "[2024/10/08 23:57:01] ppocr DEBUG: dt_boxes num : 6, elapsed : 0.13438010215759277\n", "[2024/10/08 23:57:01] ppocr DEBUG: cls num  : 6, elapsed : 0.010581254959106445\n", "[2024/10/08 23:57:02] ppocr DEBUG: rec_res num  : 6, elapsed : 0.7708988189697266\n", "[2024/10/08 23:57:02] ppocr DEBUG: dt_boxes num : 45, elapsed : 0.15004467964172363\n", "[2024/10/08 23:57:02] ppocr DEBUG: cls num  : 45, elapsed : 0.08934187889099121\n", "[2024/10/08 23:57:08] ppocr DEBUG: rec_res num  : 45, elapsed : 6.11859130859375\n", "[2024/10/08 23:57:09] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13446831703186035\n", "[2024/10/08 23:57:09] ppocr DEBUG: cls num  : 21, elapsed : 0.03917074203491211\n", "[2024/10/08 23:57:12] ppocr DEBUG: rec_res num  : 21, elapsed : 3.801344156265259\n", "[2024/10/08 23:57:13] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13232803344726562\n", "[2024/10/08 23:57:13] ppocr DEBUG: cls num  : 13, elapsed : 0.043357133865356445\n", "[2024/10/08 23:57:15] ppocr DEBUG: rec_res num  : 13, elapsed : 1.8710553646087646\n", "[2024/10/08 23:57:15] ppocr DEBUG: dt_boxes num : 21, elapsed : 0.13577675819396973\n", "[2024/10/08 23:57:15] ppocr DEBUG: cls num  : 21, elapsed : 0.05332159996032715\n", "[2024/10/08 23:57:18] ppocr DEBUG: rec_res num  : 21, elapsed : 3.4863364696502686\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:57:18.927\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364426751104374.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:57:18] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:57:20] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.18223142623901367\n", "[2024/10/08 23:57:20] ppocr DEBUG: cls num  : 25, elapsed : 0.06728315353393555\n", "[2024/10/08 23:57:21] ppocr DEBUG: rec_res num  : 25, elapsed : 0.8767201900482178\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:57:21.952\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364430308329861.pdf has 4 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:57:22] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=4, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:57:23] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.15151667594909668\n", "[2024/10/08 23:57:23] ppocr DEBUG: cls num  : 51, elapsed : 0.09989714622497559\n", "[2024/10/08 23:57:28] ppocr DEBUG: rec_res num  : 51, elapsed : 4.368621587753296\n", "[2024/10/08 23:57:28] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.14105010032653809\n", "[2024/10/08 23:57:28] ppocr DEBUG: cls num  : 23, elapsed : 0.05885195732116699\n", "[2024/10/08 23:57:29] ppocr DEBUG: rec_res num  : 23, elapsed : 0.9188137054443359\n", "[2024/10/08 23:57:29] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13468456268310547\n", "[2024/10/08 23:57:29] ppocr DEBUG: cls num  : 13, elapsed : 0.04138994216918945\n", "[2024/10/08 23:57:31] ppocr DEBUG: rec_res num  : 13, elapsed : 1.728917121887207\n", "[2024/10/08 23:57:31] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.13372421264648438\n", "[2024/10/08 23:57:31] ppocr DEBUG: cls num  : 25, elapsed : 0.04841351509094238\n", "[2024/10/08 23:57:32] ppocr DEBUG: rec_res num  : 25, elapsed : 0.8363645076751709\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:57:32.516\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364441743275500.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:57:32] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:57:34] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.191986083984375\n", "[2024/10/08 23:57:34] ppocr DEBUG: cls num  : 51, elapsed : 0.12493634223937988\n", "[2024/10/08 23:57:38] ppocr DEBUG: rec_res num  : 51, elapsed : 4.355214834213257\n", "[2024/10/08 23:57:39] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.1360187530517578\n", "[2024/10/08 23:57:39] ppocr DEBUG: cls num  : 23, elapsed : 0.0561223030090332\n", "[2024/10/08 23:57:39] ppocr DEBUG: rec_res num  : 23, elapsed : 0.8980731964111328\n", "[2024/10/08 23:57:40] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.1366586685180664\n", "[2024/10/08 23:57:40] ppocr DEBUG: cls num  : 13, elapsed : 0.041426658630371094\n", "[2024/10/08 23:57:41] ppocr DEBUG: rec_res num  : 13, elapsed : 1.7385241985321045\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:57:42.125\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364452075121300.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:57:42] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:57:43] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.1829538345336914\n", "[2024/10/08 23:57:44] ppocr DEBUG: cls num  : 25, elapsed : 0.06655454635620117\n", "[2024/10/08 23:57:44] ppocr DEBUG: rec_res num  : 25, elapsed : 0.8824334144592285\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:57:45.139\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364455586199399.pdf has 4 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:57:45] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=4, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:57:46] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.1504354476928711\n", "[2024/10/08 23:57:47] ppocr DEBUG: cls num  : 51, elapsed : 0.11102747917175293\n", "[2024/10/08 23:57:51] ppocr DEBUG: rec_res num  : 51, elapsed : 4.339670896530151\n", "[2024/10/08 23:57:51] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.13799428939819336\n", "[2024/10/08 23:57:51] ppocr DEBUG: cls num  : 23, elapsed : 0.05492806434631348\n", "[2024/10/08 23:57:52] ppocr DEBUG: rec_res num  : 23, elapsed : 0.8928170204162598\n", "[2024/10/08 23:57:52] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13454079627990723\n", "[2024/10/08 23:57:52] ppocr DEBUG: cls num  : 13, elapsed : 0.04012918472290039\n", "[2024/10/08 23:57:54] ppocr DEBUG: rec_res num  : 13, elapsed : 1.7318158149719238\n", "[2024/10/08 23:57:54] ppocr DEBUG: dt_boxes num : 25, elapsed : 0.13436007499694824\n", "[2024/10/08 23:57:54] ppocr DEBUG: cls num  : 25, elapsed : 0.048171043395996094\n", "[2024/10/08 23:57:55] ppocr DEBUG: rec_res num  : 25, elapsed : 0.833266019821167\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:57:55.680\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364466962156195.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:57:55] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:57:57] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.19045114517211914\n", "[2024/10/08 23:57:57] ppocr DEBUG: cls num  : 51, elapsed : 0.12238740921020508\n", "[2024/10/08 23:58:02] ppocr DEBUG: rec_res num  : 51, elapsed : 4.352950811386108\n", "[2024/10/08 23:58:02] ppocr DEBUG: dt_boxes num : 23, elapsed : 0.1366877555847168\n", "[2024/10/08 23:58:02] ppocr DEBUG: cls num  : 23, elapsed : 0.05532097816467285\n", "[2024/10/08 23:58:03] ppocr DEBUG: rec_res num  : 23, elapsed : 0.8965742588043213\n", "[2024/10/08 23:58:03] ppocr DEBUG: dt_boxes num : 13, elapsed : 0.13570785522460938\n", "[2024/10/08 23:58:03] ppocr DEBUG: cls num  : 13, elapsed : 0.04649949073791504\n", "[2024/10/08 23:58:05] ppocr DEBUG: rec_res num  : 13, elapsed : 1.733144760131836\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:58:05.300\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364477350767671.pdf has 6 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:58:05] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=6, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:58:07] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.1902761459350586\n", "[2024/10/08 23:58:07] ppocr DEBUG: cls num  : 51, elapsed : 0.12159061431884766\n", "[2024/10/08 23:58:11] ppocr DEBUG: rec_res num  : 51, elapsed : 4.34661078453064\n", "[2024/10/08 23:58:11] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.13718080520629883\n", "[2024/10/08 23:58:11] ppocr DEBUG: cls num  : 29, elapsed : 0.06747817993164062\n", "[2024/10/08 23:58:13] ppocr DEBUG: rec_res num  : 29, elapsed : 1.06361722946167\n", "[2024/10/08 23:58:13] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.13796210289001465\n", "[2024/10/08 23:58:13] ppocr DEBUG: cls num  : 28, elapsed : 0.06349754333496094\n", "[2024/10/08 23:58:17] ppocr DEBUG: rec_res num  : 28, elapsed : 4.440167427062988\n", "[2024/10/08 23:58:17] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.15543055534362793\n", "[2024/10/08 23:58:17] ppocr DEBUG: cls num  : 87, elapsed : 0.1545400619506836\n", "[2024/10/08 23:58:21] ppocr DEBUG: rec_res num  : 87, elapsed : 3.5699682235717773\n", "[2024/10/08 23:58:21] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.15402722358703613\n", "[2024/10/08 23:58:21] ppocr DEBUG: cls num  : 87, elapsed : 0.14836788177490234\n", "[2024/10/08 23:58:25] ppocr DEBUG: rec_res num  : 87, elapsed : 3.5267539024353027\n", "[2024/10/08 23:58:25] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.13711023330688477\n", "[2024/10/08 23:58:25] ppocr DEBUG: cls num  : 33, elapsed : 0.0594027042388916\n", "[2024/10/08 23:58:26] ppocr DEBUG: rec_res num  : 33, elapsed : 1.1941874027252197\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:58:26.995\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364500087572467.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:58:27] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:58:28] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.18626785278320312\n", "[2024/10/08 23:58:29] ppocr DEBUG: cls num  : 33, elapsed : 0.09415245056152344\n", "[2024/10/08 23:58:30] ppocr DEBUG: rec_res num  : 33, elapsed : 1.1630280017852783\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:58:30.349\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364503955259097.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:58:30] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:58:32] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.14940738677978516\n", "[2024/10/08 23:58:32] ppocr DEBUG: cls num  : 51, elapsed : 0.10103034973144531\n", "[2024/10/08 23:58:36] ppocr DEBUG: rec_res num  : 51, elapsed : 4.378329038619995\n", "[2024/10/08 23:58:36] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.13806724548339844\n", "[2024/10/08 23:58:36] ppocr DEBUG: cls num  : 29, elapsed : 0.0676875114440918\n", "[2024/10/08 23:58:38] ppocr DEBUG: rec_res num  : 29, elapsed : 1.0740740299224854\n", "[2024/10/08 23:58:38] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.1364574432373047\n", "[2024/10/08 23:58:38] ppocr DEBUG: cls num  : 28, elapsed : 0.06577086448669434\n", "[2024/10/08 23:58:42] ppocr DEBUG: rec_res num  : 28, elapsed : 4.492814064025879\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:58:42.920\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364517314206481.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:58:42] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:58:44] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.20533990859985352\n", "[2024/10/08 23:58:45] ppocr DEBUG: cls num  : 87, elapsed : 0.1888270378112793\n", "[2024/10/08 23:58:48] ppocr DEBUG: rec_res num  : 87, elapsed : 3.653498888015747\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:58:48.902\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364523800907644.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:58:48] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:58:50] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.162034273147583\n", "[2024/10/08 23:58:50] ppocr DEBUG: cls num  : 87, elapsed : 0.16864442825317383\n", "[2024/10/08 23:58:54] ppocr DEBUG: rec_res num  : 87, elapsed : 3.5861058235168457\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:58:54.714\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364530216738827.pdf has 6 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:58:54] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=6, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:58:56] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.15029025077819824\n", "[2024/10/08 23:58:56] ppocr DEBUG: cls num  : 51, elapsed : 0.09508156776428223\n", "[2024/10/08 23:59:01] ppocr DEBUG: rec_res num  : 51, elapsed : 4.359716892242432\n", "[2024/10/08 23:59:01] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.13566803932189941\n", "[2024/10/08 23:59:01] ppocr DEBUG: cls num  : 29, elapsed : 0.06529021263122559\n", "[2024/10/08 23:59:02] ppocr DEBUG: rec_res num  : 29, elapsed : 1.060978651046753\n", "[2024/10/08 23:59:02] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.14453458786010742\n", "[2024/10/08 23:59:02] ppocr DEBUG: cls num  : 28, elapsed : 0.06571006774902344\n", "[2024/10/08 23:59:07] ppocr DEBUG: rec_res num  : 28, elapsed : 4.48882532119751\n", "[2024/10/08 23:59:07] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.16342425346374512\n", "[2024/10/08 23:59:07] ppocr DEBUG: cls num  : 87, elapsed : 0.1559886932373047\n", "[2024/10/08 23:59:11] ppocr DEBUG: rec_res num  : 87, elapsed : 3.7644968032836914\n", "[2024/10/08 23:59:11] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.16293573379516602\n", "[2024/10/08 23:59:11] ppocr DEBUG: cls num  : 87, elapsed : 0.15669679641723633\n", "[2024/10/08 23:59:15] ppocr DEBUG: rec_res num  : 87, elapsed : 3.675783157348633\n", "[2024/10/08 23:59:15] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.21095991134643555\n", "[2024/10/08 23:59:15] ppocr DEBUG: cls num  : 33, elapsed : 0.0684502124786377\n", "[2024/10/08 23:59:16] ppocr DEBUG: rec_res num  : 33, elapsed : 1.3239872455596924\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:59:17.024\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364552953775126.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:59:17] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:59:19] ppocr DEBUG: dt_boxes num : 33, elapsed : 0.19034028053283691\n", "[2024/10/08 23:59:19] ppocr DEBUG: cls num  : 33, elapsed : 0.09213662147521973\n", "[2024/10/08 23:59:20] ppocr DEBUG: rec_res num  : 33, elapsed : 1.1469812393188477\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:59:20.440\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364556746380284.pdf has 3 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:59:20] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=3, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:59:22] ppocr DEBUG: dt_boxes num : 51, elapsed : 0.1488947868347168\n", "[2024/10/08 23:59:22] ppocr DEBUG: cls num  : 51, elapsed : 0.09774327278137207\n", "[2024/10/08 23:59:26] ppocr DEBUG: rec_res num  : 51, elapsed : 4.325605392456055\n", "[2024/10/08 23:59:26] ppocr DEBUG: dt_boxes num : 29, elapsed : 0.1377861499786377\n", "[2024/10/08 23:59:27] ppocr DEBUG: cls num  : 29, elapsed : 0.06558084487915039\n", "[2024/10/08 23:59:28] ppocr DEBUG: rec_res num  : 29, elapsed : 1.0941922664642334\n", "[2024/10/08 23:59:28] ppocr DEBUG: dt_boxes num : 28, elapsed : 0.1355724334716797\n", "[2024/10/08 23:59:28] ppocr DEBUG: cls num  : 28, elapsed : 0.06433820724487305\n", "[2024/10/08 23:59:32] ppocr DEBUG: rec_res num  : 28, elapsed : 4.486044406890869\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:59:33.066\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364570058616358.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:59:33] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:59:34] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.203444242477417\n", "[2024/10/08 23:59:35] ppocr DEBUG: cls num  : 87, elapsed : 0.18140745162963867\n", "[2024/10/08 23:59:38] ppocr DEBUG: rec_res num  : 87, elapsed : 3.66972017288208\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-08 23:59:39.058\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mextract_consultation_notes\u001b[0m:\u001b[36m15\u001b[0m - \u001b[1mFile ../data/tmp_1728364576470284886.pdf has 1 pages\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024/10/08 23:59:39] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, gpu_id=0, image_dir=None, page_num=1, det_algorithm='DB', det_model_dir='/root/.paddleocr/whl/det/en/en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='/root/.paddleocr/whl/rec/en/en_PP-OCRv4_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='/usr/local/lib/python3.10/site-packages/paddleocr/ppocr/utils/en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='/root/.paddleocr/whl/cls/ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, invert=False, binarize=False, alphacolor=(255, 255, 255), lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv4', structure_version='PP-StructureV2')\n", "[2024/10/08 23:59:41] ppocr DEBUG: dt_boxes num : 87, elapsed : 0.16225028038024902\n", "[2024/10/08 23:59:41] ppocr DEBUG: cls num  : 87, elapsed : 0.16200852394104004\n", "[2024/10/08 23:59:44] ppocr DEBUG: rec_res num  : 87, elapsed : 3.5667898654937744\n"]}], "source": ["\n", "for tmp_file_path in tmp_file_paths[len(ans):]:\n", "    try:\n", "        tmp_res = extract_consultation_notes(tmp_file_path)\n", "    except FileDataError as e:\n", "        logger.exception(tmp_file_path)\n", "        logger.exception(e)\n", "        tmp_res = \"\"\n", "    ans.append(tmp_res)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["86"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ans)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON>ine Result with Original Information"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["merge_df = pd.read_csv(\"../data/q3_merge.csv\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["merge_df[\"ocr_consultation_notes\"] = ans"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["(86, 27)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["merge_df.shape"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["merge_df[\"download_file\"] = tmp_file_paths"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>AnimalNo</th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>DiagnosisLineNo</th>\n", "      <th>DateTreatment</th>\n", "      <th>CaseNo</th>\n", "      <th>CaseDescription</th>\n", "      <th>DiagnosisDescription</th>\n", "      <th>...</th>\n", "      <th>Quantity</th>\n", "      <th>Weight</th>\n", "      <th>Duration</th>\n", "      <th>Evidence</th>\n", "      <th>InvoiceId</th>\n", "      <th>CspReferenceNo</th>\n", "      <th>DocumentPath</th>\n", "      <th>DocumentType</th>\n", "      <th>ocr_consultation_notes</th>\n", "      <th>download_file</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/f8274842-5b6f-...</td>\n", "      <td>pdf</td>\n", "      <td>Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...</td>\n", "      <td>../data/tmp_1728358794590598391.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/1574227f-41fe-...</td>\n", "      <td>application/pdf</td>\n", "      <td>PVS Perth Veterinary 305 Selby Street North, O...</td>\n", "      <td>../data/tmp_1728359299651144298.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/bb6831f7-dcf0-...</td>\n", "      <td>application/pdf</td>\n", "      <td>PVS Perth Veterinary 305 Selby Street North, O...</td>\n", "      <td>../data/tmp_1728359332772430795.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/3742fd45-938c-...</td>\n", "      <td>application/pdf</td>\n", "      <td>Hillarys WA 6025 2 Banks Ave Vetwest Whitfords...</td>\n", "      <td>../data/tmp_1728359357866615603.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/07a45bf7-b472-...</td>\n", "      <td>application/pdf</td>\n", "      <td>Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...</td>\n", "      <td>../data/tmp_1728359360017098653.pdf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 28 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0.1  Unnamed: 0  AnimalNo   ClaimNo  InvoiceLineNo  \\\n", "0             0           0     12004  ********          20000   \n", "1             1           0     12004  ********          20000   \n", "2             2           0     12004  ********          20000   \n", "3             3           0     12004  ********          20000   \n", "4             4           0     12004  ********          20000   \n", "\n", "   DiagnosisLineNo DateTreatment     CaseNo CaseDescription  \\\n", "0            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "1            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "2            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "3            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "4            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "\n", "            DiagnosisDescription  ...       Quantity         Weight  \\\n", "0  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "1  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "2  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "3  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "4  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "\n", "        Duration                                           Evidence InvoiceId  \\\n", "0  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "1  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "2  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "3  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "4  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "\n", "  CspReferenceNo                                       DocumentPath  \\\n", "0  V********1343  cosservice-prod-claims-********/f8274842-5b6f-...   \n", "1  V********1343  cosservice-prod-claims-********/1574227f-41fe-...   \n", "2  V********1343  cosservice-prod-claims-********/bb6831f7-dcf0-...   \n", "3  V********1343  cosservice-prod-claims-********/3742fd45-938c-...   \n", "4  V********1343  cosservice-prod-claims-********/07a45bf7-b472-...   \n", "\n", "      DocumentType                             ocr_consultation_notes  \\\n", "0              pdf  Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...   \n", "1  application/pdf  PVS Perth Veterinary 305 Selby Street North, O...   \n", "2  application/pdf  PVS Perth Veterinary 305 Selby Street North, O...   \n", "3  application/pdf  Hillarys WA 6025 2 Banks Ave Vetwest Whitfords...   \n", "4  application/pdf  Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...   \n", "\n", "                         download_file  \n", "0  ../data/tmp_1728358794590598391.pdf  \n", "1  ../data/tmp_1728359299651144298.pdf  \n", "2  ../data/tmp_1728359332772430795.pdf  \n", "3  ../data/tmp_1728359357866615603.pdf  \n", "4  ../data/tmp_1728359360017098653.pdf  \n", "\n", "[5 rows x 28 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["merge_df.head()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["merge_df[\"download_file\"] = merge_df[\"download_file\"].apply(lambda x: x[8:])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["merge_df[\"ocr_consultation_notes\"] = merge_df[\"ocr_consultation_notes\"].apply(lambda x: x.replace(\"\\n\", \" \"))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0.1</th>\n", "      <th>Unnamed: 0</th>\n", "      <th>AnimalNo</th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>DiagnosisLineNo</th>\n", "      <th>DateTreatment</th>\n", "      <th>CaseNo</th>\n", "      <th>CaseDescription</th>\n", "      <th>DiagnosisDescription</th>\n", "      <th>...</th>\n", "      <th>Quantity</th>\n", "      <th>Weight</th>\n", "      <th>Duration</th>\n", "      <th>Evidence</th>\n", "      <th>InvoiceId</th>\n", "      <th>CspReferenceNo</th>\n", "      <th>DocumentPath</th>\n", "      <th>DocumentType</th>\n", "      <th>ocr_consultation_notes</th>\n", "      <th>download_file</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/f8274842-5b6f-...</td>\n", "      <td>pdf</td>\n", "      <td>Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...</td>\n", "      <td>tmp_1728358794590598391.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/1574227f-41fe-...</td>\n", "      <td>application/pdf</td>\n", "      <td>PVS Perth Veterinary 305 Selby Street North, O...</td>\n", "      <td>tmp_1728359299651144298.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/bb6831f7-dcf0-...</td>\n", "      <td>application/pdf</td>\n", "      <td>PVS Perth Veterinary 305 Selby Street North, O...</td>\n", "      <td>tmp_1728359332772430795.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/3742fd45-938c-...</td>\n", "      <td>application/pdf</td>\n", "      <td>Hillarys WA 6025 2 Banks Ave Vetwest Whitfords...</td>\n", "      <td>tmp_1728359357866615603.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>12004</td>\n", "      <td>********</td>\n", "      <td>20000</td>\n", "      <td>30000</td>\n", "      <td>2023-07-13</td>\n", "      <td>CS3330024</td>\n", "      <td>RENAL DISEASE</td>\n", "      <td>URINARY TRACT INFECTION (UTI)</td>\n", "      <td>...</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>Not Specified</td>\n", "      <td>No direct evidence of treatment duration provided</td>\n", "      <td>2845669</td>\n", "      <td>V********1343</td>\n", "      <td>cosservice-prod-claims-********/07a45bf7-b472-...</td>\n", "      <td>application/pdf</td>\n", "      <td>Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...</td>\n", "      <td>tmp_1728359360017098653.pdf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 28 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0.1  Unnamed: 0  AnimalNo   ClaimNo  InvoiceLineNo  \\\n", "0             0           0     12004  ********          20000   \n", "1             1           0     12004  ********          20000   \n", "2             2           0     12004  ********          20000   \n", "3             3           0     12004  ********          20000   \n", "4             4           0     12004  ********          20000   \n", "\n", "   DiagnosisLineNo DateTreatment     CaseNo CaseDescription  \\\n", "0            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "1            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "2            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "3            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "4            30000    2023-07-13  CS3330024   RENAL DISEASE   \n", "\n", "            DiagnosisDescription  ...       Quantity         Weight  \\\n", "0  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "1  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "2  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "3  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "4  URINARY TRACT INFECTION (UTI)  ...  Not Specified  Not Specified   \n", "\n", "        Duration                                           Evidence InvoiceId  \\\n", "0  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "1  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "2  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "3  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "4  Not Specified  No direct evidence of treatment duration provided   2845669   \n", "\n", "  CspReferenceNo                                       DocumentPath  \\\n", "0  V********1343  cosservice-prod-claims-********/f8274842-5b6f-...   \n", "1  V********1343  cosservice-prod-claims-********/1574227f-41fe-...   \n", "2  V********1343  cosservice-prod-claims-********/bb6831f7-dcf0-...   \n", "3  V********1343  cosservice-prod-claims-********/3742fd45-938c-...   \n", "4  V********1343  cosservice-prod-claims-********/07a45bf7-b472-...   \n", "\n", "      DocumentType                             ocr_consultation_notes  \\\n", "0              pdf  Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...   \n", "1  application/pdf  PVS Perth Veterinary 305 Selby Street North, O...   \n", "2  application/pdf  PVS Perth Veterinary 305 Selby Street North, O...   \n", "3  application/pdf  Hillarys WA 6025 2 Banks Ave Vetwest Whitfords...   \n", "4  application/pdf  Whitfords 2 Banks Ave Hillarys WA 6025 Ph: 08 ...   \n", "\n", "                 download_file  \n", "0  tmp_1728358794590598391.pdf  \n", "1  tmp_1728359299651144298.pdf  \n", "2  tmp_1728359332772430795.pdf  \n", "3  tmp_1728359357866615603.pdf  \n", "4  tmp_1728359360017098653.pdf  \n", "\n", "[5 rows x 28 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["merge_df.head()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["merge_df.to_csv(\"../data/BIRI-9577_sample_ultimate_res.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}