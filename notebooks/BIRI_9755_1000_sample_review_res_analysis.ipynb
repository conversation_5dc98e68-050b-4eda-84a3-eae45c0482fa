{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BIRI_9755_1000_sample_review_result_analysis\n", "\n", "## define file path"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# file path\n", "file_path_list = [\n", "    \"/workspaces/OCR_in_house/data/OCR_in_house/res/1000_2024_sample_evaluation/1000_400_samples_DI_rule_res.xlsx\",\n", "    \"/workspaces/OCR_in_house/data/OCR_in_house/res/1000_2024_sample_evaluation/1000_600_samples_DI_rule_res.xlsx\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## load data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from typing import List, Dict\n", "import pandas as pd\n", "\n", "def load_data(file_path: List) -> pd.DataFrame:\n", "    dfs = []\n", "    for path in file_path:\n", "        if path.endswith(\"csv\"):\n", "            dfs.append(pd.read_csv(path))\n", "        elif path.endswith(\"xlsx\"):\n", "            dfs.append(pd.read_excel(path))\n", "        else:\n", "            raise TypeError(\"File type does not support.\")\n", "    return pd.concat(dfs, ignore_index=True, sort=False)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["claimno\n", "C7804454    5302\n", "C7636223     140\n", "C7651753      86\n", "C7680781      77\n", "C7620650      53\n", "            ... \n", "C7859792       1\n", "C7732844       1\n", "C7817951       1\n", "C7831147       1\n", "C7695257       1\n", "Name: count, Length: 331, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_excel(file_path_list[0])[\"claimno\"].value_counts()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["claimno\n", "C7631439    1604\n", "C7691549     264\n", "C7869742     210\n", "C7866837     133\n", "C7742535     122\n", "            ... \n", "C7699032       1\n", "C7826351       1\n", "C7830310       1\n", "C7694420       1\n", "C7728301       1\n", "Name: count, Length: 658, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_excel(file_path_list[1])[\"claimno\"].value_counts()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df = load_data(file_path_list)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>claimno</th>\n", "      <th>docfile</th>\n", "      <th>ocr_conf</th>\n", "      <th>same_claimno</th>\n", "      <th>if_fallout</th>\n", "      <th>rule_res</th>\n", "      <th>paddleocr_content</th>\n", "      <th>di_conf</th>\n", "      <th>di_invoice_no</th>\n", "      <th>upm_invoice_no</th>\n", "      <th>...</th>\n", "      <th>human_invoice_date_verify</th>\n", "      <th>human_invoice_total_verify</th>\n", "      <th>human_treatment_date_verify</th>\n", "      <th>human_treatment_verify</th>\n", "      <th>human_treatment_amount_verify</th>\n", "      <th>PASS</th>\n", "      <th>NOTE</th>\n", "      <th>human_pass + not_fall_out</th>\n", "      <th>Rules</th>\n", "      <th>Unnamed: 49</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>297b783c-2630-45ba-b46d-865d95180f72.pdf</td>\n", "      <td>0.38</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>FALL OUT: Invoice Number not extracted FALL...</td>\n", "      <td>ABN: *********** 24 Villiers Street North Melb...</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>ref as invoice no</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>297b783c-2630-45ba-b46d-865d95180f72.pdf</td>\n", "      <td>0.38</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>FALL OUT: Invoice Number not extracted FALL...</td>\n", "      <td>ABN: *********** 24 Villiers Street North Melb...</td>\n", "      <td>0.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>e621417d-0a29-4a10-967f-dae663f63e25.png</td>\n", "      <td>0.77</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>POSTPROCESS: Invoice No Replaced by PaddleOCR...</td>\n", "      <td>2:13 II 4G 6O 245 To: <PERSON> &gt;. Reply...</td>\n", "      <td>0.431</td>\n", "      <td>B248132677</td>\n", "      <td>B248132677</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>low conf</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>e621417d-0a29-4a10-967f-dae663f63e25.png</td>\n", "      <td>0.77</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>POSTPROCESS: Invoice No Replaced by PaddleOCR...</td>\n", "      <td>2:13 II 4G 6O 245 To: <PERSON> &gt;. Reply...</td>\n", "      <td>0.431</td>\n", "      <td>B248132677</td>\n", "      <td>B248132677</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>e621417d-0a29-4a10-967f-dae663f63e25.png</td>\n", "      <td>0.77</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>POSTPROCESS: Invoice No Replaced by PaddleOCR...</td>\n", "      <td>2:13 II 4G 6O 245 To: <PERSON> &gt;. Reply...</td>\n", "      <td>0.431</td>\n", "      <td>B248132677</td>\n", "      <td>B248132677</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 50 columns</p>\n", "</div>"], "text/plain": ["    claimno                                   docfile  ocr_conf  same_claimno  \\\n", "0  ********  297b783c-2630-45ba-b46d-865d95180f72.pdf      0.38             0   \n", "1  ********  297b783c-2630-45ba-b46d-865d95180f72.pdf      0.38             0   \n", "2  ********  e621417d-0a29-4a10-967f-dae663f63e25.png      0.77             1   \n", "3  ********  e621417d-0a29-4a10-967f-dae663f63e25.png      0.77             1   \n", "4  ********  e621417d-0a29-4a10-967f-dae663f63e25.png      0.77             1   \n", "\n", "   if_fallout                                           rule_res  \\\n", "0           1     FALL OUT: Invoice Number not extracted FALL...   \n", "1           1     FALL OUT: Invoice Number not extracted FALL...   \n", "2           1   POSTPROCESS: Invoice No Replaced by PaddleOCR...   \n", "3           1   POSTPROCESS: Invoice No Replaced by PaddleOCR...   \n", "4           1   POSTPROCESS: Invoice No Replaced by PaddleOCR...   \n", "\n", "                                   paddleocr_content  di_conf di_invoice_no  \\\n", "0  ABN: *********** 24 Villiers Street North Melb...    0.000           NaN   \n", "1  ABN: *********** 24 Villiers Street North Melb...    0.000           NaN   \n", "2  2:13 II 4G 6O 245 To: <PERSON> >. Reply...    0.431    B248132677   \n", "3  2:13 II 4G 6O 245 To: <PERSON> >. Reply...    0.431    B248132677   \n", "4  2:13 II 4G 6O 245 To: <PERSON> >. Reply...    0.431    B248132677   \n", "\n", "  upm_invoice_no  ...  human_invoice_date_verify  human_invoice_total_verify  \\\n", "0            NaN  ...                        1.0                         1.0   \n", "1            NaN  ...                        NaN                         NaN   \n", "2     B248132677  ...                        1.0                         1.0   \n", "3     B248132677  ...                        NaN                         NaN   \n", "4     B248132677  ...                        NaN                         NaN   \n", "\n", "   human_treatment_date_verify human_treatment_verify  \\\n", "0                          1.0                    1.0   \n", "1                          1.0                    1.0   \n", "2                          1.0                    1.0   \n", "3                          1.0                    1.0   \n", "4                          1.0                    1.0   \n", "\n", "  human_treatment_amount_verify PASS               NOTE  \\\n", "0                           1.0  0.0  ref as invoice no   \n", "1                           1.0  NaN                NaN   \n", "2                           1.0  1.0           low conf   \n", "3                           1.0  NaN                NaN   \n", "4                           1.0  NaN                NaN   \n", "\n", "   human_pass + not_fall_out  Rules  Unnamed: 49  \n", "0                        0.0    NaN          NaN  \n", "1                        NaN    NaN          NaN  \n", "2                        0.0    NaN          NaN  \n", "3                        NaN    NaN          NaN  \n", "4                        NaN    NaN          NaN  \n", "\n", "[5 rows x 50 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df[\"PASS\"] = df[\"PASS\"].fillna(\"\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["claimno\n", "C7804454    5302\n", "C7631439    1604\n", "C7691549     264\n", "C7869742     210\n", "C7636223     140\n", "            ... \n", "C7859792       1\n", "C7689983       1\n", "C7761786       1\n", "C7844483       1\n", "C7664588       1\n", "Name: count, Length: 989, dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"claimno\"].value_counts()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## parsing the result for each claim"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "from loguru import logger\n", "\n", "def if_multi_file(treatmentlines: List) -> bool:\n", "    file_names = set()\n", "    for info in treatmentlines:\n", "        if info[\"docfile\"]:\n", "            file_names.add(info[\"docfile\"])\n", "\n", "    return len(set(file_names)) > 1\n", "\n", "def if_pass_truuth(treatmentlines: List) -> bool:\n", "    confs = set()\n", "    for info in treatmentlines:\n", "        ocr_conf =  info.get(\"ocr_conf\", 0.)\n", "        try:\n", "            ocr_conf = float(ocr_conf)\n", "            confs.add(ocr_conf)\n", "        except ValueError:\n", "            return False\n", "    if len(confs)==1 and list(confs)[0] >= 0.8:\n", "        return True\n", "    return False\n", "\n", "def if_pass_diocr(treatmentlines: List) -> bool:\n", "    return all(info.get(\"if_fallout\", 1)==0 for info in treatmentlines)\n", "\n", "\n", "def get_confuse_matrix(treatmentlines: List) -> Dict:\n", "    tp = 0\n", "    fp = 0\n", "    tn = 0\n", "    fn = 0\n", "    fallout_info = set(info[\"if_fallout\"] for info in treatmentlines)\n", "    human_pass_info = set(info[\"PASS\"] for info in treatmentlines if info[\"PASS\"]!=\"\")\n", "    \n", "    if 1 in fallout_info:\n", "        if_fallout = 1\n", "    else:\n", "        if_fallout = 0\n", "\n", "    if len(human_pass_info) == 1 and list(human_pass_info)[0] == 1:\n", "        human_pass = 1\n", "    else:\n", "        human_pass = 0\n", "    \n", "    if len(human_pass_info)!=1:\n", "        logger.exception(f\"claim: {treatmentlines[0]['claimno']} has not aligned human pass info {human_pass_info}\")\n", "\n", "    if if_fallout == 1:\n", "        # negative\n", "        if human_pass:\n", "            fn += 1\n", "        else:\n", "            tn +=1\n", "    else:\n", "        # positive\n", "        if human_pass:\n", "            tp +=1\n", "        else:\n", "            fp += 1\n", "\n", "    if tp + fp + tn + fn != 1:\n", "        logger.exception(f\"claim: {treatmentlines[0]['claimno']} has incorrect confusion matrix\")\n", "    return {\n", "        \"tp\": tp,\n", "        \"fp\": fp,\n", "        \"tn\": tn,\n", "        \"fn\": fn\n", "    }\n", "\n", "def parse(df: pd.DataFrame) -> pd.DataFrame:\n", "    # step 0: transform dataframe to list of dict\n", "    data = df.to_dict(orient=\"records\")\n", "    # step 1: group information for each claim\n", "    info = defaultdict(list)\n", "    for line in data:\n", "        claimno = line[\"claimno\"]\n", "        info[claimno].append(line)\n", "    logger.info(f\"number of claims: {len(info)}\")\n", "    # step 2: get information for interests\n", "    # tp, fp, tn, fn\n", "    # truuth >= 0.8\n", "    # if multi file\n", "    # if inhouse not truuth\n", "    # if truuth not inhouse\n", "    analysis = {}\n", "    for claimno, claim_data in info.items():\n", "        if_mf = int(if_multi_file(claim_data))\n", "        if_truuth = int(if_pass_truuth(claim_data))\n", "        if_house = int(if_pass_diocr(claim_data))\n", "        confuse_matrix = get_confuse_matrix(claim_data)\n", "        analysis[claimno] = {\n", "            \"if_mf\": if_mf,\n", "            \"if_truuth\": if_truuth,\n", "            \"if_house\": if_house,\n", "            \"confuse_matrix\": confuse_matrix\n", "        }\n", "    logger.info(f\"number of analysis claims: {len(analysis)}\")\n", "    # step 3: calculate final analysis results\n", "    # overall, single file, multi file\n", "    # tp, fp, tn, fn\n", "    # acc, coverage\n", "    # truuth coverage\n", "    # inhouse_no_truuth, truuth_no_inhouse\n", "    metrics = {\n", "        \"overall\": {\n", "            \"tp\": 0,\n", "            \"fp\": 0,\n", "            \"tn\": 0,\n", "            \"fn\": 0,\n", "            \"inhouse_acc\": 0,\n", "            \"inhouse_coverage\": 0,\n", "            \"truuth_coverage\":0,\n", "            \"inhouse_no_truuth\": 0,\n", "            \"truuth_no_inhouse\":0,\n", "        },\n", "        \"single_file\": {\n", "            \"tp\": 0,\n", "            \"fp\": 0,\n", "            \"tn\": 0,\n", "            \"fn\": 0,\n", "            \"inhouse_acc\": 0,\n", "            \"inhouse_coverage\": 0,\n", "            \"truuth_coverage\":0,\n", "            \"inhouse_no_truuth\": 0,\n", "            \"truuth_no_inhouse\":0,\n", "        },\n", "        \"multi_file\": {\n", "            \"tp\": 0,\n", "            \"fp\": 0,\n", "            \"tn\": 0,\n", "            \"fn\": 0,\n", "            \"inhouse_acc\": 0,\n", "            \"inhouse_coverage\": 0,\n", "            \"truuth_coverage\":0,\n", "            \"inhouse_no_truuth\": 0,\n", "            \"truuth_no_inhouse\":0,\n", "        },\n", "    }\n", "    num_claim = 0\n", "    num_multi_file_claim = 0\n", "    num_single_file_claim = 0\n", "\n", "    num_claim_by_truuth = 0\n", "    num_multi_file_claim_by_truuth = 0\n", "    num_single_file_claim_by_truuth = 0\n", "\n", "    for claimno, claim_ana in analysis.items():\n", "        # analysis[claimno] = {\n", "        #     \"if_mf\": if_mf,\n", "        #     \"if_truuth\": if_truuth,\n", "        #     \"if_house\": if_house,\n", "        #     \"confuse_matrix\": confuse_matrix\n", "        # }\n", "\n", "        # overall\n", "        num_claim += 1\n", "        num_claim_by_truuth += int(claim_ana[\"if_truuth\"])\n", "        if claim_ana[\"confuse_matrix\"][\"fp\"] > 0:\n", "            logger.info(f\"claim: {claimno} is a False-Positive\")\n", "        for k, v in claim_ana[\"confuse_matrix\"].items():\n", "            metrics[\"overall\"][k] += v\n", "        inhouse_no_truuth = int(claim_ana[\"if_house\"] and not claim_ana[\"if_truuth\"])\n", "        truuth_no_inhouse = int(not claim_ana[\"if_house\"] and claim_ana[\"if_truuth\"])\n", "        metrics[\"overall\"][\"inhouse_no_truuth\"] += inhouse_no_truuth\n", "        metrics[\"overall\"][\"truuth_no_inhouse\"] += truuth_no_inhouse\n", "        if claim_ana[\"if_mf\"]:\n", "            num_multi_file_claim += 1\n", "            num_multi_file_claim_by_truuth += int(claim_ana[\"if_truuth\"])\n", "            for k, v in claim_ana[\"confuse_matrix\"].items():\n", "                metrics[\"multi_file\"][k] += v\n", "            metrics[\"multi_file\"][\"inhouse_no_truuth\"] += inhouse_no_truuth\n", "            metrics[\"multi_file\"][\"truuth_no_inhouse\"] += truuth_no_inhouse\n", "        else:\n", "            num_single_file_claim += 1\n", "            num_single_file_claim_by_truuth += int(claim_ana[\"if_truuth\"])\n", "            for k, v in claim_ana[\"confuse_matrix\"].items():\n", "                metrics[\"single_file\"][k] += v\n", "            metrics[\"single_file\"][\"inhouse_no_truuth\"] += inhouse_no_truuth\n", "            metrics[\"single_file\"][\"truuth_no_inhouse\"] += truuth_no_inhouse\n", "    metrics[\"overall\"][\"inhouse_acc\"] = metrics[\"overall\"][\"tp\"] / (metrics[\"overall\"][\"tp\"] + metrics[\"overall\"][\"fp\"])\n", "    metrics[\"overall\"][\"inhouse_coverage\"] = (metrics[\"overall\"][\"tp\"] + metrics[\"overall\"][\"fp\"]) / num_claim\n", "    metrics[\"overall\"][\"truuth_coverage\"] = num_claim_by_truuth / num_claim\n", "    metrics[\"overall\"][\"num_claim\"] = num_claim\n", "    metrics[\"overall\"][\"num_claim_by_truuth\"] = num_claim_by_truuth\n", "\n", "    metrics[\"multi_file\"][\"inhouse_acc\"] = metrics[\"multi_file\"][\"tp\"] / (metrics[\"multi_file\"][\"tp\"] + metrics[\"multi_file\"][\"fp\"])\n", "    metrics[\"multi_file\"][\"inhouse_coverage\"] = (metrics[\"multi_file\"][\"tp\"] + metrics[\"multi_file\"][\"fp\"]) / num_multi_file_claim\n", "    metrics[\"multi_file\"][\"truuth_coverage\"] = num_multi_file_claim_by_truuth / num_multi_file_claim\n", "    metrics[\"multi_file\"][\"num_claim\"] = num_multi_file_claim\n", "    metrics[\"multi_file\"][\"num_claim_by_truuth\"] = num_multi_file_claim_by_truuth\n", "\n", "    metrics[\"single_file\"][\"inhouse_acc\"] = metrics[\"single_file\"][\"tp\"] / (metrics[\"single_file\"][\"tp\"] + metrics[\"single_file\"][\"fp\"])\n", "    metrics[\"single_file\"][\"inhouse_coverage\"] = (metrics[\"single_file\"][\"tp\"] + metrics[\"single_file\"][\"fp\"]) / num_single_file_claim\n", "    metrics[\"single_file\"][\"truuth_coverage\"] = num_single_file_claim_by_truuth / num_single_file_claim\n", "    metrics[\"single_file\"][\"num_claim\"] = num_single_file_claim\n", "    metrics[\"single_file\"][\"num_claim_by_truuth\"] = num_single_file_claim_by_truuth\n", "\n", "    # step 4: represent results in df\n", "    return pd.DataFrame(metrics)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-11-20 02:50:41.078\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m80\u001b[0m - \u001b[1mnumber of claims: 989\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.102\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m99\u001b[0m - \u001b[1mnumber of analysis claims: 989\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.103\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7704155 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.104\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7760893 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.105\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7760951 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.106\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7791516 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.107\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7845855 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.108\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7640502 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.109\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7720011 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.109\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: C7721789 is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.110\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: ******** is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.111\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: ******** is a False-Positive\u001b[0m\n", "\u001b[32m2024-11-20 02:50:41.112\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mparse\u001b[0m:\u001b[36m161\u001b[0m - \u001b[1mclaim: ******** is a False-Positive\u001b[0m\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>overall</th>\n", "      <th>single_file</th>\n", "      <th>multi_file</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>tp</th>\n", "      <td>525.000000</td>\n", "      <td>494.000000</td>\n", "      <td>31.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fp</th>\n", "      <td>11.000000</td>\n", "      <td>9.000000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>tn</th>\n", "      <td>328.000000</td>\n", "      <td>229.000000</td>\n", "      <td>99.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>fn</th>\n", "      <td>125.000000</td>\n", "      <td>111.000000</td>\n", "      <td>14.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>inhouse_acc</th>\n", "      <td>0.979478</td>\n", "      <td>0.982107</td>\n", "      <td>0.939394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>inhouse_coverage</th>\n", "      <td>0.541962</td>\n", "      <td>0.596679</td>\n", "      <td>0.226027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>truuth_coverage</th>\n", "      <td>0.401416</td>\n", "      <td>0.470937</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>inhouse_no_truuth</th>\n", "      <td>207.000000</td>\n", "      <td>174.000000</td>\n", "      <td>33.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>truuth_no_inhouse</th>\n", "      <td>68.000000</td>\n", "      <td>68.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>num_claim</th>\n", "      <td>989.000000</td>\n", "      <td>843.000000</td>\n", "      <td>146.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>num_claim_by_truuth</th>\n", "      <td>397.000000</td>\n", "      <td>397.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        overall  single_file  multi_file\n", "tp                   525.000000   494.000000   31.000000\n", "fp                    11.000000     9.000000    2.000000\n", "tn                   328.000000   229.000000   99.000000\n", "fn                   125.000000   111.000000   14.000000\n", "inhouse_acc            0.979478     0.982107    0.939394\n", "inhouse_coverage       0.541962     0.596679    0.226027\n", "truuth_coverage        0.401416     0.470937    0.000000\n", "inhouse_no_truuth    207.000000   174.000000   33.000000\n", "truuth_no_inhouse     68.000000    68.000000    0.000000\n", "num_claim            989.000000   843.000000  146.000000\n", "num_claim_by_truuth  397.000000   397.000000    0.000000"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["parse(df)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9919678714859438"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["494/(494+4)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9905660377358491"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["525/(525+5)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}