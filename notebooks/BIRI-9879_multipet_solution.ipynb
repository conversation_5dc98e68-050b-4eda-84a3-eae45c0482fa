{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BIRI-9879_multipet_solution\n", "\n", "## T<PERSON><PERSON>\n", "\n", "1. get all the pet names for the same policy holder,  making a dict InsuredContactNo → list of pet names\n", "\n", "2. get the policy no of each claim, using claim no → InsuredContactNo\n", "\n", "3. fuzzy matching pet names from the content.\n", "\n", "4. if more than one matched, Multi-Pet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Preparation\n", "1. get all the pet names for the same policy holder,  making a dict InsuredContactNo → list of pet names\n", "\n", "2. get the policy no of each claim, using claim no → InsuredContactNo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import sys\n", "\n", "sys.path.append(\"..\")\n", "\n", "# package import\n", "import json\n", "import pickle as pk\n", "import os\n", "import glob\n", "import time\n", "from dotenv import load_dotenv\n", "from loguru import logger\n", "import pandas as pd\n", "from pathlib import Path\n", "from typing import List, Dict, Tuple, Optional, Union\n", "from tqdm import tqdm\n", "import fitz  # PyMuPDF\n", "from paddleocr import PaddleOCR, draw_ocr\n", "from fitz import FileDataError\n", "\n", "\n", "load_dotenv()\n", "\n", "pd.set_option(\"display.max_columns\", None)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from src.utils.storage import Storage\n", "from src.utils.constants import StorageType"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sql_storage = Storage(type=StorageType.SQL, server=\"*********\", database=\"FinanceDB\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# insuredcontactno2animalname_sql_script = \"\"\"\n", "# SELECT p.<PERSON>, p<PERSON>, p.<PERSON>\n", "# FROM  [BIA].[dbo].[STAGE_PolicyCurrent] p\n", "# \"\"\"\n", "\n", "# claimno2insuredcontactno_sql_script = \"\"\"\n", "# SELECT c<PERSON>, c<PERSON>, c.<PERSON>\n", "# FROM [BIA].[dbo].[BaseData_Claim_Summary] c\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# insuredcontactno2animalname_df = sql_storage.pull(query=insuredcontactno2animalname_sql_script)\n", "# claimno2insuredcontactno_df = sql_storage.pull(query=claimno2insuredcontactno_sql_script)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# insuredcontactno2animalname_df.shape, claimno2insuredcontactno_df.shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# insuredcontactno2animalname_df.to_csv(\"../data/insuredcontactno2animalname.csv\", index=False)\n", "# claimno2insuredcontactno_df.to_csv(\"../data/claimno2insuredcontactno.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# sql_script = \"\"\"\n", "# SELECT c<PERSON>, c<PERSON>, p<PERSON>, p<PERSON>, c.<PERSON> as ActualPolicyNo\n", "# FROM [BIA].[dbo].[BaseData_Claim_Summary] c\n", "# LEFT JOIN [BIA].[dbo].[STAGE_PolicyCurrent] p\n", "# ON c.InsuredContactNo= p.InsuredContactNo\n", "# \"\"\"\n", "sql_script = \"\"\"\n", "IF OBJECT_ID('tempdb..#claimInfo') IS NOT NULL DROP TABLE #claimInfo\n", "SELECT c<PERSON>, c.<PERSON>, c.<PERSON>\n", "INTO #claimInfo\n", "FROM [BIA].[dbo].[BaseData_Claim_Summary] c\n", "GROUP BY c<PERSON>, c.<PERSON>, c.<PERSON>o\n", "\n", "SELECT c<PERSON>, c<PERSON>, c.<PERSON>, p.<PERSON>, p.<PERSON>, p.FullP<PERSON>y<PERSON>, case when c.PolicyNo=p.PolicyNo then 1 else 0 end as under_policy\n", "FROM #claimInfo c\n", "LEFT JOIN [BIA].[dbo].[STAGE_PolicyCurrent] p\n", "ON c.InsuredContactNo= p.InsuredContactNo\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:11:43.304\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m7\u001b[0m - \u001b[1mpolicy_df shape is (16951770, 7)\u001b[0m\n"]}], "source": ["# load policy info\n", "try:\n", "    policy_df = pd.read_csv(\"../data/claimno_policy_info.csv\")\n", "except FileNotFoundError:\n", "    policy_df = sql_storage.pull(query=sql_script)\n", "    policy_df.to_csv(\"../data/claimno_policy_info.csv\", index=False)\n", "logger.info(f\"policy_df shape is {policy_df.shape}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>InsuredContactNo</th>\n", "      <th>PolicyNo</th>\n", "      <th>AnimalName</th>\n", "      <th>PolicyNo.1</th>\n", "      <th>FullPolicyNo</th>\n", "      <th>under_policy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>*********</td>\n", "      <td>1974376</td>\n", "      <td>Leo</td>\n", "      <td>1607286</td>\n", "      <td>**********</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>*********</td>\n", "      <td>1974376</td>\n", "      <td>Lady</td>\n", "      <td>1974376</td>\n", "      <td>MB19743760</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>*********</td>\n", "      <td>1974376</td>\n", "      <td>Leo</td>\n", "      <td>0703288</td>\n", "      <td>MB07032886</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7566094</td>\n", "      <td>CT1288005</td>\n", "      <td>1582617</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1529770</td>\n", "      <td>WW15297704</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7566094</td>\n", "      <td>CT1288005</td>\n", "      <td>1582617</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1518394</td>\n", "      <td>WW15183942</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C7593406</td>\n", "      <td>CT0454595</td>\n", "      <td>1831732</td>\n", "      <td>Crosby</td>\n", "      <td>0266149</td>\n", "      <td>RS02661494</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C7593406</td>\n", "      <td>CT0454595</td>\n", "      <td>1831732</td>\n", "      <td><PERSON></td>\n", "      <td>1831732</td>\n", "      <td>PI18317320</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C7392493</td>\n", "      <td>CT0575863</td>\n", "      <td>0459437</td>\n", "      <td>Taurus</td>\n", "      <td>0459437</td>\n", "      <td>PI04594376</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C7444811</td>\n", "      <td>CT1056294</td>\n", "      <td>1809811</td>\n", "      <td><PERSON></td>\n", "      <td>1809811</td>\n", "      <td>PC18098116</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C7446030</td>\n", "      <td>CT0473726</td>\n", "      <td>0445806</td>\n", "      <td><PERSON></td>\n", "      <td>0295490</td>\n", "      <td>BW02954902</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ClaimNo InsuredContactNo PolicyNo AnimalName PolicyNo.1 FullPolicyNo  \\\n", "0  ********        *********  1974376        Leo    1607286   **********   \n", "1  ********        *********  1974376       Lady    1974376   MB19743760   \n", "2  ********        *********  1974376        Leo    0703288   MB07032886   \n", "3  C7566094        CT1288005  1582617      Cosmo    1529770   WW15297704   \n", "4  C7566094        CT1288005  1582617      Cosmo    1518394   WW15183942   \n", "5  C7593406        CT0454595  1831732     Crosby    0266149   RS02661494   \n", "6  C7593406        CT0454595  1831732      Henry    1831732   PI18317320   \n", "7  C7392493        CT0575863  0459437     Taurus    0459437   PI04594376   \n", "8  C7444811        CT1056294  1809811       Roxy    1809811   PC18098116   \n", "9  C7446030        CT0473726  0445806      Henry    0295490   BW02954902   \n", "\n", "   under_policy  \n", "0             0  \n", "1             1  \n", "2             0  \n", "3             0  \n", "4             0  \n", "5             0  \n", "6             1  \n", "7             1  \n", "8             1  \n", "9             0  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["policy_df.head(n=10)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["policy_df[\"AnimalName\"] = policy_df[\"AnimalName\"].fillna(\"\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def parse_policy_info(data_df=policy_df):\n", "    ans = {}\n", "    for info in data_df.to_dict(orient=\"records\"):\n", "        claimno = info[\"ClaimNo\"]\n", "\n", "        animal_name = info[\"AnimalName\"].strip()#.lower().capitalize()\n", "        under_policy = info[\"under_policy\"]\n", "        if claimno not in ans:\n", "            tmp = {}\n", "            tmp[\"ClaimNo\"] = claimno\n", "            tmp[\"ClaimAnimalName\"] = \"\"\n", "            tmp[\"PolicyAnimalNames\"] = [animal_name]\n", "            ans[claimno] = tmp\n", "        else:\n", "            ans[claimno][\"PolicyAnimalNames\"].append(animal_name)\n", "\n", "        if under_policy:\n", "            ans[claimno][\"ClaimAnimalName\"] = animal_name\n", "    for k, v in ans.items():\n", "        animal_name_list_candidate = list(set(v[\"PolicyAnimalNames\"]))\n", "        animal_name_list_lower = set()\n", "        animal_name_list = []\n", "        for an in animal_name_list_candidate:\n", "            if an.lower() not in animal_name_list_lower:\n", "                animal_name_list.append(an)\n", "                animal_name_list_lower.add(an.lower())\n", "        v[\"PolicyAnimalNames\"] = animal_name_list\n", "    return ans"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["claim2policy_info = parse_policy_info(policy_df)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ClaimNo': 'C0594784', 'ClaimAnimalName': '', 'PolicyAnimalNames': ['']}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["claim2policy_info[\"C0594784\"]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# sample prefix\n", "sample_prefix1 = \"1000_600\"\n", "# Data location\n", "RAW_DATA_FOLDER1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix1}_samples_DI\"\n", "DATA_FOLDER1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix1}_samples_DI_split_pages\"\n", "RAW_DATA_STAT_FILE1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix1}_samples_DI.csv\"\n", "OUTPUT_DATA_FOLDER1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix1}_samples_DI_res\"\n", "UPM_GROUND_TRUTH_PATH1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix1}_SourceOfTruth.xlsx\"\n", "PADDLEOCR_RES_PATH1 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix1}_samples_paddleocr.csv\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# load Azure Document Intelligence results from OUTPUT_DATA_FOLDER\n", "def load_document_intelligence_res(res_data_folder: str = OUTPUT_DATA_FOLDER1, raw_data_folder: str = DATA_FOLDER1) -> List[Dict]:\n", "    try:\n", "        with open(os.path.join(res_data_folder, \"ans.pk\"), \"rb\") as fin:\n", "            ans = pk.load(fin)\n", "    except FileNotFoundError:\n", "        original_file_path_list = sorted(os.listdir(raw_data_folder))\n", "        original_file_path_dict = {}\n", "        for p in original_file_path_list:\n", "            original_file_path_dict[str(Path(p).stem)] = p\n", "        ans = []\n", "        for file_path in sorted(os.listdir(res_data_folder)):\n", "            if file_path.endswith(\".json\"):\n", "                with open(os.path.join(res_data_folder, file_path), \"r\") as fin:\n", "                    ans.append(\n", "                        {\n", "                            \"file_path\": original_file_path_dict[str(Path(file_path).stem)],\n", "                            \"invoice\": json.load(fin),\n", "                            }\n", "                    )\n", "    return ans"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["document_intelligence_res1 = load_document_intelligence_res(OUTPUT_DATA_FOLDER1)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["946"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["len(document_intelligence_res1)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# sample prefix\n", "sample_prefix2 = \"1000_400\"\n", "# Data location\n", "RAW_DATA_FOLDER2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix2}_samples_DI\"\n", "DATA_FOLDER2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix2}_samples_DI_split_pages\"\n", "RAW_DATA_STAT_FILE2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix2}_samples_DI.csv\"\n", "OUTPUT_DATA_FOLDER2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/samples/{sample_prefix2}_samples_DI_res\"\n", "UPM_GROUND_TRUTH_PATH2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix2}_SourceOfTruth.xlsx\"\n", "PADDLEOCR_RES_PATH2 = f\"/workspaces/OCR_in_house/data/OCR_in_house/data/{sample_prefix2}_samples_paddleocr.csv\""]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["document_intelligence_res2 = load_document_intelligence_res(OUTPUT_DATA_FOLDER2)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["475"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(document_intelligence_res2)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["document_intelligence_res = document_intelligence_res1 + document_intelligence_res2"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["1421"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["len(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# CHANGELOG: 04 NOV 2024 add function to verify whether the content contains numbers and puncs only\n", "import string\n", "def is_numbers_and_punctuation(s):\n", "    allowed_chars = set(string.digits + string.punctuation + \" \")\n", "    return all(char in allowed_chars for char in s)\n", "\n", "# parse Azure Document Intelligence results from ans\n", "def parse_document_intelligence_res(data_info: List[Dict] | Dict) -> Dict:\n", "    if isinstance(data_info, Dict):\n", "        data_info = [data_info]\n", "\n", "    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)\n", "\n", "    ans = {}\n", "    for data in tqdm(data_info):\n", "        file_path = data[\"file_path\"]\n", "        invoice = data[\"invoice\"]\n", "        content = invoice[\"content\"]\n", "        invoice_info = []\n", "        for document in invoice[\"documents\"]:\n", "            service_provider = document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\") + \" \" + document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"value\", \"\")\n", "            service_provider = service_provider.strip()\n", "            service_provider_conf = 0.\n", "            service_provider_count = int(document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\") != \"\") + int(document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"value\", \"\") != \"\")\n", "            service_provider_conf = (document[\"fields\"].get(\"VendorName\", {}).get(\"confidence\", 0.) or 0.) + (document[\"fields\"].get(\"VendorAddressRecipient\", {}).get(\"confidence\", 0.) or 0.)\n", "            if service_provider_conf > 0.:\n", "                service_provider_conf /= service_provider_count\n", "            # CHANGELOG: 04 NOV 2024 add service provider address extraction for service provider field fuzzy matching preparation\n", "            service_provider_address = \"\"\n", "            service_provider_address_value = document[\"fields\"].get(\"VendorAddress\", {}).get(\"value\", {})\n", "            service_provider_address_exist = service_provider_address_value.get(\"street_address\", \"\") and (service_provider_address_value.get(\"postal_code\", \"\") or service_provider_address_value.get(\"suburb\", \"\") or service_provider_address_value.get(\"city\", \"\")) \n", "            service_provider_address_content = document[\"fields\"].get(\"VendorAddress\", {}).get(\"content\", \"\").replace(\"\\t\", \" \").replace(\"\\n\", \" \")\n", "            if service_provider_address_exist:\n", "                service_provider_address = service_provider_address_content\n", "\n", "\n", "            invoice_no = document[\"fields\"].get(\"InvoiceId\", {}).get(\"value\", \"\")\n", "            invoice_date = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"value\", \"\") or \"\"\n", "            if not isinstance(invoice_date, str):\n", "                invoice_date = invoice_date.isoformat()\n", "            invoice_total_dict = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"value\", {}) or {}\n", "            invoice_total = invoice_total_dict.get(\"amount\", -1)\n", "\n", "            invoice_no_conf = document[\"fields\"].get(\"InvoiceId\", {}).get(\"confidence\", 0.) or 0.\n", "            invoice_date_conf = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"confidence\", 0.) or 0.\n", "            invoice_total_conf = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"confidence\", 0.) or 0.\n", "\n", "\n", "            treatments = []\n", "            cur_treatment_date = invoice_date\n", "            cur_treatment_date_conf = invoice_date_conf\n", "\n", "            for item in document[\"fields\"].get(\"Items\", {}).get(\"value\", []):\n", "                item_conf = item.get(\"confidence\", 0.) or 0.\n", "\n", "                treatment_date = item.get(\"value\", {}).get(\"Date\", {}).get(\"value\", cur_treatment_date) or \"\"\n", "                if not isinstance(treatment_date, str):\n", "                    treatment_date = treatment_date.isoformat()\n", "                treatment_date_conf = item.get(\"value\", {}).get(\"Date\", {}).get(\"confidence\", cur_treatment_date_conf)\n", "                if treatment_date_conf is None:\n", "                    treatment_date_conf = item_conf\n", "\n", "                if not treatment_date:\n", "                    treatment_date = cur_treatment_date\n", "                    treatment_date_conf = cur_treatment_date_conf\n", "                cur_treatment_date = treatment_date\n", "                cur_treatment_date_conf = treatment_date_conf\n", "\n", "                desc = item.get(\"value\", {}).get(\"Description\", {}).get(\"content\", \"\")\n", "                product = item.get(\"value\", {}).get(\"ProductCode\", {}).get(\"content\", \"\")\n", "                # CHANGELOG: 04 NOV 2024 ignore product if it only contains numbers and puncs.\n", "                if is_numbers_and_punctuation(product.strip()):\n", "                     product = \"\"\n", "                desc_conf = item.get(\"value\", {}).get(\"Description\", {}).get(\"confidence\", item_conf) or item_conf\n", "                product_conf = item.get(\"value\", {}).get(\"ProductCode\", {}).get(\"confidence\", item_conf) or item_conf\n", "                desc_conf = (desc_conf * int(desc!=\"\") + product_conf * int(product!=\"\"))/(int(desc!=\"\") + int(product!=\"\")+1e-7)\n", "                desc = product + \" \" + desc\n", "                desc = desc.strip()\n", "\n", "                # CHANGELOG: 01 NOV 2024 default amount change from -1 to 0. This treatment line would be removed later during post process as amount  == 0\n", "                amount_dict = item.get(\"value\", {}).get(\"Amount\", {}).get(\"value\", {}) or {}\n", "                amount = amount_dict.get(\"amount\", 0)\n", "                amount_conf = item.get(\"value\", {}).get(\"Amount\", {}).get(\"confidence\", 0.)\n", "                if amount_conf is None:\n", "                    amount_conf = item_conf\n", "\n", "\n", "                treatments.append({\"treatment_date\": treatment_date, \n", "                                   \"treatment\": desc, \n", "                                   \"amount\": amount, \n", "                                   \"treatment_date_conf\": treatment_date_conf,\n", "                                   \"treatment_conf\": desc_conf, \n", "                                   \"amount_conf\": amount_conf, \n", "                                   \"treatmentline_conf\": item_conf})\n", "\n", "            if not invoice_date:\n", "                invoice_date = cur_treatment_date\n", "                invoice_date_conf = cur_treatment_date_conf\n", "\n", "            if not isinstance(invoice_date, str):\n", "                invoice_date = invoice_date.isoformat()\n", "\n", "            invoice_info.append(\n", "                {\n", "                    \"service_provider\": service_provider,\n", "                    \"service_provider_address\": service_provider_address,\n", "                    \"content\": content,\n", "                    \"invoice_no\": invoice_no,\n", "                    \"invoice_date\": invoice_date,\n", "                    \"invoice_total\": invoice_total,\n", "                    \"service_provider_conf\": service_provider_conf,\n", "                    \"invoice_no_conf\": invoice_no_conf,\n", "                    \"invoice_date_conf\": invoice_date_conf,\n", "                    \"invoice_total_conf\": invoice_total_conf,\n", "                    \"treatments\": treatments,\n", "                    \"raw\": invoice\n", "                }\n", "            )\n", "            \n", "        ans[file_path] = invoice_info\n", "\n", "\n", "    return ans"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1421/1421 [00:00<00:00, 25185.00it/s]\n"]}], "source": ["document_intelligence_parsed_res = parse_document_intelligence_res(document_intelligence_res)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# group split di results\n", "def gather_document_intelligence_res(info: Dict) -> Dict:\n", "    ans = {}\n", "    for k, v in info.items():\n", "        if k.lower().endswith(\"pdf\"):\n", "            if len(v) == 0 or len(v[0][\"treatments\"]) ==0:\n", "                continue\n", "            stem = Path(k).stem\n", "            stem = \"-\".join(stem.split(\"-\")[:-1])\n", "            # if str(stem[-1]).isdigit() and str(stem[-2] == \"-\"):\n", "            # stem = str(stem)[:-2]\n", "            if f\"{stem}.{k[-3:]}\" not in ans:\n", "                ans[f\"{stem}.{k[-3:]}\"] = [v[0]]\n", "            else:\n", "                ans[f\"{stem}.{k[-3:]}\"].append(v[0])\n", "        else:\n", "            ans[k] = v\n", "    return ans"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["document_intelligence_parsed_res = gather_document_intelligence_res(document_intelligence_parsed_res)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["1223"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["len(document_intelligence_parsed_res)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["(14490, 50)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# load MP label\n", "ANNOTATION_PATH = f\"/workspaces/OCR_in_house/data/OCR_in_house/res/1000_2024_sample_evaluation/1000_samples_DI_rule_res.xlsx\"\n", "anno_df = pd.read_excel(ANNOTATION_PATH)\n", "anno_df.shape"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def get_claim_mp_label(anno_df=anno_df) -> Dict:\n", "    anno_df[\"NOTE\"] = anno_df[\"NOTE\"].fillna(\"\")\n", "    ans = {}\n", "    for info in anno_df.to_dict(orient=\"records\"):\n", "        claimno = info[\"claimno\"]\n", "        docfile = info[\"docfile\"]\n", "        note = info[\"NOTE\"]\n", "        if claimno not in ans:\n", "            ans[claimno] = {}\n", "        if docfile not in ans[claimno]:\n", "            ans[claimno][docfile] = {\"is_mp\": 0, \"notes\": []}\n", "        ans[claimno][docfile][\"notes\"].append(note)\n", "    for claimno, cinfo in ans.items():\n", "        for docfile, finfo in cinfo.items():\n", "            if any(\"MP\" in note for note in finfo[\"notes\"]):\n", "                finfo[\"is_mp\"] = 1\n", "\n", "    return ans"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["mp_labels = get_claim_mp_label(anno_df)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# gather all\n", "def prepare_dataset(raw_data=document_intelligence_parsed_res, claim_policy_info_df=claim2policy_info, labels=mp_labels):\n", "    # raw_data: docfile -> [{\"content\":\"\", ...}]\n", "    # claim_policy_info: claimno -> {\"ClaimAnimalName\": \"\", \"PolicyAnimalNames\": []}\n", "    # labels: claimno -> {docfile: {\"is_mp\"}}\n", "    ans = {}\n", "    for claimno, cinfo in labels.items():\n", "        claim_policy_info = claim_policy_info_df[claimno]\n", "        ans[claimno] = {}\n", "        ans[claimno][\"policy_info\"] = claim_policy_info\n", "        ans[claimno][\"docfiles\"] = {}\n", "        for docfile, dinfo in cinfo.items():\n", "            content_info = raw_data[docfile]\n", "            content = \"\\n\".join(x[\"content\"] for x in content_info)\n", "            label = dinfo[\"is_mp\"]\n", "            ans[claimno][\"docfiles\"][docfile] = {\"content\": content, \"raw\": [x[\"raw\"] for x in content_info], \"is_mp\": label}\n", "    return ans"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["mp_dataset = prepare_dataset(raw_data=document_intelligence_parsed_res, claim_policy_info_df=claim2policy_info, labels=mp_labels)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Solution\n", "3. fuzzy matching pet names from the content."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"B\\nURNE\\n154-4087SouthernAH\\nE\\n1251 Nepean Highway\\n9584 6100\\nCheltenham VIC 3192\\nBULLD\\nCUSTOMER COPY\\n1251 Nepean Highway,\\nCheltenham, VIC 3192\\nTyro Payments EFTPOS\\nLDOG\\nCLINIC\\nNAB Visa Credit\\nAID: A0000000031010\\nCard: xxxxxxxxxxxx4698 (t)\\nPurchase\\nAUD\\n$786.80\\nAPPROVED\\n00\\nTerminal ID: 3\\nTransaction Ref: 538735\\nAuthorisation No: 243163\\n20 Aug 2024 at 01:28 PM\\nRetain copy for your records\\nFerreira\\nch Road\\n.E VIC 3195\\nTAX INVOICE 20 Aug 2024\\nInvoice # C248204736\\nAnnual Health Check, C2 Vaccination and\\nPatient\\nDate\\nItem\\nQ'tity\\nTotal Inc\\nTequila\\n20/8/2024\\nVaccination C2\\n1\\n$125.00\\nTequila\\n20/8/2024 Heartworm (Proheart) 10-20KG (ml)\\n1\\n$110.00\\nTequila\\n20/8/2024 Cytopoint 20mg (single vial)\\n1\\n$158.40\\nTequila\\n20/8/2024 HANDOUT\\n1\\n$0.00\\nBrandy\\n20/8/2024 Vaccination C2\\n1\\n$125.00\\nBrandy\\n20/8/2024 Heartworm (Proheart) 10-20KG (ml)\\n1\\n$110.00\\nBrandy\\n20/8/2024\\nCytopoint 20mg (single vial)\\n1\\n$158.40\\nBrandy\\n20/8/2024 HANDOUT\\n1\\n$0.00\\nTotal Ex. Tax\\n$715.27\\nTotal GST\\n$71.53\\nTOTAL\\n$786.80\\nThe following payments have been received with thanks\\nInv. Date\\nPayment Method\\nPayment\\n20/8/2024\\nCard Payment\\n$786.80\\nACCOUNT BALANCE OUTSTANDING\\n$0.00\""]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_dataset[\"C7623251\"][\"docfiles\"][\"6c45cbae-13ee-4903-a975-dfdb3093f0dc.pdf\"][\"raw\"][0][\"content\"]#[\"pages\"][0][\"lines\"][38]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["from thefuzz import fuzz\n", "from rich import print\n", "\n", "\n", "def trim_appointment_content(content: str) -> str:\n", "    # sentences = content.split(\"\\n\")\n", "    # sentences = [sentence for sentence in sentences if \"next appointment\" not in sentence.lower()]\n", "    # return \"\\n\".join(sentences)\n", "    appointment_word_idx = content.replace(\"\\n\", \" \").lower().find(\"next appointment\")\n", "    return content[:appointment_word_idx]\n", "\n", "def get_name_box(raw_data: List, animal_name: str) -> List[List[Dict[str, float]]]:\n", "    ans = []\n", "    for raw_doc_data in raw_data:\n", "        pages = raw_doc_data[\"pages\"]\n", "        for page in pages:\n", "            lines = page[\"words\"]\n", "            for line in lines:\n", "                if line[\"content\"] == animal_name:\n", "                    ans.append(line[\"polygon\"])\n", "    return ans\n", "\n", "def get_content_same_column_via_box(raw_data: List, box: List[Dict[str, float]], acceptable_x_adjust: float = 0., acceptable_height_adjust: float = 0.05) -> List[str]:\n", "    def acceptable_box(box_data: List[Dict[str, float]], x_left: float, x_right: float, min_height: float, max_height: float) -> bool:\n", "        return x_left <= box_data[0][\"x\"] <= x_right and min_height <= abs(box_data[-1][\"y\"] - box_data[0][\"y\"]) <= max_height\n", "\n", "    ans = []\n", "    x_left = box[0][\"x\"]\n", "    x_right = box[1][\"x\"]\n", "    y_top = box[0][\"y\"]\n", "    y_bottom = box[-1][\"y\"]\n", "    box_height = abs(y_bottom - y_top)\n", "    min_box_height = (1 - acceptable_height_adjust) * box_height\n", "    max_box_height = (1 + acceptable_height_adjust) * box_height\n", "\n", "    for raw_doc_data in raw_data:\n", "        pages = raw_doc_data[\"pages\"]\n", "        for page in pages:\n", "            lines = page[\"words\"]\n", "            for line in lines:\n", "                if acceptable_box(line[\"polygon\"], x_left * (1 - acceptable_x_adjust), x_left * (1 + acceptable_x_adjust), min_box_height, max_box_height):\n", "                    # logger.info(f\"{x_left * (1 - acceptable_x_adjust)}, {x_right * (1 + acceptable_x_adjust)}\")\n", "                    # logger.info(line)\n", "                    ans.append(line[\"content\"])\n", "    return ans\n", "\n", "def fuzzy_pos_match(raw_data: List, animal_name: str, acceptable_x_adjust:float = 0.05, acceptable_height_adjust: float = 0.05) -> List[str]:\n", "    ans = []\n", "\n", "    for target_animal_name in [animal_name.lower(), animal_name.upper(), animal_name.lower().capitalize()]:\n", "        animal_name_boxes = get_name_box(raw_data, target_animal_name)\n", "        for box in animal_name_boxes:\n", "            names = get_content_same_column_via_box(raw_data, box, acceptable_x_adjust=acceptable_x_adjust, acceptable_height_adjust=acceptable_height_adjust)\n", "            ans.extend(names)\n", "    return list({n for n in ans if n.isalpha() and len(n)>=4 and n.lower() not in [\"animal\", \"patient\"]})\n", "\n", "\n", "def fuzzy_name_match(content: str, animal_name: str, use_split_name: bool = False):\n", "    score = max(fuzz.partial_ratio(animal_name, content),\n", "                fuzz.partial_ratio(animal_name.lower(), content),\n", "                fuzz.partial_ratio(animal_name.lower().capitalize(), content),\n", "                fuzz.partial_ratio(animal_name.upper(), content),\n", "                fuzz.partial_ratio(f\" {animal_name.replace(' ', '')} \", content),\n", "                fuzz.partial_ratio(f\" {animal_name.replace(' ', '').lower()} \", content),\n", "                fuzz.partial_ratio(f\" {animal_name.replace(' ', '').lower().capitalize()} \", content),\n", "                fuzz.partial_ratio(f\" {animal_name.replace(' ', '').upper()} \", content)\n", "                )\n", "    if use_split_name and \" \" in animal_name.strip():\n", "        # need to split name for checking\n", "        split_scores = max(fuzz.partial_ratio(f\" {animal_name.strip().split()[0]} \", content),\n", "                           fuzz.partial_ratio(f\" {animal_name.strip().split()[0].lower()} \", content),\n", "                           fuzz.partial_ratio(f\" {animal_name.strip().split()[0].lower().capitalize()} \", content),\n", "                           fuzz.partial_ratio(f\" {animal_name.strip().split()[0].upper()} \", content)\n", "                            )\n", "        score = max(score, split_scores)\n", "    return score\n", "\n", "def sim_score_cal(content: str, claim_animal_name: str, animal_names: List[str], use_split_name: bool = False):\n", "    content = content.replace(\"\\n\", \" \")\n", "    claim_animal_name = f\" {claim_animal_name} \"\n", "    animal_names = [f\" {animal_name} \" for animal_name in animal_names]\n", "    claim_animal_sim_score = fuzzy_name_match(content, claim_animal_name, use_split_name)\n", "\n", "    # fuzz.partial_ratio(claim_animal_name, content)\n", "    if len(animal_names) > 1:\n", "        partial_sim_scores = [fuzzy_name_match(content, animal_name, use_split_name)\n", "                                for animal_name in animal_names]\n", "        return claim_animal_sim_score, partial_sim_scores\n", "    return claim_animal_sim_score, [claim_animal_sim_score]\n", "\n", "def multipet_detection(data: Dict, \n", "                       claim_animal_name: str, \n", "                       animal_names: List[str], \n", "                       trim_appointment: bool = False,\n", "                       use_split_name: bool = False,\n", "                       threshold: int = 80, \n", "                       acceptable_x_adjust:float = 0.05, \n", "                       acceptable_height_adjust: float = 0.05):\n", "    # get information from the data\n", "    raw_data: List = data[\"raw\"]\n", "    content: str = [x[\"content\"].replace(\"\\n\", \" \") for x in raw_data]#data[\"content\"]\n", "    if trim_appointment:\n", "        content = [trim_appointment_content(c) for c in content]\n", "    content = \" \".join(content)\n", "    pred_mp_label = 0\n", "    fuzzy_pos_name_pred_mp_label = 0\n", "    rule_pred_mp_label = 0\n", "\n", "    # content = trim_appointment_content(content)\n", "    # get fuzzy match scores\n", "    claim_animal_sim_score, partial_sim_scores = sim_score_cal(content, claim_animal_name, animal_names, use_split_name)\n", "    # get fuzzy pos match animal names\n", "    potential_animal_names = fuzzy_pos_match(raw_data, claim_animal_name, acceptable_x_adjust=acceptable_x_adjust, acceptable_height_adjust=acceptable_height_adjust)\n", "\n", "    # score judgement\n", "    claim_animal_sim_score = max(threshold, claim_animal_sim_score)\n", "    if sum(partial_sim_score >= claim_animal_sim_score for partial_sim_score in partial_sim_scores) > 1:\n", "        pred_mp_label = 1\n", "        fuzzy_pos_name_pred_mp_label = 1\n", "        rule_pred_mp_label = 1\n", "    # extracted name judgement\n", "    if len(potential_animal_names) > 1:\n", "        fuzzy_pos_name_pred_mp_label = 1\n", "    # rule judgement\n", "    if content.count(\"PATIENT:\") > 1 or content.count(\"PATIENT :\") > 1 or content.count(\"PATIENT NAME:\") > 1 or content.count(\"PATIENT NAME :\") > 1 or content.count(\"Patient:\") > 1 or content.count(\"Patient :\") > 1 or content.count(\"Patient NAME:\") > 1 or content.count(\"Patient NAME :\") > 1 or content.count(\"PATIENT\\n\") > 1 or content.count(\"PATIENT \\n\") > 1 or content.count(\"PATIENT NAME\\n\") > 1 or content.count(\"PATIENT NAME \\n\") > 1 or content.count(\"Patient\\n\") > 1 or content.count(\"Patient \\n\") > 1 or content.count(\"Patient NAME\\n\") > 1 or content.count(\"Patient NAME \\n\") > 1:\n", "        rule_pred_mp_label = 1\n", "    return claim_animal_sim_score, partial_sim_scores, potential_animal_names, pred_mp_label, fuzzy_pos_name_pred_mp_label, rule_pred_mp_label"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["for claimno, cinfo in mp_dataset.items():\n", "    for docfile, dinfo in cinfo[\"docfiles\"].items():\n", "        dinfo[\"res\"] = multipet_detection(dinfo, #[\"content\"] \n", "                                          cinfo[\"policy_info\"][\"ClaimAnimalName\"],\n", "                                          cinfo[\"policy_info\"][\"PolicyAnimalNames\"],\n", "                                          threshold=76,\n", "                                          trim_appointment=False,\n", "                                          use_split_name=True\n", "                                          )\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["mp_performance = {\n", "    \"sim_score\": {\n", "        \"tp\": 0,\n", "        \"fp\": 0,\n", "        \"fn\": 0,\n", "        \"tn\": 0\n", "    },\n", "    \"sim_score_name\": {\n", "        \"tp\": 0,\n", "        \"fp\": 0,\n", "        \"fn\": 0,\n", "        \"tn\": 0\n", "    },\n", "    \"sim_score_rule\": {\n", "        \"tp\": 0,\n", "        \"fp\": 0,\n", "        \"fn\": 0,\n", "        \"tn\": 0\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["for claimno, cinfo in mp_dataset.items():\n", "    for docfile, dinfo in cinfo[\"docfiles\"].items():\n", "        mp_label = dinfo[\"is_mp\"]\n", "        _, _, _, sim_score_res, sim_score_name_res, sim_score_rul_res = dinfo[\"res\"]\n", "        mp_performance[\"sim_score\"][\"tp\"] += (mp_label == 1 and sim_score_res == 1)\n", "        mp_performance[\"sim_score\"][\"fp\"] += (mp_label == 0 and sim_score_res == 1)\n", "        mp_performance[\"sim_score\"][\"fn\"] += (mp_label == 1 and sim_score_res == 0)\n", "        mp_performance[\"sim_score\"][\"tn\"] += (mp_label == 0 and sim_score_res == 0)\n", "\n", "        mp_performance[\"sim_score_name\"][\"tp\"] += (mp_label == 1 and sim_score_name_res == 1)\n", "        mp_performance[\"sim_score_name\"][\"fp\"] += (mp_label == 0 and sim_score_name_res == 1)\n", "        mp_performance[\"sim_score_name\"][\"fn\"] += (mp_label == 1 and sim_score_name_res == 0)\n", "        mp_performance[\"sim_score_name\"][\"tn\"] += (mp_label == 0 and sim_score_name_res == 0)\n", "\n", "        mp_performance[\"sim_score_rule\"][\"tp\"] += (mp_label == 1 and sim_score_rul_res == 1)\n", "        mp_performance[\"sim_score_rule\"][\"fp\"] += (mp_label == 0 and sim_score_rul_res == 1)\n", "        mp_performance[\"sim_score_rule\"][\"fn\"] += (mp_label == 1 and sim_score_rul_res == 0)\n", "        mp_performance[\"sim_score_rule\"][\"tn\"] += (mp_label == 0 and sim_score_rul_res == 0)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'sim_score': {'tp': 37, 'fp': 13, 'fn': 5, 'tn': 1166},\n", " 'sim_score_name': {'tp': 38, 'fp': 371, 'fn': 4, 'tn': 808},\n", " 'sim_score_rule': {'tp': 40, 'fp': 17, 'fn': 2, 'tn': 1162}}"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_performance"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.695\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mSim Score: Precision: 0.74, Recall: 0.8809523809523809, F1: 0.8043478260869565\u001b[0m\n"]}], "source": ["sim_score_precision = mp_performance[\"sim_score\"][\"tp\"]/(mp_performance[\"sim_score\"][\"tp\"] + mp_performance[\"sim_score\"][\"fp\"])\n", "sim_score_recall = mp_performance[\"sim_score\"][\"tp\"]/(mp_performance[\"sim_score\"][\"tp\"] + mp_performance[\"sim_score\"][\"fn\"])\n", "sim_score_f1 = 2 * sim_score_precision * sim_score_recall / (sim_score_precision + sim_score_recall)\n", "logger.info(f\"Sim Score: Precision: {sim_score_precision}, Recall: {sim_score_recall}, F1: {sim_score_f1}\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.748\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mSim Score Name: Precision: 0.09290953545232274, Recall: 0.9047619047619048, F1: 0.16851441241685144\u001b[0m\n"]}], "source": ["sim_score_name_precision = mp_performance[\"sim_score_name\"][\"tp\"]/(mp_performance[\"sim_score_name\"][\"tp\"] + mp_performance[\"sim_score_name\"][\"fp\"])\n", "sim_score_name_recall = mp_performance[\"sim_score_name\"][\"tp\"]/(mp_performance[\"sim_score_name\"][\"tp\"] + mp_performance[\"sim_score_name\"][\"fn\"])\n", "sim_score_name_f1 = 2 * sim_score_name_precision * sim_score_name_recall / (sim_score_name_precision + sim_score_name_recall)\n", "logger.info(f\"Sim Score Name: Precision: {sim_score_name_precision}, Recall: {sim_score_name_recall}, F1: {sim_score_name_f1}\")"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.799\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mSim Score Rule: Precision: 0.7017543859649122, Recall: 0.9523809523809523, F1: 0.8080808080808081\u001b[0m\n"]}], "source": ["sim_score_rule_precision = mp_performance[\"sim_score_rule\"][\"tp\"]/(mp_performance[\"sim_score_rule\"][\"tp\"] + mp_performance[\"sim_score_rule\"][\"fp\"])\n", "sim_score_rule_recall = mp_performance[\"sim_score_rule\"][\"tp\"]/(mp_performance[\"sim_score_rule\"][\"tp\"] + mp_performance[\"sim_score_rule\"][\"fn\"])\n", "sim_score_rule_f1 = 2 * sim_score_rule_precision * sim_score_rule_recall / (sim_score_rule_precision + sim_score_rule_recall)\n", "logger.info(f\"Sim Score Rule: Precision: {sim_score_rule_precision}, Recall: {sim_score_rule_recall}, F1: {sim_score_rule_f1}\")"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'sim_score': {'tp': 37, 'fp': 13, 'fn': 5, 'tn': 1166},\n", " 'sim_score_name': {'tp': 38, 'fp': 371, 'fn': 4, 'tn': 808},\n", " 'sim_score_rule': {'tp': 40, 'fp': 17, 'fn': 2, 'tn': 1162}}"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["mp_performance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* FP"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.925\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7589207, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7589207'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Lola'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7589207'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Cresent'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'South'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Marilyn'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Annual'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'Cresent'\u001b[0m, \u001b[32m'South'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'Annual'\u001b[0m, \u001b[32m'Charlie'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">40f2526f-f8ae-4be6-8361-228dfe293c7a</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Cresent'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'South'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Marilyn'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Annual'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m40f2526f-f8ae-4be6-8361-228dfe293c7a\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Cresent'\u001b[0m, \u001b[32m'South'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'Annual'\u001b[0m, \u001b[32m'Charlie'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3331</span> Pacific Highway\n", "Springwood, Qld. <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4127</span>\n", "Phone: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3208</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9233</span>\n", "ALBERT\n", "ABN: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">98</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">738</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">704</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">049</span>\n", "ANIMAL HOSPITAL\n", "<PERSON> <PERSON>\n", "ABN:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">98</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">738</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">704</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">049</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span> Bauple Cresent\n", "Rochedale South QLD <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4123</span>\n", "Tax Invoice for Professional Services\n", "Date\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "J\n", "Transaction No\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">579254</span>\n", "<span style=\"font-weight: bold\">(</span>Ref: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18269</span><span style=\"font-weight: bold\">)</span>\n", "Reference\n", "<PERSON>\n", "Patient\n", "<PERSON>\n", "Details\n", "Service Provided\n", "No\n", "Amount\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Vaccination\n", "Triennial C3 + Annual B3 Vaccine &amp;\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">135.00</span>\n", "Wellness Check\n", "Total:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">135.00</span>\n", "Includes Tax of:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12.27</span>\n", "Balance owing $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span> at <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Next Appointment Details:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">19</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">5:00</span> PM : Recheck for Lola\n", "<EMAIL>\n", "www.albertanimalhospital.com.au\n", "</pre>\n"], "text/plain": ["\u001b[1;36m3331\u001b[0m Pacific Highway\n", "Springwood, Qld. \u001b[1;36m4127\u001b[0m\n", "Phone: \u001b[1;36m07\u001b[0m \u001b[1;36m3208\u001b[0m \u001b[1;36m9233\u001b[0m\n", "ALBERT\n", "ABN: \u001b[1;36m98\u001b[0m \u001b[1;36m738\u001b[0m \u001b[1;36m704\u001b[0m \u001b[1;36m049\u001b[0m\n", "ANIMAL HOSPITAL\n", "<PERSON> <PERSON>\n", "ABN:\u001b[1;36m98\u001b[0m \u001b[1;36m738\u001b[0m \u001b[1;36m704\u001b[0m \u001b[1;36m049\u001b[0m\n", "\u001b[1;36m14\u001b[0m <PERSON><PERSON><PERSON> C<PERSON>ent\n", "Rochedale South QLD \u001b[1;36m4123\u001b[0m\n", "Tax Invoice for Professional Services\n", "Date\n", "\u001b[1;36m31\u001b[0m/\u001b[1;36m07\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "J\n", "Transaction No\n", "\u001b[1;36m579254\u001b[0m\n", "\u001b[1m(\u001b[0mRef: \u001b[1;36m18269\u001b[0m\u001b[1m)\u001b[0m\n", "Reference\n", "<PERSON>\n", "Patient\n", "<PERSON>\n", "Details\n", "Service Provided\n", "No\n", "Amount\n", "\u001b[1;36m31\u001b[0m/\u001b[1;36m07\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Vaccination\n", "Triennial C3 + Annual B3 Vaccine &\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m135.00\u001b[0m\n", "Wellness Check\n", "Total:\n", "\u001b[1;36m135.00\u001b[0m\n", "Includes Tax of:\n", "\u001b[1;36m12.27\u001b[0m\n", "Balance owing $\u001b[1;36m0.00\u001b[0m at \u001b[1;36m31\u001b[0m/\u001b[1;36m07\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Next Appointment Details:\n", "\u001b[1;36m19\u001b[0m/\u001b[1;36m08\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1;92m5:00\u001b[0m PM : Recheck for Lola\n", "<EMAIL>\n", "www.albertanimalhospital.com.au\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">af39763b-e301-4693-b5e5-58fd27aed4b1</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">76</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">50</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">67</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93maf39763b-e301-4693-b5e5-58fd27aed4b1\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m76\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m50\u001b[0m, \u001b[1;36m67\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">TAX INVOICE\n", "Budget Pet Products\n", "ABN: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">65</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">988</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">927</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">654</span>\n", "Shipping Details:\n", "Authority To Leave\n", "<PERSON>\n", "No\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span> Bauple Crescent\n", "ROCHEDALE\n", "Delivery Instructions\n", "Rochedale South, Queensland, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4123</span>\n", "Leave out of the weather\n", "AUSTRALIA\n", "Billing Details:\n", "Invoice Order Number:\n", "#<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5810302</span>\n", "<PERSON>\n", "Invoice Paid Date:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span> Bauple Crescent\n", "ROCHEDALE, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4123</span>, Queensland\n", "Order Total Amount\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">70.11</span>\n", "Rochedale South\n", "AUSTRALIA\n", "Order Status\n", "Paid\n", "Order summary\n", "Product\n", "Price\n", "Quantity\n", "Totals\n", "Simparica TRIO For Dogs <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5.1</span>-10kg Orange Small <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45.12</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45.12</span>\n", "Chews\n", "Aloveen Oatmeal Shampoo and Conditioner Starter Pack\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24.99</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24.99</span>\n", "For Dogs And Cats\n", "Subtotal\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">70.11</span>\n", "Postage + Handling\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "GST included in total\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6.37</span>\n", "Total\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">70.11</span>\n", "Thank you for your business.\n", "</pre>\n"], "text/plain": ["TAX INVOICE\n", "Budget Pet Products\n", "ABN: \u001b[1;36m65\u001b[0m \u001b[1;36m988\u001b[0m \u001b[1;36m927\u001b[0m \u001b[1;36m654\u001b[0m\n", "Shipping Details:\n", "Authority To Leave\n", "<PERSON>\n", "No\n", "\u001b[1;36m14\u001b[0m <PERSON><PERSON><PERSON> Crescent\n", "ROCHEDALE\n", "Delivery Instructions\n", "Rochedale South, Queensland, \u001b[1;36m4123\u001b[0m\n", "Leave out of the weather\n", "AUSTRALIA\n", "Billing Details:\n", "Invoice Order Number:\n", "#\u001b[1;36m5810302\u001b[0m\n", "<PERSON>\n", "Invoice Paid Date:\n", "\u001b[1;36m16\u001b[0m/\u001b[1;36m08\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m14\u001b[0m <PERSON><PERSON><PERSON> Crescent\n", "ROCHEDALE, \u001b[1;36m4123\u001b[0m, Queensland\n", "Order Total Amount\n", "$\u001b[1;36m70.11\u001b[0m\n", "Rochedale South\n", "AUSTRALIA\n", "Order Status\n", "Paid\n", "Order summary\n", "Product\n", "Price\n", "Quantity\n", "Totals\n", "Simparica TRIO For Dogs \u001b[1;36m5.1\u001b[0m-10kg Orange Small \u001b[1;36m3\u001b[0m\n", "$\u001b[1;36m45.12\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n", "$\u001b[1;36m45.12\u001b[0m\n", "Chews\n", "Aloveen Oatmeal Shampoo and Conditioner Starter Pack\n", "$\u001b[1;36m24.99\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n", "$\u001b[1;36m24.99\u001b[0m\n", "For Dogs And Cats\n", "Subtotal\n", "$\u001b[1;36m70.11\u001b[0m\n", "Postage + Handling\n", "$\u001b[1;36m0.00\u001b[0m\n", "GST included in total\n", "$\u001b[1;36m6.37\u001b[0m\n", "Total\n", "$\u001b[1;36m70.11\u001b[0m\n", "Thank you for your business.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.953\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: ********, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'********'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Jessica'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Cleo'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'********'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON><PERSON><PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Cleo'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'outside'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Vaccination'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON><PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'outside'\u001b[0m, \u001b[32m'Vaccination'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">a159b2c6-fe78-435f-adc8-89709c091b3e</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">56</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Beransa'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Cleo'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'outside'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Vaccination'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93ma159b2c6-fe78-435f-adc8-89709c091b3e\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m56\u001b[0m, \u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'<PERSON><PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'outside'\u001b[0m, \u001b[32m'Vaccination'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Larkhill Small Animal Hospital\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> <span style=\"color: #800080; text-decoration-color: #800080\">/</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "ABN: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">96</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">093</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">395</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">254</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1774</span> Mandurah Road\n", "Port Kennedy, WA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6172</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9524</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3838</span>\n", "Client ID:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2513</span>\n", "Mrs <PERSON><PERSON>\n", "Tax Invoice #:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">193842</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16</span> Fairburn Cres\n", ":\n", "<PERSON><PERSON><PERSON><PERSON>, WA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6171</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0400</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">285</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">585</span>\n", "<EMAIL>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21530</span>\n", "Species: Canine\n", "Weight: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30.30</span> kilograms\n", "Patient Name: <PERSON>\n", "Breed: Greyhound\n", "Birthday: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2012</span>\n", "Sex: Neutered Male\n", "Description\n", "Staff Name\n", "Quantity\n", "Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Bandage - light\n", "Dr. <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37.33</span>\n", "CBC: VP H02 repeat/part\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">120.05</span>\n", "Amoxyclav 500mg tablets\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">57.10</span>\n", "Patient Subtotal:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">214.48</span>\n", "Instructions\n", "Bandage: Keep the bandage clean and dry. Check the bandage twice daily for odor, redness or swelling and please\n", "contact the hospital if any of these occur. Also call us if your pet chews or removes the bandage. When your pet\n", "needs to go outside on a damp or rainy day, cover the bandage with a small plastic cover to keep the bandage dry \n", "<span style=\"font-weight: bold\">(</span>NO\n", "RUBBER BANDS!<span style=\"font-weight: bold\">)</span>. Remove the plastic once inside again.\n", "Reminder\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> Beransa 20mg <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30.</span>1kg-40kg<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span> Vaccination - Annual BB + Pi2\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2026</span> Vaccination - C3 Duramune 3yrly +Pi/BB\n", "Invoice Total:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">214.48</span>\n", "Total:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">214.48</span>\n", "Invoice Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">214.48</span>\n", "EFTPOS:\n", "<span style=\"font-weight: bold\">(</span>$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">214.48</span><span style=\"font-weight: bold\">)</span>\n", "Less Payment:\n", "<span style=\"font-weight: bold\">(</span>$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">214.48</span><span style=\"font-weight: bold\">)</span>\n", "Invoice Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Balance Due-Inc GST:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-607.03</span>\n", "The total price includes GST of $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">19.50</span>\n", "Scheduled Appointments:\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">04:20</span> pm.\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:00</span> am.\n", "Appt. for Cleo on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">09:40</span> am.\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">08:40</span> am.\n", "Thank You\n", "</pre>\n"], "text/plain": ["Larkhill Small Animal Hospital\n", "Page \u001b[1;36m1\u001b[0m \u001b[35m/\u001b[0m \u001b[1;36m1\u001b[0m\n", "ABN: \u001b[1;36m96\u001b[0m \u001b[1;36m093\u001b[0m \u001b[1;36m395\u001b[0m \u001b[1;36m254\u001b[0m\n", "\u001b[1;36m1774\u001b[0m Mandurah Road\n", "Port Kennedy, WA \u001b[1;36m6172\u001b[0m\n", "\u001b[1;36m08\u001b[0m-\u001b[1;36m9524\u001b[0m-\u001b[1;36m3838\u001b[0m\n", "Client ID:\n", "\u001b[1;36m2513\u001b[0m\n", "Mrs <PERSON><PERSON>\n", "Tax Invoice #:\n", "\u001b[1;36m193842\u001b[0m\n", "\u001b[1;36m16\u001b[0m Fairburn Cres\n", ":\n", "<PERSON><PERSON><PERSON><PERSON>, WA \u001b[1;36m6171\u001b[0m\n", "\u001b[1;36m5\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m0400\u001b[0m \u001b[1;36m285\u001b[0m \u001b[1;36m585\u001b[0m\n", "<EMAIL>\n", "Patient ID: \u001b[1;36m21530\u001b[0m\n", "Species: Canine\n", "Weight: \u001b[1;36m30.30\u001b[0m kilograms\n", "Patient Name: <PERSON>\n", "Breed: Greyhound\n", "Birthday: \u001b[1;36m28\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2012\u001b[0m\n", "Sex: Neutered Male\n", "Description\n", "Staff Name\n", "Quantity\n", "Total\n", "\u001b[1;36m5\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Bandage - light\n", "Dr. <PERSON>\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m37.33\u001b[0m\n", "CBC: VP H02 repeat/part\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m120.05\u001b[0m\n", "Amoxyclav 500mg tablets\n", "\u001b[1;36m14.00\u001b[0m\n", "$\u001b[1;36m57.10\u001b[0m\n", "Patient Subtotal:\n", "$\u001b[1;36m214.48\u001b[0m\n", "Instructions\n", "Bandage: Keep the bandage clean and dry. Check the bandage twice daily for odor, redness or swelling and please\n", "contact the hospital if any of these occur. Also call us if your pet chews or removes the bandage. When your pet\n", "needs to go outside on a damp or rainy day, cover the bandage with a small plastic cover to keep the bandage dry \n", "\u001b[1m(\u001b[0mNO\n", "RUBBER BANDS!\u001b[1m)\u001b[0m. Remove the plastic once inside again.\n", "Reminder\n", "\u001b[1;36m24\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m Beransa 20mg \u001b[1m(\u001b[0m\u001b[1;36m30.\u001b[0m1kg-40kg\u001b[1m)\u001b[0m\n", "\u001b[1;36m25\u001b[0m/\u001b[1;36m07\u001b[0m/\u001b[1;36m2025\u001b[0m Vaccination - Annual BB + Pi2\n", "\u001b[1;36m25\u001b[0m/\u001b[1;36m07\u001b[0m/\u001b[1;36m2026\u001b[0m Vaccination - C3 Duramune 3yrly +Pi/BB\n", "Invoice Total:\n", "$\u001b[1;36m214.48\u001b[0m\n", "Total:\n", "$\u001b[1;36m214.48\u001b[0m\n", "Invoice Balance Due:\n", "$\u001b[1;36m214.48\u001b[0m\n", "EFTPOS:\n", "\u001b[1m(\u001b[0m$\u001b[1;36m214.48\u001b[0m\u001b[1m)\u001b[0m\n", "Less Payment:\n", "\u001b[1m(\u001b[0m$\u001b[1;36m214.48\u001b[0m\u001b[1m)\u001b[0m\n", "Invoice Balance Due:\n", "$\u001b[1;36m0.00\u001b[0m\n", "Balance Due-Inc GST:\n", "$\u001b[1;36m-607.03\u001b[0m\n", "The total price includes GST of $\u001b[1;36m19.50\u001b[0m\n", "Scheduled Appointments:\n", "Appt. for <PERSON> on \u001b[1;36m9\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m04:20\u001b[0m pm.\n", "Appt. for <PERSON> on \u001b[1;36m12\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m09:00\u001b[0m am.\n", "Appt. for Cleo on \u001b[1;36m24\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m09:40\u001b[0m am.\n", "Appt. for <PERSON> on \u001b[1;36m1\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m08:40\u001b[0m am.\n", "Thank You\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.971\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7705317, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7705317'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Banzai'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Cra'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Milo'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7705317'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Kingdom'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'Kingdom'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">cfd59879-2cd5-4905-9427-d560a272c887</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">62</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">80</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Jackie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Kingdom'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mcfd59879-2cd5-4905-9427-d560a272c887\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m62\u001b[0m, \u001b[1;36m80\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'Kingdom'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">St Albans Veterinary Clinic\n", "WEST FOOTSCRAY &amp; ST.ALBANS\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">263</span> Main Road <span style=\"font-weight: bold\">(</span>West<span style=\"font-weight: bold\">)</span>\n", "VETERINARY CLINIC\n", "St Albans Victoria <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3021</span>\n", "Ph: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9364</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3777</span> Fax: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9364</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3666</span>\n", "E: <EMAIL>\n", "W: www.stalbansvet.com\n", "ABN: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">75</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">006</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">952</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">316</span>\n", "Tax Invoice\n", "Mr <PERSON>\n", "Customer ID <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">19621</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span> Kingdom Ave\n", "Date\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "St Albans Victoria <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3021</span>\n", "Reference\n", "Invoice No\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6011064</span>\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> of <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "Patient\n", "Product Description\n", "Quantity\n", "Amount\n", "Jackie <span style=\"font-weight: bold\">(</span>I<span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">D:8028</span>0<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "PROTECH C5 <span style=\"font-weight: bold\">(</span>C4 + BRONCHI SHIELD ORAL<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "$ <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">108.00</span>\n", "TOTAL\n", "$ <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">108.00</span>\n", "Total includes GST\n", "$ <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9.82</span>\n", "PAID IN FULL\n", "Thank you for choosing St. Albans Veterinary Clinic - we appreciate your business.\n", "Reminder\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>\n", "Milo\n", "Pharmacy <span style=\"font-weight: bold\">[</span>Annual Dog Vaccination<span style=\"font-weight: bold\">]</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>\n", "<PERSON>\n", "Pharmacy <span style=\"font-weight: bold\">[</span>Annual Dog Vaccination<span style=\"font-weight: bold\">]</span>\n", "Incorporating the services of:\n", "Drs <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> &amp; Associates.\n", "</pre>\n"], "text/plain": ["St Albans Veterinary Clinic\n", "WEST FOOTSCRAY & ST.ALBANS\n", "\u001b[1;36m263\u001b[0m Main Road \u001b[1m(\u001b[0mWest\u001b[1m)\u001b[0m\n", "VETERINARY CLINIC\n", "St Albans Victoria \u001b[1;36m3021\u001b[0m\n", "Ph: \u001b[1;36m9364\u001b[0m \u001b[1;36m3777\u001b[0m Fax: \u001b[1;36m9364\u001b[0m \u001b[1;36m3666\u001b[0m\n", "E: <EMAIL>\n", "W: www.stalbansvet.com\n", "ABN: \u001b[1;36m75\u001b[0m \u001b[1;36m006\u001b[0m \u001b[1;36m952\u001b[0m \u001b[1;36m316\u001b[0m\n", "Tax Invoice\n", "Mr <PERSON>\n", "Customer ID \u001b[1;36m19621\u001b[0m\n", "\u001b[1;36m30\u001b[0m Kingdom Ave\n", "Date\n", "\u001b[1;36m14\u001b[0m/\u001b[1;36m9\u001b[0m/\u001b[1;36m24\u001b[0m\n", "St Albans Victoria \u001b[1;36m3021\u001b[0m\n", "Reference\n", "Invoice No\n", "\u001b[1;36m6011064\u001b[0m\n", "Page \u001b[1;36m1\u001b[0m of \u001b[1;36m1\u001b[0m\n", "Patient\n", "Product Description\n", "Quantity\n", "Amount\n", "<PERSON> \u001b[1m(\u001b[0mI\u001b[1;92mD:8028\u001b[0m0\u001b[1m)\u001b[0m\n", "\u001b[1;36m14\u001b[0m/\u001b[1;36m9\u001b[0m/\u001b[1;36m24\u001b[0m\n", "PROTECH C5 \u001b[1m(\u001b[0mC4 + BRONCHI SHIELD ORAL\u001b[1m)\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n", "$ \u001b[1;36m108.00\u001b[0m\n", "TOTAL\n", "$ \u001b[1;36m108.00\u001b[0m\n", "Total includes GST\n", "$ \u001b[1;36m9.82\u001b[0m\n", "PAID IN FULL\n", "Thank you for choosing St. Albans Veterinary Clinic - we appreciate your business.\n", "Reminder\n", "\u001b[1;36m6\u001b[0m/\u001b[1;36m1\u001b[0m/\u001b[1;36m25\u001b[0m\n", "Milo\n", "Pharmacy \u001b[1m[\u001b[0mAnnual Dog Vaccination\u001b[1m]\u001b[0m\n", "\u001b[1;36m14\u001b[0m/\u001b[1;36m9\u001b[0m/\u001b[1;36m25\u001b[0m\n", "<PERSON>\n", "Pharmacy \u001b[1m[\u001b[0mAnnual Dog Vaccination\u001b[1m]\u001b[0m\n", "Incorporating the services of:\n", "Drs <PERSON><PERSON><PERSON><PERSON>, M<PERSON><PERSON><PERSON>, Kim Cao & Associates.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:40.983\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7709267, sim_score_fp: False, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7709267'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7709267'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Canine'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'External'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'Canine'\u001b[0m, \u001b[32m'External'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">c664761a-3228-48b3-98bc-2f3f87e71ddc</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Xavier'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Canine'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'External'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mc664761a-3228-48b3-98bc-2f3f87e71ddc\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Xavier'\u001b[0m, \u001b[32m'Canine'\u001b[0m, \u001b[32m'External'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>, <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:08</span> PM\n", "Mail - <PERSON><PERSON><PERSON> - Outlook\n", "Invoice from Walkerville Vet\n", "Walkerville Vet <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">system</span><span style=\"color: #000000; text-decoration-color: #000000\">@idexxneo.com&gt;</span>\n", "<span style=\"color: #000000; text-decoration-color: #000000\">Mon </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #000000; text-decoration-color: #000000\">/</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span><span style=\"color: #000000; text-decoration-color: #000000\">/</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\"> </span><span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">9:52</span><span style=\"color: #000000; text-decoration-color: #000000\"> AM</span>\n", "<span style=\"color: #000000; text-decoration-color: #000000\">To: <PERSON><PERSON><PERSON> &lt;<EMAIL></span><span style=\"font-weight: bold\">&gt;</span>\n", "CAUTION: External email. Only click on links or open attachments from trusted senders.\n", "INVOICE\n", "Walkerville Vet\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">142</span> North East Road, Walkerville SA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5081</span>\n", "Tel: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83442000</span>\n", "Email: <EMAIL>\n", "Web: <span style=\"color: #0000ff; text-decoration-color: #0000ff; text-decoration: underline\">https://www.walkervillevet.com.au/</span>\n", "ACN <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">667</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">654</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">686</span>\n", "Client:\n", "Ms <PERSON><PERSON><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">69</span> <PERSON>\n", "Prospect SA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5082</span>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27890</span>\n", "Sex: Male\n", "Invoice Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span>-Sep <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Patient: <PERSON>\n", "Birth Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>-Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2023</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">487369</span>\n", "Species: Canine\n", "Weight: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34.000</span> kg\n", "Breed: <PERSON> Breed\n", "Product <span style=\"color: #800080; text-decoration-color: #800080\">/</span> Service\n", "QuantityPrice <span style=\"font-weight: bold\">(</span>Exc<span style=\"font-weight: bold\">)</span>\n", "Tax\n", "Amount\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. Standard Consultation\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">81.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">89.10</span>\n", "Cytopoint Injection 40mg <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">156.35</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">171.99</span>\n", "Dexadreson Inj\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.53</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">32.48</span>\n", "Chlorhexidine Disinfectant Scrub\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.67</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8.44</span>\n", "Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">302.01</span>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27890</span>\n", "Sex: Male\n", "Invoice Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span>-Sep <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Patient: <PERSON>\n", "Birth Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>-Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2023</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">487372</span>\n", "Species: Canine\n", "Weight: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34.000</span> kg\n", "Breed: <PERSON> Breed\n", "Product <span style=\"color: #800080; text-decoration-color: #800080\">/</span> Service\n", "QuantityPrice <span style=\"font-weight: bold\">(</span>Exc<span style=\"font-weight: bold\">)</span>\n", "Tax\n", "Amount\n", "Paroxetine 20mg <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">49.74</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54.71</span>\n", "Catapres <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">120.00</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">76.74</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">84.41</span>\n", "Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">139.12</span>\n", "<span style=\"color: #0000ff; text-decoration-color: #0000ff; text-decoration: underline\">https://outlook.office.com/mail/id/AAQKADBIZmlw</span> ZGI3LTE0ZjUtNDkxZi05N2FkLTlhOTUyNDhlYjQyMAAQAD9TY \n", "UoCCTpGkfVB%2FCFqU%2FQ <span style=\"color: #808000; text-decoration-color: #808000\">...</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m9\u001b[0m/\u001b[1;36m16\u001b[0m/\u001b[1;36m24\u001b[0m, \u001b[1;92m12:08\u001b[0m PM\n", "Mail - <PERSON><PERSON><PERSON> - Outlook\n", "Invoice from Walkerville Vet\n", "Walkerville Vet \u001b[1m<\u001b[0m\u001b[1;95msystem\u001b[0m\u001b[<EMAIL>>\u001b[0m\n", "\u001b[39mMon \u001b[0m\u001b[1;36m2\u001b[0m\u001b[39m/\u001b[0m\u001b[1;36m09\u001b[0m\u001b[39m/\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m \u001b[0m\u001b[1;92m9:52\u001b[0m\u001b[39m AM\u001b[0m\n", "\u001b[39mTo: <PERSON><PERSON><PERSON> <<EMAIL>\u001b[0m\u001b[1m>\u001b[0m\n", "CAUTION: External email. Only click on links or open attachments from trusted senders.\n", "INVOICE\n", "Walkerville Vet\n", "\u001b[1;36m142\u001b[0m North East Road, Walkerville SA \u001b[1;36m5081\u001b[0m\n", "Tel: \u001b[1;36m08\u001b[0m \u001b[1;36m83442000\u001b[0m\n", "Email: <EMAIL>\n", "Web: \u001b[4;94mhttps://www.walkervillevet.com.au/\u001b[0m\n", "ACN \u001b[1;36m667\u001b[0m \u001b[1;36m654\u001b[0m \u001b[1;36m686\u001b[0m\n", "Client:\n", "Ms <PERSON><PERSON><PERSON>\n", "\u001b[1;36m69\u001b[0m Percy Street\n", "Prospect SA \u001b[1;36m5082\u001b[0m\n", "Patient ID: \u001b[1;36m27890\u001b[0m\n", "Sex: Male\n", "Invoice Date: \u001b[1;36m02\u001b[0m-Sep \u001b[1;36m2024\u001b[0m\n", "Patient: <PERSON>\n", "Birth Date: \u001b[1;36m14\u001b[0m-Oct \u001b[1;36m2023\u001b[0m\n", "Invoice Number: \u001b[1;36m487369\u001b[0m\n", "Species: Canine\n", "Weight: \u001b[1;36m34.000\u001b[0m kg\n", "Breed: <PERSON> Breed\n", "Product \u001b[35m/\u001b[0m Service\n", "QuantityPrice \u001b[1m(\u001b[0mExc\u001b[1m)\u001b[0m\n", "Tax\n", "Amount\n", "\u001b[1;36m1\u001b[0m. Standard Consultation\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m81.00\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m89.10\u001b[0m\n", "Cytopoint Injection 40mg \u001b[1m(\u001b[0m\u001b[1;36m6\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m156.35\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m171.99\u001b[0m\n", "Dexadreson Inj\n", "\u001b[1;36m4.00\u001b[0m\n", "\u001b[1;36m29.53\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m32.48\u001b[0m\n", "Chlorhexidine Disinfectant Scrub\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m7.67\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m8.44\u001b[0m\n", "Invoice Total\n", "\u001b[1;36m302.01\u001b[0m\n", "Patient ID: \u001b[1;36m27890\u001b[0m\n", "Sex: Male\n", "Invoice Date: \u001b[1;36m02\u001b[0m-Sep \u001b[1;36m2024\u001b[0m\n", "Patient: <PERSON>\n", "Birth Date: \u001b[1;36m14\u001b[0m-Oct \u001b[1;36m2023\u001b[0m\n", "Invoice Number: \u001b[1;36m487372\u001b[0m\n", "Species: Canine\n", "Weight: \u001b[1;36m34.000\u001b[0m kg\n", "Breed: <PERSON> Breed\n", "Product \u001b[35m/\u001b[0m Service\n", "QuantityPrice \u001b[1m(\u001b[0mExc\u001b[1m)\u001b[0m\n", "Tax\n", "Amount\n", "Paroxetine 20mg \u001b[1m(\u001b[0m\u001b[1;36m30\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m90.00\u001b[0m\n", "\u001b[1;36m49.74\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m54.71\u001b[0m\n", "Catapres \u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m120.00\u001b[0m \u001b[1;36m76.74\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m84.41\u001b[0m\n", "Invoice Total\n", "\u001b[1;36m139.12\u001b[0m\n", "\u001b[4;94mhttps://outlook.office.com/mail/id/AAQKADBIZmlw\u001b[0m ZGI3LTE0ZjUtNDkxZi05N2FkLTlhOTUyNDhlYjQyMAAQAD9TY \n", "UoCCTpGkfVB%2FCFqU%2FQ \u001b[33m...\u001b[0m\n", "\u001b[1;36m1\u001b[0m/\u001b[1;36m2\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.000\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7709291, sim_score_fp: False, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7709291'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7709291'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Canine'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'External'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'Canine'\u001b[0m, \u001b[32m'External'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">24c32d01-f9df-491c-8faf-972789bff9c2</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Xavier'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Canine'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'External'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m24c32d01-f9df-491c-8faf-972789bff9c2\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Xavier'\u001b[0m, \u001b[32m'Canine'\u001b[0m, \u001b[32m'External'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">16</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>, <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:08</span> PM\n", "Mail - <PERSON><PERSON><PERSON> - Outlook\n", "Invoice from Walkerville Vet\n", "Walkerville Vet <span style=\"font-weight: bold\">&lt;</span><span style=\"color: #ff00ff; text-decoration-color: #ff00ff; font-weight: bold\">system</span><span style=\"color: #000000; text-decoration-color: #000000\">@idexxneo.com&gt;</span>\n", "<span style=\"color: #000000; text-decoration-color: #000000\">Mon </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #000000; text-decoration-color: #000000\">/</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span><span style=\"color: #000000; text-decoration-color: #000000\">/</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span><span style=\"color: #000000; text-decoration-color: #000000\"> </span><span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">9:52</span><span style=\"color: #000000; text-decoration-color: #000000\"> AM</span>\n", "<span style=\"color: #000000; text-decoration-color: #000000\">To: <PERSON><PERSON><PERSON> &lt;<EMAIL></span><span style=\"font-weight: bold\">&gt;</span>\n", "CAUTION: External email. Only click on links or open attachments from trusted senders.\n", "INVOICE\n", "Walkerville Vet\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">142</span> North East Road, Walkerville SA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5081</span>\n", "Tel: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83442000</span>\n", "Email: <EMAIL>\n", "Web: <span style=\"color: #0000ff; text-decoration-color: #0000ff; text-decoration: underline\">https://www.walkervillevet.com.au/</span>\n", "ACN <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">667</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">654</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">686</span>\n", "Client:\n", "Ms <PERSON><PERSON><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">69</span> <PERSON>\n", "Prospect SA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5082</span>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27890</span>\n", "Sex: Male\n", "Invoice Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span>-Sep <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Patient: <PERSON>\n", "Birth Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>-Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2023</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">487369</span>\n", "Species: Canine\n", "Weight: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34.000</span> kg\n", "Breed: <PERSON> Breed\n", "Product <span style=\"color: #800080; text-decoration-color: #800080\">/</span> Service\n", "QuantityPrice <span style=\"font-weight: bold\">(</span>Exc<span style=\"font-weight: bold\">)</span>\n", "Tax\n", "Amount\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>. Standard Consultation\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">81.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">89.10</span>\n", "Cytopoint Injection 40mg <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">156.35</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">171.99</span>\n", "Dexadreson Inj\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.53</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">32.48</span>\n", "Chlorhexidine Disinfectant Scrub\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.67</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8.44</span>\n", "Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">302.01</span>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27890</span>\n", "Sex: Male\n", "Invoice Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span>-Sep <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Patient: <PERSON>\n", "Birth Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span>-Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2023</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">487372</span>\n", "Species: Canine\n", "Weight: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34.000</span> kg\n", "Breed: <PERSON> Breed\n", "Product <span style=\"color: #800080; text-decoration-color: #800080\">/</span> Service\n", "QuantityPrice <span style=\"font-weight: bold\">(</span>Exc<span style=\"font-weight: bold\">)</span>\n", "Tax\n", "Amount\n", "Paroxetine 20mg <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">49.74</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">54.71</span>\n", "Catapres <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">120.00</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">76.74</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.00</span>% <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">84.41</span>\n", "Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">139.12</span>\n", "<span style=\"color: #0000ff; text-decoration-color: #0000ff; text-decoration: underline\">https://outlook.office.com/mail/id/AAQKADBIZmlw</span> ZGI3LTE0ZjUtNDkxZi05N2FkLTlhOTUyNDhlYjQyMAAQAD9TY \n", "UoCCTpGkfVB%2FCFqU%2FQ <span style=\"color: #808000; text-decoration-color: #808000\">...</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m9\u001b[0m/\u001b[1;36m16\u001b[0m/\u001b[1;36m24\u001b[0m, \u001b[1;92m12:08\u001b[0m PM\n", "Mail - <PERSON><PERSON><PERSON> - Outlook\n", "Invoice from Walkerville Vet\n", "Walkerville Vet \u001b[1m<\u001b[0m\u001b[1;95msystem\u001b[0m\u001b[<EMAIL>>\u001b[0m\n", "\u001b[39mMon \u001b[0m\u001b[1;36m2\u001b[0m\u001b[39m/\u001b[0m\u001b[1;36m09\u001b[0m\u001b[39m/\u001b[0m\u001b[1;36m2024\u001b[0m\u001b[39m \u001b[0m\u001b[1;92m9:52\u001b[0m\u001b[39m AM\u001b[0m\n", "\u001b[39mTo: <PERSON><PERSON><PERSON> <<EMAIL>\u001b[0m\u001b[1m>\u001b[0m\n", "CAUTION: External email. Only click on links or open attachments from trusted senders.\n", "INVOICE\n", "Walkerville Vet\n", "\u001b[1;36m142\u001b[0m North East Road, Walkerville SA \u001b[1;36m5081\u001b[0m\n", "Tel: \u001b[1;36m08\u001b[0m \u001b[1;36m83442000\u001b[0m\n", "Email: <EMAIL>\n", "Web: \u001b[4;94mhttps://www.walkervillevet.com.au/\u001b[0m\n", "ACN \u001b[1;36m667\u001b[0m \u001b[1;36m654\u001b[0m \u001b[1;36m686\u001b[0m\n", "Client:\n", "Ms <PERSON><PERSON><PERSON>\n", "\u001b[1;36m69\u001b[0m Percy Street\n", "Prospect SA \u001b[1;36m5082\u001b[0m\n", "Patient ID: \u001b[1;36m27890\u001b[0m\n", "Sex: Male\n", "Invoice Date: \u001b[1;36m02\u001b[0m-Sep \u001b[1;36m2024\u001b[0m\n", "Patient: <PERSON>\n", "Birth Date: \u001b[1;36m14\u001b[0m-Oct \u001b[1;36m2023\u001b[0m\n", "Invoice Number: \u001b[1;36m487369\u001b[0m\n", "Species: Canine\n", "Weight: \u001b[1;36m34.000\u001b[0m kg\n", "Breed: <PERSON> Breed\n", "Product \u001b[35m/\u001b[0m Service\n", "QuantityPrice \u001b[1m(\u001b[0mExc\u001b[1m)\u001b[0m\n", "Tax\n", "Amount\n", "\u001b[1;36m1\u001b[0m. Standard Consultation\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m81.00\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m89.10\u001b[0m\n", "Cytopoint Injection 40mg \u001b[1m(\u001b[0m\u001b[1;36m6\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m156.35\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m171.99\u001b[0m\n", "Dexadreson Inj\n", "\u001b[1;36m4.00\u001b[0m\n", "\u001b[1;36m29.53\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m32.48\u001b[0m\n", "Chlorhexidine Disinfectant Scrub\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m7.67\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m8.44\u001b[0m\n", "Invoice Total\n", "\u001b[1;36m302.01\u001b[0m\n", "Patient ID: \u001b[1;36m27890\u001b[0m\n", "Sex: Male\n", "Invoice Date: \u001b[1;36m02\u001b[0m-Sep \u001b[1;36m2024\u001b[0m\n", "Patient: <PERSON>\n", "Birth Date: \u001b[1;36m14\u001b[0m-Oct \u001b[1;36m2023\u001b[0m\n", "Invoice Number: \u001b[1;36m487372\u001b[0m\n", "Species: Canine\n", "Weight: \u001b[1;36m34.000\u001b[0m kg\n", "Breed: <PERSON> Breed\n", "Product \u001b[35m/\u001b[0m Service\n", "QuantityPrice \u001b[1m(\u001b[0mExc\u001b[1m)\u001b[0m\n", "Tax\n", "Amount\n", "Paroxetine 20mg \u001b[1m(\u001b[0m\u001b[1;36m30\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m90.00\u001b[0m\n", "\u001b[1;36m49.74\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m54.71\u001b[0m\n", "Catapres \u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m120.00\u001b[0m \u001b[1;36m76.74\u001b[0m\n", "\u001b[1;36m10.00\u001b[0m% \u001b[1;36m84.41\u001b[0m\n", "Invoice Total\n", "\u001b[1;36m139.12\u001b[0m\n", "\u001b[4;94mhttps://outlook.office.com/mail/id/AAQKADBIZmlw\u001b[0m ZGI3LTE0ZjUtNDkxZi05N2FkLTlhOTUyNDhlYjQyMAAQAD9TY \n", "UoCCTpGkfVB%2FCFqU%2FQ \u001b[33m...\u001b[0m\n", "\u001b[1;36m1\u001b[0m/\u001b[1;36m2\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.013\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7727846, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7727846'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Chaplin'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'CHAPLIN'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Chapli'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ROSIE'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'HAZEL'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7727846'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'CHAPLIN'\u001b[0m, \u001b[32m'<PERSON><PERSON><PERSON>'\u001b[0m, \u001b[32m'ROS<PERSON>'\u001b[0m, \u001b[32m'HAZEL'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'chaplin'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Chaplin'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'chaplin'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">91abab14-1d7c-4625-a9fd-472187c6333f</span>.jpg <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">88</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'chaplin'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Chaplin'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m91abab14-1d7c-4625-a9fd-472187c6333f\u001b[0m.jpg \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m88\u001b[0m, \u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'chaplin'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">O\n", "Rowville Veterinary Clinic &amp; Hospital\n", "ABN <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">686</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3185</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8995</span>\n", "Dr <PERSON><PERSON><PERSON> &amp; Dr <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">919</span> Stud Road\n", "Rowville, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3178</span>\n", "C5 - chaplin\n", "Phone <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9763</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1799</span>\n", "<EMAIL>\n", "rowvillevet.com.au\n", "- <PERSON>\n", "- <PERSON>\n", "Discount-\n", "Mrs. <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">13</span> <PERSON><PERSON> close\n", "Knoxfield <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3180</span>\n", "TAX INVOICE <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span> Jun <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Invoice # R226281705\n", "Split nail\n", "Patient\n", "Date\n", "<PERSON><PERSON>\n", "Q'tity\n", "Total\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Carprofen Beef 100mq <span style=\"font-weight: bold\">(</span>Tablet<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">37.99</span>\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Vacc -Canine5. Adult C5\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">102.50</span>\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Proheart Package <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>. <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span> <span style=\"font-weight: bold\">(</span>Single<span style=\"font-weight: bold\">)</span>\n", "Weight 24Kg1\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">182.00</span>\n", "Rounding\n", "TOTAL NOW DUE\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">322.49</span>\n", "includes GST of\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.32</span>\n", "The following payments have been received with thanks\n", "Inv. Date\n", "Payment Method\n", "Payment\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Credit Card\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">322.49</span>\n", "ACCOUNT BALANCE OUTSTANDING\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.0</span>\n", "Annual vaccination and proheart injection. Lam<PERSON>ss in the RFL - suspect soft tissue sprain in paw. Probable \n", "arthritis in\n", "hips. Cover with tapering course of NSAIDs and then recheck. Consider course of Pentosan; recommend 4Cyte gel in \n", "the\n", "interim.\n", "</pre>\n"], "text/plain": ["O\n", "Rowville Veterinary Clinic & Hospital\n", "ABN \u001b[1;36m686\u001b[0m \u001b[1;36m3185\u001b[0m \u001b[1;36m8995\u001b[0m\n", "Dr <PERSON><PERSON><PERSON> & Dr <PERSON>\n", "\u001b[1;36m919\u001b[0m Stud Road\n", "Rowville, \u001b[1;36m3178\u001b[0m\n", "C5 - chaplin\n", "Phone \u001b[1;36m03\u001b[0m \u001b[1;36m9763\u001b[0m \u001b[1;36m1799\u001b[0m\n", "<EMAIL>\n", "rowvillevet.com.au\n", "- <PERSON>\n", "- <PERSON>\n", "Discount-\n", "Mrs. <PERSON>\n", "\u001b[1;36m13\u001b[0m <PERSON><PERSON> close\n", "Knoxfield \u001b[1;36m3180\u001b[0m\n", "TAX INVOICE \u001b[1;36m28\u001b[0m Jun \u001b[1;36m2022\u001b[0m\n", "Invoice # R226281705\n", "Split nail\n", "Patient\n", "Date\n", "<PERSON><PERSON>\n", "Q'tity\n", "Total\n", "<PERSON>\n", "\u001b[1;36m28\u001b[0m/\u001b[1;36m6\u001b[0m/\u001b[1;36m2022\u001b[0m\n", "Carprofen Beef 100mq \u001b[1m(\u001b[0mTablet\u001b[1m)\u001b[0m\n", "\u001b[1;36m10\u001b[0m\n", "$\u001b[1;36m37.99\u001b[0m\n", "<PERSON>\n", "\u001b[1;36m28\u001b[0m/\u001b[1;36m6\u001b[0m/\u001b[1;36m2022\u001b[0m\n", "Vacc -Canine5. Adult C5\n", "\u001b[1;36m1\u001b[0m\n", "$\u001b[1;36m102.50\u001b[0m\n", "<PERSON>\n", "\u001b[1;36m28\u001b[0m/\u001b[1;36m6\u001b[0m/\u001b[1;36m2022\u001b[0m\n", "Proheart Package \u001b[1;36m3\u001b[0m. \u001b[1;36m21\u001b[0m-\u001b[1;36m30\u001b[0m \u001b[1m(\u001b[0mS<PERSON><PERSON>\u001b[1m)\u001b[0m\n", "Weight 24Kg1\n", "$\u001b[1;36m182.00\u001b[0m\n", "Rounding\n", "TOTAL NOW DUE\n", "$\u001b[1;36m322.49\u001b[0m\n", "includes GST of\n", "$\u001b[1;36m29.32\u001b[0m\n", "The following payments have been received with thanks\n", "Inv. Date\n", "Payment Method\n", "Payment\n", "\u001b[1;36m28\u001b[0m/\u001b[1;36m6\u001b[0m/\u001b[1;36m2022\u001b[0m\n", "Credit Card\n", "$\u001b[1;36m322.49\u001b[0m\n", "ACCOUNT BALANCE OUTSTANDING\n", "$\u001b[1;36m0.0\u001b[0m\n", "Annual vaccination and proheart injection. Lam<PERSON>ss in the RFL - suspect soft tissue sprain in paw. Probable \n", "arthritis in\n", "hips. Cover with tapering course of NSAIDs and then recheck. Consider course of Pentosan; recommend 4Cyte gel in \n", "the\n", "interim.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.028\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7744498, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span>\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7744498'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Theordore'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Theordore'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span><span style=\"font-weight: bold\">]</span>\n", "<span style=\"font-weight: bold\">}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\n", "    \u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7744498'\u001b[0m,\n", "    \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Theordore'\u001b[0m,\n", "    \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Theordore'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\n", "\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">c5818514-d878-43cf-9ab0-f736b40b760e</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">91</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">91</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">55</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mc5818514-d878-43cf-9ab0-f736b40b760e\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m91\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m91\u001b[0m, \u001b[1;36m100\u001b[0m, \u001b[1;36m55\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ARH\n", "ANIMAL\n", "REFERRAL HOSPITAL\n", "ARH Sinnamon Park\n", "Mrs <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">40</span> Lather Rd\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">532</span> Seventeen Mile Rocks Road,\n", "Belbowrie QLD <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4070</span>\n", "Sinnamon Park, QLD, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4073</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">96</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">614</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">558</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3172</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0593</span>\n", "<EMAIL>\n", "Tax Invoice for Professional Services\n", "Patient : <PERSON> <span style=\"font-weight: bold\">(</span>Theo<span style=\"font-weight: bold\">)</span>\n", "Details on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"font-weight: bold\">(</span>Ref: <PERSON> <PERSON><PERSON> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2297175</span><span style=\"font-weight: bold\">)</span>\n", "Service Provided\n", "No\n", "Amount\n", "Medication\n", "Ilium Methadone 10mg/ml Inj\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">67.50</span>\n", "Consultation\n", "Consult - Emergency\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">225.00</span>\n", "Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">292.50</span>\n", "Includes Tax of :\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26.59</span>\n", "Balance owing $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Ref - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">184804</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">19:28:26</span>\n", "Homebush, NSW\n", "Minchinbury, NSW\n", "West Gosford, NSW\n", "Fairy Meadow, NSW\n", "Pialligo, ACT\n", "Sinnamon Park, QLD\n", "Essendon Fields, VIC\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">250</span> Parramatta Rd\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span> <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">401</span> Manns Rd\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> Princes Highway\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">364</span> Fairbairn Ave\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">532</span> Seventeen Mile Rocks Rd\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">72</span> <PERSON><PERSON><PERSON> Ave\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9758</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8666</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8610</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3400</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4323</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3886</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4283</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8432</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6280</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6344</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3172</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0593</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9379</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0700</span>\n", "www.emergencyvet.com.au\n", "</pre>\n"], "text/plain": ["ARH\n", "ANIMAL\n", "REFERRAL HOSPITAL\n", "ARH Sinnamon Park\n", "Mrs <PERSON>\n", "\u001b[1;36m40\u001b[0m La<PERSON> Rd\n", "\u001b[1;36m532\u001b[0m Seventeen Mile Rocks Road,\n", "Belbowrie QLD \u001b[1;36m4070\u001b[0m\n", "Sinnamon Park, QLD, \u001b[1;36m4073\u001b[0m\n", "\u001b[1;36m96\u001b[0m \u001b[1;36m614\u001b[0m \u001b[1;36m558\u001b[0m \u001b[1;36m100\u001b[0m\n", "\u001b[1;36m07\u001b[0m \u001b[1;36m3172\u001b[0m-\u001b[1;36m0593\u001b[0m\n", "<EMAIL>\n", "Tax Invoice for Professional Services\n", "Patient : <PERSON> \u001b[1m(\u001b[0mThe<PERSON>\u001b[1m)\u001b[0m\n", "Details on \u001b[1;36m24\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1m(\u001b[0mRef: <PERSON> <PERSON><PERSON> - \u001b[1;36m2297175\u001b[0m\u001b[1m)\u001b[0m\n", "Service Provided\n", "No\n", "Amount\n", "Medication\n", "Ilium Methadone 10mg/ml Inj\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m67.50\u001b[0m\n", "Consultation\n", "Consult - Emergency\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m225.00\u001b[0m\n", "Total\n", "\u001b[1;36m292.50\u001b[0m\n", "Includes Tax of :\n", "\u001b[1;36m26.59\u001b[0m\n", "Balance owing $\u001b[1;36m0.00\u001b[0m\n", "Ref - \u001b[1;36m184804\u001b[0m - \u001b[1;36m24\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m19:28:26\u001b[0m\n", "Homebush, NSW\n", "Minchinbury, NSW\n", "West Gosford, NSW\n", "Fairy Meadow, NSW\n", "Pialligo, ACT\n", "Sinnamon Park, QLD\n", "Essendon Fields, VIC\n", "\u001b[1;36m250\u001b[0m Parramatta Rd\n", "\u001b[1;36m1\u001b[0m/\u001b[1;36m5\u001b[0m <PERSON>\n", "\u001b[1;36m3\u001b[0m/\u001b[1;36m401\u001b[0m Manns Rd\n", "\u001b[1;36m1\u001b[0m/\u001b[1;36m3\u001b[0m Princes Highway\n", "\u001b[1;36m364\u001b[0m Fairbairn Ave\n", "\u001b[1;36m532\u001b[0m Seventeen Mile Rocks Rd\n", "\u001b[1;36m72\u001b[0m <PERSON><PERSON><PERSON>\n", "\u001b[1;36m02\u001b[0m \u001b[1;36m9758\u001b[0m \u001b[1;36m8666\u001b[0m\n", "\u001b[1;36m02\u001b[0m \u001b[1;36m8610\u001b[0m \u001b[1;36m3400\u001b[0m\n", "\u001b[1;36m02\u001b[0m \u001b[1;36m4323\u001b[0m \u001b[1;36m3886\u001b[0m\n", "\u001b[1;36m02\u001b[0m \u001b[1;36m4283\u001b[0m \u001b[1;36m8432\u001b[0m\n", "\u001b[1;36m02\u001b[0m \u001b[1;36m6280\u001b[0m \u001b[1;36m6344\u001b[0m\n", "\u001b[1;36m07\u001b[0m \u001b[1;36m3172\u001b[0m \u001b[1;36m0593\u001b[0m\n", "\u001b[1;36m03\u001b[0m \u001b[1;36m9379\u001b[0m \u001b[1;36m0700\u001b[0m\n", "www.emergencyvet.com.au\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.040\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7754821, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7754821'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Lucianna inez'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Lucianna'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>na inez'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7754821'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON><PERSON> inez'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON> inez'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">3291814d-db4c-465a-bb9a-e245647514a0</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m3291814d-db4c-465a-bb9a-e245647514a0\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">VetPartners Australia PL ta SuperVets Manly West\n", "P O Box <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">700</span>\n", "Wynnum Qld <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4178</span>\n", "Cnr <PERSON>l Rd &amp; Radford Rd, Manly West <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4176</span>\n", "Phone: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">07</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38930509</span>\n", "<PERSON>\n", "A.B.N: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">44</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">114</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">962</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">453</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">184</span> Radford Road\n", "Tax Invoice No: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">398664</span>\n", "Manly West Queensland <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4153</span>\n", "Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">26</span> SEP <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "Vet Code: CH\n", "Client Ref: BERCO45 For Lucianna\n", "Description\n", "Quantity\n", "Total $\n", "Apoquel Tabs <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3.</span>6mg <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">188.90</span>\n", "Consultation IN CLINIC\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">98.00</span>\n", "Barazone Conditioner 100ml\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">74.75</span>\n", "Malaseb Medicated Foam 250ml\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59.05</span>\n", "Invoice Totals:\n", "Balance Forward\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "This invoice Includes GST of $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38.25</span>\n", "+ Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">420.70</span>\n", "- Receipt Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">420.70</span>\n", "Balance Due\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Thanks for supporting SuperVets. We love looking after you and your precious pets!\n", "SuperVets Manly contact number is <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38930509</span>.\n", "Direct Debit Details\n", "Vetpartners Australia Pty Ltd\n", "BSB <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">082</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">057</span>\n", "Acc <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">*********</span>\n", "</pre>\n"], "text/plain": ["VetPartners Australia PL ta SuperVets Manly West\n", "P O Box \u001b[1;36m700\u001b[0m\n", "Wynnum Qld \u001b[1;36m4178\u001b[0m\n", "Cnr Wondall Rd & Radford Rd, Manly West \u001b[1;36m4176\u001b[0m\n", "Phone: \u001b[1;36m07\u001b[0m \u001b[1;36m38930509\u001b[0m\n", "<PERSON>\n", "A.B.N: \u001b[1;36m44\u001b[0m \u001b[1;36m114\u001b[0m \u001b[1;36m962\u001b[0m \u001b[1;36m453\u001b[0m\n", "\u001b[1;36m45\u001b[0m/\u001b[1;36m184\u001b[0m Radford Road\n", "Tax Invoice No: \u001b[1;36m398664\u001b[0m\n", "Manly West Queensland \u001b[1;36m4153\u001b[0m\n", "Date: \u001b[1;36m26\u001b[0m SEP \u001b[1;36m24\u001b[0m\n", "Vet Code: CH\n", "Client Ref: BERCO45 For Lucianna\n", "Description\n", "Quantity\n", "Total $\n", "Apo<PERSON> \u001b[1;36m3.\u001b[0m6mg \u001b[1;36m100\u001b[0m\n", "\u001b[1;36m45\u001b[0m\n", "\u001b[1;36m188.90\u001b[0m\n", "Consultation IN CLINIC\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m98.00\u001b[0m\n", "Barazone Conditioner 100ml\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m74.75\u001b[0m\n", "Malaseb Medicated Foam 250ml\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m59.05\u001b[0m\n", "Invoice Totals:\n", "Balance Forward\n", "\u001b[1;36m0.00\u001b[0m\n", "This invoice Includes GST of $\u001b[1;36m38.25\u001b[0m\n", "+ Invoice Total\n", "\u001b[1;36m420.70\u001b[0m\n", "- Receipt Total\n", "\u001b[1;36m420.70\u001b[0m\n", "Balance Due\n", "\u001b[1;36m0.00\u001b[0m\n", "Thanks for supporting SuperVets. We love looking after you and your precious pets!\n", "SuperVets Manly contact number is \u001b[1;36m38930509\u001b[0m.\n", "Direct Debit Details\n", "Vetpartners Australia Pty Ltd\n", "BSB \u001b[1;36m082\u001b[0m-\u001b[1;36m057\u001b[0m\n", "Acc \u001b[1;36m*********\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.055\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7769560, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7769560'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Freya Sinead'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Freya Sinead'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Freya'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7769560'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON><PERSON>'\u001b[0m, \u001b[32m'<PERSON><PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">daaae60f-57bc-47fc-9aed-a8d73b8124ce</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mdaaae60f-57bc-47fc-9aed-a8d73b8124ce\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">WVC\n", "Wyndham Veterinary Clinic\n", "TAX INVOICE\n", "Invoice to:\n", "Mr <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29</span> Cullen Drive\n", "WYNDHAM VALE <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3024</span>\n", "Date\n", "Clinician\n", "Animal\n", "<PERSON><PERSON>\n", "Units\n", "Cost\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <PERSON>\n", "<PERSON><PERSON>\n", "Cytopoint <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30</span> mg/ml phial\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">148.72</span>\n", "Total before GST\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">135.20</span>\n", "GST\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">13.52</span>\n", "Total of bill\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">148.72</span>\n", "Existing balance\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Total owed\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">148.72</span>\n", "Amount paid\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">148.72</span>\n", "Balance\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Wyndham Veterinary Clinic PTY LTD\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">133</span> Market Rd, Werribee <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3030</span>, Australia\n", "Telephone: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9742</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5556</span> Facsimile: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9742</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4441</span>\n", "E-mail: <EMAIL>\n", "Home page: www.wyndhamvet.com.au\n", "ABN <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">22</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">600</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">445</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">574</span>\n", "</pre>\n"], "text/plain": ["WVC\n", "Wyndham Veterinary Clinic\n", "TAX INVOICE\n", "Invoice to:\n", "Mr <PERSON>\n", "\u001b[1;36m29\u001b[0m Cullen Drive\n", "WYNDHAM VALE \u001b[1;36m3024\u001b[0m\n", "Date\n", "Clinician\n", "Animal\n", "<PERSON><PERSON>\n", "Units\n", "Cost\n", "\u001b[1;36m1\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m <PERSON>\n", "<PERSON><PERSON>\n", "Cytopoint \u001b[1;36m30\u001b[0m mg/ml phial\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m148.72\u001b[0m\n", "Total before GST\n", "$\u001b[1;36m135.20\u001b[0m\n", "GST\n", "$\u001b[1;36m13.52\u001b[0m\n", "Total of bill\n", "$\u001b[1;36m148.72\u001b[0m\n", "Existing balance\n", "$\u001b[1;36m0.00\u001b[0m\n", "Total owed\n", "$\u001b[1;36m148.72\u001b[0m\n", "Amount paid\n", "$\u001b[1;36m148.72\u001b[0m\n", "Balance\n", "$\u001b[1;36m0.00\u001b[0m\n", "Wyndham Veterinary Clinic PTY LTD\n", "\u001b[1;36m133\u001b[0m Market Rd, Werribee \u001b[1;36m3030\u001b[0m, Australia\n", "Telephone: \u001b[1;36m9742\u001b[0m \u001b[1;36m5556\u001b[0m Facsimile: \u001b[1;36m9742\u001b[0m \u001b[1;36m4441\u001b[0m\n", "E-mail: <EMAIL>\n", "Home page: www.wyndhamvet.com.au\n", "ABN \u001b[1;36m22\u001b[0m \u001b[1;36m600\u001b[0m \u001b[1;36m445\u001b[0m \u001b[1;36m574\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.063\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7802338, sim_score_fp: False, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7802338'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Ragnar'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Rox'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Ragnar'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7802338'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Ragnar'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Rox'\u001b[0m, \u001b[32m'Ragnar'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Major'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Kalgoorlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Complex'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'include'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Ragnar'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'Major'\u001b[0m, \u001b[32m'Kalgoorlie'\u001b[0m, \u001b[32m'Complex'\u001b[0m, \u001b[32m'include'\u001b[0m, \u001b[32m'Ragnar'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">35a164e6-b409-416b-82b8-2cb84a1a9b4a</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">80</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Major'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Kalgoorlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Complex'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'include'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Ragnar'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m35a164e6-b409-416b-82b8-2cb84a1a9b4a\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m80\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Major'\u001b[0m, \u001b[32m'Kalgoorlie'\u001b[0m, \u001b[32m'Complex'\u001b[0m, \u001b[32m'include'\u001b[0m, \u001b[32m'Ragnar'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">THE\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">464</span> Hannan Street\n", "ANIMAL\n", "Kalgoorlie, W.A. <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6430</span>\n", "HOSPITAL\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9021</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6000</span>\n", "Fax: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9021</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6055</span>\n", "A.B.N: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">449</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">354</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">113</span>\n", "<PERSON> <PERSON><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span> Dart Street\n", "Kalgoorlie WA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6430</span>\n", "GST Invoice for Professional Services\n", "Patient : <PERSON><PERSON><PERSON>\n", "Details on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"font-weight: bold\">(</span>Ref: Dr<PERSON><PERSON> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1617838</span><span style=\"font-weight: bold\">)</span>\n", "Service Provided\n", "No\n", "Amount\n", "Flea, Worm &amp; Tick Preparations\n", "Milbemax Cat Large 20s\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">20.05</span>\n", "Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">20.05</span>\n", "Includes GST of :\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.82</span>\n", "Details on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"font-weight: bold\">(</span>Ref: Dr<PERSON><PERSON> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1617850</span><span style=\"font-weight: bold\">)</span>\n", "Hospital Care - the patient was admitted to hospital for care and to prepare for the veterinary procedure.\n", "Sedation - a sedative was administered to ensure comfort during preparation for the procedure.\n", "Anaesthesia- an evaluation was performed to ensure the safety patient under anesthesia. Medications\n", "were administered and the patient anaethetised and monitored continuously.\n", "Dentistry- a dental scale and polish was performed by the veterinary surgeon. Post op recovery was\n", "undertaken and the patient monitored. A discharge discussion was conducted with post care\n", "counseling.\n", "Service Provided\n", "No\n", "Amount\n", "F4 Vaccinations: Rhinotracheitis, Calicivirus, Panleukopenia &amp; Chlamydia <span style=\"font-weight: bold\">(</span>next due\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">97.70</span>\n", "in <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> weeks<span style=\"font-weight: bold\">)</span>\n", "Hospital &amp; Nursing Care - Medical Care unit <span style=\"font-weight: bold\">(</span>same day discharge<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">47.15</span>\n", "Sedation - Premedication before anaesthesia\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38.55</span>\n", "Anaesthesia: General - setup &amp; induction\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100.50</span>\n", "Anaesthesia: Inhalation and <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45</span> mins continuous patient monitoring\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">314.10</span>\n", "Dental: Major - Scale &amp; Polish With Extractions\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">161.30</span>\n", "Dental: Complex Surgical Extractions\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">297.60</span>\n", "Dental: Simple Non Surgical Extractions\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">78.55</span>\n", "Oral Medications\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">87.50</span>\n", "Medications\n", "Clavulox Drops <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">15</span> MI\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">58.05</span>\n", "Meloxicam Cat Oral <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span> ml Apex\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.45</span>\n", "Sedatives, Injections and other products\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "Medications\n", "Antisedan <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span> MI\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.18</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.10</span>\n", "Butordyne Inj <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span> MI Jurox <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.18</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25.85</span>\n", "Domitor <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span> MI\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.36</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10.80</span>\n", "Ref - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">123539</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">17:03:22</span>\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "THE\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">464</span> Hannan Street\n", "ANIMAL\n", "Kalgoorlie, W.A. <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6430</span>\n", "HOSPITAL\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9021</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6000</span>\n", "Fax: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9021</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6055</span>\n", "<PERSON> <PERSON><PERSON>\n", "A.B.N: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">449</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">354</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">113</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span> Dart Street\n", "Kalgoorlie WA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6430</span>\n", "GST Invoice for Professional Services\n", "VACCINATIONS: While uncommon, adverse sensitivity to immunizations may occur &amp; can include\n", "dullness, muscle soreness, low-grade fever, injection site swelling, hives and facial edoema. If any\n", "such problems occur, please call us immediately.\n", "Patient : <PERSON><PERSON><PERSON>\n", "Isoflurane Vca 250mls\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.10</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12.25</span>\n", "Ketamine Inj 50ml - by Intramuscular Injection\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.23</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.20</span>\n", "Miscellaneous Charge\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-78.20</span>\n", "Inventory for Dental Maintenance\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5.00</span>\n", "Consumables\n", "Face Masks\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.02</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.85</span>\n", "Gloves Protex P/F Med 100s\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.01</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.40</span>\n", "Prophy Cups\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.03</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.95</span>\n", "Prophy <PERSON>e 250gm\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.01</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.55</span>\n", "Tongue Depressors\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.25</span>\n", "Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">227.95</span>\n", "Includes GST of :\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">111.63</span>\n", "Total - Ra<PERSON><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">248.00</span>\n", "Amount <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1248.00</span>\n", "PAID\n", "Balance remaining\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Ref - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">123539</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">17:03:22</span>\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>\n", "</pre>\n"], "text/plain": ["THE\n", "\u001b[1;36m464\u001b[0m Hannan Street\n", "ANIMAL\n", "Kalgoorlie, W.A. \u001b[1;36m6430\u001b[0m\n", "HOSPITAL\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m08\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9021\u001b[0m \u001b[1;36m6000\u001b[0m\n", "Fax: \u001b[1m(\u001b[0m\u001b[1;36m08\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9021\u001b[0m \u001b[1;36m6055\u001b[0m\n", "A.B.N: \u001b[1;36m48\u001b[0m \u001b[1;36m449\u001b[0m \u001b[1;36m354\u001b[0m \u001b[1;36m113\u001b[0m\n", "<PERSON> <PERSON><PERSON>\n", "\u001b[1;36m7\u001b[0m Dart Street\n", "Kalgoorlie WA \u001b[1;36m6430\u001b[0m\n", "GST Invoice for Professional Services\n", "Patient : <PERSON><PERSON><PERSON>\n", "Details on \u001b[1;36m09\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1m(\u001b[0mRef: Dr.<PERSON> - \u001b[1;36m1617838\u001b[0m\u001b[1m)\u001b[0m\n", "Service Provided\n", "No\n", "Amount\n", "Flea, Worm & Tick Preparations\n", "Milbemax Cat Large 20s\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m20.05\u001b[0m\n", "Total\n", "\u001b[1;36m20.05\u001b[0m\n", "Includes GST of :\n", "\u001b[1;36m1.82\u001b[0m\n", "Details on \u001b[1;36m09\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1m(\u001b[0mRef: Dr.<PERSON> - \u001b[1;36m1617850\u001b[0m\u001b[1m)\u001b[0m\n", "Hospital Care - the patient was admitted to hospital for care and to prepare for the veterinary procedure.\n", "Sedation - a sedative was administered to ensure comfort during preparation for the procedure.\n", "Anaesthesia- an evaluation was performed to ensure the safety patient under anesthesia. Medications\n", "were administered and the patient anaethetised and monitored continuously.\n", "Dentistry- a dental scale and polish was performed by the veterinary surgeon. Post op recovery was\n", "undertaken and the patient monitored. A discharge discussion was conducted with post care\n", "counseling.\n", "Service Provided\n", "No\n", "Amount\n", "F4 Vaccinations: Rhinotracheitis, Calicivirus, Panleukopenia & Chlamydia \u001b[1m(\u001b[0mnext due\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m97.70\u001b[0m\n", "in \u001b[1;36m4\u001b[0m weeks\u001b[1m)\u001b[0m\n", "Hospital & Nursing Care - Medical Care unit \u001b[1m(\u001b[0msame day discharge\u001b[1m)\u001b[0m\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m47.15\u001b[0m\n", "Sedation - Premedication before anaesthesia\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m38.55\u001b[0m\n", "Anaesthesia: General - setup & induction\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m100.50\u001b[0m\n", "Anaesthesia: Inhalation and \u001b[1;36m45\u001b[0m mins continuous patient monitoring\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m314.10\u001b[0m\n", "Dental: Major - Scale & Polish With Extractions\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m161.30\u001b[0m\n", "Dental: Complex Surgical Extractions\n", "\u001b[1;36m2.00\u001b[0m\n", "\u001b[1;36m297.60\u001b[0m\n", "Dental: Simple Non Surgical Extractions\n", "\u001b[1;36m2.00\u001b[0m\n", "\u001b[1;36m78.55\u001b[0m\n", "Oral Medications\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m87.50\u001b[0m\n", "Medications\n", "Clavulox Drops \u001b[1;36m15\u001b[0m MI\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m58.05\u001b[0m\n", "Meloxicam Cat Oral \u001b[1;36m3\u001b[0m ml Apex\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m29.45\u001b[0m\n", "Sedatives, Injections and other products\n", "\u001b[1;36m1.00\u001b[0m\n", "Medications\n", "<PERSON><PERSON><PERSON> \u001b[1;36m10\u001b[0m MI\n", "\u001b[1;36m0.18\u001b[0m\n", "\u001b[1;36m29.10\u001b[0m\n", "Butordyne Inj \u001b[1;36m10\u001b[0m MI Jurox \u001b[1m(\u001b[0m\u001b[1;36m10\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m0.18\u001b[0m\n", "\u001b[1;36m25.85\u001b[0m\n", "Domitor \u001b[1;36m10\u001b[0m MI\n", "\u001b[1;36m0.36\u001b[0m\n", "\u001b[1;36m10.80\u001b[0m\n", "Ref - \u001b[1;36m123539\u001b[0m - \u001b[1;36m09\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m17:03:22\u001b[0m\n", "Page \u001b[1;36m1\u001b[0m\n", "THE\n", "\u001b[1;36m464\u001b[0m Hannan Street\n", "ANIMAL\n", "Kalgoorlie, W.A. \u001b[1;36m6430\u001b[0m\n", "HOSPITAL\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m08\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9021\u001b[0m \u001b[1;36m6000\u001b[0m\n", "Fax: \u001b[1m(\u001b[0m\u001b[1;36m08\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9021\u001b[0m \u001b[1;36m6055\u001b[0m\n", "<PERSON> <PERSON><PERSON>\n", "A.B.N: \u001b[1;36m48\u001b[0m \u001b[1;36m449\u001b[0m \u001b[1;36m354\u001b[0m \u001b[1;36m113\u001b[0m\n", "\u001b[1;36m7\u001b[0m Dart Street\n", "Kalgoorlie WA \u001b[1;36m6430\u001b[0m\n", "GST Invoice for Professional Services\n", "VACCINATIONS: While uncommon, adverse sensitivity to immunizations may occur & can include\n", "dullness, muscle soreness, low-grade fever, injection site swelling, hives and facial edoema. If any\n", "such problems occur, please call us immediately.\n", "Patient : <PERSON><PERSON><PERSON>\n", "Isoflurane Vca 250mls\n", "\u001b[1;36m0.10\u001b[0m\n", "\u001b[1;36m12.25\u001b[0m\n", "Ketamine Inj 50ml - by Intramuscular Injection\n", "\u001b[1;36m0.23\u001b[0m\n", "\u001b[1;36m0.20\u001b[0m\n", "Miscellaneous Charge\n", "\u001b[1;36m-78.20\u001b[0m\n", "Inventory for Dental Maintenance\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m5.00\u001b[0m\n", "Consumables\n", "Face Masks\n", "\u001b[1;36m0.02\u001b[0m\n", "\u001b[1;36m0.85\u001b[0m\n", "Gloves Protex P/F Med 100s\n", "\u001b[1;36m0.01\u001b[0m\n", "\u001b[1;36m0.40\u001b[0m\n", "Prophy Cups\n", "\u001b[1;36m0.03\u001b[0m\n", "\u001b[1;36m1.95\u001b[0m\n", "Prophy <PERSON>e 250gm\n", "\u001b[1;36m0.01\u001b[0m\n", "\u001b[1;36m1.55\u001b[0m\n", "Tongue Depressors\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m0.25\u001b[0m\n", "Total\n", "\u001b[1;36m1\u001b[0m,\u001b[1;36m227.95\u001b[0m\n", "Includes GST of :\n", "\u001b[1;36m111.63\u001b[0m\n", "Total - Ra<PERSON><PERSON>\n", "\u001b[1;36m1\u001b[0m,\u001b[1;36m248.00\u001b[0m\n", "Amount <PERSON>\n", "\u001b[1;36m1248.00\u001b[0m\n", "PAID\n", "Balance remaining\n", "\u001b[1;36m0.00\u001b[0m\n", "Ref - \u001b[1;36m123539\u001b[0m - \u001b[1;36m09\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m17:03:22\u001b[0m\n", "Page \u001b[1;36m2\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.081\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7878445, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7878445'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7878445'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">af8cf716-1ee5-4ffb-abd7-01d4635ce0e7</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93maf8cf716-1ee5-4ffb-abd7-01d4635ce0e7\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Arthur'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9563</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6488</span> After Hours <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9572</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1966</span>\n", "Monday - Friday 8am - 6pm\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">181</span> Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE <span style=\"color: #800080; text-decoration-color: #800080\">/</span> RECEIPT\n", "ABN # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">***********</span>\n", "<PERSON>. Client # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59950</span>\n", "Date: Wednesday, October <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "PO Box <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">559</span>\n", "Moama, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2731</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">237409</span>\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Tacrolimus eye drops <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.02</span>%\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span> Arthur\n", "Previous Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-99.00</span>\n", "Subtotal:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span>\n", "GST:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9.00</span>\n", "Payments:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Discounts:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Ending Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "</pre>\n"], "text/plain": ["ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9563\u001b[0m \u001b[1;36m6488\u001b[0m After Hours \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9572\u001b[0m \u001b[1;36m1966\u001b[0m\n", "Monday - Friday 8am - 6pm\n", "\u001b[1;36m181\u001b[0m Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE \u001b[35m/\u001b[0m RECEIPT\n", "ABN # \u001b[1;36m***********\u001b[0m\n", "<PERSON>. Client # \u001b[1;36m59950\u001b[0m\n", "Date: Wednesday, October \u001b[1;36m23\u001b[0m, \u001b[1;36m2024\u001b[0m\n", "PO Box \u001b[1;36m559\u001b[0m\n", "Moama, \u001b[1;36m2731\u001b[0m\n", "Invoice Number: \u001b[1;36m237409\u001b[0m\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m59\u001b[0m Tacrolimus eye drops \u001b[1;36m0.02\u001b[0m%\n", "\u001b[1;36m9.00\u001b[0m\n", "\u001b[1;36m90.00\u001b[0m Arthur\n", "Previous Balance:\n", "\u001b[1;36m-99.00\u001b[0m\n", "Subtotal:\n", "\u001b[1;36m90.00\u001b[0m\n", "GST:\n", "\u001b[1;36m9.00\u001b[0m\n", "Payments:\n", "\u001b[1;36m0.00\u001b[0m\n", "Discounts:\n", "\u001b[1;36m0.00\u001b[0m\n", "Ending Balance:\n", "\u001b[1;36m0.00\u001b[0m\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">cd0ffb8f-9e59-4167-95b4-e0728a16132b</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Name'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'please'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'give'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mcd0ffb8f-9e59-4167-95b4-e0728a16132b\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Name'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'please'\u001b[0m, \u001b[32m'give'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9563</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6488</span> After Hours <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9572</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1966</span>\n", "Monday - Friday 8am - 6pm\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">181</span> Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE <span style=\"color: #800080; text-decoration-color: #800080\">/</span> RECEIPT\n", "ABN # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">***********</span>\n", "<PERSON>. Client # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59950</span>\n", "Date: Wednesday, October <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "PO Box <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">559</span>\n", "Moama, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2731</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">237403</span>\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Initial Eye Examination - Emergency\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27.27</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">272.73</span> Arthur\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Payment - . East Malvern Card\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-99.00</span> Arthur\n", "Previous Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-300.00</span>\n", "Subtotal:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">272.73</span>\n", "GST:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27.27</span>\n", "Payments:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">99.00</span>\n", "Discounts:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "-\n", "Ending Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-99.00</span>\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "</pre>\n"], "text/plain": ["ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9563\u001b[0m \u001b[1;36m6488\u001b[0m After Hours \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9572\u001b[0m \u001b[1;36m1966\u001b[0m\n", "Monday - Friday 8am - 6pm\n", "\u001b[1;36m181\u001b[0m Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE \u001b[35m/\u001b[0m RECEIPT\n", "ABN # \u001b[1;36m***********\u001b[0m\n", "<PERSON>. Client # \u001b[1;36m59950\u001b[0m\n", "Date: Wednesday, October \u001b[1;36m23\u001b[0m, \u001b[1;36m2024\u001b[0m\n", "PO Box \u001b[1;36m559\u001b[0m\n", "Moama, \u001b[1;36m2731\u001b[0m\n", "Invoice Number: \u001b[1;36m237403\u001b[0m\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m59\u001b[0m Initial Eye Examination - Emergency\n", "\u001b[1;36m27.27\u001b[0m\n", "\u001b[1;36m272.73\u001b[0m Arthur\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m59\u001b[0m Payment - . East Malvern Card\n", "\u001b[1;36m0.00\u001b[0m\n", "\u001b[1;36m-99.00\u001b[0m Arthur\n", "Previous Balance:\n", "\u001b[1;36m-300.00\u001b[0m\n", "Subtotal:\n", "\u001b[1;36m272.73\u001b[0m\n", "GST:\n", "\u001b[1;36m27.27\u001b[0m\n", "Payments:\n", "\u001b[1;36m99.00\u001b[0m\n", "Discounts:\n", "\u001b[1;36m0.00\u001b[0m\n", "-\n", "Ending Balance:\n", "\u001b[1;36m-99.00\u001b[0m\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.099\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7878445, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7878445'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7878445'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Name'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'please'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'give'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'Name'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'please'\u001b[0m, \u001b[32m'give'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">af8cf716-1ee5-4ffb-abd7-01d4635ce0e7</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93maf8cf716-1ee5-4ffb-abd7-01d4635ce0e7\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Arthur'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9563</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6488</span> After Hours <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9572</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1966</span>\n", "Monday - Friday 8am - 6pm\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">181</span> Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE <span style=\"color: #800080; text-decoration-color: #800080\">/</span> RECEIPT\n", "ABN # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">***********</span>\n", "<PERSON>. Client # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59950</span>\n", "Date: Wednesday, October <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "PO Box <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">559</span>\n", "Moama, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2731</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">237409</span>\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Tacrolimus eye drops <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.02</span>%\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span> Arthur\n", "Previous Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-99.00</span>\n", "Subtotal:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span>\n", "GST:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9.00</span>\n", "Payments:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Discounts:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Ending Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "</pre>\n"], "text/plain": ["ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9563\u001b[0m \u001b[1;36m6488\u001b[0m After Hours \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9572\u001b[0m \u001b[1;36m1966\u001b[0m\n", "Monday - Friday 8am - 6pm\n", "\u001b[1;36m181\u001b[0m Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE \u001b[35m/\u001b[0m RECEIPT\n", "ABN # \u001b[1;36m***********\u001b[0m\n", "<PERSON>. Client # \u001b[1;36m59950\u001b[0m\n", "Date: Wednesday, October \u001b[1;36m23\u001b[0m, \u001b[1;36m2024\u001b[0m\n", "PO Box \u001b[1;36m559\u001b[0m\n", "Moama, \u001b[1;36m2731\u001b[0m\n", "Invoice Number: \u001b[1;36m237409\u001b[0m\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m59\u001b[0m Tacrolimus eye drops \u001b[1;36m0.02\u001b[0m%\n", "\u001b[1;36m9.00\u001b[0m\n", "\u001b[1;36m90.00\u001b[0m Arthur\n", "Previous Balance:\n", "\u001b[1;36m-99.00\u001b[0m\n", "Subtotal:\n", "\u001b[1;36m90.00\u001b[0m\n", "GST:\n", "\u001b[1;36m9.00\u001b[0m\n", "Payments:\n", "\u001b[1;36m0.00\u001b[0m\n", "Discounts:\n", "\u001b[1;36m0.00\u001b[0m\n", "Ending Balance:\n", "\u001b[1;36m0.00\u001b[0m\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">cd0ffb8f-9e59-4167-95b4-e0728a16132b</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Name'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Arthur'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'please'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'give'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mcd0ffb8f-9e59-4167-95b4-e0728a16132b\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Name'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'please'\u001b[0m, \u001b[32m'give'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9563</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6488</span> After Hours <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9572</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1966</span>\n", "Monday - Friday 8am - 6pm\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">181</span> Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE <span style=\"color: #800080; text-decoration-color: #800080\">/</span> RECEIPT\n", "ABN # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">***********</span>\n", "<PERSON>. Client # <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59950</span>\n", "Date: Wednesday, October <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "PO Box <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">559</span>\n", "Moama, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2731</span>\n", "Invoice Number: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">237403</span>\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Initial Eye Examination - Emergency\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27.27</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">272.73</span> Arthur\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Payment - . East Malvern Card\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-99.00</span> Arthur\n", "Previous Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-300.00</span>\n", "Subtotal:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">272.73</span>\n", "GST:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27.27</span>\n", "Payments:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">99.00</span>\n", "Discounts:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "-\n", "Ending Balance:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-99.00</span>\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "</pre>\n"], "text/plain": ["ANIMAL EYE CARE\n", "BE PART OF OUR VISION\n", "<PERSON> <PERSON> | <PERSON> <PERSON>\n", "Dr <PERSON> | Dr <PERSON> | Dr <PERSON><PERSON> | Dr <PERSON>\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9563\u001b[0m \u001b[1;36m6488\u001b[0m After Hours \u001b[1m(\u001b[0m\u001b[1;36m03\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9572\u001b[0m \u001b[1;36m1966\u001b[0m\n", "Monday - Friday 8am - 6pm\n", "\u001b[1;36m181\u001b[0m Darling Rd, East Malvern - Corner Brunel St\n", "TAX INVOICE \u001b[35m/\u001b[0m RECEIPT\n", "ABN # \u001b[1;36m***********\u001b[0m\n", "<PERSON>. Client # \u001b[1;36m59950\u001b[0m\n", "Date: Wednesday, October \u001b[1;36m23\u001b[0m, \u001b[1;36m2024\u001b[0m\n", "PO Box \u001b[1;36m559\u001b[0m\n", "Moama, \u001b[1;36m2731\u001b[0m\n", "Invoice Number: \u001b[1;36m237403\u001b[0m\n", "Date\n", "Doctor Description\n", "GST\n", "Charge Pat Name\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m59\u001b[0m Initial Eye Examination - Emergency\n", "\u001b[1;36m27.27\u001b[0m\n", "\u001b[1;36m272.73\u001b[0m Arthur\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m59\u001b[0m Payment - . East Malvern Card\n", "\u001b[1;36m0.00\u001b[0m\n", "\u001b[1;36m-99.00\u001b[0m Arthur\n", "Previous Balance:\n", "\u001b[1;36m-300.00\u001b[0m\n", "Subtotal:\n", "\u001b[1;36m272.73\u001b[0m\n", "GST:\n", "\u001b[1;36m27.27\u001b[0m\n", "Payments:\n", "\u001b[1;36m99.00\u001b[0m\n", "Discounts:\n", "\u001b[1;36m0.00\u001b[0m\n", "-\n", "Ending Balance:\n", "\u001b[1;36m-99.00\u001b[0m\n", "Thank you for trusting us with the eye care of <PERSON>. If you have any questions or concerns, please give us a \n", "call.\n", "Our aim is to provide the best possible Animal Eye Care.\n", "\u001b[1;36m10\u001b[0m/\u001b[1;36m23\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.119\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7881294, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7881294'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Coco Fanny'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Coco Fanny'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Cooper'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7881294'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Coco Fanny'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Coco Fanny'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">eb3e43cb-6666-4ff9-a9cb-9177b660385b</span>.png <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93meb3e43cb-6666-4ff9-a9cb-9177b660385b\u001b[0m.png \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:42</span>\n", ".<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>|| 5G <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">85</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>\n", "Your Invoice from <span style=\"color: #808000; text-decoration-color: #808000\">...</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "If you have requested medical records for insurance purposes, please take\n", "note these will come sperately and may take up to <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48</span> hours.\n", "Vet24\n", "Follow us:\n", "f\n", "GENERAL | EMERGENCY I REFERRAL\n", "<EMAIL>\n", "Phone: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6318</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5300</span>\n", "vet24.com.au\n", "Thank You for Visiting!\n", "Vet24\n", "This message is confidential. It may also be privileged or otherwise protected by work product immunity or other \n", "legal rules. If you have received it by mistake,\n", "please let us know by e-mail reply and delete it from your system; you may not copy this message or disclose its \n", "contents to anyone. Please send us by fax any\n", "message containing deadlines as incoming e-mails are not screened for response deadlines. The integrity and \n", "security of this message cannot be guaranteed on the\n", "Internet.\n", "Vet24\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> <span style=\"color: #800080; text-decoration-color: #800080\">/</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "ABN:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">55</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">465</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">940</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">665</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">59</span> Erindale Rd\n", "Balcatta, WA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6021</span>\n", "Ph: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6318</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5300</span>\n", "Client ID:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">66872</span>\n", "<PERSON>\n", "TAX INVOICE #:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">741695</span>\n", "16c Offham Way\n", "Date:\n", "Westminster, WA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6061</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">**********</span>\n", "<EMAIL>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">108661</span>\n", "Species: Canine\n", "Weight:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14.10</span> kilograms\n", "Patient Name: <PERSON><PERSON>\n", "Breed: <PERSON><PERSON><PERSON><PERSON>\n", "Birthday: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">01</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2014</span>\n", "Sex: S<PERSON>yed Female\n", "Description\n", "Staff Name\n", "Quantity\n", "Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Vetoryl Capsules 30mg\n", "Dr. <PERSON> BVSc.\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">230.95</span>\n", "Consultation Progress - Check\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">90.00</span>\n", "Patient Subtotal:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320.95</span>\n", "Invoice Total:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320.95</span>\n", "Total:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320.95</span>\n", "Invoice Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320.95</span>\n", ".Eftpos/CC's/Diners/Amex:\n", "<span style=\"font-weight: bold\">(</span>$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320.95</span><span style=\"font-weight: bold\">)</span>\n", "Less Payment:\n", "<span style=\"font-weight: bold\">(</span>$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">320.95</span><span style=\"font-weight: bold\">)</span>\n", "Invoice Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "The total price includes GST of $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.18</span>\n", "Scheduled Appointments:\n", "Appt. for Coco on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">07:30</span> am.\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:00</span> am. :selected: :selected: :unselected:\n", "</pre>\n"], "text/plain": ["\u001b[1;92m12:42\u001b[0m\n", ".\u001b[1;36m1\u001b[0m|| 5G \u001b[1;36m85\u001b[0m\n", "\u001b[1;36m2\u001b[0m\n", "Your Invoice from \u001b[33m...\u001b[0m\n", "\u001b[1;36m1\u001b[0m\n", "If you have requested medical records for insurance purposes, please take\n", "note these will come sperately and may take up to \u001b[1;36m48\u001b[0m hours.\n", "Vet24\n", "Follow us:\n", "f\n", "GENERAL | EMERGENCY I REFERRAL\n", "<EMAIL>\n", "Phone: \u001b[1;36m08\u001b[0m \u001b[1;36m6318\u001b[0m \u001b[1;36m5300\u001b[0m\n", "vet24.com.au\n", "Thank You for Visiting!\n", "Vet24\n", "This message is confidential. It may also be privileged or otherwise protected by work product immunity or other \n", "legal rules. If you have received it by mistake,\n", "please let us know by e-mail reply and delete it from your system; you may not copy this message or disclose its \n", "contents to anyone. Please send us by fax any\n", "message containing deadlines as incoming e-mails are not screened for response deadlines. The integrity and \n", "security of this message cannot be guaranteed on the\n", "Internet.\n", "Vet24\n", "Page \u001b[1;36m1\u001b[0m \u001b[35m/\u001b[0m \u001b[1;36m1\u001b[0m\n", "ABN:\u001b[1;36m55\u001b[0m \u001b[1;36m465\u001b[0m \u001b[1;36m940\u001b[0m \u001b[1;36m665\u001b[0m\n", "\u001b[1;36m59\u001b[0m Erindale Rd\n", "Balcatta, WA \u001b[1;36m6021\u001b[0m\n", "Ph: \u001b[1;36m6318\u001b[0m-\u001b[1;36m5300\u001b[0m\n", "Client ID:\n", "\u001b[1;36m66872\u001b[0m\n", "<PERSON>\n", "TAX INVOICE #:\n", "\u001b[1;36m741695\u001b[0m\n", "16c Offham Way\n", "Date:\n", "Westminster, WA \u001b[1;36m6061\u001b[0m\n", "\u001b[1;36m28\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "\u001b[1;36m**********\u001b[0m\n", "<EMAIL>\n", "Patient ID: \u001b[1;36m108661\u001b[0m\n", "Species: Canine\n", "Weight:\n", "\u001b[1;36m14.10\u001b[0m kilograms\n", "Patient Name: <PERSON><PERSON>\n", "Breed: <PERSON><PERSON><PERSON><PERSON>\n", "Birthday: \u001b[1;36m31\u001b[0m/\u001b[1;36m01\u001b[0m/\u001b[1;36m2014\u001b[0m\n", "Sex: S<PERSON>yed Female\n", "Description\n", "Staff Name\n", "Quantity\n", "Total\n", "\u001b[1;36m28\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Vetoryl Capsules 30mg\n", "Dr. <PERSON> BVSc.\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m230.95\u001b[0m\n", "Consultation Progress - Check\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m90.00\u001b[0m\n", "Patient Subtotal:\n", "$\u001b[1;36m320.95\u001b[0m\n", "Invoice Total:\n", "$\u001b[1;36m320.95\u001b[0m\n", "Total:\n", "$\u001b[1;36m320.95\u001b[0m\n", "Invoice Balance Due:\n", "$\u001b[1;36m320.95\u001b[0m\n", ".Eftpos/CC's/Diners/Amex:\n", "\u001b[1m(\u001b[0m$\u001b[1;36m320.95\u001b[0m\u001b[1m)\u001b[0m\n", "Less Payment:\n", "\u001b[1m(\u001b[0m$\u001b[1;36m320.95\u001b[0m\u001b[1m)\u001b[0m\n", "Invoice Balance Due:\n", "$\u001b[1;36m0.00\u001b[0m\n", "Balance Due:\n", "$\u001b[1;36m0.00\u001b[0m\n", "The total price includes GST of $\u001b[1;36m29.18\u001b[0m\n", "Scheduled Appointments:\n", "Appt. for Coco on \u001b[1;36m7\u001b[0m/\u001b[1;36m11\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m07:30\u001b[0m am.\n", "Appt. for <PERSON> on \u001b[1;36m18\u001b[0m/\u001b[1;36m11\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m10:00\u001b[0m am. :selected: :selected: :unselected:\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.132\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7694777, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7694777'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Walter'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Maple'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Walter'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7694777'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Maple'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'CYTOPOINT'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'DURAMUNE'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PROTECH'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'DENTAL'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Maple'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Lola'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ADULT'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Walter'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'BRONCHISHIELD'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PROHEART'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'CYTOPOINT'\u001b[0m, \u001b[32m'DURAMUNE'\u001b[0m, \u001b[32m'PROTECH'\u001b[0m, \u001b[32m'DENTAL'\u001b[0m, \u001b[32m'Maple'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'ADULT'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'BRONCHISHIELD'\u001b[0m, \u001b[32m'PROHEART'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">872bd819-93a8-4e1f-a2f6-9d78c4c41118</span>.png <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>,\n", "    <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'CYTOPOINT'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'DURAMUNE'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'PROTECH'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'DENTAL'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Maple'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Lola'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'ADULT'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'BRONCHISHIELD'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'PROHEART'</span>\n", "    <span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m872bd819-93a8-4e1f-a2f6-9d78c4c41118\u001b[0m.png \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\n", "    \u001b[1;36m100\u001b[0m,\n", "    \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m,\n", "    \u001b[1m[\u001b[0m\n", "        \u001b[32m'CYTOPOINT'\u001b[0m,\n", "        \u001b[32m'DURAMUNE'\u001b[0m,\n", "        \u001b[32m'PROTECH'\u001b[0m,\n", "        \u001b[32m'DENTAL'\u001b[0m,\n", "        \u001b[32m'Maple'\u001b[0m,\n", "        \u001b[32m'<PERSON>'\u001b[0m,\n", "        \u001b[32m'ADULT'\u001b[0m,\n", "        \u001b[32m'<PERSON>'\u001b[0m,\n", "        \u001b[32m'BRONCHISHIELD'\u001b[0m,\n", "        \u001b[32m'PROHEART'\u001b[0m\n", "    \u001b[1m]\u001b[0m,\n", "    \u001b[1;36m1\u001b[0m,\n", "    \u001b[1;36m1\u001b[0m,\n", "    \u001b[1;36m1\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">8:08</span>\n", "X\n", "Stanbridge-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09112024</span>\n", "PDF - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">15</span> KB\n", "Port Adelaide Veterinary Clinic\n", "ABN:<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">***********</span>\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> <span style=\"color: #800080; text-decoration-color: #800080\">/</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">190</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">194</span> Hart Street\n", "Ethelton, SA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5015</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8449</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4011</span>\n", "Mr. <PERSON>\n", "Client ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8969</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">13</span> Bartlett Terrace\n", "Semaphore Park, SA <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5019</span>\n", "Tax Invoice #: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">146617</span>\n", "Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Patient ID: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">49005</span>\n", "Species: Canine\n", "Weight: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">41.80</span> kilograms\n", "Patient Name: <PERSON>\n", "Breed: <PERSON><PERSON><PERSON>\n", "Birthday: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2021</span>\n", "Sex: Neutered Male\n", "Description\n", "Staff Name\n", "Quantity\n", "Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "CONSULTATION REVIEW\n", "Dr. <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">99.00</span>\n", "CYTOLOGY PER SLIDE\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">79.70</span>\n", "CYTOPOINT 40MG <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> VIAL\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">246.40</span>\n", "Patient Subtotal:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span>\n", "Reminder\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> DENTAL HEALTH CHECK\n", "CYTOPOINT 40MG <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> VIAL\n", "ADULT DOG HEALTH CHECK\n", "PROTECH PI2 VACCINATION\n", "PROHEART SR-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span> INJECTION\n", "BRONCHISHIELD ORAL VACCINATION\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2025</span> DURAMUNE ADULT C4 VACCINATION\n", "Invoice Total:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span>\n", "Total:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span>\n", "Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span>\n", "Previous Balance:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span>\n", "Credit Card:\n", "<span style=\"font-weight: bold\">(</span>$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span><span style=\"font-weight: bold\">)</span>\n", "Less Payment:\n", "<span style=\"font-weight: bold\">(</span>$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">425.10</span><span style=\"font-weight: bold\">)</span>\n", "Balance Due:\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "The total price includes GST of $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38.65</span>\n", "Scheduled Appointments:\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">12:00</span> pm.\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">04:45</span> pm.\n", "Appt. for Maple on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">23</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">10:45</span> am.\n", "Appt. for Maple on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">06:15</span> pm.\n", "Appt. for <PERSON> on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> at <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">06:30</span> pm.\n", "Please note that revisit charges will apply in most cases.\n", "of service to you and your furry loved ones !\n", "It is a pleasure being\n", "Open Microsoft <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">365</span> <span style=\"font-weight: bold\">(</span>Office<span style=\"font-weight: bold\">)</span>\n", "+ :unselected:\n", "</pre>\n"], "text/plain": ["\u001b[1;92m8:08\u001b[0m\n", "X\n", "Stanbridge-\u001b[1;36m09112024\u001b[0m\n", "PDF - \u001b[1;36m15\u001b[0m KB\n", "Port Adelaide Veterinary Clinic\n", "ABN:\u001b[1;36m***********\u001b[0m\n", "Page \u001b[1;36m1\u001b[0m \u001b[35m/\u001b[0m \u001b[1;36m1\u001b[0m\n", "\u001b[1;36m190\u001b[0m-\u001b[1;36m194\u001b[0m Hart Street\n", "Ethelton, SA \u001b[1;36m5015\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m08\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m8449\u001b[0m \u001b[1;36m4011\u001b[0m\n", "Mr. <PERSON>\n", "Client ID: \u001b[1;36m8969\u001b[0m\n", "\u001b[1;36m1\u001b[0m/\u001b[1;36m13\u001b[0m Bartlett Terrace\n", "Semaphore Park, SA \u001b[1;36m5019\u001b[0m\n", "Tax Invoice #: \u001b[1;36m146617\u001b[0m\n", "Date: \u001b[1;36m11\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Patient ID: \u001b[1;36m49005\u001b[0m\n", "Species: Canine\n", "Weight: \u001b[1;36m41.80\u001b[0m kilograms\n", "Patient Name: <PERSON>\n", "Breed: <PERSON><PERSON><PERSON>\n", "Birthday: \u001b[1;36m09\u001b[0m/\u001b[1;36m08\u001b[0m/\u001b[1;36m2021\u001b[0m\n", "Sex: Neutered Male\n", "Description\n", "Staff Name\n", "Quantity\n", "Total\n", "\u001b[1;36m11\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "CONSULTATION REVIEW\n", "Dr. <PERSON>\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m99.00\u001b[0m\n", "CYTOLOGY PER SLIDE\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m79.70\u001b[0m\n", "CYTOPOINT 40MG \u001b[1;36m1\u001b[0m VIAL\n", "\u001b[1;36m1.00\u001b[0m\n", "$\u001b[1;36m246.40\u001b[0m\n", "Patient Subtotal:\n", "$\u001b[1;36m425.10\u001b[0m\n", "Reminder\n", "\u001b[1;36m18\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m DENTAL HEALTH CHECK\n", "CYTOPOINT 40MG \u001b[1;36m1\u001b[0m VIAL\n", "ADULT DOG HEALTH CHECK\n", "PROTECH PI2 VACCINATION\n", "PROHEART SR-\u001b[1;36m12\u001b[0m INJECTION\n", "BRONCHISHIELD ORAL VACCINATION\n", "\u001b[1;36m21\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2025\u001b[0m DURAMUNE ADULT C4 VACCINATION\n", "Invoice Total:\n", "$\u001b[1;36m425.10\u001b[0m\n", "Total:\n", "$\u001b[1;36m425.10\u001b[0m\n", "Balance Due:\n", "$\u001b[1;36m425.10\u001b[0m\n", "Previous Balance:\n", "$\u001b[1;36m0.00\u001b[0m\n", "Balance Due:\n", "$\u001b[1;36m425.10\u001b[0m\n", "Credit Card:\n", "\u001b[1m(\u001b[0m$\u001b[1;36m425.10\u001b[0m\u001b[1m)\u001b[0m\n", "Less Payment:\n", "\u001b[1m(\u001b[0m$\u001b[1;36m425.10\u001b[0m\u001b[1m)\u001b[0m\n", "Balance Due:\n", "$\u001b[1;36m0.00\u001b[0m\n", "The total price includes GST of $\u001b[1;36m38.65\u001b[0m\n", "Scheduled Appointments:\n", "Appt. for <PERSON> on \u001b[1;36m12\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m12:00\u001b[0m pm.\n", "Appt. for <PERSON> on \u001b[1;36m18\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m04:45\u001b[0m pm.\n", "Appt. for Maple on \u001b[1;36m23\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m10:45\u001b[0m am.\n", "Appt. for Maple on \u001b[1;36m25\u001b[0m/\u001b[1;36m11\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m06:15\u001b[0m pm.\n", "Appt. for Lola on \u001b[1;36m25\u001b[0m/\u001b[1;36m11\u001b[0m/\u001b[1;36m2024\u001b[0m at \u001b[1;92m06:30\u001b[0m pm.\n", "Please note that revisit charges will apply in most cases.\n", "of service to you and your furry loved ones !\n", "It is a pleasure being\n", "Open Microsoft \u001b[1;36m365\u001b[0m \u001b[1m(\u001b[0mOffice\u001b[1m)\u001b[0m\n", "+ :unselected:\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.146\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7720325, sim_score_fp: False, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7720325'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Luna'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Luna'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7720325'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Luna'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Luna'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span>\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'damage'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Therapy'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Complex'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'once'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'supportive'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Suture'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Metacam'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Routine'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'During'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'progress'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Luna'</span>,\n", "    <span style=\"color: #008000; text-decoration-color: #008000\">'Clavulox'</span>\n", "<span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\n", "    \u001b[32m'damage'\u001b[0m,\n", "    \u001b[32m'Therapy'\u001b[0m,\n", "    \u001b[32m'Complex'\u001b[0m,\n", "    \u001b[32m'once'\u001b[0m,\n", "    \u001b[32m'supportive'\u001b[0m,\n", "    \u001b[32m'Su<PERSON>'\u001b[0m,\n", "    \u001b[32m'Metacam'\u001b[0m,\n", "    \u001b[32m'Routine'\u001b[0m,\n", "    \u001b[32m'During'\u001b[0m,\n", "    \u001b[32m'progress'\u001b[0m,\n", "    \u001b[32m'Luna'\u001b[0m,\n", "    \u001b[32m'Clavulox'\u001b[0m\n", "\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">80b94826-d25c-416e-8573-d0e0e6f676d3</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span>\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>,\n", "    <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"font-weight: bold\">[</span>\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'damage'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Therapy'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Complex'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'once'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'supportive'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Suture'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Metacam'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Routine'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'During'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'progress'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Luna'</span>,\n", "        <span style=\"color: #008000; text-decoration-color: #008000\">'Clavulox'</span>\n", "    <span style=\"font-weight: bold\">]</span>,\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>,\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,\n", "    <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m80b94826-d25c-416e-8573-d0e0e6f676d3\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\n", "    \u001b[1;36m100\u001b[0m,\n", "    \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m,\n", "    \u001b[1m[\u001b[0m\n", "        \u001b[32m'damage'\u001b[0m,\n", "        \u001b[32m'Therapy'\u001b[0m,\n", "        \u001b[32m'Complex'\u001b[0m,\n", "        \u001b[32m'once'\u001b[0m,\n", "        \u001b[32m'supportive'\u001b[0m,\n", "        \u001b[32m'Su<PERSON>'\u001b[0m,\n", "        \u001b[32m'Metacam'\u001b[0m,\n", "        \u001b[32m'Routine'\u001b[0m,\n", "        \u001b[32m'During'\u001b[0m,\n", "        \u001b[32m'progress'\u001b[0m,\n", "        \u001b[32m'Luna'\u001b[0m,\n", "        \u001b[32m'Clavulox'\u001b[0m\n", "    \u001b[1m]\u001b[0m,\n", "    \u001b[1;36m0\u001b[0m,\n", "    \u001b[1;36m1\u001b[0m,\n", "    \u001b[1;36m1\u001b[0m\n", "\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">155</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">694</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">164</span>\n", "VIC <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3121</span>\n", "Tax Invoice for Professional Services\n", "Patient : Luna\n", "Details on <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Ref : <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">760474</span> - Surgery\n", "The <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> most common reasons for tooth extraction in Cats is Tooth resorption <span style=\"font-weight: bold\">(</span>TR<span style=\"font-weight: bold\">)</span> and Periodontitis <span style=\"font-weight: bold\">(</span>PD<span style=\"font-weight: bold\">)</span>.\n", "Your cat has most likely had <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> or both of these conditions identified today, leading to the tooth\n", "extractions.\n", "Tooth resorption <span style=\"font-weight: bold\">(</span>TR<span style=\"font-weight: bold\">)</span> is a condition where the tooth hard tissue <span style=\"font-weight: bold\">(</span>enamel and dentin<span style=\"font-weight: bold\">)</span> is destroyed. This\n", "can be secondary to an inflammatory process, or can <span style=\"color: #008000; text-decoration-color: #008000\">'just happen'</span>. Sometimes the tooth root is replaced\n", "by bone, and in these cases, the tooth roots do not need to be extracted. There is nothing you can do to\n", "prevent TR. If it's going to happen, it will regardless of your home dental care. We can identify late\n", "stages of TR, once the crown has started to break. We can only identify early stages with dental\n", "radiography. If TR is detected, it will progress to the point of tooth destruction, unless the affected tooth\n", "is extracted. TR is a painful process once it is exposed to the oral cavity. Tooth extraction is the\n", "treatment of choice\n", "Periodontitis <span style=\"font-weight: bold\">(</span>PD<span style=\"font-weight: bold\">)</span> is an inflammatory immune disease that results in destruction of the supportive tooth\n", "structures <span style=\"font-weight: bold\">(</span>gingiva, periodontal ligament, alveolar bone<span style=\"font-weight: bold\">)</span>. The inciting cause is oral bacterial plaque, but\n", "the most damage is actually done by the cat's own immune system as it tries to destroy the oral bacteria.\n", "PD is reversible in the early stages <span style=\"font-weight: bold\">(</span>gingivitis<span style=\"font-weight: bold\">)</span> by a professional teeth clean, and then home care\n", "management. Once there is destruction of the peridontal ligament and alveolar bone, the damage is\n", "permanent. The weakening of these supportive structures then leads to loose teeth, and extraction is\n", "indicated. We assess the degree of PD in a dental COHAT, and if there is irreversible damage, the tooth\n", "will be extracted.\n", "Please see the home care instructions for ways to prevent PD at home.\n", "Service Provided <span style=\"font-weight: bold\">(</span>Ref: <PERSON> <PERSON><span style=\"font-weight: bold\">)</span>\n", "No\n", "Amount\n", "Medication\n", "Clavulox 50mg Antibiotic Tablets\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">12.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">34.40</span>\n", "Clavulox Antibiotic Injectable\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.27</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">39.00</span>\n", "Lignocaine 20mg/ml Local Anaesthetic\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.50</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">38.30</span>\n", "Metacam 3ml For Pain Relief/Arthritis\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">40.30</span>\n", "Fluid Therapy\n", "Intravenous Fluid Support During Surgery\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">112.30</span>\n", "Anaesthesia\n", "Gaseous Anaesthesia\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">420.00</span>\n", "Hospitalisation\n", "Routine Hospitalisation\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">109.50</span>\n", "Dentistry\n", "Complex Extraction With Gingival Flap\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">300.00</span>\n", "Ultrasonic Scale, Clean &amp; Polish\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150.00</span>\n", "Dental/Wet Pack Preparation Fee\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">52.50</span>\n", "Full Mouth Dental Radiographs\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">150.00</span>\n", "Routine Extraction\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">45.00</span>\n", "Consumables\n", "Suture - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">5</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> Monosyn C0022419\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">28.50</span>\n", "Inhouse Pathology\n", "IH Pre-Anaesthetic Profile <span style=\"font-weight: bold\">(</span>CBC + Chem <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span> + Lyte <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span><span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">198.00</span>\n", "Ref <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24129</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">15:27:06</span>\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<PERSON> <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">155</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">694</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">164</span>\n", "VIC <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3121</span>\n", "Tax Invoice for Professional Services\n", "Patient : Luna\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">717.80</span>\n", "Includes Tax of :\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">156.16</span>\n", "No charges\n", "Total - Luna\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>,<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">717.80</span>\n", "Balance owing $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Next Appointment Details:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">15:00</span> PM : Consult for Luna\n", "Ref <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24129</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">15:27:06</span>\n", "Page <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span>\n", "</pre>\n"], "text/plain": ["<PERSON> <PERSON>\n", "\u001b[1;36m83\u001b[0m \u001b[1;36m155\u001b[0m \u001b[1;36m694\u001b[0m \u001b[1;36m164\u001b[0m\n", "VIC \u001b[1;36m3121\u001b[0m\n", "Tax Invoice for Professional Services\n", "Patient : Luna\n", "Details on \u001b[1;36m11\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Ref : \u001b[1;36m760474\u001b[0m - Surgery\n", "The \u001b[1;36m2\u001b[0m most common reasons for tooth extraction in Cats is Tooth resorption \u001b[1m(\u001b[0mTR\u001b[1m)\u001b[0m and Periodontitis \u001b[1m(\u001b[0mPD\u001b[1m)\u001b[0m.\n", "Your cat has most likely had \u001b[1;36m1\u001b[0m or both of these conditions identified today, leading to the tooth\n", "extractions.\n", "Tooth resorption \u001b[1m(\u001b[0mTR\u001b[1m)\u001b[0m is a condition where the tooth hard tissue \u001b[1m(\u001b[0menamel and dentin\u001b[1m)\u001b[0m is destroyed. This\n", "can be secondary to an inflammatory process, or can \u001b[32m'just happen'\u001b[0m. Sometimes the tooth root is replaced\n", "by bone, and in these cases, the tooth roots do not need to be extracted. There is nothing you can do to\n", "prevent TR. If it's going to happen, it will regardless of your home dental care. We can identify late\n", "stages of TR, once the crown has started to break. We can only identify early stages with dental\n", "radiography. If TR is detected, it will progress to the point of tooth destruction, unless the affected tooth\n", "is extracted. TR is a painful process once it is exposed to the oral cavity. Tooth extraction is the\n", "treatment of choice\n", "Periodontitis \u001b[1m(\u001b[0mPD\u001b[1m)\u001b[0m is an inflammatory immune disease that results in destruction of the supportive tooth\n", "structures \u001b[1m(\u001b[0mgingiva, periodontal ligament, alveolar bone\u001b[1m)\u001b[0m. The inciting cause is oral bacterial plaque, but\n", "the most damage is actually done by the cat's own immune system as it tries to destroy the oral bacteria.\n", "PD is reversible in the early stages \u001b[1m(\u001b[0mgingivitis\u001b[1m)\u001b[0m by a professional teeth clean, and then home care\n", "management. Once there is destruction of the peridontal ligament and alveolar bone, the damage is\n", "permanent. The weakening of these supportive structures then leads to loose teeth, and extraction is\n", "indicated. We assess the degree of PD in a dental COHAT, and if there is irreversible damage, the tooth\n", "will be extracted.\n", "Please see the home care instructions for ways to prevent PD at home.\n", "Service Provided \u001b[1m(\u001b[0mRef: <PERSON> <PERSON>\u001b[1m)\u001b[0m\n", "No\n", "Amount\n", "Medication\n", "Clavulox 50mg Antibiotic Tablets\n", "\u001b[1;36m12.00\u001b[0m\n", "\u001b[1;36m34.40\u001b[0m\n", "Clavulox Antibiotic Injectable\n", "\u001b[1;36m0.27\u001b[0m\n", "\u001b[1;36m39.00\u001b[0m\n", "Lignocaine 20mg/ml Local Anaesthetic\n", "\u001b[1;36m0.50\u001b[0m\n", "\u001b[1;36m38.30\u001b[0m\n", "Metacam 3ml For Pain Relief/Arthritis\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m40.30\u001b[0m\n", "Fluid Therapy\n", "Intravenous Fluid Support During Surgery\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m112.30\u001b[0m\n", "Anaesthesia\n", "Gaseous Anaesthesia\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m420.00\u001b[0m\n", "Hospitalisation\n", "Routine Hospitalisation\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m109.50\u001b[0m\n", "Dentistry\n", "Complex Extraction With Gingival Flap\n", "\u001b[1;36m2.00\u001b[0m\n", "\u001b[1;36m300.00\u001b[0m\n", "Ultrasonic Scale, Clean & Polish\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m150.00\u001b[0m\n", "Dental/Wet Pack Preparation Fee\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m52.50\u001b[0m\n", "Full Mouth Dental Radiographs\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m150.00\u001b[0m\n", "Routine Extraction\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m45.00\u001b[0m\n", "Consumables\n", "Suture - \u001b[1;36m5\u001b[0m/\u001b[1;36m0\u001b[0m Monosyn C0022419\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m28.50\u001b[0m\n", "Inhouse Pathology\n", "IH Pre-Anaesthetic Profile \u001b[1m(\u001b[0mCBC + Chem \u001b[1;36m11\u001b[0m + Lyte \u001b[1;36m4\u001b[0m\u001b[1m)\u001b[0m\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m198.00\u001b[0m\n", "Ref \u001b[1;36m24129\u001b[0m - \u001b[1;36m18\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m15:27:06\u001b[0m\n", "Page \u001b[1;36m1\u001b[0m\n", "<PERSON> <PERSON>\n", "\u001b[1;36m83\u001b[0m \u001b[1;36m155\u001b[0m \u001b[1;36m694\u001b[0m \u001b[1;36m164\u001b[0m\n", "VIC \u001b[1;36m3121\u001b[0m\n", "Tax Invoice for Professional Services\n", "Patient : Luna\n", "\u001b[1;36m1\u001b[0m,\u001b[1;36m717.80\u001b[0m\n", "Includes Tax of :\n", "\u001b[1;36m156.16\u001b[0m\n", "No charges\n", "Total - Luna\n", "\u001b[1;36m1\u001b[0m,\u001b[1;36m717.80\u001b[0m\n", "Balance owing $\u001b[1;36m0.00\u001b[0m\n", "Next Appointment Details:\n", "\u001b[1;36m24\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1;92m15:00\u001b[0m PM : Consult for Luna\n", "Ref \u001b[1;36m24129\u001b[0m - \u001b[1;36m18\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m15:27:06\u001b[0m\n", "Page \u001b[1;36m2\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.167\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7835185, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7835185'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7835185'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Veterinary'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">b6faedae-fb87-4ed4-90de-4d6a227a53cb</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">67</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Veterinary'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Trevor'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Charlie'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mb6faedae-fb87-4ed4-90de-4d6a227a53cb\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m67\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Veterinary'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Mr <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">155</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">694</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">164</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">27</span> Northcote Street\n", "Richmond VIC <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3121</span>\n", "Tax Invoice for Professional Services\n", "Date\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Transaction No\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">669821</span>\n", "Reference\n", "<PERSON>\n", "Patient\n", "<PERSON>\n", "Details\n", "Service Provided\n", "No\n", "Amount\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">21</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">09</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2022</span>\n", "Professional Fees\n", "Consultation\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">94.50</span>\n", "Office\n", "Discount\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">-31.40</span>\n", "Inhouse Pathology\n", "IH Comprehensive Blood Profile\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">165.00</span>\n", "IH Urinalysis <span style=\"font-weight: bold\">(</span>Dipstick &amp; USG<span style=\"font-weight: bold\">)</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.50</span>\n", "Laboratory\n", "ASAP Lab In-Clinic Analyser Support Fee\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">31.40</span>\n", "Idexx Lab SDMA Renal Screnning Panel\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48.50</span>\n", "Total:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">337.50</span>\n", "Includes Tax of:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">30.68</span>\n", "PAID\n", "Amount <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">289.00</span>\n", "Balance remaining\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48.50</span>\n", "Thankyou for choosing Richmond Veterinary Clinic\n", "Next Appointment Details:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">22</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">08:00</span> AM : Consult for Charlie\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">22</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">08:15</span> AM : Consult for Trevor\n", "Ref <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18512</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">17</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">10</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">14:42:50</span>\n", "</pre>\n"], "text/plain": ["Mr <PERSON>\n", "\u001b[1;36m83\u001b[0m \u001b[1;36m155\u001b[0m \u001b[1;36m694\u001b[0m \u001b[1;36m164\u001b[0m\n", "\u001b[1;36m27\u001b[0m Northcote Street\n", "Richmond VIC \u001b[1;36m3121\u001b[0m\n", "Tax Invoice for Professional Services\n", "Date\n", "\u001b[1;36m21\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2022\u001b[0m\n", "Transaction No\n", "\u001b[1;36m669821\u001b[0m\n", "Reference\n", "<PERSON>\n", "Patient\n", "<PERSON>\n", "Details\n", "Service Provided\n", "No\n", "Amount\n", "\u001b[1;36m21\u001b[0m/\u001b[1;36m09\u001b[0m/\u001b[1;36m2022\u001b[0m\n", "Professional Fees\n", "Consultation\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m94.50\u001b[0m\n", "Office\n", "Discount\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m-31.40\u001b[0m\n", "Inhouse Pathology\n", "IH Comprehensive Blood Profile\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m165.00\u001b[0m\n", "IH Urinalysis \u001b[1m(\u001b[0mDipstick & USG\u001b[1m)\u001b[0m\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m29.50\u001b[0m\n", "Laboratory\n", "ASAP Lab In-Clinic Analyser Support Fee\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m31.40\u001b[0m\n", "Idexx Lab SDMA Renal Screnning Panel\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m48.50\u001b[0m\n", "Total:\n", "\u001b[1;36m337.50\u001b[0m\n", "Includes Tax of:\n", "\u001b[1;36m30.68\u001b[0m\n", "PAID\n", "Amount <PERSON>\n", "\u001b[1;36m289.00\u001b[0m\n", "Balance remaining\n", "\u001b[1;36m48.50\u001b[0m\n", "Thankyou for choosing Richmond Veterinary Clinic\n", "Next Appointment Details:\n", "\u001b[1;36m22\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1;92m08:00\u001b[0m AM : Consult for Charlie\n", "\u001b[1;36m22\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m \u001b[1;92m08:15\u001b[0m AM : Consult for Trevor\n", "Ref \u001b[1;36m18512\u001b[0m - \u001b[1;36m17\u001b[0m/\u001b[1;36m10\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m14:42:50\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.185\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7843809, sim_score_fp: True, sim_score_name_fp: True, sim_score_rule_fp: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7843809'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Coco'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Coco'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Leo'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7843809'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Coco'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Co<PERSON>'\u001b[0m, \u001b[32m'<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Point'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Coco'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'Point'\u001b[0m, \u001b[32m'Coco'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">ccb13161-37d9-44cf-8eac-a5d5eaccda45</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Point'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'Coco'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mccb13161-37d9-44cf-8eac-a5d5eaccda45\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Point'\u001b[0m, \u001b[32m'Coco'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m, \u001b[1;36m1\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Dr <PERSON><PERSON>-Smith <PERSON>.V.Sc., M.A.C.V.Sc.\n", "<PERSON> <PERSON>.V.Sc.\n", "<PERSON> <PERSON>, Vet.MB\n", "Pittwater\n", "<PERSON> <PERSON> <span style=\"font-weight: bold\">(</span>Hons<span style=\"font-weight: bold\">)</span> MSc\n", "and Associates\n", "ANIMAL HOSPITAL\n", "Corner Pittwater Road &amp; Arnott Crescent Warriewood NSW <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2102</span>\n", "Email: <EMAIL>\n", "Phone: <span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9913</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7979</span>\n", "www.pittwateranimalhospital.com.au\n", "Like us on Facebook\n", "f\n", "ABN <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">001</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">336</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">723</span>\n", "Mrs <PERSON>\n", "Pittwater Animal Hospital\n", "25c <PERSON>s Rd\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span> Arnott Crescent\n", "Church Point NSW <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2105</span>\n", "Warriewood NSW <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2102</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">***********</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span><span style=\"font-weight: bold\">)</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9913</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7979</span>\n", "<EMAIL>\n", "Tax Invoice for Professional Services\n", "Date\n", "Details\n", "Service Provided\n", "No\n", "Amount\n", "Patient: Co<PERSON>\n", "Patient: <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span>\n", "Ref: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">817013</span> - Consultation\n", "Consultation\n", "Re-Examination/Follow Up\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">73.30</span>\n", "Pathology - External\n", "Biochemistry, Haematology +\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">248.00</span>\n", "T4, Vet Interpretation\n", "Radiology\n", "Radiograph - Single View\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">189.00</span>\n", "Medication\n", "Cytopoint Injection 40mg\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1.00</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">201.20</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">711.50</span>\n", "Includes Tax of:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">64.68</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">711.50</span>\n", "PAID\n", "Amount <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">711.50</span>\n", "Balance remaining\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Ref <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25450</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2024</span> - <span style=\"color: #00ff00; text-decoration-color: #00ff00; font-weight: bold\">4:03:12</span>PM\n", "</pre>\n"], "text/plain": ["<PERSON> <PERSON><PERSON>-Smith <PERSON>.V.Sc., M.A.C.V.Sc.\n", "<PERSON> <PERSON>.V.Sc.\n", "<PERSON> <PERSON>, Vet.MB\n", "Pittwater\n", "<PERSON> <PERSON> \u001b[1m(\u001b[0mHons\u001b[1m)\u001b[0m MSc\n", "and Associates\n", "ANIMAL HOSPITAL\n", "Corner Pittwater Road & Arnott Crescent Warriewood NSW \u001b[1;36m2102\u001b[0m\n", "Email: <EMAIL>\n", "Phone: \u001b[1m(\u001b[0m\u001b[1;36m02\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9913\u001b[0m \u001b[1;36m7979\u001b[0m\n", "www.pittwateranimalhospital.com.au\n", "Like us on Facebook\n", "f\n", "ABN \u001b[1;36m25\u001b[0m \u001b[1;36m001\u001b[0m \u001b[1;36m336\u001b[0m \u001b[1;36m723\u001b[0m\n", "Mrs <PERSON>\n", "Pittwater Animal Hospital\n", "25c <PERSON>s Rd\n", "\u001b[1;36m1\u001b[0m <PERSON><PERSON><PERSON>\n", "Church Point NSW \u001b[1;36m2105\u001b[0m\n", "Warriewood NSW \u001b[1;36m2102\u001b[0m\n", "\u001b[1;36m***********\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m02\u001b[0m\u001b[1m)\u001b[0m \u001b[1;36m9913\u001b[0m \u001b[1;36m7979\u001b[0m\n", "<EMAIL>\n", "Tax Invoice for Professional Services\n", "Date\n", "Details\n", "Service Provided\n", "No\n", "Amount\n", "Patient: Co<PERSON>\n", "Patient: <PERSON>\n", "\u001b[1;36m4\u001b[0m/\u001b[1;36m03\u001b[0m/\u001b[1;36m2024\u001b[0m\n", "Ref: \u001b[1;36m817013\u001b[0m - Consultation\n", "Consultation\n", "Re-Examination/Follow Up\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m73.30\u001b[0m\n", "Pathology - External\n", "Biochemistry, Haematology +\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m248.00\u001b[0m\n", "T4, Vet Interpretation\n", "Radiology\n", "Radiograph - Single View\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m189.00\u001b[0m\n", "Medication\n", "Cytopoint Injection 40mg\n", "\u001b[1;36m1.00\u001b[0m\n", "\u001b[1;36m201.20\u001b[0m\n", "\u001b[1;36m711.50\u001b[0m\n", "Includes Tax of:\n", "\u001b[1;36m64.68\u001b[0m\n", "\u001b[1;36m711.50\u001b[0m\n", "PAID\n", "Amount <PERSON>\n", "\u001b[1;36m711.50\u001b[0m\n", "Balance remaining\n", "\u001b[1;36m0.00\u001b[0m\n", "Ref \u001b[1;36m25450\u001b[0m - \u001b[1;36m4\u001b[0m/\u001b[1;36m03\u001b[0m/\u001b[1;36m2024\u001b[0m - \u001b[1;92m4:03:12\u001b[0mPM\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for claimno, cinfo in mp_dataset.items():\n", "    for docfile, dinfo in cinfo[\"docfiles\"].items():\n", "        mp_label = dinfo[\"is_mp\"]\n", "        _, _, potential_animal_names, sim_score_res, sim_score_name_res, sim_score_rul_res = dinfo[\"res\"]\n", "        sim_score_fp = (mp_label == 0 and sim_score_res == 1)\n", "        sim_score_name_fp = (mp_label == 0 and sim_score_name_res == 1)\n", "        sim_score_rule_fp = (mp_label == 0 and sim_score_rul_res == 1)\n", "        if sim_score_rule_fp: #sim_score_fp or \n", "            logger.info(f\"claimno: {claimno}, sim_score_fp: {sim_score_fp}, sim_score_name_fp: {sim_score_name_fp}, sim_score_rule_fp: {sim_score_rule_fp}\")            \n", "            print(cinfo[\"policy_info\"])\n", "            print(potential_animal_names)\n", "            for k, v in cinfo[\"docfiles\"].items():\n", "                print(k, v[\"is_mp\"], v[\"res\"])\n", "                print(v[\"content\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* FN"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.319\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7631171, sim_score_fn: True, sim_score_name_fn: True, sim_score_rule_fn: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7631171'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Gigi'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON>igi'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7631171'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Gigi'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'G<PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Gigi'</span><span style=\"font-weight: bold\">]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[32m'<PERSON><PERSON>'\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">ba862bc9-83dd-4ce5-8707-3fb2a2772ed8</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'Gigi'</span><span style=\"font-weight: bold\">]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mba862bc9-83dd-4ce5-8707-3fb2a2772ed8\u001b[0m.pdf \u001b[1;36m1\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[32m'Gigi'\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">PORT PHILLIP\n", "animal hospital\n", "excellence in care\n", "Client information:\n", "Invoice:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">715473</span>\n", "Ms Mercado, Reza\n", "Date:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">218</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">275</span> Abbotsford Street\n", "Client number:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">202043</span>\n", "North Melbourne, Victoria <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3051</span>\n", "Animal:\n", "<PERSON><PERSON>\n", "Consult number:\n", "DESCRIPTION\n", "STAFF MEMBER\n", "QTY\n", "TOTAL <span style=\"font-weight: bold\">(</span>incl<span style=\"font-weight: bold\">)</span>\n", "Cytopoint 20mg 6s <span style=\"font-weight: bold\">(</span>per vial<span style=\"font-weight: bold\">)</span>\n", "Dr. <PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">171.60</span>\n", "Port Phillip Animal Hospital\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">46</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48</span> Mills St, Albert Park <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3206</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9686</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8838</span>\n", "Subtotal $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">171.60</span>\n", "ABN: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">302</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">692</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">365</span>\n", "Inc. GST $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">15.60</span>\n", "PAYMENT TERMS: Payment in full is expected upon completion of treatment. Total\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">171.60</span>\n", "Administration fees and collection fees will be applied to overdue accounts.\n", "Bank Account: BSB: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">063100</span> Account: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>\n", "Paid\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">171.60</span>\n", "If you are paying by bank transfer,\n", "Due\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "please note your surname as the reference number.\n", "PORT PHILLIP\n", "animal hospital\n", "excellence in care\n", "Client information:\n", "Invoice:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">715474</span>\n", "Ms Mercado, Reza\n", "Date:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">08</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">218</span>/<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">275</span> Abbotsford Street\n", "Client number:\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">202043</span>\n", "North Melbourne, Victoria <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3051</span>\n", "Animal:\n", "<PERSON>\n", "Consult number:\n", "DESCRIPTION\n", "STAFF MEMBER\n", "QTY\n", "TOTAL <span style=\"font-weight: bold\">(</span>incl<span style=\"font-weight: bold\">)</span>\n", "Prescription Fee\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25.00</span>\n", "Port Phillip Animal Hospital\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">46</span>-<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">48</span> Mills St, Albert Park <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3206</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9686</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">8838</span>\n", "Subtotal $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25.00</span>\n", "ABN: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">11</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">302</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">692</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">365</span>\n", "Inc. GST $<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.27</span>\n", "PAYMENT TERMS: Payment in full is expected upon completion of treatment. Total\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25.00</span>\n", "Administration fees and collection fees will be applied to overdue accounts.\n", "Bank Account: BSB: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">063100</span> Account: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">********</span>\n", "Paid\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">25.00</span>\n", "If you are paying by bank transfer,\n", "Due\n", "$<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "please note your surname as the reference number.\n", "</pre>\n"], "text/plain": ["PORT PHILLIP\n", "animal hospital\n", "excellence in care\n", "Client information:\n", "Invoice:\n", "\u001b[1;36m715473\u001b[0m\n", "Ms Mercado, Reza\n", "Date:\n", "\u001b[1;36m24\u001b[0m/\u001b[1;36m08\u001b[0m/\u001b[1;36m24\u001b[0m\n", "\u001b[1;36m218\u001b[0m/\u001b[1;36m275\u001b[0m Abbotsford Street\n", "Client number:\n", "\u001b[1;36m202043\u001b[0m\n", "North Melbourne, Victoria \u001b[1;36m3051\u001b[0m\n", "Animal:\n", "<PERSON><PERSON>\n", "Consult number:\n", "DESCRIPTION\n", "STAFF MEMBER\n", "QTY\n", "TOTAL \u001b[1m(\u001b[0mincl\u001b[1m)\u001b[0m\n", "Cytopoint 20mg 6s \u001b[1m(\u001b[0mper vial\u001b[1m)\u001b[0m\n", "Dr. <PERSON>\n", "\u001b[1;36m1\u001b[0m\n", "$\u001b[1;36m171.60\u001b[0m\n", "Port Phillip Animal Hospital\n", "\u001b[1;36m46\u001b[0m-\u001b[1;36m48\u001b[0m Mills St, Albert Park \u001b[1;36m3206\u001b[0m\n", "\u001b[1;36m9686\u001b[0m \u001b[1;36m8838\u001b[0m\n", "Subtotal $\u001b[1;36m171.60\u001b[0m\n", "ABN: \u001b[1;36m11\u001b[0m \u001b[1;36m302\u001b[0m \u001b[1;36m692\u001b[0m \u001b[1;36m365\u001b[0m\n", "Inc. GST $\u001b[1;36m15.60\u001b[0m\n", "PAYMENT TERMS: Payment in full is expected upon completion of treatment. Total\n", "$\u001b[1;36m171.60\u001b[0m\n", "Administration fees and collection fees will be applied to overdue accounts.\n", "Bank Account: BSB: \u001b[1;36m063100\u001b[0m Account: \u001b[1;36m********\u001b[0m\n", "Paid\n", "$\u001b[1;36m171.60\u001b[0m\n", "If you are paying by bank transfer,\n", "Due\n", "$\u001b[1;36m0.00\u001b[0m\n", "please note your surname as the reference number.\n", "PORT PHILLIP\n", "animal hospital\n", "excellence in care\n", "Client information:\n", "Invoice:\n", "\u001b[1;36m715474\u001b[0m\n", "Ms Mercado, Reza\n", "Date:\n", "\u001b[1;36m24\u001b[0m/\u001b[1;36m08\u001b[0m/\u001b[1;36m24\u001b[0m\n", "\u001b[1;36m218\u001b[0m/\u001b[1;36m275\u001b[0m Abbotsford Street\n", "Client number:\n", "\u001b[1;36m202043\u001b[0m\n", "North Melbourne, Victoria \u001b[1;36m3051\u001b[0m\n", "Animal:\n", "<PERSON>\n", "Consult number:\n", "DESCRIPTION\n", "STAFF MEMBER\n", "QTY\n", "TOTAL \u001b[1m(\u001b[0mincl\u001b[1m)\u001b[0m\n", "Prescription Fee\n", "<PERSON>\n", "\u001b[1;36m1\u001b[0m\n", "$\u001b[1;36m25.00\u001b[0m\n", "Port Phillip Animal Hospital\n", "\u001b[1;36m46\u001b[0m-\u001b[1;36m48\u001b[0m Mills St, Albert Park \u001b[1;36m3206\u001b[0m\n", "\u001b[1;36m9686\u001b[0m \u001b[1;36m8838\u001b[0m\n", "Subtotal $\u001b[1;36m25.00\u001b[0m\n", "ABN: \u001b[1;36m11\u001b[0m \u001b[1;36m302\u001b[0m \u001b[1;36m692\u001b[0m \u001b[1;36m365\u001b[0m\n", "Inc. GST $\u001b[1;36m2.27\u001b[0m\n", "PAYMENT TERMS: Payment in full is expected upon completion of treatment. Total\n", "$\u001b[1;36m25.00\u001b[0m\n", "Administration fees and collection fees will be applied to overdue accounts.\n", "Bank Account: BSB: \u001b[1;36m063100\u001b[0m Account: \u001b[1;36m********\u001b[0m\n", "Paid\n", "$\u001b[1;36m25.00\u001b[0m\n", "If you are paying by bank transfer,\n", "Due\n", "$\u001b[1;36m0.00\u001b[0m\n", "please note your surname as the reference number.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:13:41.340\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mclaimno: C7797228, sim_score_fn: True, sim_score_name_fn: True, sim_score_rule_fn: True\u001b[0m\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">{</span><span style=\"color: #008000; text-decoration-color: #008000\">'ClaimNo'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'C7797228'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'ClaimAnimalName'</span>: <span style=\"color: #008000; text-decoration-color: #008000\">'Russell Jordan'</span>, <span style=\"color: #008000; text-decoration-color: #008000\">'PolicyAnimalNames'</span>: <span style=\"font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">'<PERSON> Jordan'</span><span style=\"font-weight: bold\">]}</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m{\u001b[0m\u001b[32m'ClaimNo'\u001b[0m: \u001b[32m'C7797228'\u001b[0m, \u001b[32m'ClaimAnimalName'\u001b[0m: \u001b[32m'Russell Jordan'\u001b[0m, \u001b[32m'PolicyAnimalNames'\u001b[0m: \u001b[1m[\u001b[0m\u001b[32m'Russell <PERSON>'\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">[]</span>\n", "</pre>\n"], "text/plain": ["\u001b[1m[\u001b[0m\u001b[1m]\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">4a53ed96-dedb-4cad-a10a-b1c73f5acbae</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93m4a53ed96-dedb-4cad-a10a-b1c73f5acbae\u001b[0m.pdf \u001b[1;36m1\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">A\n", "Cnr. Wells Road &amp; Sutherland Avenue\n", "Aspendale Gardens Victoria <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3195</span>\n", "Telephone: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9587</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4599</span>\n", "Aspendale Gardens\n", "Email: <EMAIL>\n", "www.aspendalegardensvet.com.au\n", "Veterinary Hospital\n", "f\n", "Find us on\n", "Facebook\n", "A.B.N. <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">67</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">602</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">160</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">967</span>\n", "Owner Details\n", "Mrs <PERSON><PERSON>\n", "Tax Invoice No: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">168780</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> Pine Crescent\n", "Tax Invoice\n", "Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span> Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "Aspendale VIC <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3195</span>\n", "Description\n", "Animal\n", "Quantity\n", "Total $\n", "Consultation - Level One\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">129.00</span>\n", "Medication review\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Basic Health Blood Test\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">190.00</span>\n", "Allocated Payments\n", "Date\n", "Amount $\n", "EFTPOS\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span> Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">319.00</span>\n", "Invoice Totals:\n", "Balance Forward\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "This invoice includes GST of <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">29.00</span>\n", "Your next appointment for is on the\n", "+ Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">319.00</span>\n", "- Payments\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">319.00</span>\n", "Balance Due\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "</pre>\n"], "text/plain": ["A\n", "Cnr. Wells Road & Sutherland Avenue\n", "Aspendale Gardens Victoria \u001b[1;36m3195\u001b[0m\n", "Telephone: \u001b[1;36m9587\u001b[0m \u001b[1;36m4599\u001b[0m\n", "Aspendale Gardens\n", "Email: <EMAIL>\n", "www.aspendalegardensvet.com.au\n", "Veterinary Hospital\n", "f\n", "Find us on\n", "Facebook\n", "A.B.N<PERSON> \u001b[1;36m67\u001b[0m \u001b[1;36m602\u001b[0m \u001b[1;36m160\u001b[0m \u001b[1;36m967\u001b[0m\n", "Owner Details\n", "Mrs <PERSON><PERSON>\n", "Tax Invoice No: \u001b[1;36m168780\u001b[0m\n", "\u001b[1;36m2\u001b[0m Pine Crescent\n", "Tax Invoice\n", "Date: \u001b[1;36m03\u001b[0m Oct \u001b[1;36m24\u001b[0m\n", "Aspendale VIC \u001b[1;36m3195\u001b[0m\n", "Description\n", "Animal\n", "Quantity\n", "Total $\n", "Consultation - Level One\n", "<PERSON>\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m129.00\u001b[0m\n", "Medication review\n", "<PERSON>\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m0.00\u001b[0m\n", "Basic Health Blood Test\n", "<PERSON>\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m190.00\u001b[0m\n", "Allocated Payments\n", "Date\n", "Amount $\n", "EFTPOS\n", "\u001b[1;36m03\u001b[0m Oct \u001b[1;36m24\u001b[0m\n", "\u001b[1;36m319.00\u001b[0m\n", "Invoice Totals:\n", "Balance Forward\n", "\u001b[1;36m0.00\u001b[0m\n", "This invoice includes GST of \u001b[1;36m29.00\u001b[0m\n", "Your next appointment for is on the\n", "+ Invoice Total\n", "\u001b[1;36m319.00\u001b[0m\n", "- Payments\n", "\u001b[1;36m319.00\u001b[0m\n", "Balance Due\n", "\u001b[1;36m0.00\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #ffff00; text-decoration-color: #ffff00\">ab2e6403-64a6-4946-9430-b22eda47b25d</span>.pdf <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>\n", "<span style=\"font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>, <span style=\"font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span><span style=\"font-weight: bold\">]</span>, <span style=\"font-weight: bold\">[]</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span>, <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[93mab2e6403-64a6-4946-9430-b22eda47b25d\u001b[0m.pdf \u001b[1;36m0\u001b[0m\n", "\u001b[1m(\u001b[0m\u001b[1;36m100\u001b[0m, \u001b[1m[\u001b[0m\u001b[1;36m100\u001b[0m\u001b[1m]\u001b[0m, \u001b[1m[\u001b[0m\u001b[1m]\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m, \u001b[1;36m0\u001b[0m\u001b[1m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">A\n", "Cnr. Wells Road &amp; Sutherland Avenue\n", "Aspendale Gardens Victoria <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3195</span>\n", "Telephone: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">9587</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4599</span>\n", "Aspendale Gardens\n", "Email: <EMAIL>\n", "www.aspendalegardensvet.com.au\n", "Veterinary Hospital\n", "f\n", "Find us on\n", "Facebook\n", "A.B.N. <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">67</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">602</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">160</span> <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">967</span>\n", "Owner Details\n", "Mrs <PERSON><PERSON>\n", "Tax Invoice No: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">168730</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span> Pine Crescent\n", "Tax Invoice\n", "Date: <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">02</span> Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "Aspendale VIC <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3195</span>\n", "Description\n", "Animal\n", "Quantity\n", "Total $\n", "Tapewormer For Dogs And Cats\n", "<PERSON><PERSON><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.5</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3.05</span>\n", "Tapewormer For Dogs And Cats\n", "<PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">18.30</span>\n", "Bravecto PLUS Cat <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2.8</span> - <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">6.</span>25kg 3m\n", "<PERSON><PERSON><PERSON>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">61.70</span>\n", "Download the Bravecto Rewards app and upload your receipt - Collect <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span> stars &amp; earn a FREE dose\n", "Allocated Payments\n", "Date\n", "Amount $\n", "EFTPOS\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">03</span> Oct <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">24</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83.05</span>\n", "Invoice Totals:\n", "This invoice includes GST of <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">7.55</span>\n", "Balance Forward\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span>\n", "Your next appointment for is on the\n", "+ Invoice Total\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83.05</span>\n", "- Payments\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">83.05</span>\n", "Balance Due\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0.00</span> :unselected:\n", "</pre>\n"], "text/plain": ["A\n", "Cnr. Wells Road & Sutherland Avenue\n", "Aspendale Gardens Victoria \u001b[1;36m3195\u001b[0m\n", "Telephone: \u001b[1;36m9587\u001b[0m \u001b[1;36m4599\u001b[0m\n", "Aspendale Gardens\n", "Email: <EMAIL>\n", "www.aspendalegardensvet.com.au\n", "Veterinary Hospital\n", "f\n", "Find us on\n", "Facebook\n", "A.B.N<PERSON> \u001b[1;36m67\u001b[0m \u001b[1;36m602\u001b[0m \u001b[1;36m160\u001b[0m \u001b[1;36m967\u001b[0m\n", "Owner Details\n", "Mrs <PERSON><PERSON>\n", "Tax Invoice No: \u001b[1;36m168730\u001b[0m\n", "\u001b[1;36m2\u001b[0m Pine Crescent\n", "Tax Invoice\n", "Date: \u001b[1;36m02\u001b[0m Oct \u001b[1;36m24\u001b[0m\n", "Aspendale VIC \u001b[1;36m3195\u001b[0m\n", "Description\n", "Animal\n", "Quantity\n", "Total $\n", "Tapewormer For Dogs And Cats\n", "<PERSON><PERSON><PERSON>\n", "\u001b[1;36m0.5\u001b[0m\n", "\u001b[1;36m3.05\u001b[0m\n", "Tapewormer For Dogs And Cats\n", "<PERSON>\n", "\u001b[1;36m3\u001b[0m\n", "\u001b[1;36m18.30\u001b[0m\n", "Bravecto PLUS Cat \u001b[1;36m2.8\u001b[0m - \u001b[1;36m6.\u001b[0m25kg 3m\n", "<PERSON><PERSON><PERSON>\n", "\u001b[1;36m1\u001b[0m\n", "\u001b[1;36m61.70\u001b[0m\n", "Download the Bravecto Rewards app and upload your receipt - Collect \u001b[1;36m4\u001b[0m stars & earn a FREE dose\n", "Allocated Payments\n", "Date\n", "Amount $\n", "EFTPOS\n", "\u001b[1;36m03\u001b[0m Oct \u001b[1;36m24\u001b[0m\n", "\u001b[1;36m83.05\u001b[0m\n", "Invoice Totals:\n", "This invoice includes GST of \u001b[1;36m7.55\u001b[0m\n", "Balance Forward\n", "\u001b[1;36m0.00\u001b[0m\n", "Your next appointment for is on the\n", "+ Invoice Total\n", "\u001b[1;36m83.05\u001b[0m\n", "- Payments\n", "\u001b[1;36m83.05\u001b[0m\n", "Balance Due\n", "\u001b[1;36m0.00\u001b[0m :unselected:\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for claimno, cinfo in mp_dataset.items():\n", "    for docfile, dinfo in cinfo[\"docfiles\"].items():\n", "        mp_label = dinfo[\"is_mp\"]\n", "        _, _, potential_animal_names, sim_score_res, sim_score_name_res, sim_score_rul_res = dinfo[\"res\"]\n", "        sim_score_fn = (mp_label == 1 and sim_score_res == 0)\n", "        sim_score_name_fn = (mp_label == 1 and sim_score_name_res == 0)\n", "        sim_score_rule_fn = (mp_label == 1 and sim_score_rul_res == 0)\n", "        if sim_score_rule_fn: # sim_score_fn or sim_score_name_fn or \n", "            logger.info(f\"claimno: {claimno}, sim_score_fn: {sim_score_fn}, sim_score_name_fn: {sim_score_name_fn}, sim_score_rule_fn: {sim_score_rule_fn}\")\n", "            print(cinfo[\"policy_info\"])\n", "            print(potential_animal_names)\n", "            for k, v in cinfo[\"docfiles\"].items():\n", "                print(k, v[\"is_mp\"], v[\"res\"])\n", "                print(v[\"content\"])"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-17 03:21:32.412\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mtotal token num: 188720, num_invoice: 989, average token per claim: 190.8190091001011\u001b[0m\n"]}], "source": ["total_token = 0\n", "for claimno, cinfo in mp_dataset.items():\n", "    for docfile, dinfo in cinfo[\"docfiles\"].items():\n", "        content = dinfo[\"content\"]\n", "        total_token += len(content.split())\n", "logger.info(f\"total token num: {total_token}, num_invoice: {len(mp_dataset)}, average token per claim: {total_token/len(mp_dataset)}\")"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.1400000000000001"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["190 * 2 /1000000 * 30 * 100"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["# import spacy\n", "\n", "# nlp = spacy.load(\"en_core_web_trf\")\n", "# test_content = \"\"\"\n", "# 'PORT PHILLIP\\nanimal hospital\\nexcellence in care\\nClient \n", "# information:\\nInvoice:\\n715473\\n<PERSON> Mercado, Reza\\nDate:\\n24/08/24\\n218/275 Abbotsford Street\\nClient \n", "# number:\\n202043\\nNorth Melbourne, Victoria 3051\\nAnimal:\\nGigi\\nConsult number:\\nDESCRIPTION\\nSTAFF \n", "# MEMBER\\nQTY\\nTOTAL (incl)\\nCytopoint 20mg 6s (per vial)\\nDr. <PERSON>\\n1\\n$171.60\\nPort Phillip Animal \n", "# Hospital\\n46-48 Mills St, Albert Park 3206\\n9686 8838\\nSubtotal $171.60\\nABN: **************\\nInc. GST \n", "# $15.60\\nPAYMENT TERMS: Payment in full is expected upon completion of treatment. Total\\n$171.60\\nAdministration \n", "# fees and collection fees will be applied to overdue accounts.\\nBank Account: BSB: 063100 Account: \n", "# ********\\nPaid\\n$171.60\\nIf you are paying by bank transfer,\\nDue\\n$0.00\\nplease note your surname as the reference\n", "# number.\\nPORT PHILLIP\\nanimal hospital\\nexcellence in care\\nClient information:\\nInvoice:\\n715474\\nMs Mercado, \n", "# Reza\\nDate:\\n24/08/24\\n218/275 Abbotsford Street\\nClient number:\\n202043\\nNorth Melbourne, Victoria \n", "# 3051\\nAnimal:\\nLoki\\nConsult number:\\nDESCRIPTION\\nSTAFF MEMBER\\nQTY\\nTOTAL (incl)\\nPrescription \n", "# Fee\\nKirby\\n1\\n$25.00\\nPort Phillip Animal Hospital\\n46-48 Mills St, Albert Park 3206\\n9686 8838\\nSubtotal \n", "# $25.00\\nABN: **************\\nInc. GST $2.27\\nPAYMENT TERMS: Payment in full is expected upon completion of \n", "# treatment. Total\\n$25.00\\nAdministration fees and collection fees will be applied to overdue accounts.\\nBank \n", "# Account: BSB: 063100 Account: ********\\nPaid\\n$25.00\\nIf you are paying by bank transfer,\\nDue\\n$0.00\\nplease note \n", "# your surname as the reference number.'\n", "# \"\"\"\n", "# doc = nlp(test_content.replace(\"\\n\", \" \"))\n", "\n", "# print(test_content)\n", "# for ent in doc.ents:\n", "#     print(ent.text, ent.start_char, ent.end_char, ent.label_)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}