{"cells": [{"cell_type": "code", "execution_count": 9, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2023-10-20T05:05:16.486397400Z", "start_time": "2023-10-20T05:05:16.470393Z"}}, "outputs": [], "source": ["import torch\n", "from transformers import AutoProcessor, AutoModel, AutoModelForSequenceClassification\n", "from datasets import load_dataset"]}, {"cell_type": "code", "execution_count": 5, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of LayoutLMv3ForSequenceClassification were not initialized from the model checkpoint at microsoft/layoutlmv3-base and are newly initialized: ['classifier.dense.bias', 'classifier.out_proj.weight', 'classifier.dense.weight', 'classifier.out_proj.bias']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["processor = AutoProcessor.from_pretrained(\"microsoft/layoutlmv3-base\", apply_ocr=False)\n", "model = AutoModelForSequenceClassification.from_pretrained(\"microsoft/layoutlmv3-base\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-20T05:02:45.743875900Z", "start_time": "2023-10-20T05:02:42.568406100Z"}}, "id": "6961b872ecbe406d"}, {"cell_type": "code", "execution_count": 7, "outputs": [], "source": ["dataset = load_dataset(\"nielsr/funsd-layoutlmv3\", split=\"train\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-20T05:03:46.326364700Z", "start_time": "2023-10-20T05:03:41.729490900Z"}}, "id": "6aa545a9e9d44d34"}, {"cell_type": "code", "execution_count": 8, "outputs": [], "source": ["example = dataset[0]\n", "image = example[\"image\"]\n", "words = example[\"tokens\"]\n", "boxes = example[\"bboxes\"]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-20T05:04:14.915520900Z", "start_time": "2023-10-20T05:04:14.854517800Z"}}, "id": "d905381454e2c3c1"}, {"cell_type": "code", "execution_count": 11, "outputs": [], "source": ["encoding = processor(image, words, boxes=boxes, return_tensors=\"pt\")\n", "sequence_label = torch.tensor([1])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-20T05:05:54.417608800Z", "start_time": "2023-10-20T05:05:54.350603200Z"}}, "id": "d87276c42f3c9ad6"}, {"cell_type": "code", "execution_count": 12, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["F:\\Anaconda3\\envs\\OCR_in_house\\lib\\site-packages\\transformers\\modeling_utils.py:905: FutureWarning: The `device` argument is deprecated and will be removed in v5 of Transformers.\n", "  warnings.warn(\n"]}], "source": ["outputs = model(**encoding, labels=sequence_label)\n", "loss = outputs.loss\n", "logits = outputs.logits"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-20T05:06:31.826015400Z", "start_time": "2023-10-20T05:06:29.865673500Z"}}, "id": "2570cba674e35c37"}, {"cell_type": "code", "execution_count": 14, "outputs": [{"data": {"text/plain": "tensor([1])"}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["logits.argmax(-1)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-20T05:07:12.402112300Z", "start_time": "2023-10-20T05:07:12.391111500Z"}}, "id": "9412fae09cb3af63"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "5bb2ca2ac75db582"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}