{"cells": [{"cell_type": "code", "execution_count": 3, "id": "1dda0668", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pandas as pd\n", "import numpy as np\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "import urllib\n", "import sqlalchemy as sa\n", "import PyPDF2\n", "from PIL import Image\n", "import glob\n", "import re\n", "import shutil\n", "from PyPDF2 import PdfFileMerger, PdfFileReader"]}, {"cell_type": "code", "execution_count": 6, "id": "0cbae370", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>DocumentId</th>\n", "      <th>DocumentName</th>\n", "      <th>DocumentType</th>\n", "      <th>DocumentPath</th>\n", "      <th>DocumentDate</th>\n", "      <th>CspReferenceNo</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>10000</td>\n", "      <td>1098892e-8d49-44d5-bb9d-59d51f4f2db1</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20241111/217655e6-5eb8-...</td>\n", "      <td>2024-11-11 02:11:12.3933333</td>\n", "      <td>C202411111052</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C7671602</td>\n", "      <td>10000</td>\n", "      <td>d390da44-afde-4821-a2a0-e25839a0cd54</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240905/3347ab4a-a180-...</td>\n", "      <td>2024-09-05 09:16:07.1600000</td>\n", "      <td>C202409053485</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7195655</td>\n", "      <td>10000</td>\n", "      <td>33c8a39c-32d3-4c99-97ee-ea62e9c6ed11</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240506/00b78673-50d9-...</td>\n", "      <td>2024-05-06 06:08:56.9600000</td>\n", "      <td>V202405062140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C8293171</td>\n", "      <td>10000</td>\n", "      <td>f8debfa2-378f-4977-9b2e-82d746774407</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20250205/fdf001a7-0111-...</td>\n", "      <td>2025-02-05 07:42:45.4366667</td>\n", "      <td>V202502053436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C8026653</td>\n", "      <td>10000</td>\n", "      <td>2d97d8bf-afca-4edc-a29d-dc66d2b3a6d9</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20241203/a43ffd08-07af-...</td>\n", "      <td>2024-12-03 05:03:56.7600000</td>\n", "      <td>V202412032215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>C6869540</td>\n", "      <td>10000</td>\n", "      <td>15521ce7-e724-4206-bdf8-11f7046674b8</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240212/586f59ee-65e9-...</td>\n", "      <td>2024-02-12 06:33:12.5500000</td>\n", "      <td>V202402122737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>C6797886</td>\n", "      <td>10000</td>\n", "      <td>0db7ab2d-dd18-42a9-aecb-65edd8ba4d71</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240124/4315cdde-e5d6-...</td>\n", "      <td>2024-01-24 09:16:10.0400000</td>\n", "      <td>V202401243470</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>C7237515</td>\n", "      <td>10000</td>\n", "      <td>998e4841-7cfe-4fe9-bebc-24e094a3798b</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240516/973864fe-b0ca-...</td>\n", "      <td>2024-05-16 05:38:52.9633333</td>\n", "      <td>V202405162103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>C7754251</td>\n", "      <td>10000</td>\n", "      <td>65c51600-acfd-4128-adca-092597a60581</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240926/44d67a92-3c59-...</td>\n", "      <td>2024-09-26 21:33:23.4600000</td>\n", "      <td>C202409263914</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>C7578726</td>\n", "      <td>10000</td>\n", "      <td>e7453ae0-ebed-440e-a092-eaf6ab0797d6</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240813/2fa5fe04-fe2d-...</td>\n", "      <td>2024-08-13 09:06:13.7800000</td>\n", "      <td>C202408133513</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     ClaimNo  InvoiceLineNo                            DocumentId  \\\n", "0   ********          10000  1098892e-8d49-44d5-bb9d-59d51f4f2db1   \n", "1   C7671602          10000  d390da44-afde-4821-a2a0-e25839a0cd54   \n", "2   C7195655          10000  33c8a39c-32d3-4c99-97ee-ea62e9c6ed11   \n", "3   C8293171          10000  f8debfa2-378f-4977-9b2e-82d746774407   \n", "4   C8026653          10000  2d97d8bf-afca-4edc-a29d-dc66d2b3a6d9   \n", "..       ...            ...                                   ...   \n", "95  C6869540          10000  15521ce7-e724-4206-bdf8-11f7046674b8   \n", "96  C6797886          10000  0db7ab2d-dd18-42a9-aecb-65edd8ba4d71   \n", "97  C7237515          10000  998e4841-7cfe-4fe9-bebc-24e094a3798b   \n", "98  C7754251          10000  65c51600-acfd-4128-adca-092597a60581   \n", "99  C7578726          10000  e7453ae0-ebed-440e-a092-eaf6ab0797d6   \n", "\n", "       DocumentName DocumentType  \\\n", "0   Vet History.pdf          pdf   \n", "1   Vet History.pdf          pdf   \n", "2   Vet History.pdf          pdf   \n", "3   Vet History.pdf          pdf   \n", "4   Vet History.pdf          pdf   \n", "..              ...          ...   \n", "95  Vet History.pdf          pdf   \n", "96  Vet History.pdf          pdf   \n", "97  Vet History.pdf          pdf   \n", "98  Vet History.pdf          pdf   \n", "99  Vet History.pdf          pdf   \n", "\n", "                                         DocumentPath  \\\n", "0   cosservice-prod-claims-20241111/217655e6-5eb8-...   \n", "1   cosservice-prod-claims-20240905/3347ab4a-a180-...   \n", "2   cosservice-prod-claims-20240506/00b78673-50d9-...   \n", "3   cosservice-prod-claims-20250205/fdf001a7-0111-...   \n", "4   cosservice-prod-claims-20241203/a43ffd08-07af-...   \n", "..                                                ...   \n", "95  cosservice-prod-claims-20240212/586f59ee-65e9-...   \n", "96  cosservice-prod-claims-20240124/4315cdde-e5d6-...   \n", "97  cosservice-prod-claims-20240516/973864fe-b0ca-...   \n", "98  cosservice-prod-claims-20240926/44d67a92-3c59-...   \n", "99  cosservice-prod-claims-20240813/2fa5fe04-fe2d-...   \n", "\n", "                   DocumentDate CspReferenceNo  \n", "0   2024-11-11 02:11:12.3933333  C202411111052  \n", "1   2024-09-05 09:16:07.1600000  C202409053485  \n", "2   2024-05-06 06:08:56.9600000  V202405062140  \n", "3   2025-02-05 07:42:45.4366667  V202502053436  \n", "4   2024-12-03 05:03:56.7600000  V202412032215  \n", "..                          ...            ...  \n", "95  2024-02-12 06:33:12.5500000  V202402122737  \n", "96  2024-01-24 09:16:10.0400000  V202401243470  \n", "97  2024-05-16 05:38:52.9633333  V202405162103  \n", "98  2024-09-26 21:33:23.4600000  C202409263914  \n", "99  2024-08-13 09:06:13.7800000  C202408133513  \n", "\n", "[100 rows x 8 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Read the csv file produced by SQL query\n", "df = pd.read_csv(\"/workspaces/OCR_in_house/data/merge_consultation notes.csv\")\n", "df\n", "# # 10 Unique Claim Reference Number\n", "# df['ClaimRefNumber'].unique()\n", "# df[['ClaimRefNumber','DocumentPath']]"]}, {"cell_type": "code", "execution_count": 8, "id": "ff0418b3", "metadata": {}, "outputs": [], "source": ["df= df.head(10)"]}, {"cell_type": "code", "execution_count": 11, "id": "f71cd84b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>DocumentId</th>\n", "      <th>DocumentName</th>\n", "      <th>DocumentType</th>\n", "      <th>DocumentPath</th>\n", "      <th>DocumentDate</th>\n", "      <th>CspReferenceNo</th>\n", "      <th>DocContainer</th>\n", "      <th>DocFile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>10000</td>\n", "      <td>1098892e-8d49-44d5-bb9d-59d51f4f2db1</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20241111/217655e6-5eb8-...</td>\n", "      <td>2024-11-11 02:11:12.3933333</td>\n", "      <td>C202411111052</td>\n", "      <td>cosservice-prod-claims-20241111</td>\n", "      <td>217655e6-5eb8-4f6e-b241-13d28b0ac59f.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C7671602</td>\n", "      <td>10000</td>\n", "      <td>d390da44-afde-4821-a2a0-e25839a0cd54</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240905/3347ab4a-a180-...</td>\n", "      <td>2024-09-05 09:16:07.1600000</td>\n", "      <td>C202409053485</td>\n", "      <td>cosservice-prod-claims-20240905</td>\n", "      <td>3347ab4a-a180-4f2f-8d25-17d70a0cfff4.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7195655</td>\n", "      <td>10000</td>\n", "      <td>33c8a39c-32d3-4c99-97ee-ea62e9c6ed11</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240506/00b78673-50d9-...</td>\n", "      <td>2024-05-06 06:08:56.9600000</td>\n", "      <td>V202405062140</td>\n", "      <td>cosservice-prod-claims-20240506</td>\n", "      <td>00b78673-50d9-45f9-a57d-8e660a70023b.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C8293171</td>\n", "      <td>10000</td>\n", "      <td>f8debfa2-378f-4977-9b2e-82d746774407</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20250205/fdf001a7-0111-...</td>\n", "      <td>2025-02-05 07:42:45.4366667</td>\n", "      <td>V202502053436</td>\n", "      <td>cosservice-prod-claims-20250205</td>\n", "      <td>fdf001a7-0111-4b06-ae8f-8508f8bbd204.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C8026653</td>\n", "      <td>10000</td>\n", "      <td>2d97d8bf-afca-4edc-a29d-dc66d2b3a6d9</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20241203/a43ffd08-07af-...</td>\n", "      <td>2024-12-03 05:03:56.7600000</td>\n", "      <td>V202412032215</td>\n", "      <td>cosservice-prod-claims-20241203</td>\n", "      <td>a43ffd08-07af-40cd-8c4e-0c612ac4bd28.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C7454555</td>\n", "      <td>20000</td>\n", "      <td>0ce6bda2-2264-4629-96a8-c2dad3b45a89</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240712/79a7006d-5db0-...</td>\n", "      <td>2024-07-12 00:05:28.0466667</td>\n", "      <td>V2024071235</td>\n", "      <td>cosservice-prod-claims-20240712</td>\n", "      <td>79a7006d-5db0-4251-b0a3-664be72ec112.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C6934113</td>\n", "      <td>10000</td>\n", "      <td>56db4b72-f59d-493c-877d-b1ffe4dbde9f</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240228/2475d364-5d15-...</td>\n", "      <td>2024-02-28 06:19:31.7966667</td>\n", "      <td>V202402282427</td>\n", "      <td>cosservice-prod-claims-20240228</td>\n", "      <td>2475d364-5d15-41a5-acf4-089152bd342c.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C7354655</td>\n", "      <td>10000</td>\n", "      <td>74bfb95a-5ef8-485c-a6ca-c3a8b05be26e</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240616/158c4820-c3c0-...</td>\n", "      <td>2024-06-16 23:51:13.8366667</td>\n", "      <td>C202406161256</td>\n", "      <td>cosservice-prod-claims-20240616</td>\n", "      <td>158c4820-c3c0-4678-ba03-ef91a9e536a1.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C6968821</td>\n", "      <td>10000</td>\n", "      <td>5d198607-cf39-420b-8c3e-a89e3835f3e4</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240307/b16c00aa-fb49-...</td>\n", "      <td>2024-03-07 08:44:07.5433333</td>\n", "      <td>C202403073235</td>\n", "      <td>cosservice-prod-claims-20240307</td>\n", "      <td>b16c00aa-fb49-4ac5-9f6f-c3003c116c6e.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C6797728</td>\n", "      <td>10000</td>\n", "      <td>f97aa00f-540d-4ca0-83bd-6d6e5e6613a0</td>\n", "      <td>Vet History.pdf</td>\n", "      <td>pdf</td>\n", "      <td>cosservice-prod-claims-20240124/ad23de74-df3a-...</td>\n", "      <td>2024-01-24 08:25:13.0266667</td>\n", "      <td>V202401243326</td>\n", "      <td>cosservice-prod-claims-20240124</td>\n", "      <td>ad23de74-df3a-482c-b5b3-59db29493d48.pdf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ClaimNo  InvoiceLineNo                            DocumentId  \\\n", "0  ********          10000  1098892e-8d49-44d5-bb9d-59d51f4f2db1   \n", "1  C7671602          10000  d390da44-afde-4821-a2a0-e25839a0cd54   \n", "2  C7195655          10000  33c8a39c-32d3-4c99-97ee-ea62e9c6ed11   \n", "3  C8293171          10000  f8debfa2-378f-4977-9b2e-82d746774407   \n", "4  C8026653          10000  2d97d8bf-afca-4edc-a29d-dc66d2b3a6d9   \n", "5  C7454555          20000  0ce6bda2-2264-4629-96a8-c2dad3b45a89   \n", "6  C6934113          10000  56db4b72-f59d-493c-877d-b1ffe4dbde9f   \n", "7  C7354655          10000  74bfb95a-5ef8-485c-a6ca-c3a8b05be26e   \n", "8  C6968821          10000  5d198607-cf39-420b-8c3e-a89e3835f3e4   \n", "9  C6797728          10000  f97aa00f-540d-4ca0-83bd-6d6e5e6613a0   \n", "\n", "      DocumentName DocumentType  \\\n", "0  Vet History.pdf          pdf   \n", "1  Vet History.pdf          pdf   \n", "2  Vet History.pdf          pdf   \n", "3  Vet History.pdf          pdf   \n", "4  Vet History.pdf          pdf   \n", "5  Vet History.pdf          pdf   \n", "6  Vet History.pdf          pdf   \n", "7  Vet History.pdf          pdf   \n", "8  Vet History.pdf          pdf   \n", "9  Vet History.pdf          pdf   \n", "\n", "                                        DocumentPath  \\\n", "0  cosservice-prod-claims-20241111/217655e6-5eb8-...   \n", "1  cosservice-prod-claims-20240905/3347ab4a-a180-...   \n", "2  cosservice-prod-claims-20240506/00b78673-50d9-...   \n", "3  cosservice-prod-claims-20250205/fdf001a7-0111-...   \n", "4  cosservice-prod-claims-20241203/a43ffd08-07af-...   \n", "5  cosservice-prod-claims-20240712/79a7006d-5db0-...   \n", "6  cosservice-prod-claims-20240228/2475d364-5d15-...   \n", "7  cosservice-prod-claims-20240616/158c4820-c3c0-...   \n", "8  cosservice-prod-claims-20240307/b16c00aa-fb49-...   \n", "9  cosservice-prod-claims-20240124/ad23de74-df3a-...   \n", "\n", "                  DocumentDate CspReferenceNo  \\\n", "0  2024-11-11 02:11:12.3933333  C202411111052   \n", "1  2024-09-05 09:16:07.1600000  C202409053485   \n", "2  2024-05-06 06:08:56.9600000  V202405062140   \n", "3  2025-02-05 07:42:45.4366667  V202502053436   \n", "4  2024-12-03 05:03:56.7600000  V202412032215   \n", "5  2024-07-12 00:05:28.0466667    V2024071235   \n", "6  2024-02-28 06:19:31.7966667  V202402282427   \n", "7  2024-06-16 23:51:13.8366667  C202406161256   \n", "8  2024-03-07 08:44:07.5433333  C202403073235   \n", "9  2024-01-24 08:25:13.0266667  V202401243326   \n", "\n", "                      DocContainer                                   DocFile  \n", "0  cosservice-prod-claims-20241111  217655e6-5eb8-4f6e-b241-13d28b0ac59f.pdf  \n", "1  cosservice-prod-claims-20240905  3347ab4a-a180-4f2f-8d25-17d70a0cfff4.pdf  \n", "2  cosservice-prod-claims-20240506  00b78673-50d9-45f9-a57d-8e660a70023b.pdf  \n", "3  cosservice-prod-claims-20250205  fdf001a7-0111-4b06-ae8f-8508f8bbd204.pdf  \n", "4  cosservice-prod-claims-20241203  a43ffd08-07af-40cd-8c4e-0c612ac4bd28.pdf  \n", "5  cosservice-prod-claims-20240712  79a7006d-5db0-4251-b0a3-664be72ec112.pdf  \n", "6  cosservice-prod-claims-20240228  2475d364-5d15-41a5-acf4-089152bd342c.pdf  \n", "7  cosservice-prod-claims-20240616  158c4820-c3c0-4678-ba03-ef91a9e536a1.pdf  \n", "8  cosservice-prod-claims-20240307  b16c00aa-fb49-4ac5-9f6f-c3003c116c6e.pdf  \n", "9  cosservice-prod-claims-20240124  ad23de74-df3a-482c-b5b3-59db29493d48.pdf  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 9, "id": "c7718893", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_9010/3403535205.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['DocContainer'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "/tmp/ipykernel_9010/3403535205.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['DocFile'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]\n"]}], "source": ["# Separate document container and document file\n", "df['DocContainer'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "df['DocFile'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]"]}, {"cell_type": "code", "execution_count": 13, "id": "b77f27da", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DocContainer</th>\n", "      <th>DocFile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>cosservice-prod-claims-20241111</td>\n", "      <td>217655e6-5eb8-4f6e-b241-13d28b0ac59f.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>cosservice-prod-claims-20240905</td>\n", "      <td>3347ab4a-a180-4f2f-8d25-17d70a0cfff4.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>cosservice-prod-claims-20240506</td>\n", "      <td>00b78673-50d9-45f9-a57d-8e660a70023b.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>cosservice-prod-claims-20250205</td>\n", "      <td>fdf001a7-0111-4b06-ae8f-8508f8bbd204.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>cosservice-prod-claims-20241203</td>\n", "      <td>a43ffd08-07af-40cd-8c4e-0c612ac4bd28.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>cosservice-prod-claims-20240712</td>\n", "      <td>79a7006d-5db0-4251-b0a3-664be72ec112.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>cosservice-prod-claims-20240228</td>\n", "      <td>2475d364-5d15-41a5-acf4-089152bd342c.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>cosservice-prod-claims-20240616</td>\n", "      <td>158c4820-c3c0-4678-ba03-ef91a9e536a1.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>cosservice-prod-claims-20240307</td>\n", "      <td>b16c00aa-fb49-4ac5-9f6f-c3003c116c6e.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>cosservice-prod-claims-20240124</td>\n", "      <td>ad23de74-df3a-482c-b5b3-59db29493d48.pdf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      DocContainer                                   DocFile\n", "0  cosservice-prod-claims-20241111  217655e6-5eb8-4f6e-b241-13d28b0ac59f.pdf\n", "1  cosservice-prod-claims-20240905  3347ab4a-a180-4f2f-8d25-17d70a0cfff4.pdf\n", "2  cosservice-prod-claims-20240506  00b78673-50d9-45f9-a57d-8e660a70023b.pdf\n", "3  cosservice-prod-claims-20250205  fdf001a7-0111-4b06-ae8f-8508f8bbd204.pdf\n", "4  cosservice-prod-claims-20241203  a43ffd08-07af-40cd-8c4e-0c612ac4bd28.pdf\n", "5  cosservice-prod-claims-20240712  79a7006d-5db0-4251-b0a3-664be72ec112.pdf\n", "6  cosservice-prod-claims-20240228  2475d364-5d15-41a5-acf4-089152bd342c.pdf\n", "7  cosservice-prod-claims-20240616  158c4820-c3c0-4678-ba03-ef91a9e536a1.pdf\n", "8  cosservice-prod-claims-20240307  b16c00aa-fb49-4ac5-9f6f-c3003c116c6e.pdf\n", "9  cosservice-prod-claims-20240124  ad23de74-df3a-482c-b5b3-59db29493d48.pdf"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>']]"]}, {"cell_type": "code", "execution_count": 15, "id": "fff248ca", "metadata": {}, "outputs": [{"ename": "ResourceNotFoundError", "evalue": "The specified container does not exist.\nRequestId:eb70a315-a01e-0072-187e-9ced6b000000\nTime:2025-03-24T05:32:59.8092815Z\nErrorCode:ContainerNotFound\nContent: <?xml version=\"1.0\" encoding=\"utf-8\"?><Error><Code>ContainerNotFound</Code><Message>The specified container does not exist.\nRequestId:eb70a315-a01e-0072-187e-9ced6b000000\nTime:2025-03-24T05:32:59.8092815Z</Message></Error>", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mResourceNotFoundError\u001b[0m                     <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[15], line 25\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m idx \u001b[38;5;129;01min\u001b[39;00m df\u001b[38;5;241m.\u001b[39mindex:\n\u001b[1;32m     24\u001b[0m     path \u001b[38;5;241m=\u001b[39m  \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/workspaces/OCR_in_house/data/10_consultation_notes/\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m+\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDocFile\u001b[39m\u001b[38;5;124m'\u001b[39m][idx]\n\u001b[0;32m---> 25\u001b[0m     \u001b[43mdownload\u001b[49m\u001b[43m(\u001b[49m\u001b[43msource_blob_service_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDocC<PERSON><PERSON>\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43midx\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDocFile\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43midx\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[15], line 20\u001b[0m, in \u001b[0;36mdownload\u001b[0;34m(blob_service_client, container_name, file_name, dest_path)\u001b[0m\n\u001b[1;32m     18\u001b[0m blob_client \u001b[38;5;241m=\u001b[39m container_client\u001b[38;5;241m.\u001b[39mget_blob_client(file_name)\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(dest_path, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwb\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m---> 20\u001b[0m     f\u001b[38;5;241m.\u001b[39mwrite(\u001b[43mblob_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload_blob\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mreadall())\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py:105\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 105\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_blob_client.py:942\u001b[0m, in \u001b[0;36mBlobClient.download_blob\u001b[0;34m(self, offset, length, encoding, **kwargs)\u001b[0m\n\u001b[1;32m    848\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Downloads a blob to the StorageStreamDownloader. The readall() method must\u001b[39;00m\n\u001b[1;32m    849\u001b[0m \u001b[38;5;124;03mbe used to read all the content or readinto() must be used to download the blob into\u001b[39;00m\n\u001b[1;32m    850\u001b[0m \u001b[38;5;124;03ma stream. Using chunks() returns an iterator which allows the user to iterate over the content in chunks.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    935\u001b[0m \u001b[38;5;124;03m        :caption: Download a blob.\u001b[39;00m\n\u001b[1;32m    936\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    937\u001b[0m options \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_download_blob_options(\n\u001b[1;32m    938\u001b[0m     offset\u001b[38;5;241m=\u001b[39moffset,\n\u001b[1;32m    939\u001b[0m     length\u001b[38;5;241m=\u001b[39mlength,\n\u001b[1;32m    940\u001b[0m     encoding\u001b[38;5;241m=\u001b[39mencoding,\n\u001b[1;32m    941\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m--> 942\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mStorageStreamDownloader\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_download.py:366\u001b[0m, in \u001b[0;36mStorageStreamDownloader.__init__\u001b[0;34m(self, clients, config, start_range, end_range, validate_content, encryption_options, max_concurrency, name, container, encoding, download_cls, **kwargs)\u001b[0m\n\u001b[1;32m    356\u001b[0m     initial_request_end \u001b[38;5;241m=\u001b[39m initial_request_start \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_first_get_size \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m    358\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial_range, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial_offset \u001b[38;5;241m=\u001b[39m process_range_and_offset(\n\u001b[1;32m    359\u001b[0m     initial_request_start,\n\u001b[1;32m    360\u001b[0m     initial_request_end,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    363\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_encryption_data\n\u001b[1;32m    364\u001b[0m )\n\u001b[0;32m--> 366\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_initial_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    367\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproperties \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response\u001b[38;5;241m.\u001b[39mproperties\n\u001b[1;32m    368\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproperties\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_download.py:462\u001b[0m, in \u001b[0;36mStorageStreamDownloader._initial_request\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    460\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_file_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m    461\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 462\u001b[0m         \u001b[43mprocess_storage_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    464\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    465\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msize \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_shared/response_handlers.py:184\u001b[0m, in \u001b[0;36mprocess_storage_error\u001b[0;34m(storage_error)\u001b[0m\n\u001b[1;32m    181\u001b[0m error\u001b[38;5;241m.\u001b[39margs \u001b[38;5;241m=\u001b[39m (error\u001b[38;5;241m.\u001b[39mmessage,)\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    183\u001b[0m     \u001b[38;5;66;03m# `from None` prevents us from double printing the exception (suppresses generated layer error context)\u001b[39;00m\n\u001b[0;32m--> 184\u001b[0m     \u001b[43mexec\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mraise error from None\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m   \u001b[38;5;66;03m# pylint: disable=exec-used # nosec\u001b[39;00m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mSyntaxError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    186\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m error \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mexc\u001b[39;00m\n", "File \u001b[0;32m<string>:1\u001b[0m\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_download.py:414\u001b[0m, in \u001b[0;36mStorageStreamDownloader._initial_request\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    412\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m retry_active:\n\u001b[1;32m    413\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 414\u001b[0m         location_mode, response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_clients\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mblob\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    415\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mrange\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrange_header\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrange_get_content_md5\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrange_validation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    417\u001b[0m \u001b[43m            \u001b[49m\u001b[43mvalidate_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    418\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdata_stream_total\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    419\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdownload_stream_current\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    420\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request_options\u001b[49m\n\u001b[1;32m    421\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    423\u001b[0m         \u001b[38;5;66;03m# Check the location we read from to ensure we use the same one\u001b[39;00m\n\u001b[1;32m    424\u001b[0m         \u001b[38;5;66;03m# for subsequent requests.\u001b[39;00m\n\u001b[1;32m    425\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_location_mode \u001b[38;5;241m=\u001b[39m location_mode\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py:105\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 105\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_generated/operations/_blob_operations.py:1611\u001b[0m, in \u001b[0;36mBlobOperations.download\u001b[0;34m(self, snapshot, version_id, timeout, range, range_get_content_md5, range_get_content_crc64, request_id_parameter, lease_access_conditions, cpk_info, modified_access_conditions, **kwargs)\u001b[0m\n\u001b[1;32m   1608\u001b[0m response \u001b[38;5;241m=\u001b[39m pipeline_response\u001b[38;5;241m.\u001b[39mhttp_response\n\u001b[1;32m   1610\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;241m200\u001b[39m, \u001b[38;5;241m206\u001b[39m]:\n\u001b[0;32m-> 1611\u001b[0m     \u001b[43mmap_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstatus_code\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstatus_code\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_map\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1612\u001b[0m     error \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_deserialize\u001b[38;5;241m.\u001b[39mfailsafe_deserialize(_models\u001b[38;5;241m.\u001b[39mStorageError, pipeline_response)\n\u001b[1;32m   1613\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HttpResponseError(response\u001b[38;5;241m=\u001b[39mresponse, model\u001b[38;5;241m=\u001b[39merror)\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/core/exceptions.py:163\u001b[0m, in \u001b[0;36mmap_error\u001b[0;34m(status_code, response, error_map)\u001b[0m\n\u001b[1;32m    161\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    162\u001b[0m error \u001b[38;5;241m=\u001b[39m error_type(response\u001b[38;5;241m=\u001b[39mresponse)\n\u001b[0;32m--> 163\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m error\n", "\u001b[0;31mResourceNotFoundError\u001b[0m: The specified container does not exist.\nRequestId:eb70a315-a01e-0072-187e-9ced6b000000\nTime:2025-03-24T05:32:59.8092815Z\nErrorCode:ContainerNotFound\nContent: <?xml version=\"1.0\" encoding=\"utf-8\"?><Error><Code>ContainerNotFound</Code><Message>The specified container does not exist.\nRequestId:eb70a315-a01e-0072-187e-9ced6b000000\nTime:2025-03-24T05:32:59.8092815Z</Message></Error>"]}], "source": ["# define credentials\n", "    \n", "source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='\n", "\n", "source_account_name = 'p3storageprod'\n", "\n", "sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,\n", "                                     resource_types=ResourceTypes(\n", "                                         service=True, container=True, object=True),\n", "permission=AccountSasPermissions(read=True),\n", "                                     expiry=datetime.utcnow() + <PERSON><PERSON><PERSON>(hours=1))\n", "\n", "source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)\n", "\n", "# Create download function\n", "def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "# Download documents from blob storage to local folder called pdf             \n", "for idx in df.index:\n", "    path =  \"/workspaces/OCR_in_house/data/10_consultation_notes/\"+df['DocFile'][idx]\n", "    download(source_blob_service_client,df['DocContainer'].iloc[idx],df['DocFile'].iloc[idx],path)"]}, {"cell_type": "code", "execution_count": null, "id": "34184de4", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "import fitz  # PyMuPDF\n", "from azure.storage.blob import (\n", "    BlobServiceClient,\n", ")\n", "from dotenv import load_dotenv\n", "from loguru import logger\n", "from paddleocr import PaddleOCR\n", "from scripts.sql_engine import *\n", "\n", "load_dotenv()\n", "\n", "# blob storage download\n", "def blob_download(container_name: str, file_name: str, dest_path: str):\n", "    source_key = os.getenv(\"AZURE_OCR_BLOB_STORAGE_KEY\")\n", "    source_account_url = os.getenv(\"AZURE_OCR_BLOB_STORAGE_ENDPONT\")\n", "    source_blob_service_client = BlobServiceClient(\n", "        account_url=source_account_url, credential=source_key\n", "    )\n", "\n", "    # Create download function\n", "    def download(\n", "        blob_service_client: BlobServiceClient,\n", "        container_name: str,\n", "        file_name: str,\n", "        dest_file_path,\n", "    ):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_file_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "    # Ensure the directory exists\n", "    directory = os.path.dirname(dest_path)\n", "    if directory and not os.path.exists(directory):\n", "        os.makedirs(directory)\n", "\n", "    try:\n", "        download(source_blob_service_client, container_name, file_name, dest_path)\n", "        logger.info(f\"file downloaded to {dest_path}\")\n", "        # copy2(dest_path, f\"../data/tmp_{time.time_ns()}.pdf\")\n", "    except Exception as e:\n", "        logger.exception(e)"]}, {"cell_type": "markdown", "id": "beecc1ad", "metadata": {}, "source": ["## Upload Doc to blob storage"]}, {"cell_type": "code", "execution_count": null, "id": "ec41ca8b", "metadata": {}, "outputs": [], "source": ["account_url = 'https://psvenazsocr.blob.core.windows.net/'\n", "account_key = '****************************************************************************************'\n", "local_folder = r'C:\\Users\\<USER>\\Documents\\Projects\\ocr_audit\\OCR_discount\\sample'\n", "virtual_directory = \"discount_samples/others/\"\n", "\n", "# Create the BlobServiceClient object\n", "blob_service_client = BlobServiceClient(account_url, credential=account_key)\n", "\n", "# Iterate over files in the local directory\n", "for filename in os.listdir(local_folder):\n", "    if os.path.isfile(os.path.join(local_folder, filename)):\n", "        blob_name = virtual_directory + filename\n", "        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)\n", "\n", "        # Check if the blob already exists\n", "        try:\n", "            blob_client.get_blob_properties()\n", "            print(f\"Blob '{blob_name}' already exists. Skipping upload.\")\n", "        except Exception as e:\n", "            # Blob doesn't exist, so upload it\n", "            with open(os.path.join(local_folder, filename), \"rb\") as data:\n", "                blob_client.upload_blob(data)\n", "                print(f\"Uploaded '{blob_name}'\")\n", "\n", "print(\"Upload completed.\")"]}, {"cell_type": "markdown", "id": "33907b99", "metadata": {}, "source": ["## Upload Folder to blob storage"]}, {"cell_type": "code", "execution_count": null, "id": "028bfb98", "metadata": {}, "outputs": [], "source": ["account_url = 'https://psvenazsocr.blob.core.windows.net/'\n", "account_key = '****************************************************************************************'\n", "local_folder = r'C:\\Users\\<USER>\\Documents\\Projects\\ocr_audit\\OCR_discount\\sample'\n", "virtual_directory = \"discount_samples/others/\"\n", "\n", "# Create the BlobServiceClient object\n", "blob_service_client = BlobServiceClient(account_url, credential=account_key)\n", "\n", "# Iterate over files in the local directory\n", "for filename in os.listdir(local_folder):\n", "    if os.path.isfile(os.path.join(local_folder, filename)):\n", "        blob_name = virtual_directory + filename\n", "        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)\n", "\n", "        # Check if the blob already exists\n", "        try:\n", "            blob_client.get_blob_properties()\n", "            print(f\"Blob '{blob_name}' already exists. Skipping upload.\")\n", "        except Exception as e:\n", "            # Blob doesn't exist, so upload it\n", "            with open(os.path.join(local_folder, filename), \"rb\") as data:\n", "                blob_client.upload_blob(data)\n", "                print(f\"Uploaded '{blob_name}'\")\n", "\n", "print(\"Upload completed.\")\n"]}, {"cell_type": "markdown", "id": "ee131fb1", "metadata": {}, "source": ["## Delete blob folder"]}, {"cell_type": "code", "execution_count": null, "id": "8a3d6aad", "metadata": {}, "outputs": [], "source": ["from azure.storage.blob import BlobServiceClient\n", "\n", "# Azure Storage account information\n", "account_url = 'https://psvenazsocr.blob.core.windows.net/'\n", "account_key = '****************************************************************************************'\n", "container_name ='ocr-latest-deployment-test'  # Replace with your container name\n", "\n", "virtual_directory = 'multipet_samples/verified_samples/positive/'  # Replace with your virtual directory path\n", "\n", "# Create the BlobServiceClient object\n", "blob_service_client = BlobServiceClient(account_url, credential=account_key)\n", "\n", "# Get a client to interact with the specified container\n", "container_client = blob_service_client.get_container_client(container=container_name)\n", "\n", "# List all blobs in the virtual directory and delete them\n", "blob_list = container_client.list_blobs(name_starts_with=virtual_directory)\n", "for blob in blob_list:\n", "    blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob.name)\n", "    blob_client.delete_blob()\n", "    print(f\"Deleted blob: {blob.name}\")\n", "\n", "print(\"Virtual directory deletion completed.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}