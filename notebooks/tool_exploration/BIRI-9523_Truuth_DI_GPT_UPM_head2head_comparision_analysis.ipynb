{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import pickle as pk\n", "import os\n", "import glob\n", "import time\n", "from dotenv import load_dotenv\n", "from loguru import logger\n", "import pandas as pd\n", "pd.set_option(\"display.max_columns\", None)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["RAW_DATA_STAT_FILE = \"/workspaces/OCR_in_house//data/OCR_in_house/data/100_samples_DI.csv\"\n", "Truuth_RES_FILE = \"/workspaces/OCR_in_house/data/OCR_in_house/data/100_OcrTruuthJsonOutputResult.xlsx\"\n", "UPM_RES_FILE = \"/workspaces/OCR_in_house/data/OCR_in_house/data/100_SourceOfTruth.xlsx\"\n", "DI_RES_FILE = \"/workspaces/OCR_in_house/data/OCR_in_house/data/100_samples_DI_res_conf.xlsx\"\n", "\n", "RAW_DATA_FOLDER = \"/workspaces/OCR_in_house//data/OCR_in_house/samples/100_samples_DI/\"\n", "MAX_RETRY = 3\n", "DI_OUPUT_DATA_FOLDER = \"/workspaces/OCR_in_house//data/OCR_in_house/samples/100_samples_DI_res/\"\n", "GPT_OUPUT_DATA_FOLDER = \"/workspaces/OCR_in_house//data/OCR_in_house/samples/100_samples_gpt_res/\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim Number</th>\n", "      <th>Claim Date Created</th>\n", "      <th>Document Confidence</th>\n", "      <th>AI Type</th>\n", "      <th>Is GapOnly</th>\n", "      <th>Amount Claimed (OCR)</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "      <th>Amount Claimed (Difference)</th>\n", "      <th>Invoice Count (OCR)</th>\n", "      <th>Invoice Count (UPM)</th>\n", "      <th>Invoice Count (Difference)</th>\n", "      <th>Are Invoice Numbers Different</th>\n", "      <th>Treatment Count (OCR)</th>\n", "      <th>Treatment Count (UPM)</th>\n", "      <th>Treatment Count (Difference)</th>\n", "      <th>Is Service Provider Different</th>\n", "      <th>Audit Category</th>\n", "      <th>Risk Category</th>\n", "      <th>OCR_ServiceProviderName</th>\n", "      <th>UPM_ServiceProviderName</th>\n", "      <th>InvoiceId</th>\n", "      <th>ClaimId</th>\n", "      <th>CspReferenceNo</th>\n", "      <th>ClaimNo</th>\n", "      <th>InvoiceNo</th>\n", "      <th>DateInvoice</th>\n", "      <th>AmountInclVat</th>\n", "      <th>DocumentId</th>\n", "      <th>ClaimRefNumber</th>\n", "      <th>DocumentName</th>\n", "      <th>DocumentDate</th>\n", "      <th>VetPracticeId</th>\n", "      <th>Source</th>\n", "      <th>DocumentPath</th>\n", "      <th>FileSize</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>MatchedOn</th>\n", "      <th>ConfidenceRange</th>\n", "      <th>DocContainer</th>\n", "      <th>DocFile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C7734807</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.0925</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>69.89</td>\n", "      <td>-69.89</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>Ignore</td>\n", "      <td>Ignore</td>\n", "      <td>VetSupply.com.au</td>\n", "      <td>VetSupply.com.au</td>\n", "      <td>4488788</td>\n", "      <td>3796841</td>\n", "      <td>C20240922446</td>\n", "      <td>C7734807</td>\n", "      <td>2528535</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.00</td>\n", "      <td>A8FD48DC-F6A6-4479-BE6C-D76EBB347F4F</td>\n", "      <td>C20240922446</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>2024-09-22 06:58:09.580</td>\n", "      <td>NaN</td>\n", "      <td>CSP</td>\n", "      <td>csp-prod-claim-20240922/92ceb1a1-2fce-4468-abd...</td>\n", "      <td>64216</td>\n", "      <td>10000</td>\n", "      <td>InvoiceNo and DateInvoice</td>\n", "      <td>0-0.19</td>\n", "      <td>csp-prod-claim-20240922</td>\n", "      <td>92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C7734630</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.1300</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>610.80</td>\n", "      <td>568.50</td>\n", "      <td>42.30</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Ignore</td>\n", "      <td>Ignore</td>\n", "      <td>Bass Hill Veterinary Hospital</td>\n", "      <td>Bass Hill Veterinary Hospital</td>\n", "      <td>4488551</td>\n", "      <td>3796612</td>\n", "      <td>C20240922266</td>\n", "      <td>C7734630</td>\n", "      <td>467772</td>\n", "      <td>2024-09-16</td>\n", "      <td>603.30</td>\n", "      <td>C7F9046D-EED8-4398-8AD7-59BA9E5FA9E2</td>\n", "      <td>C20240922266</td>\n", "      <td>Invoice.jpg</td>\n", "      <td>2024-09-22 04:02:07.357</td>\n", "      <td>NaN</td>\n", "      <td>CSP</td>\n", "      <td>csp-prod-claim-20240922/426dd3f2-6ad7-4a93-a80...</td>\n", "      <td>2312843</td>\n", "      <td>0</td>\n", "      <td>No Matches</td>\n", "      <td>0-0.19</td>\n", "      <td>csp-prod-claim-20240922</td>\n", "      <td>426dd3f2-6ad7-4a93-a801-71647893c19d.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7734873</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.1700</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>320.19</td>\n", "      <td>160.09</td>\n", "      <td>160.10</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Ignore</td>\n", "      <td>Ignore</td>\n", "      <td>Petscripts Pty Ltd</td>\n", "      <td>Petscripts Pty Ltd</td>\n", "      <td>4488929</td>\n", "      <td>3796980</td>\n", "      <td>C20240922513</td>\n", "      <td>C7734873</td>\n", "      <td>**********</td>\n", "      <td>2024-04-17</td>\n", "      <td>320.19</td>\n", "      <td>A94C8953-7BEE-4D08-9392-F705E345F0E6</td>\n", "      <td>C20240922513</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>2024-09-22 07:52:04.567</td>\n", "      <td>NaN</td>\n", "      <td>CSP</td>\n", "      <td>csp-prod-claim-20240922/28ba0bd8-eaf3-4b1c-a05...</td>\n", "      <td>161272</td>\n", "      <td>10000</td>\n", "      <td>InvoiceNo and DateInvoice</td>\n", "      <td>0-0.19</td>\n", "      <td>csp-prod-claim-20240922</td>\n", "      <td>28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7734282</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.2100</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>756.94</td>\n", "      <td>-756.94</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>-7</td>\n", "      <td>0</td>\n", "      <td>Ignore</td>\n", "      <td>Ignore</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>4488292</td>\n", "      <td>3796359</td>\n", "      <td>C202409211790</td>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>2024-10-09</td>\n", "      <td>0.00</td>\n", "      <td>DAEBBF17-9A39-4F8F-9276-ED3D97DB6603</td>\n", "      <td>C202409211790</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>2024-09-21 16:43:11.887</td>\n", "      <td>NaN</td>\n", "      <td>CSP</td>\n", "      <td>csp-prod-claim-20240921/d444e324-a0cd-4949-aac...</td>\n", "      <td>48638</td>\n", "      <td>10000</td>\n", "      <td>InvoiceNo</td>\n", "      <td>0.20-0.39</td>\n", "      <td>csp-prod-claim-20240921</td>\n", "      <td>d444e324-a0cd-4949-aac8-311cb8b591e7.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7734396</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.3750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>538.86</td>\n", "      <td>0.00</td>\n", "      <td>538.86</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>Ignore</td>\n", "      <td>Ignore</td>\n", "      <td>Toowong Family Vet</td>\n", "      <td>Unknown SP-Do Not Finalise Claim</td>\n", "      <td>4488390</td>\n", "      <td>3796455</td>\n", "      <td>C2024092226</td>\n", "      <td>C7734396</td>\n", "      <td>24_137296</td>\n", "      <td>2024-09-18</td>\n", "      <td>538.85</td>\n", "      <td>F54FE673-566D-48FA-B11A-E4816FE7C52B</td>\n", "      <td>C2024092226</td>\n", "      <td>Invoice.jpg</td>\n", "      <td>2024-09-22 00:22:06.643</td>\n", "      <td>NaN</td>\n", "      <td>CSP</td>\n", "      <td>csp-prod-claim-20240922/fa970016-e3b5-4013-a5e...</td>\n", "      <td>2126019</td>\n", "      <td>0</td>\n", "      <td>No Matches</td>\n", "      <td>0.20-0.39</td>\n", "      <td>csp-prod-claim-20240922</td>\n", "      <td>fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Claim Number Claim Date Created  Document Confidence  AI Type  Is GapOnly  \\\n", "0     C7734807         2024-09-22               0.0925        0           0   \n", "1     C7734630         2024-09-22               0.1300        0           0   \n", "2     C7734873         2024-09-22               0.1700        0           0   \n", "3     C7734282         2024-09-22               0.2100        0           0   \n", "4     C7734396         2024-09-22               0.3750        0           0   \n", "\n", "   Amount Claimed (OCR)  Amount Claimed (UPM)  Amount Claimed (Difference)  \\\n", "0                  0.00                 69.89                       -69.89   \n", "1                610.80                568.50                        42.30   \n", "2                320.19                160.09                       160.10   \n", "3                  0.00                756.94                      -756.94   \n", "4                538.86                  0.00                       538.86   \n", "\n", "   Invoice Count (OCR)  Invoice Count (UPM)  Invoice Count (Difference)  \\\n", "0                    0                    1                          -1   \n", "1                    1                    1                           0   \n", "2                    1                    1                           0   \n", "3                    0                    1                          -1   \n", "4                    1                    1                           0   \n", "\n", "   Are Invoice Numbers Different  Treatment Count (OCR)  \\\n", "0                              0                      0   \n", "1                              1                      5   \n", "2                              0                      1   \n", "3                              0                      0   \n", "4                              1                      7   \n", "\n", "   Treatment Count (UPM)  Treatment Count (Difference)  \\\n", "0                      1                            -1   \n", "1                      4                             1   \n", "2                      1                             0   \n", "3                      7                            -7   \n", "4                      1                             6   \n", "\n", "   Is Service Provider Different Audit Category Risk Category  \\\n", "0                              0         Ignore        Ignore   \n", "1                              0         Ignore        Ignore   \n", "2                              0         Ignore        Ignore   \n", "3                              0         Ignore        Ignore   \n", "4                              1         Ignore        Ignore   \n", "\n", "           OCR_ServiceProviderName           UPM_ServiceProviderName  \\\n", "0                 VetSupply.com.au                  VetSupply.com.au   \n", "1    Bass Hill Veterinary Hospital     Bass Hill Veterinary Hospital   \n", "2               Petscripts Pty Ltd                Petscripts Pty Ltd   \n", "3  Pascoe Vale Veterinary Hospital   Pascoe Vale Veterinary Hospital   \n", "4               Toowong Family Vet  Unknown SP-Do Not Finalise Claim   \n", "\n", "   InvoiceId  ClaimId CspReferenceNo   ClaimNo   InvoiceNo DateInvoice  \\\n", "0    4488788  3796841   C20240922446  C7734807     2528535  2024-09-22   \n", "1    4488551  3796612   C20240922266  C7734630      467772  2024-09-16   \n", "2    4488929  3796980   C20240922513  C7734873  **********  2024-04-17   \n", "3    4488292  3796359  C202409211790  C7734282      857098  2024-10-09   \n", "4    4488390  3796455    C2024092226  C7734396   24_137296  2024-09-18   \n", "\n", "   AmountInclVat                            DocumentId ClaimRefNumber  \\\n", "0           0.00  A8FD48DC-F6A6-4479-BE6C-D76EBB347F4F   C20240922446   \n", "1         603.30  C7F9046D-EED8-4398-8AD7-59BA9E5FA9E2   C20240922266   \n", "2         320.19  A94C8953-7BEE-4D08-9392-F705E345F0E6   C20240922513   \n", "3           0.00  DAEBBF17-9A39-4F8F-9276-ED3D97DB6603  C202409211790   \n", "4         538.85  F54FE673-566D-48FA-B11A-E4816FE7C52B    C2024092226   \n", "\n", "  DocumentName             DocumentDate VetPracticeId Source  \\\n", "0  Invoice.pdf  2024-09-22 06:58:09.580           NaN    CSP   \n", "1  Invoice.jpg  2024-09-22 04:02:07.357           NaN    CSP   \n", "2  Invoice.pdf  2024-09-22 07:52:04.567           NaN    CSP   \n", "3  Invoice.pdf  2024-09-21 16:43:11.887           NaN    CSP   \n", "4  Invoice.jpg  2024-09-22 00:22:06.643           NaN    CSP   \n", "\n", "                                        DocumentPath  FileSize  InvoiceLineNo  \\\n", "0  csp-prod-claim-20240922/92ceb1a1-2fce-4468-abd...     64216          10000   \n", "1  csp-prod-claim-20240922/426dd3f2-6ad7-4a93-a80...   2312843              0   \n", "2  csp-prod-claim-20240922/28ba0bd8-eaf3-4b1c-a05...    161272          10000   \n", "3  csp-prod-claim-20240921/d444e324-a0cd-4949-aac...     48638          10000   \n", "4  csp-prod-claim-20240922/fa970016-e3b5-4013-a5e...   2126019              0   \n", "\n", "                   MatchedOn ConfidenceRange             DocContainer  \\\n", "0  InvoiceNo and DateInvoice          0-0.19  csp-prod-claim-20240922   \n", "1                 No Matches          0-0.19  csp-prod-claim-20240922   \n", "2  InvoiceNo and DateInvoice          0-0.19  csp-prod-claim-20240922   \n", "3                  InvoiceNo       0.20-0.39  csp-prod-claim-20240921   \n", "4                 No Matches       0.20-0.39  csp-prod-claim-20240922   \n", "\n", "                                    DocFile  \n", "0  92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf  \n", "1  426dd3f2-6ad7-4a93-a801-71647893c19d.jpg  \n", "2  28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf  \n", "3  d444e324-a0cd-4949-aac8-311cb8b591e7.pdf  \n", "4  fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["stat_df = pd.read_csv(RAW_DATA_STAT_FILE)[['Claim Number', 'Claim Date Created',\n", "       'Document Confidence', 'AI Type', 'Is GapOnly', 'Amount Claimed (OCR)',\n", "       'Amount <PERSON><PERSON><PERSON> (UPM)', 'Amount <PERSON><PERSON><PERSON> (Difference)',\n", "       'Invoice Count (OCR)', 'Invoice Count (UPM)',\n", "       'Invoice Count (Difference)', 'Are Invoice Numbers Different',\n", "       'Treatment Count (OCR)', 'Treatment Count (UPM)',\n", "       'Treatment Count (Difference)', 'Is Service Provider Different',\n", "       'Audit Category', 'Risk Category', 'OCR_ServiceProviderName',\n", "       'UPM_ServiceProviderName', 'InvoiceId', 'ClaimId', 'CspReferenceNo',\n", "       'ClaimNo', 'InvoiceNo', 'DateInvoice', 'AmountInclVat', 'DocumentId',\n", "       'ClaimRefNumber', 'DocumentName', 'DocumentDate', 'VetPracticeId',\n", "       'Source', 'DocumentPath', 'FileSize', 'InvoiceLineNo', 'MatchedOn',\n", "       'Confidence<PERSON><PERSON><PERSON>', 'DocContainer', 'DocFile']]\n", "stat_df[[\"AmountInclVat\", \"Amount Claimed (OCR)\"]] = stat_df[[\"AmountInclVat\", \"Amount Claimed (OCR)\"]].fillna(value=0)\n", "stat_df.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim No</th>\n", "      <th>Invoice No</th>\n", "      <th>Service Provider Name</th>\n", "      <th>Date Treatment</th>\n", "      <th>Treatment Drug Description</th>\n", "      <th>Amount Inc Vat</th>\n", "      <th>DateInvoice</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>1261715</td>\n", "      <td>The Village Vet</td>\n", "      <td>2024-09-17</td>\n", "      <td>Procedure Fee</td>\n", "      <td>106.0</td>\n", "      <td>2024-09-17</td>\n", "      <td>213.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>1261715</td>\n", "      <td>The Village Vet</td>\n", "      <td>2024-09-17</td>\n", "      <td>Vaccination (Canine)</td>\n", "      <td>107.2</td>\n", "      <td>2024-09-17</td>\n", "      <td>213.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7734279</td>\n", "      <td>188201</td>\n", "      <td>Malaga Vet</td>\n", "      <td>2024-09-21</td>\n", "      <td>Arthritis Injection</td>\n", "      <td>116.4</td>\n", "      <td>2024-09-21</td>\n", "      <td>116.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Consumables</td>\n", "      <td>62.5</td>\n", "      <td>2024-10-09</td>\n", "      <td>756.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Desexing</td>\n", "      <td>374.5</td>\n", "      <td>2024-10-09</td>\n", "      <td>756.94</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Claim No Invoice No            Service Provider Name Date Treatment  \\\n", "0  ********    1261715                  The Village Vet     2024-09-17   \n", "1  ********    1261715                  The Village Vet     2024-09-17   \n", "2  C7734279     188201                       Malaga Vet     2024-09-21   \n", "3  C7734282     857098  Pascoe Vale Veterinary Hospital     2024-09-10   \n", "4  C7734282     857098  Pascoe Vale Veterinary Hospital     2024-09-10   \n", "\n", "  Treatment Drug Description  Amount Inc Vat DateInvoice  Amount Claimed (UPM)  \n", "0              Procedure Fee           106.0  2024-09-17                213.20  \n", "1       Vaccination (Canine)           107.2  2024-09-17                213.20  \n", "2        Arthritis Injection           116.4  2024-09-21                116.40  \n", "3                Consumables            62.5  2024-10-09                756.94  \n", "4                   Desexing           374.5  2024-10-09                756.94  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["upm_df = pd.read_excel(UPM_RES_FILE)[['Claim No', 'Invoice No', 'Service Provider Name',\n", "       'Date Treatment', 'Treatment Drug Description', 'Amount Inc Vat',\n", "       'DateInvoice', 'Amount <PERSON>laimed (UPM)']]\n", "upm_df.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>ClaimAmount</th>\n", "      <th>ClaimAmount_conf</th>\n", "      <th>ServiceProvider</th>\n", "      <th>ServiceProvider_conf</th>\n", "      <th>ServiceProviderId</th>\n", "      <th>ServiceProviderId_conf</th>\n", "      <th>InvoiceDate</th>\n", "      <th>InvoiceDate_conf</th>\n", "      <th>InvoiceLineNo</th>\n", "      <th>InvoiceNumber</th>\n", "      <th>InvoiceNumber_conf</th>\n", "      <th>InvoiceAmount</th>\n", "      <th>InvoiceAmount_conf</th>\n", "      <th>TreatmentDate</th>\n", "      <th>TreatmentDate_conf</th>\n", "      <th>TreatmentLineNo</th>\n", "      <th>TreatmentDescription</th>\n", "      <th>TreatmentDescription_conf</th>\n", "      <th>Quantity</th>\n", "      <th>TreatmentAmount</th>\n", "      <th>TreatmentAmount_conf</th>\n", "      <th>DocumentConfidence</th>\n", "      <th>ErrorCode</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C7734807</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Vetsupplycomau</td>\n", "      <td>0.42</td>\n", "      <td>CT0484019</td>\n", "      <td>0.42</td>\n", "      <td>2024-09-22T00:00:00</td>\n", "      <td>0.28</td>\n", "      <td>10000</td>\n", "      <td>2528535</td>\n", "      <td>0.39</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0925</td>\n", "      <td>[{'Code': '106', 'Description': 'Invoice Numbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C7734630</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>Bass Hill Vet Hospital</td>\n", "      <td>0.85</td>\n", "      <td>CT0000496</td>\n", "      <td>0.85</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.70</td>\n", "      <td>10000</td>\n", "      <td>467772</td>\n", "      <td>0.70</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>10000.0</td>\n", "      <td>Trazodone Capsules 200mg</td>\n", "      <td>0.7</td>\n", "      <td>NaN</td>\n", "      <td>42.3</td>\n", "      <td>0.7</td>\n", "      <td>0.1300</td>\n", "      <td>[{'Code': '104', 'Description': 'Invoice Numbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7734630</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>Bass Hill Vet Hospital</td>\n", "      <td>0.85</td>\n", "      <td>CT0000496</td>\n", "      <td>0.85</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.70</td>\n", "      <td>10000</td>\n", "      <td>467772</td>\n", "      <td>0.70</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>20000.0</td>\n", "      <td>Consultation - Standard</td>\n", "      <td>0.7</td>\n", "      <td>NaN</td>\n", "      <td>94.0</td>\n", "      <td>0.7</td>\n", "      <td>0.1300</td>\n", "      <td>[{'Code': '104', 'Description': 'Invoice Numbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7734630</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>Bass Hill Vet Hospital</td>\n", "      <td>0.85</td>\n", "      <td>CT0000496</td>\n", "      <td>0.85</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.70</td>\n", "      <td>10000</td>\n", "      <td>467772</td>\n", "      <td>0.70</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>30000.0</td>\n", "      <td>17, <PERSON><PERSON><PERSON>, Sdma, T4)</td>\n", "      <td>0.7</td>\n", "      <td>NaN</td>\n", "      <td>270.0</td>\n", "      <td>0.7</td>\n", "      <td>0.1300</td>\n", "      <td>[{'Code': '104', 'Description': 'Invoice Numbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7734630</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>Bass Hill Vet Hospital</td>\n", "      <td>0.85</td>\n", "      <td>CT0000496</td>\n", "      <td>0.85</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.70</td>\n", "      <td>10000</td>\n", "      <td>467772</td>\n", "      <td>0.70</td>\n", "      <td>603.3</td>\n", "      <td>0.6</td>\n", "      <td>2024-09-16T00:00:00</td>\n", "      <td>0.7</td>\n", "      <td>40000.0</td>\n", "      <td>And Usg In House</td>\n", "      <td>0.7</td>\n", "      <td>NaN</td>\n", "      <td>120.0</td>\n", "      <td>0.7</td>\n", "      <td>0.1300</td>\n", "      <td>[{'Code': '104', 'Description': 'Invoice Numbe...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ClaimNo  ClaimAmount  ClaimAmount_conf         ServiceProvider  \\\n", "0  C7734807          NaN               NaN          Vetsupplycomau   \n", "1  C7734630        603.3               0.6  Bass Hill Vet Hospital   \n", "2  C7734630        603.3               0.6  Bass Hill Vet Hospital   \n", "3  C7734630        603.3               0.6  Bass Hill Vet Hospital   \n", "4  C7734630        603.3               0.6  Bass Hill Vet Hospital   \n", "\n", "   ServiceProvider_conf ServiceProviderId  ServiceProviderId_conf  \\\n", "0                  0.42         CT0484019                    0.42   \n", "1                  0.85         CT0000496                    0.85   \n", "2                  0.85         CT0000496                    0.85   \n", "3                  0.85         CT0000496                    0.85   \n", "4                  0.85         CT0000496                    0.85   \n", "\n", "           InvoiceDate  InvoiceDate_conf  InvoiceLineNo InvoiceNumber  \\\n", "0  2024-09-22T00:00:00              0.28          10000       2528535   \n", "1  2024-09-16T00:00:00              0.70          10000        467772   \n", "2  2024-09-16T00:00:00              0.70          10000        467772   \n", "3  2024-09-16T00:00:00              0.70          10000        467772   \n", "4  2024-09-16T00:00:00              0.70          10000        467772   \n", "\n", "   InvoiceNumber_conf  InvoiceAmount  InvoiceAmount_conf        TreatmentDate  \\\n", "0                0.39            NaN                 NaN                  NaN   \n", "1                0.70          603.3                 0.6  2024-09-16T00:00:00   \n", "2                0.70          603.3                 0.6  2024-09-16T00:00:00   \n", "3                0.70          603.3                 0.6  2024-09-16T00:00:00   \n", "4                0.70          603.3                 0.6  2024-09-16T00:00:00   \n", "\n", "   TreatmentDate_conf  TreatmentLineNo      TreatmentDescription  \\\n", "0                 NaN              NaN                       NaN   \n", "1                 0.7          10000.0  Trazodone Capsules 200mg   \n", "2                 0.7          20000.0   Consultation - Standard   \n", "3                 0.7          30000.0      17, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, T4)   \n", "4                 0.7          40000.0          And Usg In House   \n", "\n", "   TreatmentDescription_conf  Quantity  TreatmentAmount  TreatmentAmount_conf  \\\n", "0                        NaN       NaN              NaN                   NaN   \n", "1                        0.7       NaN             42.3                   0.7   \n", "2                        0.7       NaN             94.0                   0.7   \n", "3                        0.7       NaN            270.0                   0.7   \n", "4                        0.7       NaN            120.0                   0.7   \n", "\n", "   DocumentConfidence                                          ErrorCode  \n", "0              0.0925  [{'Code': '106', 'Description': 'Invoice Numbe...  \n", "1              0.1300  [{'Code': '104', 'Description': 'Invoice Numbe...  \n", "2              0.1300  [{'Code': '104', 'Description': 'Invoice Numbe...  \n", "3              0.1300  [{'Code': '104', 'Description': 'Invoice Numbe...  \n", "4              0.1300  [{'Code': '104', 'Description': 'Invoice Numbe...  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["truuth_df = pd.read_excel(Truuth_RES_FILE)[['ClaimNo', 'ClaimAmount', 'ClaimAmount_conf',\n", "       'ServiceProvider', 'ServiceProvider_conf', 'ServiceProviderId',\n", "       'ServiceProviderId_conf', 'InvoiceDate', 'InvoiceDate_conf',\n", "       'InvoiceLineNo', 'InvoiceNumber', 'InvoiceNumber_conf', 'InvoiceAmount',\n", "       'InvoiceAmount_conf', 'TreatmentDate', 'TreatmentDate_conf',\n", "       'TreatmentLineNo', 'TreatmentDescription', 'TreatmentDescription_conf',\n", "       'Quantity', 'TreatmentAmount', 'TreatmentAmount_conf',\n", "       'DocumentConfidence', 'ErrorCode']]\n", "truuth_df.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["with open(os.path.join(DI_OUPUT_DATA_FOLDER, \"ans.pk\"), \"rb\") as fin:\n", "    di_ans = pk.load(fin)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Tuple, Optional, Union\n", "from tqdm import tqdm\n", "\n", "def ckeckpoint_extraction(data_info: List[Dict] | Dict) -> Dict:\n", "    if isinstance(data_info, Dict):\n", "        data_info = [data_info]\n", "    \n", "    assert isinstance(data_info, List) and all(isinstance(data, Dict) for data in data_info)\n", "\n", "    ans = {}\n", "    for data in tqdm(data_info):\n", "        file_path = data[\"file_path\"]\n", "        invoice = data[\"invoice\"]\n", "\n", "        invoice_info = []\n", "        for document in invoice[\"documents\"]:\n", "            service_provider = document[\"fields\"].get(\"VendorName\", {}).get(\"value\", \"\")\n", "            service_provider_conf = document[\"fields\"].get(\"VendorName\", {}).get(\"confidence\", 0.)\n", "\n", "            invoice_no = document[\"fields\"].get(\"InvoiceId\", {}).get(\"value\", \"\")\n", "            invoice_date = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"value\", \"\")\n", "            invoice_total = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"value\", {}).get(\"amount\", -1)\n", "\n", "            invoice_no_conf = document[\"fields\"].get(\"InvoiceId\", {}).get(\"confidence\", 0.)\n", "            invoice_date_conf = document[\"fields\"].get(\"InvoiceDate\", {}).get(\"confidence\", 0.)\n", "            invoice_total_conf = document[\"fields\"].get(\"InvoiceTotal\", {}).get(\"confidence\", 0.)\n", "\n", "\n", "            treatments = []\n", "\n", "            for item in document[\"fields\"].get(\"Items\", {}).get(\"value\", []):\n", "                desc = item.get(\"value\", {}).get(\"Description\", {}).get(\"content\", \"\")\n", "                desc_conf = item.get(\"value\", {}).get(\"Description\", {}).get(\"confidence\", 0.)\n", "                amount = item.get(\"value\", {}).get(\"Amount\", {}).get(\"value\", {}).get(\"amount\", -1)\n", "                amount_conf = item.get(\"value\", {}).get(\"Amount\", {}).get(\"confidence\", 0.)\n", "                item_conf = item.get(\"confidence\", 0.)\n", "                treatments.append({\"treatment\": desc, \"amount\": amount, \"treatment_conf\": desc_conf, \"amount_conf\": amount_conf, \"treatmentline_conf\": item_conf})\n", "\n", "            invoice_info.append(\n", "                {\n", "                    \"service_provider\": service_provider,\n", "                    \"invoice_no\": invoice_no,\n", "                    \"invoice_date\": invoice_date,\n", "                    \"invoice_total\": invoice_total,\n", "                    \"service_provider_conf\": service_provider_conf,\n", "                    \"invoice_no_conf\": invoice_no_conf,\n", "                    \"invoice_date_conf\": invoice_date_conf,\n", "                    \"invoice_total_conf\": invoice_total_conf,\n", "                    \"treatments\": treatments\n", "                }\n", "            )\n", "            \n", "        ans[file_path] = invoice_info\n", "\n", "\n", "    return ans"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 111/111 [00:00<00:00, 55135.92it/s]\n"]}], "source": ["di_ans_info = ckeckpoint_extraction(di_ans)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "def post_process_di_info(info: Dict) -> Dict:\n", "    ans = {}\n", "    for k, v in info.items():\n", "        if k.lower().endswith(\"pdf\"):\n", "            if len(v[0][\"treatments\"]) ==0:\n", "                continue\n", "            stem = Path(k).stem\n", "            if str(stem[-1]).isdigit() and str(stem[-2] == \"-\"):\n", "                stem = str(stem)[:-2]\n", "                if f\"{stem}.{k[-3:]}\" not in ans:\n", "                    ans[f\"{stem}.{k[-3:]}\"] = [v[0]]\n", "                else:\n", "                    ans[f\"{stem}.{k[-3:]}\"].append(v[0])\n", "        else:\n", "            ans[k] = v\n", "    return ans"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["di_info_dict = post_process_di_info(di_ans_info)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_path</th>\n", "      <th>Invoice No (DI)</th>\n", "      <th>Invoice No (DI) conf</th>\n", "      <th>Service Provider Name (DI)</th>\n", "      <th>Service Provider Name (DI) conf</th>\n", "      <th>Date Treatment (DI)</th>\n", "      <th>Date Treatment (DI) conf</th>\n", "      <th>Treatment Drug Description (DI)</th>\n", "      <th>Treatment Drug Description (DI) conf</th>\n", "      <th>Amount Inc Vat (DI)</th>\n", "      <th>Amount Inc Vat (DI) conf</th>\n", "      <th>DateInvoice (DI)</th>\n", "      <th>DateInvoice (DI) conf</th>\n", "      <th>Amount Claimed (DI)</th>\n", "      <th>Amount Claimed (DI) conf</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1/928537</td>\n", "      <td>0.94</td>\n", "      <td>Baldivis Vet Hospital Pty Ltd</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>Consult 19:00 - 23:30</td>\n", "      <td>0.888</td>\n", "      <td>190.00</td>\n", "      <td>0.889</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>2634.57</td>\n", "      <td>0.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1/928537</td>\n", "      <td>0.94</td>\n", "      <td>Baldivis Vet Hospital Pty Ltd</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>Maropitant - (Prevomax)</td>\n", "      <td>0.889</td>\n", "      <td>51.77</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>2634.57</td>\n", "      <td>0.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1/928537</td>\n", "      <td>0.94</td>\n", "      <td>Baldivis Vet Hospital Pty Ltd</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>Lignocaine 2% Injection</td>\n", "      <td>0.904</td>\n", "      <td>27.82</td>\n", "      <td>0.889</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>2634.57</td>\n", "      <td>0.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1/928537</td>\n", "      <td>0.94</td>\n", "      <td>Baldivis Vet Hospital Pty Ltd</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>Fluid set up</td>\n", "      <td>0.895</td>\n", "      <td>248.00</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>2634.57</td>\n", "      <td>0.93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1/928537</td>\n", "      <td>0.94</td>\n", "      <td>Baldivis Vet Hospital Pty Ltd</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>Ondansetron 8mg Wafers</td>\n", "      <td>0.888</td>\n", "      <td>69.50</td>\n", "      <td>0.888</td>\n", "      <td>2024-09-22</td>\n", "      <td>0.941</td>\n", "      <td>2634.57</td>\n", "      <td>0.93</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_path Invoice No (DI)  \\\n", "0  00dcaff3-051f-4064-ae19-254264e8e242.jpeg        1/928537   \n", "1  00dcaff3-051f-4064-ae19-254264e8e242.jpeg        1/928537   \n", "2  00dcaff3-051f-4064-ae19-254264e8e242.jpeg        1/928537   \n", "3  00dcaff3-051f-4064-ae19-254264e8e242.jpeg        1/928537   \n", "4  00dcaff3-051f-4064-ae19-254264e8e242.jpeg        1/928537   \n", "\n", "   Invoice No (DI) conf     Service Provider Name (DI)  \\\n", "0                  0.94  Baldivis Vet Hospital Pty Ltd   \n", "1                  0.94  Baldivis Vet Hospital Pty Ltd   \n", "2                  0.94  Baldivis Vet Hospital Pty Ltd   \n", "3                  0.94  Baldivis Vet Hospital Pty Ltd   \n", "4                  0.94  Baldivis Vet Hospital Pty Ltd   \n", "\n", "   Service Provider Name (DI) conf Date Treatment (DI)  \\\n", "0                            0.888          2024-09-22   \n", "1                            0.888          2024-09-22   \n", "2                            0.888          2024-09-22   \n", "3                            0.888          2024-09-22   \n", "4                            0.888          2024-09-22   \n", "\n", "   Date Treatment (DI) conf Treatment Drug Description (DI)  \\\n", "0                     0.941           Consult 19:00 - 23:30   \n", "1                     0.941         Maropitant - (Prevomax)   \n", "2                     0.941         Lignocaine 2% Injection   \n", "3                     0.941                    Fluid set up   \n", "4                     0.941          Ondansetron 8mg Wafers   \n", "\n", "   Treatment Drug Description (DI) conf  Amount Inc Vat (DI)  \\\n", "0                                 0.888               190.00   \n", "1                                 0.889                51.77   \n", "2                                 0.904                27.82   \n", "3                                 0.895               248.00   \n", "4                                 0.888                69.50   \n", "\n", "   Amount Inc Vat (DI) conf DateInvoice (DI)  DateInvoice (DI) conf  \\\n", "0                     0.889       2024-09-22                  0.941   \n", "1                     0.888       2024-09-22                  0.941   \n", "2                     0.889       2024-09-22                  0.941   \n", "3                     0.888       2024-09-22                  0.941   \n", "4                     0.888       2024-09-22                  0.941   \n", "\n", "   Amount Clai<PERSON> (DI)  Amount Claimed (DI) conf  \n", "0              2634.57                      0.93  \n", "1              2634.57                      0.93  \n", "2              2634.57                      0.93  \n", "3              2634.57                      0.93  \n", "4              2634.57                      0.93  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["di_df = pd.read_excel(DI_RES_FILE)[['file_path', 'Invoice No (DI)', 'Invoice No (DI) conf',\n", "       'Service Provider Name (DI)', 'Service Provider Name (DI) conf',\n", "       'Date Treatment (DI)', 'Date Treatment (DI) conf',\n", "       'Treatment Drug Description (DI)',\n", "       'Treatment Drug Description (DI) conf', 'Amount Inc Vat (DI)',\n", "       'Amount Inc Vat (DI) conf', 'DateInvoice (DI)', 'DateInvoice (DI) conf',\n", "       'Amount Claimed (DI)', 'Amount <PERSON><PERSON><PERSON> (DI) conf']]\n", "di_df.head()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim No</th>\n", "      <th>Invoice No</th>\n", "      <th>Service Provider Name</th>\n", "      <th>Date Treatment</th>\n", "      <th>Treatment Drug Description</th>\n", "      <th>Amount Inc Vat</th>\n", "      <th>DateInvoice</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>1261715</td>\n", "      <td>The Village Vet</td>\n", "      <td>2024-09-17</td>\n", "      <td>Procedure Fee</td>\n", "      <td>106.0</td>\n", "      <td>2024-09-17</td>\n", "      <td>213.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>1261715</td>\n", "      <td>The Village Vet</td>\n", "      <td>2024-09-17</td>\n", "      <td>Vaccination (Canine)</td>\n", "      <td>107.2</td>\n", "      <td>2024-09-17</td>\n", "      <td>213.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7734279</td>\n", "      <td>188201</td>\n", "      <td>Malaga Vet</td>\n", "      <td>2024-09-21</td>\n", "      <td>Arthritis Injection</td>\n", "      <td>116.4</td>\n", "      <td>2024-09-21</td>\n", "      <td>116.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Consumables</td>\n", "      <td>62.5</td>\n", "      <td>2024-10-09</td>\n", "      <td>756.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Desexing</td>\n", "      <td>374.5</td>\n", "      <td>2024-10-09</td>\n", "      <td>756.94</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Claim No Invoice No            Service Provider Name Date Treatment  \\\n", "0  ********    1261715                  The Village Vet     2024-09-17   \n", "1  ********    1261715                  The Village Vet     2024-09-17   \n", "2  C7734279     188201                       Malaga Vet     2024-09-21   \n", "3  C7734282     857098  Pascoe Vale Veterinary Hospital     2024-09-10   \n", "4  C7734282     857098  Pascoe Vale Veterinary Hospital     2024-09-10   \n", "\n", "  Treatment Drug Description  Amount Inc Vat DateInvoice  Amount Claimed (UPM)  \n", "0              Procedure Fee           106.0  2024-09-17                213.20  \n", "1       Vaccination (Canine)           107.2  2024-09-17                213.20  \n", "2        Arthritis Injection           116.4  2024-09-21                116.40  \n", "3                Consumables            62.5  2024-10-09                756.94  \n", "4                   Desexing           374.5  2024-10-09                756.94  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["upm_df.head()"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["# TODO gather GPT res into xlsx\n", "gpt_ans = []\n", "gpt_info_dict = {}\n", "for file_path in sorted(os.listdir(GPT_OUPUT_DATA_FOLDER)):\n", "    with open(os.path.join(GPT_OUPUT_DATA_FOLDER, file_path), \"r\") as f:\n", "        file_info = json.load(f)\n", "        service_provider = file_info.get(\"VetXmlClaim\", {}).get(\"InfoFromVet\", {}).get(\"Vet\", {}).get(\"PracticeName\", \"\")\n", "        for condition in file_info.get(\"VetXmlClaim\", {}).get(\"InfoFromVet\", {}).get(\"Conditions\", []):\n", "            claim_total = condition.get(\"Financial\", {}).get(\"TotalIncVat\", -1)\n", "            for invoice in condition.get(\"Financial\", {}).get(\"Invoices\", {}):\n", "                invoice_no = invoice.get(\"InvoiceNumber\", \"\")\n", "                invoice_date = invoice.get(\"InvoiceDate\", \"\")\n", "                invoice_amount = invoice.get(\"TotalIncVat\", -1)\n", "                treatments = []\n", "                for item in invoice.get(\"Items\", []):\n", "                    treatment_date = item.get(\"TreatmentDate\", None) or invoice_date\n", "                    treatment_desc = item.get(\"Description\", \"\")\n", "                    treatment_amount = item.get(\"TotalIncVAT\", -1)\n", "                    treatments.append(\n", "                        {\n", "                            \"date_treatment\": treatment_date,\n", "                            \"treatment\": treatment_desc,\n", "                            \"amount\": treatment_amount,\n", "                        }\n", "                    )\n", "                    gpt_ans.append(\n", "                        {\n", "                            \"file_path\": file_path,\n", "                            \"invoice_no\": invoice_no,\n", "                            \"invoice_date\": invoice_date,\n", "                            \"invoice_total\": invoice_amount,\n", "                            \"service_provider\": service_provider,\n", "                            \"date_treatment\": treatment_date,\n", "                            \"treatment\": treatment_desc,\n", "                            \"amount\": treatment_amount,\n", "                        }\n", "                    )\n", "                gpt_info_dict[file_path] = {\n", "                    \"file_path\": file_path,\n", "                    \"invoice_no\": invoice_no,\n", "                    \"invoice_date\": invoice_date,\n", "                    \"invoice_total\": invoice_amount,\n", "                    \"service_provider\": service_provider,\n", "                    \"treatments\": [\n", "                        {\n", "                            \"date_treatment\": treatment_date,\n", "                            \"treatment\": treatment_desc,\n", "                            \"amount\": treatment_amount,\n", "                        }\n", "                    ]\n", "                }\n", "        "]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_path</th>\n", "      <th>invoice_no</th>\n", "      <th>invoice_date</th>\n", "      <th>invoice_total</th>\n", "      <th>service_provider</th>\n", "      <th>date_treatment</th>\n", "      <th>treatment</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.json</td>\n", "      <td>1012538</td>\n", "      <td>22/09/2024</td>\n", "      <td>448.35</td>\n", "      <td>Greater Springfield Veterinary - <PERSON>...</td>\n", "      <td>22/09/2024</td>\n", "      <td>Consultation After Hours</td>\n", "      <td>205.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.json</td>\n", "      <td>1012538</td>\n", "      <td>22/09/2024</td>\n", "      <td>448.35</td>\n", "      <td>Greater Springfield Veterinary - <PERSON>...</td>\n", "      <td>22/09/2024</td>\n", "      <td>In-House Ear Cytology</td>\n", "      <td>58.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.json</td>\n", "      <td>1012538</td>\n", "      <td>22/09/2024</td>\n", "      <td>448.35</td>\n", "      <td>Greater Springfield Veterinary - <PERSON>...</td>\n", "      <td>22/09/2024</td>\n", "      <td>Cephalexin 1000mg Tablets</td>\n", "      <td>77.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.json</td>\n", "      <td>1012538</td>\n", "      <td>22/09/2024</td>\n", "      <td>448.35</td>\n", "      <td>Greater Springfield Veterinary - <PERSON>...</td>\n", "      <td>22/09/2024</td>\n", "      <td>PMP Ear Ointment 30ml</td>\n", "      <td>76.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.json</td>\n", "      <td>1012538</td>\n", "      <td>22/09/2024</td>\n", "      <td>448.35</td>\n", "      <td>Greater Springfield Veterinary - <PERSON>...</td>\n", "      <td>22/09/2024</td>\n", "      <td><PERSON><PERSON> 30cm</td>\n", "      <td>31.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_path invoice_no invoice_date  \\\n", "0  08b8af75-483d-4160-9fba-f75344f4ce4f.json    1012538   22/09/2024   \n", "1  08b8af75-483d-4160-9fba-f75344f4ce4f.json    1012538   22/09/2024   \n", "2  08b8af75-483d-4160-9fba-f75344f4ce4f.json    1012538   22/09/2024   \n", "3  08b8af75-483d-4160-9fba-f75344f4ce4f.json    1012538   22/09/2024   \n", "4  08b8af75-483d-4160-9fba-f75344f4ce4f.json    1012538   22/09/2024   \n", "\n", "  invoice_total                                   service_provider  \\\n", "0        448.35  Greater Springfield Veterinary - <PERSON>...   \n", "1        448.35  Greater Springfield Veterinary - <PERSON>...   \n", "2        448.35  Greater Springfield Veterinary - <PERSON>...   \n", "3        448.35  Greater Springfield Veterinary - <PERSON>...   \n", "4        448.35  Greater Springfield Veterinary - <PERSON>...   \n", "\n", "  date_treatment                  treatment  amount  \n", "0     22/09/2024   Consultation After Hours  205.00  \n", "1     22/09/2024      In-House Ear Cytology   58.00  \n", "2     22/09/2024  Cephalexin 1000mg Tablets   77.25  \n", "3     22/09/2024      PMP Ear Ointment 30ml   76.60  \n", "4     22/09/2024    <PERSON><PERSON> 30cm   31.50  "]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["gpt_df = pd.DataFrame(gpt_ans)\n", "gpt_df.to_excel(\"/workspaces/OCR_in_house/data/OCR_in_house/data/100_samples_gpt_res.xlsx\", index=False)\n", "gpt_df.head()"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'00dcaff3-051f-4064-ae19-254264e8e242.json',\n", " '145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.json',\n", " '29eb8c55-14df-4a91-b2bb-9bc29ab69a63.json'}"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["set(sorted(os.listdir(GPT_OUPUT_DATA_FOLDER))) - set(gpt_df[\"file_path\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# transform data from df to dict"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def map_claimno_docfile(df: pd.DataFrame = stat_df) -> Tuple[Dict, Dict]:\n", "    claimno2docfile = {}\n", "    docfile2claimno = {}\n", "\n", "    for claimno, docfile in zip(df[\"Claim Number\"], df[\"DocFile\"]):\n", "        docfile = docfile.lower()\n", "        if claimno not in claimno2docfile:\n", "            claimno2docfile[claimno] = docfile\n", "\n", "        if docfile not in docfile2claimno:\n", "            docfile2claimno[docfile] = claimno\n", "\n", "    return claimno2docfile, docfile2claimno"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["claimno2docfile, docfile2claimno = map_claimno_docfile(stat_df)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def gather_stat_info(df: pd.DataFrame = stat_df) -> Dict:\n", "    ans = {}\n", "    \n", "    for info in df.to_dict(orient=\"records\"):\n", "        claim_no = info[\"Claim Number\"]\n", "        doc_conf = info[\"Document Confidence\"]\n", "\n", "        invoice_total_ocr = info[\"Amount Claimed (OCR)\"]\n", "        invoice_total_upm = info[\"Amount Claimed (UPM)\"]\n", "        invoice_total_diff = info[\"Amount Claimed (Difference)\"]\n", "\n", "        invoice_count_ocr = info[\"Invoice Count (OCR)\"]\n", "        invoice_count_upm = info[\"Invoice Count (UPM)\"]\n", "        invoice_count_diff = info[\"Invoice Count (Difference)\"]\n", "        is_invoice_num_diff = info[\"Are Invoice Numbers Different\"]\n", "\n", "        treatment_count_ocr = info[\"Treatment Count (OCR)\"]\n", "        treatment_count_upm = info[\"Treatment Count (UPM)\"]\n", "        treatment_count_diff = info[\"Treatment Count (Difference)\"]\n", "\n", "\n", "        invoice_no = info[\"InvoiceNo\"]\n", "        invoice_date = info[\"DateInvoice\"]\n", "\n", "        service_provider_ocr = info[\"OCR_ServiceProviderName\"]\n", "        service_provider_upm = info[\"UPM_ServiceProviderName\"]\n", "        is_service_provider_diff = info[\"Is Service Provider Different\"]\n", "\n", "        amount = info[\"AmountInclVat\"]\n", "        doc_file = info[\"DocFile\"]\n", "\n", "        ans[claim_no] = {\n", "            \"claim_no\": claim_no,\n", "            \"doc_conf\": doc_conf,\n", "            \"invoice_total_ocr\": invoice_total_ocr,\n", "            \"invoice_total_upm\": invoice_total_upm,\n", "            \"invoice_total_diff\": invoice_total_diff,\n", "            \"invoice_count_ocr\": invoice_count_ocr,\n", "            \"invoice_count_upm\": invoice_count_upm,\n", "            \"invoice_count_diff\": invoice_count_diff,\n", "            \"is_invoice_num_diff\": is_invoice_num_diff,\n", "            \"treatment_count_ocr\": treatment_count_ocr,\n", "            \"treatment_count_upm\": treatment_count_upm,\n", "            \"treatment_count_diff\": treatment_count_diff,\n", "            \"invoice_no\": invoice_no,\n", "            \"invoice_date\": invoice_date,\n", "            \"service_provider_ocr\": service_provider_ocr,\n", "            \"service_provider_upm\": service_provider_upm,\n", "            \"is_service_provider_diff\": is_service_provider_diff,\n", "            \"amount\": amount,\n", "            \"doc_file\": doc_file,\n", "        }\n", "    return ans"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["stat_info_dict = gather_stat_info(stat_df)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def gather_truuth_info(df: pd.DataFrame = truuth_df) -> Dict:\n", "    ans = {}\n", "\n", "    for info in df.to_dict(orient=\"records\"):\n", "        claim_no = info[\"ClaimNo\"]\n", "        invoice_no = info[\"InvoiceNumber\"]\n", "        service_provider = info[\"ServiceProvider\"]\n", "        date_treatment = info[\"TreatmentDate\"]\n", "        treatment = info[\"TreatmentDescription\"]\n", "        amount = info[\"TreatmentAmount\"]\n", "        invoice_date = info[\"InvoiceDate\"]\n", "        invoice_total = info[\"ClaimAmount\"]\n", "        doc_conf = info[\"DocumentConfidence\"]\n", "\n", "        if claim_no not in ans:\n", "            ans[claim_no] = {\n", "                \"claim_no\": claim_no,\n", "                \"invoice_no\": invoice_no,\n", "                \"invoice_date\": invoice_date,\n", "                \"invoice_total\": invoice_total,\n", "                \"service_provider\": service_provider,\n", "                \"doc_conf\": doc_conf,\n", "                \"treatments\": [\n", "                    {\n", "                        \"date_treatment\": date_treatment,\n", "                        \"treatment\": treatment,\n", "                        \"amount\": amount,\n", "                    }\n", "                ]\n", "            }\n", "        else:\n", "            ans[claim_no][\"treatments\"].append(\n", "                {\n", "                    \"date_treatment\": date_treatment,\n", "                    \"treatment\": treatment,\n", "                    \"amount\": amount,\n", "                }\n", "            )\n", "    return ans"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["truuth_info_dict = gather_truuth_info(truuth_df)"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim No</th>\n", "      <th>Invoice No</th>\n", "      <th>Service Provider Name</th>\n", "      <th>Date Treatment</th>\n", "      <th>Treatment Drug Description</th>\n", "      <th>Amount Inc Vat</th>\n", "      <th>DateInvoice</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>1261715</td>\n", "      <td>The Village Vet</td>\n", "      <td>2024-09-17</td>\n", "      <td>Procedure Fee</td>\n", "      <td>106.0</td>\n", "      <td>2024-09-17</td>\n", "      <td>213.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>1261715</td>\n", "      <td>The Village Vet</td>\n", "      <td>2024-09-17</td>\n", "      <td>Vaccination (Canine)</td>\n", "      <td>107.2</td>\n", "      <td>2024-09-17</td>\n", "      <td>213.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C7734279</td>\n", "      <td>188201</td>\n", "      <td>Malaga Vet</td>\n", "      <td>2024-09-21</td>\n", "      <td>Arthritis Injection</td>\n", "      <td>116.4</td>\n", "      <td>2024-09-21</td>\n", "      <td>116.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Consumables</td>\n", "      <td>62.5</td>\n", "      <td>2024-10-09</td>\n", "      <td>756.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7734282</td>\n", "      <td>857098</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Desexing</td>\n", "      <td>374.5</td>\n", "      <td>2024-10-09</td>\n", "      <td>756.94</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Claim No Invoice No            Service Provider Name Date Treatment  \\\n", "0  ********    1261715                  The Village Vet     2024-09-17   \n", "1  ********    1261715                  The Village Vet     2024-09-17   \n", "2  C7734279     188201                       Malaga Vet     2024-09-21   \n", "3  C7734282     857098  Pascoe Vale Veterinary Hospital     2024-09-10   \n", "4  C7734282     857098  Pascoe Vale Veterinary Hospital     2024-09-10   \n", "\n", "  Treatment Drug Description  Amount Inc Vat DateInvoice  Amount Claimed (UPM)  \n", "0              Procedure Fee           106.0  2024-09-17                213.20  \n", "1       Vaccination (Canine)           107.2  2024-09-17                213.20  \n", "2        Arthritis Injection           116.4  2024-09-21                116.40  \n", "3                Consumables            62.5  2024-10-09                756.94  \n", "4                   Desexing           374.5  2024-10-09                756.94  "]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["upm_df.head()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def gather_upm_info(df: pd.DataFrame = upm_df) -> Dict:\n", "    ans = {}\n", "\n", "    for info in df.to_dict(orient=\"records\"):\n", "        claim_no = info[\"Claim No\"]\n", "        invoice_no = info[\"Invoice No\"]\n", "        service_provider = info[\"Service Provider Name\"]\n", "        date_treatment = info[\"Date Treatment\"]\n", "        treatment = info[\"Treatment Drug Description\"]\n", "        amount = info[\"Amount Inc Vat\"]\n", "        invoice_date = info[\"DateInvoice\"]\n", "        invoice_total = info[\"Amount Claimed (UPM)\"]\n", "\n", "        if claim_no not in ans:\n", "            ans[claim_no] = {\n", "                \"claim_no\": claim_no,\n", "                \"invoice_no\": invoice_no,\n", "                \"invoice_date\": invoice_date,\n", "                \"invoice_total\": invoice_total,\n", "                \"service_provider\": service_provider,\n", "                \"treatments\": [\n", "                    {\n", "                        \"date_treatment\": date_treatment,\n", "                        \"treatment\": treatment,\n", "                        \"amount\": amount,\n", "                    }\n", "                ]\n", "            }\n", "        else:\n", "            ans[claim_no][\"treatments\"].append(\n", "                {\n", "                    \"date_treatment\": date_treatment,\n", "                    \"treatment\": treatment,\n", "                    \"amount\": amount,\n", "                }\n", "            )\n", "    return ans"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [], "source": ["upm_info_dict = gather_upm_info(upm_df)"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim No</th>\n", "      <th>Invoice No</th>\n", "      <th>Service Provider Name</th>\n", "      <th>Date Treatment</th>\n", "      <th>Treatment Drug Description</th>\n", "      <th>Amount Inc Vat</th>\n", "      <th>DateInvoice</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>********</td>\n", "      <td>600084</td>\n", "      <td>Northern Suburbs Vet Hospitals Greensborough</td>\n", "      <td>2024-09-20</td>\n", "      <td>Diagnostic Test - Blood Test</td>\n", "      <td>210.0</td>\n", "      <td>2025-01-26</td>\n", "      <td>816.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>166</th>\n", "      <td>********</td>\n", "      <td>600084</td>\n", "      <td>Northern Suburbs Vet Hospitals Greensborough</td>\n", "      <td>2024-09-20</td>\n", "      <td>Procedure Fee - Radiology</td>\n", "      <td>349.0</td>\n", "      <td>2025-01-26</td>\n", "      <td>816.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>167</th>\n", "      <td>********</td>\n", "      <td>600084</td>\n", "      <td>Northern Suburbs Vet Hospitals Greensborough</td>\n", "      <td>2024-09-20</td>\n", "      <td>Sedation for Procedure</td>\n", "      <td>257.0</td>\n", "      <td>2025-01-26</td>\n", "      <td>816.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Claim No Invoice No                         Service Provider Name  \\\n", "165  ********     600084  Northern Suburbs Vet Hospitals Greensborough   \n", "166  ********     600084  Northern Suburbs Vet Hospitals Greensborough   \n", "167  ********     600084  Northern Suburbs Vet Hospitals Greensborough   \n", "\n", "    Date Treatment    Treatment Drug Description  Amount Inc Vat DateInvoice  \\\n", "165     2024-09-20  Diagnostic Test - Blood Test           210.0  2025-01-26   \n", "166     2024-09-20     Procedure Fee - Radiology           349.0  2025-01-26   \n", "167     2024-09-20        Sedation for Procedure           257.0  2025-01-26   \n", "\n", "     Amount <PERSON> (UPM)  \n", "165                 816.0  \n", "166                 816.0  \n", "167                 816.0  "]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["upm_df[upm_df[\"Claim No\"] == \"********\"]"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'claim_no': '********',\n", " 'invoice_no': '600084',\n", " 'invoice_date': Timestamp('2025-01-26 00:00:00'),\n", " 'invoice_total': 816.0,\n", " 'service_provider': 'Northern Suburbs Vet Hospitals Greensborough',\n", " 'treatments': [{'date_treatment': Timestamp('2024-09-20 00:00:00'),\n", "   'treatment': 'Diagnostic Test - Blood Test',\n", "   'amount': 210.0},\n", "  {'date_treatment': Timestamp('2024-09-20 00:00:00'),\n", "   'treatment': 'Procedure Fee - Radiology',\n", "   'amount': 349.0},\n", "  {'date_treatment': Timestamp('2024-09-20 00:00:00'),\n", "   'treatment': 'Sedation for Procedure',\n", "   'amount': 257.0}]}"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["upm_info_dict[\"********\"]"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["(dict_keys(['00dcaff3-051f-4064-ae19-254264e8e242.jpeg', '08b8af75-483d-4160-9fba-f75344f4ce4f.pdf', '0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg', '0ca22e7d-42bb-45b5-8ba0-212faba169ad.png', '0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg', '0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg', '1085fe16-0014-489d-bfab-6d005298074a.pdf', '10ec75dd-e526-444f-895b-7faac9c0cd93.jpg', '145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg', '20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg', '2836a967-2f4c-4655-bf35-c148e26d9f07.pdf', '28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf', '29eb8c55-14df-4a91-b2bb-9bc29ab69a63.pdf', '2e5efa44-3660-40df-9701-245d015f3771.pdf', '309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg', '30d78167-a823-44da-a272-a5b36acc66aa.pdf', '37d281d0-903c-482d-aec6-a603544de1d6.pdf', '392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg', '3d59fa71-6b7c-4c5a-93af-cc5bc87036fd.pdf', '3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg', '4234dfb8-5e66-452a-88ba-d520698fb608.jpg', '426dd3f2-6ad7-4a93-a801-71647893c19d.jpg', '42c562c8-ad0a-4119-9b2a-a98702470659.png', '452156dd-abc3-4c1d-b31c-fde48aadf60c.pdf', '4626aa97-e040-494f-9896-0469eeffd02f.pdf', '46b70eea-f82b-4f9e-b453-9fbdc0104549.png', '474466f5-70ca-4c63-83f8-bae13e06e70e.pdf', '48d16278-7a91-4d47-b916-0b1360d784ab.jpeg', '4c52e8fd-f13c-4a77-961a-7e69294d66e4.pdf', '4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png', '50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg', '519d1e33-094f-4c75-a2a4-2aebac4455aa.png', '54addd43-e63e-495d-9b59-fcd34768a072.pdf', '5601aa3e-b487-4e8d-9ae1-0feaacfd0be8.pdf', '56210846-cdf7-4cc4-9489-56e5946d27a4.jpg', '5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg', '65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png', '67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg', '6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9.pdf', '6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg', '6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg', '70f5599a-60dd-4b2c-9f53-ecf07cc714a5.pdf', '728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg', '75d665c1-a17c-4882-9804-7574b221dd21.png', '79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png', '7f6f0d3e-6892-497f-b571-57c5542830a4.png', '7f7ce38b-533e-46c7-a7dd-618fd83c2f5e.pdf', '811bf137-b832-4774-a0ba-42314e20fae2.pdf', '89926c19-088e-49e8-9bf7-3dc947307dad.pdf', '8d3e1857-b6c2-4b94-b757-798cf906675b.pdf', '8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4.pdf', '90683a89-d579-4d61-962c-0625189d0f5b.pdf', '92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf', '9356b36f-4e13-4733-9c77-dc8555add32e.pdf', '99bababe-a109-413f-90e4-608f1eb9e293.pdf', '9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf', 'a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg', 'a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg', 'a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg', 'a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg', 'a85e2454-2d4a-4813-bd81-1af1c54dcc9a.pdf', 'b1f83929-9b81-402f-8b37-13065b355db1.pdf', 'b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg', 'b514aa28-d6db-4360-87f9-0554233f4a86.pdf', 'b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg', 'ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png', 'bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png', 'c607919d-3678-491f-823f-d6c75818c995.png', 'c6282f11-1e0f-4213-9d54-9a5138f72441.jpg', 'c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5.pdf', 'cdc164de-ca0a-4f3d-9bde-3552f06ae74a.pdf', 'ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg', 'cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg', 'd0be39b5-e383-4ffa-9a15-901af56e5baa.pdf', 'd0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg', 'd444e324-a0cd-4949-aac8-311cb8b591e7.pdf', 'd757112f-9c32-4a9b-81e7-140f1d30e717.pdf', 'd80c66ae-b17d-4c47-840d-da293939f843.jpeg', 'd9b8c26d-6e6d-48d3-a763-f8f45fd91477.pdf', 'dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg', 'e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg', 'e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg', 'e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg', 'e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg', 'e4f69236-845c-4fcb-abf9-9df336b0478f.pdf', 'ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6.pdf', 'eb362ff0-c953-4049-9810-0bfbffc1772c.jpg', 'ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg', 'ed611268-36c2-4c56-bee4-26f1005ddca2.pdf', 'f13c4fa7-0df6-4b48-816a-e11e55d5e9f0.pdf', 'f56eb768-41d9-4cdd-bbe6-4969cc51ed86.pdf', 'f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg', 'f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg', 'fa6f85ee-cd21-4eef-8805-939fbcd36712.pdf', 'fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg', 'fbae41f6-4864-419e-bed0-944c831570f2.pdf']),\n", " dict_keys(['********', 'C7734279', 'C7734282', 'C7734284', 'C7734286', 'C7734298', 'C7734305', 'C7734307', 'C7734327', 'C7734331', 'C7734332', 'C7734340', 'C7734346', 'C7734360', 'C7734371', 'C7734396', 'C7734397', 'C7734400', 'C7734412', 'C7734422', 'C7734437', 'C7734458', 'C7734476', 'C7734483', 'C7734495', 'C7734496', 'C7734507', 'C7734510', 'C7734515', 'C7734517', 'C7734520', 'C7734521', 'C7734528', 'C7734532', 'C7734545', 'C7734551', 'C7734568', 'C7734579', 'C7734614', 'C7734616', 'C7734624', 'C7734630', 'C7734643', 'C7734644', 'C7734650', 'C7734675', 'C7734679', 'C7734684', '********', 'C7734729', 'C7734730', 'C7734743', 'C7734746', 'C7734751', 'C7734760', 'C7734763', 'C7734766', 'C7734771', 'C7734772', 'C7734788', 'C7734793', 'C7734796', 'C7734805', 'C7734807', 'C7734815', 'C7734823', 'C7734833', 'C7734846', 'C7734850', 'C7734856', 'C7734860', 'C7734863', 'C7734870', 'C7734873', 'C7734876', 'C7734881', 'C7734907', 'C7734911', 'C7734925', 'C7734927', 'C7734932', 'C7734935', 'C7734944', 'C7734945', 'C7734952', 'C7734954', 'C7734985', 'C7734987', 'C7734992', 'C7734995', 'C7735000', 'C7735007', 'C7735012', 'C7735015', 'C7735019', 'C7735020', 'C7735036', 'C7735044', 'C7735051', 'C7735052']),\n", " dict_keys(['08b8af75-483d-4160-9fba-f75344f4ce4f.json', '0b2c5c86-90a8-4490-af46-2cb4a460b5bd.json', '0ca22e7d-42bb-45b5-8ba0-212faba169ad.json', '0f3de264-9a93-45b5-a56a-1696edceb3ce.json', '0f88ab75-a51e-4799-a0fb-49eb3f2a9161.json', '1085fe16-0014-489d-bfab-6d005298074a.json', '10ec75dd-e526-444f-895b-7faac9c0cd93.json', '20238f33-c059-4eeb-8544-0bfd9eb555ef.json', '204231ce-913e-405c-9132-20cfd100a14b.json', '2836a967-2f4c-4655-bf35-c148e26d9f07.json', '28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.json', '2e5efa44-3660-40df-9701-245d015f3771.json', '309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.json', '30d78167-a823-44da-a272-a5b36acc66aa.json', '37d281d0-903c-482d-aec6-a603544de1d6.json', '392f63c9-1dc3-48ba-a563-f90b8c923058.json', '3d59fa71-6b7c-4c5a-93af-cc5bc87036fd.json', '3e463fdf-fbf8-4366-8b07-0ba07f62df6c.json', '4234dfb8-5e66-452a-88ba-d520698fb608.json', '426dd3f2-6ad7-4a93-a801-71647893c19d.json', '42c562c8-ad0a-4119-9b2a-a98702470659.json', '452156dd-abc3-4c1d-b31c-fde48aadf60c.json', '4626aa97-e040-494f-9896-0469eeffd02f.json', '46b70eea-f82b-4f9e-b453-9fbdc0104549.json', '474466f5-70ca-4c63-83f8-bae13e06e70e.json', '48d16278-7a91-4d47-b916-0b1360d784ab.json', '4c03cf6c-0701-434b-a779-399d45ce051d.json', '4c52e8fd-f13c-4a77-961a-7e69294d66e4.json', '4e9ff370-bc9c-4d1e-aa33-2313d90139b8.json', '50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.json', '519d1e33-094f-4c75-a2a4-2aebac4455aa.json', '54addd43-e63e-495d-9b59-fcd34768a072.json', '5601aa3e-b487-4e8d-9ae1-0feaacfd0be8.json', '56210846-cdf7-4cc4-9489-56e5946d27a4.json', '5686cd7a-c2a6-45cc-95a5-902caa94daa4.json', '65e2706d-f5b0-4bd3-96ad-13a16bb710e0.json', '67dc83a3-e15d-4869-8168-659a8a50cd9e.json', '6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9.json', '6bf46030-cb4a-4add-bbf3-fbd57d3e9248.json', '6da16dc2-eb22-4e29-830a-2aa9db3bd599.json', '70f5599a-60dd-4b2c-9f53-ecf07cc714a5.json', '728e11b5-5923-456e-8c13-b857e7b11a3d.json', '75d665c1-a17c-4882-9804-7574b221dd21.json', '79796bd8-ae1a-4771-bab8-ff65fa35a4f7.json', '7f6f0d3e-6892-497f-b571-57c5542830a4.json', '7f7ce38b-533e-46c7-a7dd-618fd83c2f5e.json', '811bf137-b832-4774-a0ba-42314e20fae2.json', '89926c19-088e-49e8-9bf7-3dc947307dad.json', '8d3e1857-b6c2-4b94-b757-798cf906675b.json', '8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4.json', '90683a89-d579-4d61-962c-0625189d0f5b.json', '91df8902-e51d-4fe3-be8b-6af88d9e6a42.json', '92ceb1a1-2fce-4468-abd8-e477b7b5d317.json', '9356b36f-4e13-4733-9c77-dc8555add32e.json', '99bababe-a109-413f-90e4-608f1eb9e293.json', '9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.json', 'a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.json', 'a37382de-b1cc-4425-8f2f-bb6cae59a0f8.json', 'a6742d14-2825-4ec5-9f60-c180daaf0b78.json', 'a7f99b6b-8c72-44b0-9550-46cae60ee18c.json', 'a85e2454-2d4a-4813-bd81-1af1c54dcc9a.json', 'b1f83929-9b81-402f-8b37-13065b355db1.json', 'b2eee775-e155-4b3f-8dd9-2a1a6f41e285.json', 'b514aa28-d6db-4360-87f9-0554233f4a86.json', 'b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.json', 'ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.json', 'bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.json', 'c607919d-3678-491f-823f-d6c75818c995.json', 'c6282f11-1e0f-4213-9d54-9a5138f72441.json', 'c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5.json', 'cdc164de-ca0a-4f3d-9bde-3552f06ae74a.json', 'ce810450-7f72-490b-9e09-c3d5ae1dad88.json', 'cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.json', 'd0be39b5-e383-4ffa-9a15-901af56e5baa.json', 'd0e3a9af-80ec-4b89-b87b-26869dbaeed8.json', 'd444e324-a0cd-4949-aac8-311cb8b591e7.json', 'd757112f-9c32-4a9b-81e7-140f1d30e717.json', 'd80c66ae-b17d-4c47-840d-da293939f843.json', 'd9b8c26d-6e6d-48d3-a763-f8f45fd91477.json', 'dbd5b507-08c8-47bd-b98b-e030fb9b468b.json', 'e10d92e1-0995-49c9-ab4d-950e04f11c8e.json', 'e23da3d2-dfda-43d2-977e-b19ef997f205.json', 'e2b44cd9-f38b-4337-a537-19d7c39bfc4b.json', 'e4efb75c-f421-4f10-aed9-61e6004938c6.json', 'e4f69236-845c-4fcb-abf9-9df336b0478f.json', 'ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6.json', 'eb362ff0-c953-4049-9810-0bfbffc1772c.json', 'ebb628ae-1660-46f1-8aed-e674bc2fa512.json', 'ed611268-36c2-4c56-bee4-26f1005ddca2.json', 'f13c4fa7-0df6-4b48-816a-e11e55d5e9f0.json', 'f43fba4c-2325-4151-8cfc-263fd2f04d11.json', 'f56eb768-41d9-4cdd-bbe6-4969cc51ed86.json', 'f5ff435f-9fc1-467a-97a9-bd304891ebec.json', 'f8ebb75c-b0b0-4209-8f4e-e37370507f40.json', 'fa6f85ee-cd21-4eef-8805-939fbcd36712.json', 'fbae41f6-4864-419e-bed0-944c831570f2.json']),\n", " dict_keys(['C7734807', 'C7734630', 'C7734873', 'C7734282', 'C7734396', 'C7734805', 'C7734517', 'C7734846', 'C7734954', 'C7735051', 'C7734743', 'C7734850', 'C7734495', 'C7734860', 'C7734340', 'C7734815', 'C7734793', 'C7734532', 'C7734307', 'C7734614', 'C7734528', 'C7734327', 'C7734823', 'C7734751', 'C7734876', 'C7734458', 'C7734331', 'C7734520', 'C7734400', 'C7734881', 'C7734932', 'C7734371', 'C7734286', 'C7734675', 'C7735007', 'C7734771', 'C7734907', 'C7734729', 'C7734833', 'C7735052', 'C7734332', 'C7734360', 'C7734730', 'C7734422', 'C7734772', 'C7734944', 'C7734412', 'C7734679', 'C7734952', 'C7734650', 'C7735012', 'C7734788', 'C7735020', 'C7734945', 'C7734521', 'C7734935', 'C7734925', 'C7734483', '********', 'C7734995', 'C7734298', 'C7734305', 'C7734346', 'C7734437', 'C7734927', 'C7734568', 'C7734863', 'C7734507', 'C7734284', 'C7734870', 'C7734796', 'C7734551', 'C7734510', 'C7734985', 'C7734911', 'C7735036', 'C7734579', 'C7734644', 'C7734496', 'C7734279', 'C7734992', 'C7735000', 'C7734643', 'C7734987', 'C7734624', 'C7734616', 'C7734766', 'C7734746', 'C7735019', 'C7734763', 'C7735044', 'C7734760', 'C7734856', 'C7734476', 'C7734397', 'C7734684', '********', 'C7735015', 'C7734515', 'C7734545']))"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["di_info_dict.keys(), upm_info_dict.keys(), gpt_info_dict.keys(), truuth_info_dict.keys()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["(96, 96, 100, 100)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["len(di_info_dict), len(gpt_info_dict), len(truuth_info_dict), len(upm_info_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filepath2filestem = {for k in docfile2claimno}"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf', '426dd3f2-6ad7-4a93-a801-71647893c19d.jpg', '28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf', 'd444e324-a0cd-4949-aac8-311cb8b591e7.pdf', 'fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg', 'fbae41f6-4864-419e-bed0-944c831570f2.pdf', '91df8902-e51d-4fe3-be8b-6af88d9e6a42.pdf', '37d281d0-903c-482d-aec6-a603544de1d6.pdf', 'a85e2454-2d4a-4813-bd81-1af1c54dcc9a.pdf', '75d665c1-a17c-4882-9804-7574b221dd21.png', '0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg', '1085fe16-0014-489d-bfab-6d005298074a.pdf', 'c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5.pdf', '6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg', 'f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg', '4626aa97-e040-494f-9896-0469eeffd02f.pdf', 'e4f69236-845c-4fcb-abf9-9df336b0478f.pdf', '79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png', '50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg', '56210846-cdf7-4cc4-9489-56e5946d27a4.jpg', 'd80c66ae-b17d-4c47-840d-da293939f843.jpeg', '65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png', '9356b36f-4e13-4733-9c77-dc8555add32e.pdf', '0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg', '10ec75dd-e526-444f-895b-7faac9c0cd93.jpg', '7f6f0d3e-6892-497f-b571-57c5542830a4.png', 'dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg', 'f56eb768-41d9-4cdd-bbe6-4969cc51ed86.pdf', 'e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg', 'a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg', '70f5599a-60dd-4b2c-9f53-ecf07cc714a5.pdf', '48d16278-7a91-4d47-b916-0b1360d784ab.jpeg', '42c562c8-ad0a-4119-9b2a-a98702470659.png', '4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png', 'fa6f85ee-cd21-4eef-8805-939fbcd36712.pdf', '29eb8c55-14df-4a91-b2bb-9bc29ab69a63.pdf', '6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg', '309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg', '8d3e1857-b6c2-4b94-b757-798cf906675b.pdf', 'f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg', 'eb362ff0-c953-4049-9810-0bfbffc1772c.jpg', '0ca22e7d-42bb-45b5-8ba0-212faba169ad.png', '20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg', 'cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg', '00dcaff3-051f-4064-ae19-254264e8e242.jpeg', 'c6282f11-1e0f-4213-9d54-9a5138f72441.jpg', 'ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6.pdf', '5601aa3e-b487-4e8d-9ae1-0feaacfd0be8.pdf', 'f13c4fa7-0df6-4b48-816a-e11e55d5e9f0.pdf', '7f7ce38b-533e-46c7-a7dd-618fd83c2f5e.pdf', '519d1e33-094f-4c75-a2a4-2aebac4455aa.png', '3d59fa71-6b7c-4c5a-93af-cc5bc87036fd.pdf', 'a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg', 'b514aa28-d6db-4360-87f9-0554233f4a86.pdf', 'a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg', '54addd43-e63e-495d-9b59-fcd34768a072.pdf', '392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg', '4234dfb8-5e66-452a-88ba-d520698fb608.jpg', '46b70eea-f82b-4f9e-b453-9fbdc0104549.png', 'ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg', '452156dd-abc3-4c1d-b31c-fde48aadf60c.pdf', 'd9b8c26d-6e6d-48d3-a763-f8f45fd91477.pdf', '2e5efa44-3660-40df-9701-245d015f3771.pdf', '6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9.pdf', 'cdc164de-ca0a-4f3d-9bde-3552f06ae74a.pdf', 'b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg', '0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg', 'a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg', 'ed611268-36c2-4c56-bee4-26f1005ddca2.pdf', 'ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png', '30d78167-a823-44da-a272-a5b36acc66aa.pdf', '145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg', '08b8af75-483d-4160-9fba-f75344f4ce4f.pdf', '67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg', '4c03cf6c-0701-434b-a779-399d45ce051d.jpg', 'e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg', 'e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg', 'b1f83929-9b81-402f-8b37-13065b355db1.pdf', '3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg', '9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf', 'd0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg', '4c52e8fd-f13c-4a77-961a-7e69294d66e4.pdf', 'e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg', '89926c19-088e-49e8-9bf7-3dc947307dad.pdf', 'b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg', 'f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg', '474466f5-70ca-4c63-83f8-bae13e06e70e.pdf', '811bf137-b832-4774-a0ba-42314e20fae2.pdf', '204231ce-913e-405c-9132-20cfd100a14b.jpeg', 'd757112f-9c32-4a9b-81e7-140f1d30e717.pdf', '2836a967-2f4c-4655-bf35-c148e26d9f07.pdf', 'd0be39b5-e383-4ffa-9a15-901af56e5baa.pdf', '99bababe-a109-413f-90e4-608f1eb9e293.pdf', '728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg', '8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4.pdf', 'ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg', '5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg', 'c607919d-3678-491f-823f-d6c75818c995.png', '90683a89-d579-4d61-962c-0625189d0f5b.pdf', 'bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png'])"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["docfile2claimno.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["# gather info\n", "info_dict = {}\n", "for k, v in upm_info_dict.items():\n", "    claim_no = k\n", "    upm_info = v\n", "    truuth_info = truuth_info_dict.get(claim_no, {})\n", "    filepath = claimno2docfile[claim_no]\n", "    di_info = di_info_dict.get(filepath, {})\n", "    gpt_info = gpt_info_dict.get(f\"{str(Path(filepath).stem)}.json\", {})\n", "    info_dict[claim_no] = {\n", "        \"claim_no\": claim_no,\n", "        \"upm_info\": upm_info,\n", "        \"truuth_info\": truuth_info,\n", "        \"di_info\": di_info,\n", "        \"gpt_info\": gpt_info\n", "    }"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["len(info_dict)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["import pickle as pk\n", "with open(\"/workspaces/OCR_in_house//data/OCR_in_house/data/100_samples_solution_info.pk\", \"wb\") as f:\n", "    pk.dump(info_dict, f)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["with open(\"/workspaces/OCR_in_house//data/OCR_in_house/data/100_samples_solution_info.json\", \"w\") as f:\n", "    json.dump(info_dict, f, indent=4, default=str)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# export information to excel"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["gpt C7734396\n", "di C7734517\n", "gpt C7734551\n", "di C7734616\n", "gpt C7734771\n", "gpt C7734772\n", "di C7734911\n", "di C7735019\n"]}], "source": ["for claimno, info in info_dict.items():\n", "    if len(info[\"di_info\"]) == 0:\n", "        print(\"di\",claimno)\n", "    if len(info[\"gpt_info\"]) == 0:\n", "        print(\"gpt\",claimno)"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["ans = []\n", "for claimno, info in info_dict.items():\n", "    claimno = claimno\n", "    filepath = claimno2docfile[claimno]\n", "    truuth_conf = info[\"truuth_info\"][\"doc_conf\"]\n", "\n", "    try:\n", "        di_info = info[\"di_info\"][0]\n", "    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>):\n", "        di_info = {}\n", "\n", "    upm_invoice_no = info[\"upm_info\"][\"invoice_no\"]\n", "    di_invoice_no = di_info.get(\"invoice_no\", \"\")\n", "    di_invoice_no_conf = di_info.get(\"invoice_no_conf\", -1)\n", "    truuth_invoice_no = info[\"truuth_info\"][\"invoice_no\"]\n", "    gpt_invoice_no = info[\"gpt_info\"].get(\"invoice_no\", \"\")\n", "\n", "    upm_invoice_date = info[\"upm_info\"][\"invoice_date\"].date().isoformat() if info[\"upm_info\"][\"invoice_date\"] else \"\"\n", "    di_invoice_date = di_info[\"invoice_date\"].isoformat() if di_info.get(\"invoice_date\", \"\") else \"\"\n", "    di_invoice_date_conf = di_info.get(\"invoice_date_conf\", -1)\n", "    truuth_invoice_date = info[\"truuth_info\"][\"invoice_date\"]\n", "    gpt_invoice_date = info[\"gpt_info\"].get(\"invoice_date\", \"\")\n", "\n", "    upm_invoice_total = info[\"upm_info\"][\"invoice_total\"]\n", "    di_invoice_total = di_info.get(\"invoice_total\", -1)\n", "    di_invoice_total_conf = di_info.get(\"invoice_total_conf\", -1)\n", "    truuth_invoice_total = info[\"truuth_info\"][\"invoice_total\"]\n", "    gpt_invoice_total = info[\"gpt_info\"].get(\"invoice_total\", -1)\n", "\n", "    upm_service_provider = info[\"upm_info\"][\"service_provider\"]\n", "    di_service_provider = di_info.get(\"service_provider\", \"\")\n", "    di_service_provider_conf = di_info.get(\"service_provider_conf\", -1)\n", "    truuth_service_provider = info[\"truuth_info\"][\"service_provider\"]\n", "    gpt_service_provider = info[\"gpt_info\"].get(\"service_provider\", \"\")\n", "\n", "    upm_treatments = info[\"upm_info\"][\"treatments\"]\n", "    di_treatments = [x for x in di_info.get(\"treatments\", []) if x.get(\"treatment\", \"\")]\n", "    truuth_treatments = info[\"truuth_info\"][\"treatments\"]\n", "    gpt_treatments = info[\"gpt_info\"].get(\"treatments\", [])\n", "\n", "    num_treatments = max([len(upm_treatments), len(di_treatments), len(truuth_treatments), len(gpt_treatments)])\n", "\n", "    for i in range(num_treatments):\n", "        try:\n", "            upm_treatment = upm_treatments[i]\n", "        except IndexError:\n", "            upm_treatment = {}\n", "\n", "        try:\n", "            di_treatment = di_treatments[i]\n", "        except IndexError:\n", "            di_treatment = {}\n", "\n", "        try:\n", "            truuth_treatment = truuth_treatments[i]\n", "        except IndexError:\n", "            truuth_treatment = {}\n", "\n", "        try:\n", "            gpt_treatment = gpt_treatments[i]\n", "        except IndexError:\n", "            gpt_treatment = {}\n", "\n", "        upm_date_treatment = upm_treatment.get(\"date_treatment\", \"\")\n", "        if upm_date_treatment:\n", "            upm_date_treatment = upm_date_treatment.date().isoformat()\n", "\n", "        upm_treatment_desc = upm_treatment.get(\"treatment\", \"\")\n", "        upm_amount = upm_treatment.get(\"amount\", -1)\n", "\n", "        di_date_treatment = di_treatment.get(\"date_treatment\", di_invoice_date)\n", "        di_treatment_desc = di_treatment.get(\"treatment\", \"\")\n", "        di_amount = di_treatment.get(\"amount\", -1)\n", "\n", "        truuth_date_treatment = truuth_treatment.get(\"date_treatment\", \"\")\n", "        truuth_treatment_desc = truuth_treatment.get(\"treatment\", \"\")\n", "        truuth_amount = truuth_treatment.get(\"amount\", -1)\n", "\n", "        gpt_date_treatment = gpt_treatment.get(\"date_treatment\", \"\")\n", "        gpt_treatment_desc = gpt_treatment.get(\"treatment\", \"\")\n", "        gpt_amount = gpt_treatment.get(\"amount\", -1)\n", "\n", "        if i == 0:\n", "            ans.append({\n", "                \"claimno\": claimno,\n", "                \"filepath\": filepath,\n", "                \"truuth_conf\": truuth_conf,\n", "\n", "                \"upm_invoice_no\": upm_invoice_no,\n", "                \"di_invoice_no\": di_invoice_no,\n", "                \"di_invoice_no_conf\": di_invoice_no_conf,\n", "                \"truuth_invoice_no\": truuth_invoice_no,\n", "                \"gpt_invoice_no\": gpt_invoice_no,\n", "\n", "                \"upm_invoice_date\": upm_invoice_date,\n", "                \"di_invoice_date\": di_invoice_date,\n", "                \"di_invoice_date_conf\": di_invoice_date_conf,\n", "                \"truuth_invoice_date\": truuth_invoice_date,\n", "                \"gpt_invoice_date\": gpt_invoice_date,\n", "\n", "                \"upm_invoice_total\": upm_invoice_total,\n", "                \"di_invoice_total\": di_invoice_total,\n", "                \"di_invoice_total_conf\": di_invoice_total_conf,\n", "                \"truuth_invoice_total\": truuth_invoice_total,\n", "                \"gpt_invoice_total\": gpt_invoice_total,\n", "\n", "                \"upm_service_provider\": upm_service_provider,\n", "                \"di_service_provider\": di_service_provider,\n", "                \"di_service_provider_conf\": di_service_provider_conf,\n", "                \"truuth_service_provider\": truuth_service_provider,\n", "                \"gpt_service_provider\": gpt_service_provider,\n", "\n", "                \"upm_date_treatment\": upm_date_treatment,\n", "                \"upm_treatment_desc\": upm_treatment_desc,\n", "                \"upm_amount\": upm_amount,\n", "\n", "                \"di_date_treatment\": di_date_treatment,\n", "                \"di_treatment_desc\": di_treatment_desc,\n", "                \"di_amount\": di_amount,\n", "\n", "                \"truuth_date_treatment\": truuth_date_treatment,\n", "                \"truuth_treatment_desc\": truuth_treatment_desc,\n", "                \"truuth_amount\": truuth_amount,\n", "\n", "                \"gpt_date_treatment\": gpt_date_treatment,\n", "                \"gpt_treatment_desc\": gpt_treatment_desc,\n", "                \"gpt_amount\": gpt_amount,\n", "            })\n", "        else:\n", "            ans.append({\n", "                \"upm_date_treatment\": upm_date_treatment,\n", "                \"upm_treatment_desc\": upm_treatment_desc,\n", "                \"upm_amount\": upm_amount,\n", "\n", "                \"di_date_treatment\": di_date_treatment,\n", "                \"di_treatment_desc\": di_treatment_desc,\n", "                \"di_amount\": di_amount,\n", "\n", "                \"truuth_date_treatment\": truuth_date_treatment,\n", "                \"truuth_treatment_desc\": truuth_treatment_desc,\n", "                \"truuth_amount\": truuth_amount,\n", "\n", "                \"gpt_date_treatment\": gpt_date_treatment,\n", "                \"gpt_treatment_desc\": gpt_treatment_desc,\n", "                \"gpt_amount\": gpt_amount,\n", "            })"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["410"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ans)"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>claimno</th>\n", "      <th>filepath</th>\n", "      <th>truuth_conf</th>\n", "      <th>upm_invoice_no</th>\n", "      <th>di_invoice_no</th>\n", "      <th>di_invoice_no_conf</th>\n", "      <th>truuth_invoice_no</th>\n", "      <th>gpt_invoice_no</th>\n", "      <th>upm_invoice_date</th>\n", "      <th>di_invoice_date</th>\n", "      <th>di_invoice_date_conf</th>\n", "      <th>truuth_invoice_date</th>\n", "      <th>gpt_invoice_date</th>\n", "      <th>upm_invoice_total</th>\n", "      <th>di_invoice_total</th>\n", "      <th>di_invoice_total_conf</th>\n", "      <th>truuth_invoice_total</th>\n", "      <th>gpt_invoice_total</th>\n", "      <th>upm_service_provider</th>\n", "      <th>di_service_provider</th>\n", "      <th>di_service_provider_conf</th>\n", "      <th>truuth_service_provider</th>\n", "      <th>gpt_service_provider</th>\n", "      <th>upm_date_treatment</th>\n", "      <th>upm_treatment_desc</th>\n", "      <th>upm_amount</th>\n", "      <th>di_date_treatment</th>\n", "      <th>di_treatment_desc</th>\n", "      <th>di_amount</th>\n", "      <th>truuth_date_treatment</th>\n", "      <th>truuth_treatment_desc</th>\n", "      <th>truuth_amount</th>\n", "      <th>gpt_date_treatment</th>\n", "      <th>gpt_treatment_desc</th>\n", "      <th>gpt_amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>46b70eea-f82b-4f9e-b453-9fbdc0104549.png</td>\n", "      <td>0.93</td>\n", "      <td>1261715</td>\n", "      <td>1261715</td>\n", "      <td>0.950</td>\n", "      <td>1261715</td>\n", "      <td>1261715</td>\n", "      <td>2024-09-17</td>\n", "      <td>2024-09-17</td>\n", "      <td>0.950</td>\n", "      <td>2024-09-17T00:00:00</td>\n", "      <td>17/09/24</td>\n", "      <td>213.20</td>\n", "      <td>213.20</td>\n", "      <td>0.685</td>\n", "      <td>213.2</td>\n", "      <td>213.20</td>\n", "      <td>The Village Vet</td>\n", "      <td>The Village Vet In Downer</td>\n", "      <td>0.747</td>\n", "      <td>The Village Vet</td>\n", "      <td>The Village Vet In Downer</td>\n", "      <td>2024-09-17</td>\n", "      <td>Procedure Fee</td>\n", "      <td>106.0</td>\n", "      <td>2024-09-17</td>\n", "      <td>C3 3-yearly Vaccination - <PERSON><PERSON>, Adenov...</td>\n", "      <td>63.30</td>\n", "      <td>2024-09-17T00:00:00</td>\n", "      <td>C3 3-yearly Vaccination - <PERSON><PERSON>, Adenov...</td>\n", "      <td>63.3</td>\n", "      <td>17/09/24</td>\n", "      <td>KC vaccination</td>\n", "      <td>43.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024-09-17</td>\n", "      <td>Vaccination (Canine)</td>\n", "      <td>107.2</td>\n", "      <td>2024-09-17</td>\n", "      <td>Consultation</td>\n", "      <td>106.00</td>\n", "      <td>2024-09-17T00:00:00</td>\n", "      <td>Consultation</td>\n", "      <td>106.0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>-1.0</td>\n", "      <td>2024-09-17</td>\n", "      <td>KC vaccination</td>\n", "      <td>43.90</td>\n", "      <td>2024-09-17T00:00:00</td>\n", "      <td>KC vaccination</td>\n", "      <td>43.9</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C7734279</td>\n", "      <td>9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf</td>\n", "      <td>0.95</td>\n", "      <td>188201</td>\n", "      <td>188202</td>\n", "      <td>0.966</td>\n", "      <td>188201</td>\n", "      <td>188202</td>\n", "      <td>2024-09-21</td>\n", "      <td>2024-09-21</td>\n", "      <td>0.926</td>\n", "      <td>2024-09-21T00:00:00</td>\n", "      <td>21 Sep 24</td>\n", "      <td>116.40</td>\n", "      <td>116.40</td>\n", "      <td>0.946</td>\n", "      <td>116.4</td>\n", "      <td>116.40</td>\n", "      <td>Malaga Vet</td>\n", "      <td>MALAGAVET\\nVeterinary Hospital</td>\n", "      <td>0.924</td>\n", "      <td>Malaga Vet Hospital And Surgery</td>\n", "      <td>MalagaVet Veterinary Hospital</td>\n", "      <td>2024-09-21</td>\n", "      <td>Arthritis Injection</td>\n", "      <td>116.4</td>\n", "      <td>2024-09-21</td>\n", "      <td>Beransa 5mg Vial (0-10kg)</td>\n", "      <td>116.40</td>\n", "      <td>2024-09-21T00:00:00</td>\n", "      <td>Beransa 5mg Vial (0-10kg)</td>\n", "      <td>116.4</td>\n", "      <td>21 Sep 24</td>\n", "      <td>Beransa 5mg Vial (0-10kg)</td>\n", "      <td>116.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C7734282</td>\n", "      <td>d444e324-a0cd-4949-aac8-311cb8b591e7.pdf</td>\n", "      <td>0.21</td>\n", "      <td>857098</td>\n", "      <td>857098</td>\n", "      <td>0.950</td>\n", "      <td>857098</td>\n", "      <td>857098</td>\n", "      <td>2024-10-09</td>\n", "      <td>2024-09-10</td>\n", "      <td>0.950</td>\n", "      <td>2024-10-09T00:00:00</td>\n", "      <td>10-09-2024</td>\n", "      <td>756.94</td>\n", "      <td>756.94</td>\n", "      <td>0.947</td>\n", "      <td>NaN</td>\n", "      <td>$756.94</td>\n", "      <td>Pascoe Vale Veterinary Hospital</td>\n", "      <td>Pascoe Vale\\nVet Hospital</td>\n", "      <td>0.915</td>\n", "      <td>Pascoe Vale Vet Hospital</td>\n", "      <td>Pascoe Vale Vet Hospital</td>\n", "      <td>2024-09-10</td>\n", "      <td>Consumables</td>\n", "      <td>62.5</td>\n", "      <td>2024-09-10</td>\n", "      <td>Idexx Internal Lab</td>\n", "      <td>167.77</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10-09-2024</td>\n", "      <td>NVC Surgery - Consumables</td>\n", "      <td>$62.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    claimno                                  filepath  truuth_conf  \\\n", "0  ********  46b70eea-f82b-4f9e-b453-9fbdc0104549.png         0.93   \n", "1       NaN                                       NaN          NaN   \n", "2       NaN                                       NaN          NaN   \n", "3  C7734279  9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf         0.95   \n", "4  C7734282  d444e324-a0cd-4949-aac8-311cb8b591e7.pdf         0.21   \n", "\n", "  upm_invoice_no di_invoice_no  di_invoice_no_conf truuth_invoice_no  \\\n", "0        1261715       1261715               0.950           1261715   \n", "1            NaN           NaN                 NaN               NaN   \n", "2            NaN           NaN                 NaN               NaN   \n", "3         188201        188202               0.966            188201   \n", "4         857098        857098               0.950            857098   \n", "\n", "  gpt_invoice_no upm_invoice_date di_invoice_date  di_invoice_date_conf  \\\n", "0        1261715       2024-09-17      2024-09-17                 0.950   \n", "1            NaN              NaN             NaN                   NaN   \n", "2            NaN              NaN             NaN                   NaN   \n", "3         188202       2024-09-21      2024-09-21                 0.926   \n", "4         857098       2024-10-09      2024-09-10                 0.950   \n", "\n", "   truuth_invoice_date gpt_invoice_date  upm_invoice_total  di_invoice_total  \\\n", "0  2024-09-17T00:00:00         17/09/24             213.20            213.20   \n", "1                  NaN              NaN                NaN               NaN   \n", "2                  NaN              NaN                NaN               NaN   \n", "3  2024-09-21T00:00:00        21 Sep 24             116.40            116.40   \n", "4  2024-10-09T00:00:00       10-09-2024             756.94            756.94   \n", "\n", "   di_invoice_total_conf  truuth_invoice_total gpt_invoice_total  \\\n", "0                  0.685                 213.2            213.20   \n", "1                    NaN                   NaN               NaN   \n", "2                    NaN                   NaN               NaN   \n", "3                  0.946                 116.4            116.40   \n", "4                  0.947                   NaN           $756.94   \n", "\n", "              upm_service_provider             di_service_provider  \\\n", "0                  The Village Vet       The Village Vet In Downer   \n", "1                              NaN                             NaN   \n", "2                              NaN                             NaN   \n", "3                       Malaga Vet  MALAGAVET\\nVeterinary Hospital   \n", "4  Pascoe Vale Veterinary Hospital       Pascoe Vale\\nVet Hospital   \n", "\n", "   di_service_provider_conf          truuth_service_provider  \\\n", "0                     0.747                  The Village Vet   \n", "1                       NaN                              NaN   \n", "2                       NaN                              NaN   \n", "3                     0.924  Malaga Vet Hospital And Surgery   \n", "4                     0.915         Pascoe Vale Vet Hospital   \n", "\n", "            gpt_service_provider upm_date_treatment    upm_treatment_desc  \\\n", "0      The Village Vet In Downer         2024-09-17         Procedure Fee   \n", "1                            NaN         2024-09-17  Vaccination (Canine)   \n", "2                            NaN                                            \n", "3  MalagaVet Veterinary Hospital         2024-09-21   Arthritis Injection   \n", "4       Pascoe Vale Vet Hospital         2024-09-10           Consumables   \n", "\n", "   upm_amount di_date_treatment  \\\n", "0       106.0        2024-09-17   \n", "1       107.2        2024-09-17   \n", "2        -1.0        2024-09-17   \n", "3       116.4        2024-09-21   \n", "4        62.5        2024-09-10   \n", "\n", "                                   di_treatment_desc  di_amount  \\\n", "0  C3 3-yearly Vaccination - <PERSON><PERSON>, Adenov...      63.30   \n", "1                                       Consultation     106.00   \n", "2                                     KC vaccination      43.90   \n", "3                          Beransa 5mg Vial (0-10kg)     116.40   \n", "4                                 Idexx Internal Lab     167.77   \n", "\n", "  truuth_date_treatment                              truuth_treatment_desc  \\\n", "0   2024-09-17T00:00:00  C3 3-yearly Vaccination - <PERSON><PERSON>, Adenov...   \n", "1   2024-09-17T00:00:00                                       Consultation   \n", "2   2024-09-17T00:00:00                                     KC vaccination   \n", "3   2024-09-21T00:00:00                          Beransa 5mg Vial (0-10kg)   \n", "4                   NaN                                                NaN   \n", "\n", "   truuth_amount gpt_date_treatment         gpt_treatment_desc gpt_amount  \n", "0           63.3           17/09/24             KC vaccination      43.90  \n", "1          106.0                                                       -1  \n", "2           43.9                                                       -1  \n", "3          116.4          21 Sep 24  Beransa 5mg Vial (0-10kg)     116.40  \n", "4            NaN         10-09-2024  NVC Surgery - Consumables     $62.50  "]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df = pd.DataFrame(ans)\n", "final_df.head()"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["final_df.to_excel(\"/workspaces/OCR_in_house//data/OCR_in_house/data/100_samples_comparison.xlsx\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}