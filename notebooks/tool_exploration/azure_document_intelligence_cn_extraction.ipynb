{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# construct testing invoice files"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# import fitz  # PyMuPDF\n", "\n", "# file_path = \"../../data/tmp_1728358794590598391.pdf\"\n", "# pdf_document = fitz.open(file_path)\n", "\n", "# invoice1_output_path = \"../../data/test_invoice1.pdf\"\n", "# invoice1_output_pdf = fitz.open()\n", "# invoice1_output_pdf.insert_pdf(pdf_document, from_page=0, to_page=0)\n", "# invoice1_output_pdf.save(invoice1_output_path)\n", "# invoice1_output_pdf.close()\n", "\n", "\n", "# invoice2_output_path = \"../../data/test_invoice2.pdf\"\n", "# invoice2_output_pdf = fitz.open()\n", "# invoice2_output_pdf.insert_pdf(pdf_document, from_page=1, to_page=1)\n", "# invoice2_output_pdf.save(invoice2_output_path)\n", "# invoice2_output_pdf.close()\n", "\n", "# pdf_document.close()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "import pandas as pd\n", "import threading\n", "from typing import Any, Union, Tuple, Dict\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from loguru import logger\n", "from shutil import copy2\n", "from dotenv import load_dotenv\n", "from pathlib import Path\n", "import glob\n", "import fitz  # PyMuPDF\n", "from paddleocr import PaddleOCR, draw_ocr\n", "from fitz import FileDataError\n", "import sqlite3\n", "import csv\n", "import pyodbc\n", "import sys\n", "import re\n", "\n", "os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'\n", "\n", "module = '.'\n", "sys.path.insert(1, module)\n", "\n", "\n", "load_dotenv()\n", "ROOTDIR = Path(\"/workspaces/OCR_in_house/notebooks/tool_exploration\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["\n", "df = pd.read_csv((ROOTDIR / \"claims_path.csv\"))\n", "df['DocContainer'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "df['DocFile'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]\n", "# df[\"ContainerName\"] = df[\"DocumentPath\"].apply(lambda x: str(Path(x).parent))\n", "# df[\"DocumentFile\"] = df[\"DocumentPath\"].apply(lambda x: str(Path(x).name))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "ResourceNotFoundError", "evalue": "The specified container does not exist.\nRequestId:435f3d65-a01e-004d-54b9-9725c8000000\nTime:2025-03-18T03:56:35.3839466Z\nErrorCode:ContainerNotFound\nContent: <?xml version=\"1.0\" encoding=\"utf-8\"?><Error><Code>ContainerNotFound</Code><Message>The specified container does not exist.\nRequestId:435f3d65-a01e-004d-54b9-9725c8000000\nTime:2025-03-18T03:56:35.3839466Z</Message></Error>", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mResourceNotFoundError\u001b[0m                     <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 33\u001b[0m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m idx \u001b[38;5;129;01min\u001b[39;00m df\u001b[38;5;241m.\u001b[39mindex:\n\u001b[1;32m     32\u001b[0m     path \u001b[38;5;241m=\u001b[39m  \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m/workspaces/OCR_in_house/data/DI_consultation_notes/\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m+\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDocFile\u001b[39m\u001b[38;5;124m'\u001b[39m][idx]\n\u001b[0;32m---> 33\u001b[0m     \u001b[43mdownload\u001b[49m\u001b[43m(\u001b[49m\u001b[43msource_blob_service_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDocC<PERSON><PERSON>\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43midx\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mDocFile\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43miloc\u001b[49m\u001b[43m[\u001b[49m\u001b[43midx\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[12], line 28\u001b[0m, in \u001b[0;36mdownload\u001b[0;34m(blob_service_client, container_name, file_name, dest_path)\u001b[0m\n\u001b[1;32m     26\u001b[0m blob_client \u001b[38;5;241m=\u001b[39m container_client\u001b[38;5;241m.\u001b[39mget_blob_client(file_name)\n\u001b[1;32m     27\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(dest_path, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwb\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[0;32m---> 28\u001b[0m     f\u001b[38;5;241m.\u001b[39mwrite(\u001b[43mblob_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload_blob\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mreadall())\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py:105\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 105\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_blob_client.py:942\u001b[0m, in \u001b[0;36mBlobClient.download_blob\u001b[0;34m(self, offset, length, encoding, **kwargs)\u001b[0m\n\u001b[1;32m    848\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Downloads a blob to the StorageStreamDownloader. The readall() method must\u001b[39;00m\n\u001b[1;32m    849\u001b[0m \u001b[38;5;124;03mbe used to read all the content or readinto() must be used to download the blob into\u001b[39;00m\n\u001b[1;32m    850\u001b[0m \u001b[38;5;124;03ma stream. Using chunks() returns an iterator which allows the user to iterate over the content in chunks.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    935\u001b[0m \u001b[38;5;124;03m        :caption: Download a blob.\u001b[39;00m\n\u001b[1;32m    936\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    937\u001b[0m options \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_download_blob_options(\n\u001b[1;32m    938\u001b[0m     offset\u001b[38;5;241m=\u001b[39moffset,\n\u001b[1;32m    939\u001b[0m     length\u001b[38;5;241m=\u001b[39mlength,\n\u001b[1;32m    940\u001b[0m     encoding\u001b[38;5;241m=\u001b[39mencoding,\n\u001b[1;32m    941\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m--> 942\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mStorageStreamDownloader\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_download.py:366\u001b[0m, in \u001b[0;36mStorageStreamDownloader.__init__\u001b[0;34m(self, clients, config, start_range, end_range, validate_content, encryption_options, max_concurrency, name, container, encoding, download_cls, **kwargs)\u001b[0m\n\u001b[1;32m    356\u001b[0m     initial_request_end \u001b[38;5;241m=\u001b[39m initial_request_start \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_first_get_size \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m    358\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial_range, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial_offset \u001b[38;5;241m=\u001b[39m process_range_and_offset(\n\u001b[1;32m    359\u001b[0m     initial_request_start,\n\u001b[1;32m    360\u001b[0m     initial_request_end,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    363\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_encryption_data\n\u001b[1;32m    364\u001b[0m )\n\u001b[0;32m--> 366\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_initial_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    367\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproperties \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response\u001b[38;5;241m.\u001b[39mproperties\n\u001b[1;32m    368\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproperties\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_download.py:462\u001b[0m, in \u001b[0;36mStorageStreamDownloader._initial_request\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    460\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_file_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m    461\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 462\u001b[0m         \u001b[43mprocess_storage_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    464\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    465\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msize \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_shared/response_handlers.py:184\u001b[0m, in \u001b[0;36mprocess_storage_error\u001b[0;34m(storage_error)\u001b[0m\n\u001b[1;32m    181\u001b[0m error\u001b[38;5;241m.\u001b[39margs \u001b[38;5;241m=\u001b[39m (error\u001b[38;5;241m.\u001b[39mmessage,)\n\u001b[1;32m    182\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    183\u001b[0m     \u001b[38;5;66;03m# `from None` prevents us from double printing the exception (suppresses generated layer error context)\u001b[39;00m\n\u001b[0;32m--> 184\u001b[0m     \u001b[43mexec\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mraise error from None\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m   \u001b[38;5;66;03m# pylint: disable=exec-used # nosec\u001b[39;00m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mSyntaxError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    186\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m error \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mexc\u001b[39;00m\n", "File \u001b[0;32m<string>:1\u001b[0m\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_download.py:414\u001b[0m, in \u001b[0;36mStorageStreamDownloader._initial_request\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    412\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m retry_active:\n\u001b[1;32m    413\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 414\u001b[0m         location_mode, response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_clients\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mblob\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdownload\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    415\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mrange\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrange_header\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrange_get_content_md5\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrange_validation\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    417\u001b[0m \u001b[43m            \u001b[49m\u001b[43mvalidate_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    418\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdata_stream_total\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    419\u001b[0m \u001b[43m            \u001b[49m\u001b[43mdownload_stream_current\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    420\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request_options\u001b[49m\n\u001b[1;32m    421\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    423\u001b[0m         \u001b[38;5;66;03m# Check the location we read from to ensure we use the same one\u001b[39;00m\n\u001b[1;32m    424\u001b[0m         \u001b[38;5;66;03m# for subsequent requests.\u001b[39;00m\n\u001b[1;32m    425\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_location_mode \u001b[38;5;241m=\u001b[39m location_mode\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py:105\u001b[0m, in \u001b[0;36mdistributed_trace.<locals>.decorator.<locals>.wrapper_use_tracer\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m span_impl_type \u001b[38;5;241m=\u001b[39m settings\u001b[38;5;241m.\u001b[39mtracing_implementation()\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m span_impl_type \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 105\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m \u001b[38;5;66;03m# Merge span is parameter is set, but only if no explicit parent are passed\u001b[39;00m\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m merge_span \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m passed_in_parent:\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/storage/blob/_generated/operations/_blob_operations.py:1611\u001b[0m, in \u001b[0;36mBlobOperations.download\u001b[0;34m(self, snapshot, version_id, timeout, range, range_get_content_md5, range_get_content_crc64, request_id_parameter, lease_access_conditions, cpk_info, modified_access_conditions, **kwargs)\u001b[0m\n\u001b[1;32m   1608\u001b[0m response \u001b[38;5;241m=\u001b[39m pipeline_response\u001b[38;5;241m.\u001b[39mhttp_response\n\u001b[1;32m   1610\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;241m200\u001b[39m, \u001b[38;5;241m206\u001b[39m]:\n\u001b[0;32m-> 1611\u001b[0m     \u001b[43mmap_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstatus_code\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstatus_code\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_map\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1612\u001b[0m     error \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_deserialize\u001b[38;5;241m.\u001b[39mfailsafe_deserialize(_models\u001b[38;5;241m.\u001b[39mStorageError, pipeline_response)\n\u001b[1;32m   1613\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HttpResponseError(response\u001b[38;5;241m=\u001b[39mresponse, model\u001b[38;5;241m=\u001b[39merror)\n", "File \u001b[0;32m/usr/local/lib/python3.10/site-packages/azure/core/exceptions.py:163\u001b[0m, in \u001b[0;36mmap_error\u001b[0;34m(status_code, response, error_map)\u001b[0m\n\u001b[1;32m    161\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    162\u001b[0m error \u001b[38;5;241m=\u001b[39m error_type(response\u001b[38;5;241m=\u001b[39mresponse)\n\u001b[0;32m--> 163\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m error\n", "\u001b[0;31mResourceNotFoundError\u001b[0m: The specified container does not exist.\nRequestId:435f3d65-a01e-004d-54b9-9725c8000000\nTime:2025-03-18T03:56:35.3839466Z\nErrorCode:ContainerNotFound\nContent: <?xml version=\"1.0\" encoding=\"utf-8\"?><Error><Code>ContainerNotFound</Code><Message>The specified container does not exist.\nRequestId:435f3d65-a01e-004d-54b9-9725c8000000\nTime:2025-03-18T03:56:35.3839466Z</Message></Error>"]}], "source": ["# define credentials\n", "import pandas as pd\n", "import pyodbc\n", "import sqlalchemy as sa\n", "import urllib.parse\n", "from sqlalchemy.sql import text\n", "import numpy as np\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from datetime import datetime, timedelta\n", "\n", "source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='\n", "\n", "source_account_name = 'p3storageprod'\n", "\n", "sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,\n", "                                     resource_types=ResourceTypes(\n", "                                         service=True, container=True, object=True),\n", "permission=AccountSasPermissions(read=True),\n", "                                     expiry=datetime.utcnow() + <PERSON><PERSON><PERSON>(hours=1))\n", "\n", "source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)\n", "\n", "# Create download function\n", "def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "# Download documents from blob storage to local folder called pdf             \n", "for idx in df.index:\n", "    path =  '/workspaces/OCR_in_house/data/DI_consultation_notes/'+df['DocFile'][idx]\n", "    download(source_blob_service_client,df['DocContainer'].iloc[idx],df['DocFile'].iloc[idx],path)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n", "environ({'HOSTNAME': '32a93bd6c4b3', 'HOME': '/root', 'PYTHONUNBUFFERED': '1', 'GPG_KEY': 'A035C8C19219BA821ECEA86B64E628F8D684696D', 'PYTHON_SHA256': 'aab0950817735172601879872d937c1e4928a57c409ae02369ec3d91dccebe79', 'PYTHONDONTWRITEBYTECODE': '1', 'PATH': '/usr/local/bin:/root/.local/bin:/vscode/vscode-server/bin/linux-x64/ddc367ed5c8936efe395cffeec279b04ffd7db78/bin/remote-cli:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/opt/mssql-tools/bin:/root/.local/bin:/root/.local/bin:/vscode/vscode-server/bin/linux-x64/ddc367ed5c8936efe395cffeec279b04ffd7db78/bin/remote-cli:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/opt/mssql-tools/bin:/root/.local/bin:/root/.local/bin', 'LANG': 'C.UTF-8', 'SHELL': '/bin/bash', 'PYTHON_VERSION': '3.10.15', 'PWD': '/vscode/vscode-server/bin/linux-x64/ddc367ed5c8936efe395cffeec279b04ffd7db78', 'PYTHONPATH': '/workspaces/crispy_framework/src/:/workspaces/crispy_framework/src/', 'VSCODE_CWD': '/vscode/vscode-server/bin/linux-x64/ddc367ed5c8936efe395cffeec279b04ffd7db78', 'VSCODE_NLS_CONFIG': '{\"userLocale\":\"en\",\"osLocale\":\"en\",\"resolvedLanguage\":\"en\",\"defaultMessagesFile\":\"/vscode/vscode-server/bin/linux-x64/ddc367ed5c8936efe395cffeec279b04ffd7db78/out/nls.messages.json\",\"locale\":\"en\",\"availableLanguages\":{}}', 'VSCODE_HANDLES_SIGPIPE': 'true', 'VSCODE_ESM_ENTRYPOINT': 'vs/workbench/api/node/extensionHostProcess', 'VSCODE_HANDLES_UNCAUGHT_ERRORS': 'true', 'REMOTE_CONTAINERS_IPC': '/tmp/vscode-remote-containers-ipc-92d30260-6e27-44e5-bfb9-d1436c6795e5.sock', 'POSH_SHELL': 'bash', 'POSH_SHELL_VERSION': '5.2.15(1)-release', 'POSH_SESSION_ID': 'd3136d36-c775-4192-823e-8259f1df9946', 'OSTYPE': 'linux-gnu', 'CONDA_PROMPT_MODIFIER': 'false', 'REMOTE_CONTAINERS_SOCKETS': '[\"/tmp/vscode-ssh-auth-92d30260-6e27-44e5-bfb9-d1436c6795e5.sock\",\"/tmp/.X11-unix/X23\",\"/root/.gnupg/S.gpg-agent\"]', 'SHLVL': '1', 'POSH_THEME': '/root/.cache/oh-my-posh/config.7011ebf1f6f276e50d24850df5822b1cc22fd56a7b4c642ede1fcc18f3391714.omp.json', 'POWERLINE_COMMAND': 'oh-my-posh', '_': '/usr/bin/cat', 'SSH_AUTH_SOCK': '/tmp/vscode-ssh-auth-92d30260-6e27-44e5-bfb9-d1436c6795e5.sock', 'DISPLAY': ':23', 'REMOTE_CONTAINERS_DISPLAY_SOCK': '/tmp/.X11-unix/X23', 'REMOTE_CONTAINERS': 'true', 'BROWSER': '/vscode/vscode-server/bin/linux-x64/ddc367ed5c8936efe395cffeec279b04ffd7db78/bin/helpers/browser.sh', 'ELECTRON_RUN_AS_NODE': '1', 'VSCODE_IPC_HOOK_CLI': '/tmp/vscode-ipc-502eb89e-fd6a-4aa1-a6d2-ce5529dd79e2.sock', 'VSCODE_L10N_BUNDLE_LOCATION': '', 'PYDEVD_IPYTHON_COMPATIBLE_DEBUGGING': '1', 'PYTHONIOENCODING': 'utf-8', 'PYTHON_FROZEN_MODULES': 'on', 'PYDEVD_USE_FRAME_EVAL': 'NO', 'TERM': 'xterm-color', 'CLICOLOR': '1', 'FORCE_COLOR': '1', 'CLICOLOR_FORCE': '1', 'PAGER': 'cat', 'GIT_PAGER': 'cat', 'MPLBACKEND': 'module://matplotlib_inline.backend_inline', 'CUSTOM_DEVICE_ROOT': '', 'OMP_NUM_THREADS': '1', 'QT_QPA_PLATFORM_PLUGIN_PATH': '/usr/local/lib/python3.10/site-packages/cv2/qt/plugins', 'QT_QPA_FONTDIR': '/usr/local/lib/python3.10/site-packages/cv2/qt/fonts', 'LD_LIBRARY_PATH': '/usr/local/lib/python3.10/site-packages/cv2/../../lib64:', 'FLAGS_allocator_strategy': 'auto_growth', 'KMP_DUPLICATE_LIB_OK': 'TRUE', 'AZURE_DOCUMENT_INTELLIGENCE_ENDPONT': 'https://document-intellience-exploration.cognitiveservices.azure.com/', 'AZURE_DOCUMENT_INTELLIGENCE_KEY': 'a2359fd15a484809b4da8bc57f367afd'})\n"]}], "source": ["print(os.getenv(\"AZURE_OCR_BLOB_STORAGE_ENDPONT\"))  # Check the specific variable\n", "print(os.environ)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pre-Process Files for Document Intelligence"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import os\n", "from typing import List, Dict\n", "import fitz\n", "from fitz import FileDataError\n", "import glob\n", "from loguru import logger\n", "from shutil import copy2\n", "\n", "def split_pdf_file(file_path: str, target_folder_path: str) -> List[str]:\n", "    assert Path(file_path).exists and Path(file_path).is_file and Path(file_path).suffix.lower() == \".pdf\"\n", "    assert Path(target_folder_path).is_dir\n", "    if not Path(target_folder_path).exists():\n", "        logger.info(f\"target folder {target_folder_path} created\")\n", "        os.makedirs(target_folder_path)\n", "\n", "    file_stem = str(Path(file_path).stem)\n", "    try:\n", "        pdf_document = fitz.open(file_path)\n", "        num_page = pdf_document.page_count\n", "    except FileDataError as e:\n", "        logger.exception(e)\n", "        return []\n", "\n", "    output_file_name_list = []\n", "    for i in range(num_page):\n", "        target_file_path = os.path.join(target_folder_path, f\"{file_stem}-{i}.pdf\")\n", "\n", "        output_pdf = fitz.open()\n", "        output_pdf.insert_pdf(pdf_document, from_page=i, to_page=i)\n", "        output_pdf.save(target_file_path)\n", "        output_pdf.close()\n", "        output_file_name_list.append(target_file_path)\n", "\n", "    pdf_document.close()\n", "    return output_file_name_list\n", "\n", "def batch_split_pdf_files(source_folder_path: str, target_folder_path: str) -> List[str]:\n", "    assert Path(source_folder_path).is_dir and Path(source_folder_path).exists\n", "    assert Path(target_folder_path).is_dir\n", "    if not Path(target_folder_path).exists():\n", "        os.makedirs(target_folder_path)\n", "\n", "    output_file_name_list = []\n", "\n", "    for file_path in sorted(os.listdir(source_folder_path)):\n", "        suffix = str(Path(file_path).suffix)\n", "\n", "        if suffix.lower() == \".pdf\":\n", "            logger.info(f\"PDF file {file_path} get split.\")\n", "            pdf_output_file_name_list = split_pdf_file(os.path.join(source_folder_path,file_path), target_folder_path)\n", "            output_file_name_list.extend(pdf_output_file_name_list)\n", "        elif suffix.lower() in [\".jpeg\", \".jpg\", \".png\", \".bmp\", \".tiff\", \".heif\"]:\n", "            logger.info(f\"Image file {file_path} add to the output file list\")\n", "            copy2(os.path.join(source_folder_path,file_path), os.path.join(target_folder_path, file_path))\n", "            output_file_name_list.append(file_path)\n", "        else:\n", "            logger.exception(f\"file {file_path} {suffix} gets IGNORED due to unacceptable file type.\")\n", "    return output_file_name_list\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-10 02:26:51.554\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 00dcaff3-051f-4064-ae19-254264e8e242.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.559\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 08b8af75-483d-4160-9fba-f75344f4ce4f.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.564\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.565\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0ca22e7d-42bb-45b5-8ba0-212faba169ad.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.567\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.576\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.579\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 1085fe16-0014-489d-bfab-6d005298074a.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.581\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 10ec75dd-e526-444f-895b-7faac9c0cd93.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.583\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.588\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.592\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 204231ce-913e-405c-9132-20cfd100a14b.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.598\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 2836a967-2f4c-4655-bf35-c148e26d9f07.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.601\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.604\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 29eb8c55-14df-4a91-b2bb-9bc29ab69a63.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.607\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 2e5efa44-3660-40df-9701-245d015f3771.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.609\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.611\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 30d78167-a823-44da-a272-a5b36acc66aa.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.614\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 37d281d0-903c-482d-aec6-a603544de1d6.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.619\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.621\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.623\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.626\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 4234dfb8-5e66-452a-88ba-d520698fb608.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.627\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 426dd3f2-6ad7-4a93-a801-71647893c19d.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.630\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 42c562c8-ad0a-4119-9b2a-a98702470659.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.632\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 452156dd-abc3-4c1d-b31c-fde48aadf60c.PDF get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.634\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 4626aa97-e040-494f-9896-0469eeffd02f.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 46b70eea-f82b-4f9e-b453-9fbdc0104549.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.637\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 474466f5-70ca-4c63-83f8-bae13e06e70e.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.642\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 48d16278-7a91-4d47-b916-0b1360d784ab.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.644\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 4c03cf6c-0701-434b-a779-399d45ce051d.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.648\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 4c52e8fd-f13c-4a77-961a-7e69294d66e4.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.651\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.652\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.656\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 519d1e33-094f-4c75-a2a4-2aebac4455aa.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.657\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 54addd43-e63e-495d-9b59-fcd34768a072.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.659\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.662\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 56210846-cdf7-4cc4-9489-56e5946d27a4.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.666\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.667\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.668\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.670\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.673\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.677\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.680\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 70f5599a-60dd-4b2c-9f53-ecf07cc714a5.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.686\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.689\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 75d665c1-a17c-4882-9804-7574b221dd21.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.692\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.692\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 7f6f0d3e-6892-497f-b571-57c5542830a4.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.694\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.696\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 811bf137-b832-4774-a0ba-42314e20fae2.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.698\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 89926c19-088e-49e8-9bf7-3dc947307dad.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.700\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 8d3e1857-b6c2-4b94-b757-798cf906675b.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.703\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.705\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 90683a89-d579-4d61-962c-0625189d0f5b.PDF get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.707\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 91df8902-e51d-4fe3-be8b-6af88d9e6a42.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.709\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.712\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 9356b36f-4e13-4733-9c77-dc8555add32e.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.714\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 99bababe-a109-413f-90e4-608f1eb9e293.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.716\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.719\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.723\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.724\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.726\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.731\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file a85e2454-2d4a-4813-bd81-1af1c54dcc9a.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.733\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file b1f83929-9b81-402f-8b37-13065b355db1.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.738\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.742\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file b514aa28-d6db-4360-87f9-0554233f4a86.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.744\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.747\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.748\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file c607919d-3678-491f-823f-d6c75818c995.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.753\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file c6282f11-1e0f-4213-9d54-9a5138f72441.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.754\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.755\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file cdc164de-ca0a-4f3d-9bde-3552f06ae74a.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.758\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.760\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.764\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d0be39b5-e383-4ffa-9a15-901af56e5baa.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.766\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file d0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.770\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d444e324-a0cd-4949-aac8-311cb8b591e7.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.772\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d757112f-9c32-4a9b-81e7-140f1d30e717.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.777\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file d80c66ae-b17d-4c47-840d-da293939f843.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.781\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d9b8c26d-6e6d-48d3-a763-f8f45fd91477.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.784\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.788\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.791\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.794\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.797\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.801\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file e4f69236-845c-4fcb-abf9-9df336b0478f.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.802\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.805\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file eb362ff0-c953-4049-9810-0bfbffc1772c.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.809\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.812\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file ed611268-36c2-4c56-bee4-26f1005ddca2.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.814\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file f13c4fa7-0df6-4b48-816a-e11e55d5e9f0.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.816\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.824\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file f56eb768-41d9-4cdd-bbe6-4969cc51ed86.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.827\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.828\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.832\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file fa6f85ee-cd21-4eef-8805-939fbcd36712.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.834\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.838\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file fbae41f6-4864-419e-bed0-944c831570f2.pdf get split.\u001b[0m\n"]}, {"data": {"text/plain": ["['00dcaff3-051f-4064-ae19-254264e8e242.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/08b8af75-483d-4160-9fba-f75344f4ce4f-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/08b8af75-483d-4160-9fba-f75344f4ce4f-1.pdf',\n", " '0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg',\n", " '0ca22e7d-42bb-45b5-8ba0-212faba169ad.png',\n", " '0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg',\n", " '0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/1085fe16-0014-489d-bfab-6d005298074a-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/1085fe16-0014-489d-bfab-6d005298074a-1.pdf',\n", " '10ec75dd-e526-444f-895b-7faac9c0cd93.jpg',\n", " '145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg',\n", " '20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg',\n", " '204231ce-913e-405c-9132-20cfd100a14b.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/2836a967-2f4c-4655-bf35-c148e26d9f07-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/2e5efa44-3660-40df-9701-245d015f3771-0.pdf',\n", " '309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/30d78167-a823-44da-a272-a5b36acc66aa-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/37d281d0-903c-482d-aec6-a603544de1d6-0.pdf',\n", " '392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.pdf',\n", " '3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg',\n", " '4234dfb8-5e66-452a-88ba-d520698fb608.jpg',\n", " '426dd3f2-6ad7-4a93-a801-71647893c19d.jpg',\n", " '42c562c8-ad0a-4119-9b2a-a98702470659.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/452156dd-abc3-4c1d-b31c-fde48aadf60c-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4626aa97-e040-494f-9896-0469eeffd02f-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4626aa97-e040-494f-9896-0469eeffd02f-1.pdf',\n", " '46b70eea-f82b-4f9e-b453-9fbdc0104549.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/474466f5-70ca-4c63-83f8-bae13e06e70e-0.pdf',\n", " '48d16278-7a91-4d47-b916-0b1360d784ab.jpeg',\n", " '4c03cf6c-0701-434b-a779-399d45ce051d.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.pdf',\n", " '4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png',\n", " '50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg',\n", " '519d1e33-094f-4c75-a2a4-2aebac4455aa.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/54addd43-e63e-495d-9b59-fcd34768a072-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.pdf',\n", " '56210846-cdf7-4cc4-9489-56e5946d27a4.jpg',\n", " '5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg',\n", " '65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png',\n", " '67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.pdf',\n", " '6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg',\n", " '6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.pdf',\n", " '728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg',\n", " '75d665c1-a17c-4882-9804-7574b221dd21.png',\n", " '79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png',\n", " '7f6f0d3e-6892-497f-b571-57c5542830a4.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/811bf137-b832-4774-a0ba-42314e20fae2-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/89926c19-088e-49e8-9bf7-3dc947307dad-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/8d3e1857-b6c2-4b94-b757-798cf906675b-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/90683a89-d579-4d61-962c-0625189d0f5b-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/9356b36f-4e13-4733-9c77-dc8555add32e-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/99bababe-a109-413f-90e4-608f1eb9e293-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.pdf',\n", " 'a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg',\n", " 'a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg',\n", " 'a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg',\n", " 'a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-1.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-2.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-3.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-4.pdf',\n", " 'b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b514aa28-d6db-4360-87f9-0554233f4a86-0.pdf',\n", " 'b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg',\n", " 'ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png',\n", " 'bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png',\n", " 'c607919d-3678-491f-823f-d6c75818c995.png',\n", " 'c6282f11-1e0f-4213-9d54-9a5138f72441.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.pdf',\n", " 'ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg',\n", " 'cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d0be39b5-e383-4ffa-9a15-901af56e5baa-0.pdf',\n", " 'd0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d444e324-a0cd-4949-aac8-311cb8b591e7-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d757112f-9c32-4a9b-81e7-140f1d30e717-0.pdf',\n", " 'd80c66ae-b17d-4c47-840d-da293939f843.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.pdf',\n", " 'dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg',\n", " 'e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg',\n", " 'e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg',\n", " 'e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg',\n", " 'e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/e4f69236-845c-4fcb-abf9-9df336b0478f-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.pdf',\n", " 'eb362ff0-c953-4049-9810-0bfbffc1772c.jpg',\n", " 'ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/ed611268-36c2-4c56-bee4-26f1005ddca2-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.pdf',\n", " 'f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.pdf',\n", " 'f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg',\n", " 'f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/fa6f85ee-cd21-4eef-8805-939fbcd36712-0.pdf',\n", " 'fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/fbae41f6-4864-419e-bed0-944c831570f2-0.pdf']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["batch_split_pdf_files(\"../../data/OCR_in_house/samples/100_samples_DI/\", \"../../data/OCR_in_house/samples/100_samples_DI_split_pages/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# test api connection to azure document intelligence"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# import libraries\n", "import os\n", "from dotenv import load_dotenv\n", "from loguru import logger\n", "\n", "load_dotenv()\n", "from azure.ai.formrecognizer import DocumentAnalysisClient\n", "from azure.core.credentials import AzureKeyCredential\n", "from azure.core.exceptions import HttpResponseError\n", "\n", "# set `<your-endpoint>` and `<your-key>` variables with the values from the Azure portal\n", "endpoint = os.getenv(\"AZURE_DOCUMENT_INTELLIGENCE_ENDPONT\")\n", "key = os.getenv(\"AZURE_DOCUMENT_INTELLIGENCE_KEY\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["document_analysis_client = DocumentAnalysisClient(\n", "        endpoint=endpoint, credential=AzureKeyCredential(key)\n", "    )"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import glob\n", "import time\n", "\n", "\n", "DATA_FOLDER = \"../../data/OCR_in_house/samples/100_samples_DI_split_pages/\"\n", "MAX_RETRY = 3\n", "\n", "ans = []"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["doc_path_list = sorted(os.listdir(DATA_FOLDER))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["'../../data/OCR_in_house/samples/100_samples_DI_split_pages/00dcaff3-051f-4064-ae19-254264e8e242.jpeg'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["file_path = os.path.join(DATA_FOLDER ,doc_path_list[0])\n", "file_path"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["with open(file_path, \"rb\") as f:\n", "    poller = document_analysis_client.begin_analyze_document(model_id=\"prebuilt-invoice\", document=f)\n", "    invoices = poller.result()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["ans.append({\n", "        \"file_path\": file_path,\n", "        \"invoice\": invoices.to_dict(),\n", "        })"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ans)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUPUT_DATA_FOLDER = \"../../data/OCR_in_house/samples/100_samples_DI_res/\"\n", "if not Path(OUPUT_DATA_FOLDER).exists():\n", "    os.makedirs(OUPUT_DATA_FOLDER)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-11 02:53:42.201\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 00dcaff3-051f-4064-ae19-254264e8e242.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:53:52.917\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 00dcaff3-051f-4064-ae19-254264e8e242.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:53:52.918\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 08b8af75-483d-4160-9fba-f75344f4ce4f-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:54:03.206\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 08b8af75-483d-4160-9fba-f75344f4ce4f-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:54:03.209\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 08b8af75-483d-4160-9fba-f75344f4ce4f-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:54:13.494\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 08b8af75-483d-4160-9fba-f75344f4ce4f-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:54:13.495\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:54:23.808\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:54:23.809\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 0ca22e7d-42bb-45b5-8ba0-212faba169ad.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:54:34.082\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 0ca22e7d-42bb-45b5-8ba0-212faba169ad.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:54:34.083\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:54:44.653\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:54:44.655\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:54:54.895\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:54:54.896\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 1085fe16-0014-489d-bfab-6d005298074a-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:05.251\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 1085fe16-0014-489d-bfab-6d005298074a-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:55:05.252\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 1085fe16-0014-489d-bfab-6d005298074a-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:15.501\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 1085fe16-0014-489d-bfab-6d005298074a-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:55:15.502\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 10ec75dd-e526-444f-895b-7faac9c0cd93.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:25.840\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 10ec75dd-e526-444f-895b-7faac9c0cd93.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:55:25.841\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:36.404\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:55:36.405\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:46.701\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:55:46.702\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 204231ce-913e-405c-9132-20cfd100a14b.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:46.821\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/204231ce-913e-405c-9132-20cfd100a14b.jpeg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 02:55:51.887\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 204231ce-913e-405c-9132-20cfd100a14b.jpeg... Retried 1 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:51.972\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/204231ce-913e-405c-9132-20cfd100a14b.jpeg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 02:55:57.012\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 204231ce-913e-405c-9132-20cfd100a14b.jpeg... Retried 2 times\u001b[0m\n", "\u001b[32m2024-10-11 02:55:57.115\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/204231ce-913e-405c-9132-20cfd100a14b.jpeg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 02:56:02.158\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m44\u001b[0m - \u001b[31m\u001b[1mfile 204231ce-913e-405c-9132-20cfd100a14b.jpeg processed FAIL\u001b[0m\n", "\u001b[31m\u001b[1mNoneType\u001b[0m:\u001b[1m None\u001b[0m\n", "\u001b[32m2024-10-11 02:56:02.159\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2836a967-2f4c-4655-bf35-c148e26d9f07-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:56:12.429\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2836a967-2f4c-4655-bf35-c148e26d9f07-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:56:12.430\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:56:22.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:56:22.637\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:56:32.929\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:56:32.930\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:56:49.574\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:56:49.575\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 2e5efa44-3660-40df-9701-245d015f3771-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:57:03.005\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 2e5efa44-3660-40df-9701-245d015f3771-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:57:03.006\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:57:18.359\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:57:18.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 30d78167-a823-44da-a272-a5b36acc66aa-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:57:28.617\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 30d78167-a823-44da-a272-a5b36acc66aa-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:57:28.618\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 37d281d0-903c-482d-aec6-a603544de1d6-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:57:38.884\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 37d281d0-903c-482d-aec6-a603544de1d6-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:57:38.885\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:57:49.146\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:57:49.147\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:57:59.393\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:57:59.394\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:58:09.787\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:58:09.788\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4234dfb8-5e66-452a-88ba-d520698fb608.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:58:20.025\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4234dfb8-5e66-452a-88ba-d520698fb608.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:58:20.026\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 426dd3f2-6ad7-4a93-a801-71647893c19d.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:58:30.528\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 426dd3f2-6ad7-4a93-a801-71647893c19d.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:58:30.529\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 42c562c8-ad0a-4119-9b2a-a98702470659.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:58:40.855\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 42c562c8-ad0a-4119-9b2a-a98702470659.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:58:40.856\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 452156dd-abc3-4c1d-b31c-fde48aadf60c-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:58:51.074\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 452156dd-abc3-4c1d-b31c-fde48aadf60c-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:58:51.075\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4626aa97-e040-494f-9896-0469eeffd02f-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:01.324\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4626aa97-e040-494f-9896-0469eeffd02f-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:59:01.325\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4626aa97-e040-494f-9896-0469eeffd02f-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:11.631\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4626aa97-e040-494f-9896-0469eeffd02f-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:59:11.632\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 46b70eea-f82b-4f9e-b453-9fbdc0104549.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:21.864\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 46b70eea-f82b-4f9e-b453-9fbdc0104549.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:59:21.865\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 474466f5-70ca-4c63-83f8-bae13e06e70e-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:32.193\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 474466f5-70ca-4c63-83f8-bae13e06e70e-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:59:32.194\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 48d16278-7a91-4d47-b916-0b1360d784ab.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:42.660\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 48d16278-7a91-4d47-b916-0b1360d784ab.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 02:59:42.661\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4c03cf6c-0701-434b-a779-399d45ce051d.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:42.778\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c03cf6c-0701-434b-a779-399d45ce051d.jpg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 02:59:47.873\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4c03cf6c-0701-434b-a779-399d45ce051d.jpg... Retried 1 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:47.959\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c03cf6c-0701-434b-a779-399d45ce051d.jpg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 02:59:53.047\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4c03cf6c-0701-434b-a779-399d45ce051d.jpg... Retried 2 times\u001b[0m\n", "\u001b[32m2024-10-11 02:59:53.148\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c03cf6c-0701-434b-a779-399d45ce051d.jpg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 02:59:58.244\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m44\u001b[0m - \u001b[31m\u001b[1mfile 4c03cf6c-0701-434b-a779-399d45ce051d.jpg processed FAIL\u001b[0m\n", "\u001b[31m\u001b[1mNoneType\u001b[0m:\u001b[1m None\u001b[0m\n", "\u001b[32m2024-10-11 02:59:58.245\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:00:08.482\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:00:08.483\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:00:18.769\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:00:18.770\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:00:29.039\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:00:29.040\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:00:39.521\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:00:39.522\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 519d1e33-094f-4c75-a2a4-2aebac4455aa.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:00:49.892\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 519d1e33-094f-4c75-a2a4-2aebac4455aa.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:00:49.894\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 54addd43-e63e-495d-9b59-fcd34768a072-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:01:00.132\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 54addd43-e63e-495d-9b59-fcd34768a072-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:01:00.133\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:01:12.633\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:01:12.634\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:01:22.820\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:01:22.821\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 56210846-cdf7-4cc4-9489-56e5946d27a4.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:01:33.297\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 56210846-cdf7-4cc4-9489-56e5946d27a4.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:01:33.298\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:01:43.612\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:01:43.613\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:01:54.063\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:01:54.064\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:02:04.502\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:02:04.503\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:02:14.751\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:02:14.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:02:30.486\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:02:30.487\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:02:38.954\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:02:38.955\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:02:49.265\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:02:49.266\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:02:59.482\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:02:59.483\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:03:09.929\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:03:09.931\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 75d665c1-a17c-4882-9804-7574b221dd21.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:03:20.299\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 75d665c1-a17c-4882-9804-7574b221dd21.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:03:20.300\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:03:30.559\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:03:30.560\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7f6f0d3e-6892-497f-b571-57c5542830a4.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:03:40.789\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7f6f0d3e-6892-497f-b571-57c5542830a4.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:03:40.790\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:03:51.032\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:03:51.033\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 811bf137-b832-4774-a0ba-42314e20fae2-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:04:01.283\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 811bf137-b832-4774-a0ba-42314e20fae2-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:04:01.284\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 89926c19-088e-49e8-9bf7-3dc947307dad-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:04:11.558\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 89926c19-088e-49e8-9bf7-3dc947307dad-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:04:11.559\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8d3e1857-b6c2-4b94-b757-798cf906675b-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:04:21.802\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8d3e1857-b6c2-4b94-b757-798cf906675b-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:04:21.803\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:04:35.114\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:04:35.119\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 90683a89-d579-4d61-962c-0625189d0f5b-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:04:47.407\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 90683a89-d579-4d61-962c-0625189d0f5b-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:04:47.408\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:04:59.704\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:04:59.705\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:05:09.946\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:05:09.947\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:05:20.121\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:05:20.121\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9356b36f-4e13-4733-9c77-dc8555add32e-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:05:30.493\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9356b36f-4e13-4733-9c77-dc8555add32e-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:05:30.494\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 99bababe-a109-413f-90e4-608f1eb9e293-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:05:40.764\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 99bababe-a109-413f-90e4-608f1eb9e293-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:05:40.765\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:05:51.028\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:05:51.029\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:06:01.492\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:06:01.493\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:06:11.750\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:06:11.751\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:06:22.082\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:06:22.083\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:06:32.622\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:06:32.622\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:06:42.856\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:06:42.856\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b1f83929-9b81-402f-8b37-13065b355db1-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:06:53.090\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b1f83929-9b81-402f-8b37-13065b355db1-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:06:53.091\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b1f83929-9b81-402f-8b37-13065b355db1-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:07:03.342\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b1f83929-9b81-402f-8b37-13065b355db1-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:07:03.343\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b1f83929-9b81-402f-8b37-13065b355db1-2.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:07:13.611\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b1f83929-9b81-402f-8b37-13065b355db1-2.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:07:13.612\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b1f83929-9b81-402f-8b37-13065b355db1-3.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:07:23.941\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b1f83929-9b81-402f-8b37-13065b355db1-3.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:07:23.942\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b1f83929-9b81-402f-8b37-13065b355db1-4.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:07:34.184\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b1f83929-9b81-402f-8b37-13065b355db1-4.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:07:34.185\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:07:44.638\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:07:44.639\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b514aa28-d6db-4360-87f9-0554233f4a86-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:07:54.862\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b514aa28-d6db-4360-87f9-0554233f4a86-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:07:54.863\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:08:05.325\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:08:05.326\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:08:15.594\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:08:15.595\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:08:25.921\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:08:25.922\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c607919d-3678-491f-823f-d6c75818c995.png... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:08:36.169\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c607919d-3678-491f-823f-d6c75818c995.png processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:08:36.171\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c6282f11-1e0f-4213-9d54-9a5138f72441.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:08:46.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c6282f11-1e0f-4213-9d54-9a5138f72441.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:08:46.419\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:08:56.644\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:08:56.645\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:09:08.975\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:09:08.976\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:09:19.166\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:09:19.167\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:09:29.549\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:09:29.550\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:09:40.200\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:09:40.201\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d0be39b5-e383-4ffa-9a15-901af56e5baa-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:09:50.440\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d0be39b5-e383-4ffa-9a15-901af56e5baa-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:09:50.441\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:10:00.972\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:10:00.974\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d444e324-a0cd-4949-aac8-311cb8b591e7-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:10:11.219\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d444e324-a0cd-4949-aac8-311cb8b591e7-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:10:11.220\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d757112f-9c32-4a9b-81e7-140f1d30e717-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:10:21.516\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d757112f-9c32-4a9b-81e7-140f1d30e717-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:10:21.517\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d80c66ae-b17d-4c47-840d-da293939f843.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:10:34.102\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d80c66ae-b17d-4c47-840d-da293939f843.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:10:34.104\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:10:44.346\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:10:44.347\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:10:54.573\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:10:54.575\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:11:05.120\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:11:05.121\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:11:15.500\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:11:15.501\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:11:25.936\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:11:25.937\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:11:36.330\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:11:36.331\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:11:46.851\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:11:46.852\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing e4f69236-845c-4fcb-abf9-9df336b0478f-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:11:57.113\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile e4f69236-845c-4fcb-abf9-9df336b0478f-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:11:57.114\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:07.444\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:12:07.445\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing eb362ff0-c953-4049-9810-0bfbffc1772c.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:17.938\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile eb362ff0-c953-4049-9810-0bfbffc1772c.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:12:17.939\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:28.358\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:12:28.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing ed611268-36c2-4c56-bee4-26f1005ddca2-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:38.635\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile ed611268-36c2-4c56-bee4-26f1005ddca2-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:12:38.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:48.879\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:12:48.881\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:48.987\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 03:12:54.224\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg... Retried 1 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:54.336\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 03:12:59.571\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg... Retried 2 times\u001b[0m\n", "\u001b[32m2024-10-11 03:12:59.660\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m32\u001b[0m - \u001b[31m\u001b[1m(InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7b25628ad3f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7b2562a75000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7b25602c5ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7b25601197e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7b25629dcdc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7b2561994310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7b256010a950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7b2561995e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7b2561b757e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...4B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7b2560280280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.Frame(b'782f506d-6ce'...36B)>, <zmq.Frame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'e658d2f5cf70'...64B)>, <zmq.Frame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7b256010af20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7b25575987b0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 11, 2, 53, 42, 193000, tzinfo=tzutc()), 'msg_id': '5f76e756-9e2c-4a5e-9a36-3e...\n", "                                  │       └ [b'782f506d-6cea-4eec-a255-2635873429c4']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7b256010a680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7b2557599000>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7b25602af0a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('import json\\nimport pickle as pk\\n\\nfor file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\\n    retry_num = 0\\n\\n    ...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7b2560dddea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "             └ <function _pseudo_sync_runner at 0x7b2560dc97e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7b2557598f90>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_49060/3387578015.py'\n", "                       │    │             │        └ [<ast.Import object at 0x7b25575b8a30>, <ast.Import object at 0x7b25575b8b20>, <ast.For object at 0x7b25575b8bb0>]\n", "                       │    │             └ <ast.Module object at 0x7b25575b8970>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7b2560dde170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7b255d9d15a0, execution_count=7 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7b2560dde200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         │         │    └ <property object at 0x7b2560dd09a0>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7b255d994070>\n", "         └ <code object <module> at 0x7b256287b3c0, file \"/tmp/ipykernel_49060/3387578015.py\", line 1>\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_49060/\u001b[0m\u001b[32m\u001b[1m3387578015.py\u001b[0m\", line \u001b[33m12\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mpoller\u001b[0m \u001b[35m\u001b[1m=\u001b[0m \u001b[1mdocument_analysis_client\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mbegin_analyze_document\u001b[0m\u001b[1m(\u001b[0m\u001b[1mmodel_id\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[36m\"prebuilt-invoice\"\u001b[0m\u001b[1m,\u001b[0m \u001b[1mdocument\u001b[0m\u001b[35m\u001b[1m=\u001b[0m\u001b[1mf\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m         │                        │                                                            └ \u001b[0m\u001b[36m\u001b[1m<_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_pages/f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg'>\u001b[0m\n", "    \u001b[36m         │                        └ \u001b[0m\u001b[36m\u001b[1m<function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f5b0>\u001b[0m\n", "    \u001b[36m         └ \u001b[0m\u001b[36m\u001b[1m<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'document': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_split_p...\n", "           │     └ (<azure.ai.formrecognizer._document_analysis_client.DocumentAnalysisClient object at 0x7b255d9d3dc0>,)\n", "           └ <function DocumentAnalysisClient.begin_analyze_document at 0x7b255758f490>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_document_analysis_client.py\", line 129, in begin_analyze_document\n", "    return _client_op_path.begin_analyze_document(  # type: ignore\n", "           │               └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94b80>\n", "           └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/core/tracing/decorator.py\", line 94, in wrapper_use_tracer\n", "    return func(*args, **kwargs)\n", "           │     │       └ {'model_id': 'prebuilt-invoice', 'analyze_request': <_io.BufferedReader name='../../data/OCR_in_house/samples/100_samples_DI_...\n", "           │     └ (<azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x...\n", "           └ <function DocumentModelsOperations.begin_analyze_document at 0x7b2557f94a60>\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 518, in begin_analyze_document\n", "    raw_result = self._analyze_document_initial(  # type: ignore\n", "                 │    └ <function DocumentModelsOperations._analyze_document_initial at 0x7b2557f949d0>\n", "                 └ <azure.ai.formrecognizer._generated.v2023_07_31.operations._document_models_operations.DocumentModelsOperations object at 0x7...\n", "  File \"/usr/local/lib/python3.10/site-packages/azure/ai/formrecognizer/_generated/v2023_07_31/operations/_document_models_operations.py\", line 443, in _analyze_document_initial\n", "    raise HttpResponseError(response=response)\n", "          │                          └ <RequestsTransportResponse: 400 Bad Request, Content-Type: application/json; charset=utf-8>\n", "          └ <class 'azure.core.exceptions.HttpResponseError'>\n", "\n", "\u001b[31m\u001b[1mazure.core.exceptions.HttpResponseError\u001b[0m:\u001b[1m (InvalidRequest) Invalid request.\n", "Code: InvalidRequest\n", "Message: Invalid request.\n", "Inner error: {\n", "    \"code\": \"InvalidContentLength\",\n", "    \"message\": \"The input image is too large. Refer to documentation for the maximum file size.\"\n", "}\u001b[0m\n", "\u001b[32m2024-10-11 03:13:04.887\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m44\u001b[0m - \u001b[31m\u001b[1mfile f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg processed FAIL\u001b[0m\n", "\u001b[31m\u001b[1mNoneType\u001b[0m:\u001b[1m None\u001b[0m\n", "\u001b[32m2024-10-11 03:13:04.888\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:13:15.122\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:13:15.123\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:13:25.358\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:13:25.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:13:35.796\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:13:35.797\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fa6f85ee-cd21-4eef-8805-939fbcd36712-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:13:46.002\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fa6f85ee-cd21-4eef-8805-939fbcd36712-0.pdf processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:13:46.003\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:13:58.125\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg processed SUCCESS\u001b[0m\n", "\u001b[32m2024-10-11 03:13:58.126\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m9\u001b[0m - \u001b[1mPrecessing fbae41f6-4864-419e-bed0-944c831570f2-0.pdf... Retried 0 times\u001b[0m\n", "\u001b[32m2024-10-11 03:14:08.369\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m41\u001b[0m - \u001b[1mfile fbae41f6-4864-419e-bed0-944c831570f2-0.pdf processed SUCCESS\u001b[0m\n"]}], "source": ["import json\n", "import pickle as pk\n", "\n", "for file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\n", "    retry_num = 0\n", "\n", "    file_stem = str(Path(file_path).stem)\n", "    while retry_num < MAX_RETRY:\n", "        logger.info(f\"Precessing {file_path}... Retried {retry_num} times\")\n", "        try:\n", "            with open(os.path.join(DATA_FOLDER, file_path), \"rb\") as f:\n", "                poller = document_analysis_client.begin_analyze_document(model_id=\"prebuilt-invoice\", document=f)\n", "                invoices = poller.result()\n", "                invoice_dict = invoices.to_dict()\n", "                ans.append(\n", "                    {\n", "                        \"file_path\": file_path,\n", "                        \"invoice\": invoice_dict,\n", "                        }\n", "                    )\n", "                time.sleep(5)\n", "\n", "                # dumpt to pk file\n", "                with open(os.path.join(OUPUT_DATA_FOLDER, f\"{file_stem}.pk\"), \"wb\") as fout:\n", "                    pk.dump(invoice_dict, fout)\n", "                # dumpt to json file\n", "                with open(os.path.join(OUPUT_DATA_FOLDER, f\"{file_stem}.json\"), \"w\") as fout:\n", "                    json.dump(invoice_dict, fout, indent=4, default=str)\n", "\n", "                break\n", "        except HttpResponseError as hre:\n", "            logger.exception(hre)\n", "            retry_num += 1\n", "            time.sleep(5)\n", "        except Exception as e:\n", "            logger.exception(e)\n", "            break\n", "\n", "    if ans and ans[-1][\"file_path\"] == file_path:\n", "        # process succeed\n", "        logger.info(f\"file {file_path} processed SUCCESS\")\n", "    else:\n", "        # process failed\n", "        logger.exception(f\"file {file_path} processed FAIL\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# dumpt to pk file\n", "with open(os.path.join(OUPUT_DATA_FOLDER, \"ans.pk\"), \"wb\") as fout:\n", "    pk.dump(ans, fout)\n", "# dumpt to json file\n", "with open(os.path.join(OUPUT_DATA_FOLDER, \"ans.json\"), \"w\") as fout:\n", "    json.dump(ans, fout, indent=4, default=str)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# some test result. DO NOT OVERRIDE."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["AnalyzeResult(api_version=2023-07-31, model_id=prebuilt-invoice, content=Whitfords\n", "2 Banks Ave\n", "Hillarys WA 6025\n", "Ph: 08 9404 1133\n", "E: <EMAIL>\n", "Tax Invoice\n", "<PERSON>\n", "Customer ID 172386\n", "21 Aspendale Place\n", "Date\n", "13/07/23\n", "Hillarys WA 6025\n", "Reference\n", "Invoice No\n", "********\n", "Page 1 of 1\n", "Patient\n", "Service Provided\n", "Claimed\n", "Quantity\n", "Amount\n", "<PERSON> (ID:385654)\n", "10/07/23 Consultation- Complex\n", "✓\n", "1\n", "$ 0.00\n", "Flusapex 100ml\n", "✓\n", "1\n", "$ 132.28\n", "You have been given a discount of: $ 156.00\n", "TOTAL\n", "$ 132.28\n", "Total includes GST\n", "$ 12.03\n", "Balance Owing\n", "$ 83.48 :selected: :selected:, languages=[], pages=[DocumentPage(page_number=1, angle=None, width=8.2639, height=11.6944, unit=inch, lines=[DocumentLine(content=Whitfords, polygon=[Point(x=4.9035, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.604), Point(x=4.9035, y=0.5989)], spans=[DocumentSpan(offset=0, length=9)]), DocumentLine(content=2 Banks Ave, polygon=[Point(x=4.9035, y=0.6548), Point(x=5.9746, y=0.6598), Point(x=5.9746, y=0.8223), Point(x=4.9035, y=0.8172)], spans=[DocumentSpan(offset=10, length=11)]), DocumentLine(content=Hillarys WA 6025, polygon=[Point(x=4.9035, y=0.8324), Point(x=6.3857, y=0.8273), Point(x=6.3908, y=1.005), Point(x=4.9035, y=1.0101)], spans=[DocumentSpan(offset=22, length=16)]), DocumentLine(content=Ph: 08 9404 1133, polygon=[Point(x=4.9035, y=1.0253), Point(x=6.2791, y=1.0253), Point(x=6.2791, y=1.1928), Point(x=4.9086, y=1.1928)], spans=[DocumentSpan(offset=39, length=16)]), DocumentLine(content=E: <EMAIL>, polygon=[Point(x=4.9035, y=1.2131), Point(x=7.2487, y=1.2232), Point(x=7.2487, y=1.3806), Point(x=4.9035, y=1.3755)], spans=[DocumentSpan(offset=56, length=27)]), DocumentLine(content=Tax Invoice, polygon=[Point(x=5.6091, y=2.1978), Point(x=7.2182, y=2.1978), Point(x=7.2182, y=2.4313), Point(x=5.6091, y=2.4363)], spans=[DocumentSpan(offset=84, length=11)]), DocumentLine(content=Kevin Jones, polygon=[Point(x=1.3198, y=2.7257), Point(x=2.132, y=2.7307), Point(x=2.132, y=2.883), Point(x=1.3198, y=2.8779)], spans=[DocumentSpan(offset=96, length=11)]), DocumentLine(content=Customer ID 172386, polygon=[Point(x=5.7664, y=2.6851), Point(x=7.3045, y=2.6851), Point(x=7.3045, y=2.8576), Point(x=5.7664, y=2.8576)], spans=[DocumentSpan(offset=108, length=18)]), DocumentLine(content=21 Aspendale Place, polygon=[Point(x=1.2792, y=2.8982), Point(x=2.6548, y=2.8982), Point(x=2.6548, y=3.0607), Point(x=1.2792, y=3.0607)], spans=[DocumentSpan(offset=127, length=18)]), DocumentLine(content=Date, polygon=[Point(x=5.7715, y=2.9033), Point(x=6.1116, y=2.9033), Point(x=6.1116, y=3.0302), Point(x=5.7766, y=3.0302)], spans=[DocumentSpan(offset=146, length=4)]), DocumentLine(content=13/07/23, polygon=[Point(x=6.7614, y=2.8729), Point(x=7.3959, y=2.8779), Point(x=7.3959, y=3.0505), Point(x=6.7614, y=3.0454)], spans=[DocumentSpan(offset=151, length=8)]), DocumentLine(content=Hillarys WA 6025, polygon=[Point(x=1.2792, y=3.0657), Point(x=2.4771, y=3.0607), Point(x=2.4822, y=3.218), Point(x=1.2792, y=3.2231)], spans=[DocumentSpan(offset=160, length=16)]), DocumentLine(content=Reference, polygon=[Point(x=5.7766, y=3.0911), Point(x=6.4822, y=3.0911), Point(x=6.4822, y=3.2282), Point(x=5.7766, y=3.2282)], spans=[DocumentSpan(offset=177, length=9)]), DocumentLine(content=Invoice No, polygon=[Point(x=5.7715, y=3.284), Point(x=6.5076, y=3.284), Point(x=6.5076, y=3.4261), Point(x=5.7715, y=3.4261)], spans=[DocumentSpan(offset=187, length=10)]), DocumentLine(content=********, polygon=[Point(x=6.7614, y=3.284), Point(x=7.4872, y=3.284), Point(x=7.4872, y=3.4261), Point(x=6.7614, y=3.421)], spans=[DocumentSpan(offset=198, length=8)]), DocumentLine(content=Page 1 of 1, polygon=[Point(x=6.7614, y=3.4769), Point(x=7.5583, y=3.4769), Point(x=7.5532, y=3.6342), Point(x=6.7614, y=3.6342)], spans=[DocumentSpan(offset=207, length=11)]), DocumentLine(content=Patient, polygon=[Point(x=0.4518, y=3.9895), Point(x=1.0508, y=3.9895), Point(x=1.0508, y=4.1367), Point(x=0.4518, y=4.1367)], spans=[DocumentSpan(offset=219, length=7)]), DocumentLine(content=Service Provided, polygon=[Point(x=1.4619, y=3.9844), Point(x=2.7969, y=3.9844), Point(x=2.7969, y=4.1367), Point(x=1.4619, y=4.1367)], spans=[DocumentSpan(offset=227, length=16)]), DocumentLine(content=Claimed, polygon=[Point(x=5.2588, y=3.9997), Point(x=5.9035, y=3.9997), Point(x=5.9035, y=4.157), Point(x=5.2588, y=4.1519)], spans=[DocumentSpan(offset=244, length=7)]), DocumentLine(content=Quantity, polygon=[Point(x=6.2233, y=3.9844), Point(x=6.939, y=3.9895), Point(x=6.939, y=4.157), Point(x=6.2233, y=4.1469)], spans=[DocumentSpan(offset=252, length=8)]), DocumentLine(content=Amount, polygon=[Point(x=7.1471, y=3.9895), Point(x=7.7817, y=3.9895), Point(x=7.7817, y=4.1367), Point(x=7.1471, y=4.1367)], spans=[DocumentSpan(offset=261, length=6)]), DocumentLine(content=Freddie (ID:385654), polygon=[Point(x=0.467, y=4.2788), Point(x=2.0863, y=4.2788), Point(x=2.0863, y=4.4463), Point(x=0.467, y=4.4412)], spans=[DocumentSpan(offset=268, length=19)]), DocumentLine(content=10/07/23 Consultation- Complex, polygon=[Point(x=0.6903, y=4.4565), Point(x=3.0507, y=4.4616), Point(x=3.0507, y=4.624), Point(x=0.6903, y=4.6138)], spans=[DocumentSpan(offset=288, length=30)]), DocumentLine(content=✓, polygon=[Point(x=5.5208, y=4.5079), Point(x=5.5926, y=4.5079), Point(x=5.5926, y=4.5832), Point(x=5.5208, y=4.5832)], spans=[DocumentSpan(offset=319, length=1)]), DocumentLine(content=1, polygon=[Point(x=6.4416, y=4.4666), Point(x=6.5228, y=4.4666), Point(x=6.5228, y=4.5986), Point(x=6.4416, y=4.5935)], spans=[DocumentSpan(offset=321, length=1)]), DocumentLine(content=$ 0.00, polygon=[Point(x=7.3147, y=4.4616), Point(x=7.7817, y=4.4616), Point(x=7.7817, y=4.6138), Point(x=7.3147, y=4.6138)], spans=[DocumentSpan(offset=323, length=6)]), DocumentLine(content=Flusapex 100ml, polygon=[Point(x=1.4518, y=4.9082), Point(x=2.5939, y=4.9031), Point(x=2.5939, y=5.0808), Point(x=1.4518, y=5.0859)], spans=[DocumentSpan(offset=330, length=14)]), DocumentLine(content=✓, polygon=[Point(x=5.5208, y=4.9662), Point(x=5.5926, y=4.9662), Point(x=5.5926, y=5.0415), Point(x=5.5208, y=5.0415)], spans=[DocumentSpan(offset=345, length=1)]), DocumentLine(content=1, polygon=[Point(x=6.4466, y=4.9285), Point(x=6.5177, y=4.9234), Point(x=6.5177, y=5.0554), Point(x=6.4517, y=5.0503)], spans=[DocumentSpan(offset=347, length=1)]), DocumentLine(content=$ 132.28, polygon=[Point(x=7.1471, y=4.9234), Point(x=7.7817, y=4.9184), Point(x=7.7817, y=5.0656), Point(x=7.1471, y=5.0706)], spans=[DocumentSpan(offset=349, length=8)]), DocumentLine(content=You have been given a discount of: $ 156.00, polygon=[Point(x=0.4975, y=5.4462), Point(x=3.7106, y=5.4412), Point(x=3.7106, y=5.6137), Point(x=0.4975, y=5.6137)], spans=[DocumentSpan(offset=358, length=43)]), DocumentLine(content=TOTAL, polygon=[Point(x=6.0761, y=5.4462), Point(x=6.5989, y=5.4462), Point(x=6.5989, y=5.5985), Point(x=6.0761, y=5.5934)], spans=[DocumentSpan(offset=402, length=5)]), DocumentLine(content=$ 132.28, polygon=[Point(x=7.0761, y=5.4412), Point(x=7.7715, y=5.4361), Point(x=7.7766, y=5.6087), Point(x=7.0761, y=5.6087)], spans=[DocumentSpan(offset=408, length=8)]), DocumentLine(content=Total includes GST, polygon=[Point(x=5.1116, y=5.634), Point(x=6.6192, y=5.629), Point(x=6.6192, y=5.8066), Point(x=5.1116, y=5.8117)], spans=[DocumentSpan(offset=417, length=18)]), DocumentLine(content=$ 12.03, polygon=[Point(x=7.2284, y=5.6493), Point(x=7.7715, y=5.6493), Point(x=7.7715, y=5.8015), Point(x=7.2284, y=5.8015)], spans=[DocumentSpan(offset=436, length=7)]), DocumentLine(content=Balance Owing, polygon=[Point(x=5.4314, y=5.8472), Point(x=6.604, y=5.8523), Point(x=6.5989, y=6.0198), Point(x=5.4314, y=6.0096)], spans=[DocumentSpan(offset=444, length=13)]), DocumentLine(content=$ 83.48, polygon=[Point(x=7.1827, y=5.8523), Point(x=7.7867, y=5.8472), Point(x=7.7867, y=6.0046), Point(x=7.1827, y=6.0046)], spans=[DocumentSpan(offset=458, length=7)])], words=[DocumentWord(content=Whitfords, polygon=[Point(x=4.9086, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.6091), Point(x=4.9086, y=0.6091)], span=DocumentSpan(offset=0, length=9), confidence=0.995), DocumentWord(content=2, polygon=[Point(x=4.9137, y=0.6598), Point(x=5.0101, y=0.6598), Point(x=5.0152, y=0.8223), Point(x=4.9238, y=0.8223)], span=DocumentSpan(offset=10, length=1), confidence=0.965), DocumentWord(content=Banks, polygon=[Point(x=5.071, y=0.6598), Point(x=5.5837, y=0.6598), Point(x=5.5888, y=0.8172), Point(x=5.0761, y=0.8223)], span=DocumentSpan(offset=12, length=5), confidence=0.995), DocumentWord(content=Ave, polygon=[Point(x=5.6599, y=0.6598), Point(x=5.9492, y=0.6649), Point(x=5.9492, y=0.8223), Point(x=5.6599, y=0.8172)], span=DocumentSpan(offset=18, length=3), confidence=0.996), DocumentWord(content=Hillarys, polygon=[Point(x=4.9035, y=0.8324), Point(x=5.5583, y=0.8426), Point(x=5.5634, y=1.0101), Point(x=4.9086, y=1.0151)], span=DocumentSpan(offset=22, length=8), confidence=0.993), DocumentWord(content=WA, polygon=[Point(x=5.6192, y=0.8426), Point(x=5.8781, y=0.8426), Point(x=5.8832, y=1.0101), Point(x=5.6192, y=1.0101)], span=DocumentSpan(offset=31, length=2), confidence=0.996), DocumentWord(content=6025, polygon=[Point(x=5.9593, y=0.8375), Point(x=6.3654, y=0.8324), Point(x=6.3705, y=1.0101), Point(x=5.9593, y=1.0101)], span=DocumentSpan(offset=34, length=4), confidence=0.993), DocumentWord(content=Ph:, polygon=[Point(x=4.9086, y=1.0304), Point(x=5.1827, y=1.0354), Point(x=5.1878, y=1.1928), Point(x=4.9137, y=1.1979)], span=DocumentSpan(offset=39, length=3), confidence=0.996), DocumentWord(content=08, polygon=[Point(x=5.2132, y=1.0354), Point(x=5.401, y=1.0405), Point(x=5.401, y=1.1928), Point(x=5.2182, y=1.1928)], span=DocumentSpan(offset=43, length=2), confidence=0.995), DocumentWord(content=9404, polygon=[Point(x=5.4619, y=1.0405), Point(x=5.8324, y=1.0405), Point(x=5.8324, y=1.1928), Point(x=5.4619, y=1.1928)], span=DocumentSpan(offset=46, length=4), confidence=0.989), DocumentWord(content=1133, polygon=[Point(x=5.9035, y=1.0405), Point(x=6.2791, y=1.0304), Point(x=6.2791, y=1.1928), Point(x=5.9086, y=1.1928)], span=DocumentSpan(offset=51, length=4), confidence=0.993), DocumentWord(content=E:, polygon=[Point(x=4.9086, y=1.2131), Point(x=5.071, y=1.2182), Point(x=5.0761, y=1.3806), Point(x=4.9137, y=1.3806)], span=DocumentSpan(offset=56, length=2), confidence=0.995), DocumentWord(content=<EMAIL>, polygon=[Point(x=5.1066, y=1.2182), Point(x=7.2284, y=1.2232), Point(x=7.2334, y=1.3857), Point(x=5.1066, y=1.3806)], span=DocumentSpan(offset=59, length=24), confidence=0.948), DocumentWord(content=Tax, polygon=[Point(x=5.6497, y=2.2029), Point(x=6.1065, y=2.2079), Point(x=6.0964, y=2.4363), Point(x=5.6345, y=2.4363)], span=DocumentSpan(offset=84, length=3), confidence=0.995), DocumentWord(content=Invoice, polygon=[Point(x=6.2131, y=2.2079), Point(x=7.1827, y=2.1978), Point(x=7.1878, y=2.4363), Point(x=6.203, y=2.4363)], span=DocumentSpan(offset=88, length=7), confidence=0.969), DocumentWord(content=Kevin, polygon=[Point(x=1.3249, y=2.7307), Point(x=1.6903, y=2.7307), Point(x=1.6954, y=2.883), Point(x=1.3249, y=2.883)], span=DocumentSpan(offset=96, length=5), confidence=0.995), DocumentWord(content=Jones, polygon=[Point(x=1.7411, y=2.7307), Point(x=2.1269, y=2.7358), Point(x=2.1269, y=2.8881), Point(x=1.7462, y=2.883)], span=DocumentSpan(offset=102, length=5), confidence=0.993), DocumentWord(content=Customer, polygon=[Point(x=5.7715, y=2.6901), Point(x=6.4365, y=2.6901), Point(x=6.4365, y=2.8576), Point(x=5.7664, y=2.8576)], span=DocumentSpan(offset=108, length=8), confidence=0.992), DocumentWord(content=ID, polygon=[Point(x=6.472, y=2.6901), Point(x=6.6192, y=2.6901), Point(x=6.6192, y=2.8576), Point(x=6.472, y=2.8576)], span=DocumentSpan(offset=117, length=2), confidence=0.995), DocumentWord(content=172386, polygon=[Point(x=6.7867, y=2.6901), Point(x=7.2994, y=2.6901), Point(x=7.2994, y=2.8627), Point(x=6.7867, y=2.8576)], span=DocumentSpan(offset=120, length=6), confidence=0.995), DocumentWord(content=21, polygon=[Point(x=1.2893, y=2.9033), Point(x=1.4619, y=2.9033), Point(x=1.467, y=3.0607), Point(x=1.2893, y=3.0607)], span=DocumentSpan(offset=127, length=2), confidence=0.995), DocumentWord(content=Aspendale, polygon=[Point(x=1.5025, y=2.9084), Point(x=2.2335, y=2.9033), Point(x=2.2335, y=3.0607), Point(x=1.5076, y=3.0607)], span=DocumentSpan(offset=130, length=9), confidence=0.994), DocumentWord(content=Place, polygon=[Point(x=2.2741, y=2.9033), Point(x=2.6446, y=2.8982), Point(x=2.6446, y=3.0657), Point(x=2.2741, y=3.0607)], span=DocumentSpan(offset=140, length=5), confidence=0.993), DocumentWord(content=Date, polygon=[Point(x=5.7766, y=2.9033), Point(x=6.0913, y=2.9033), Point(x=6.0913, y=3.0302), Point(x=5.7766, y=3.0302)], span=DocumentSpan(offset=146, length=4), confidence=0.993), DocumentWord(content=13/07/23, polygon=[Point(x=6.7715, y=2.8779), Point(x=7.3959, y=2.8779), Point(x=7.3959, y=3.0556), Point(x=6.7715, y=3.0505)], span=DocumentSpan(offset=151, length=8), confidence=0.995), DocumentWord(content=Hillarys, polygon=[Point(x=1.2843, y=3.0657), Point(x=1.8122, y=3.0708), Point(x=1.8122, y=3.2231), Point(x=1.2843, y=3.2282)], span=DocumentSpan(offset=160, length=8), confidence=0.994), DocumentWord(content=WA, polygon=[Point(x=1.8426, y=3.0708), Point(x=2.066, y=3.0708), Point(x=2.066, y=3.2231), Point(x=1.8426, y=3.2231)], span=DocumentSpan(offset=169, length=2), confidence=0.995), DocumentWord(content=6025, polygon=[Point(x=2.1167, y=3.0708), Point(x=2.4721, y=3.0657), Point(x=2.4721, y=3.2231), Point(x=2.1167, y=3.2231)], span=DocumentSpan(offset=172, length=4), confidence=0.993), DocumentWord(content=Reference, polygon=[Point(x=5.7766, y=3.0962), Point(x=6.472, y=3.1013), Point(x=6.472, y=3.2282), Point(x=5.7817, y=3.2332)], span=DocumentSpan(offset=177, length=9), confidence=0.994), DocumentWord(content=Invoice, polygon=[Point(x=5.7715, y=3.2891), Point(x=6.2538, y=3.2891), Point(x=6.2538, y=3.4312), Point(x=5.7766, y=3.4312)], span=DocumentSpan(offset=187, length=7), confidence=0.993), DocumentWord(content=No, polygon=[Point(x=6.2994, y=3.2891), Point(x=6.4873, y=3.2891), Point(x=6.4923, y=3.4312), Point(x=6.3045, y=3.4312)], span=DocumentSpan(offset=195, length=2), confidence=0.995), DocumentWord(content=********, polygon=[Point(x=6.7817, y=3.284), Point(x=7.472, y=3.2891), Point(x=7.472, y=3.4312), Point(x=6.7817, y=3.4261)], span=DocumentSpan(offset=198, length=8), confidence=0.995), DocumentWord(content=Page, polygon=[Point(x=6.7614, y=3.4819), Point(x=7.1116, y=3.4819), Point(x=7.1167, y=3.6393), Point(x=6.7664, y=3.6342)], span=DocumentSpan(offset=207, length=4), confidence=0.993), DocumentWord(content=1, polygon=[Point(x=7.1624, y=3.4819), Point(x=7.2487, y=3.4819), Point(x=7.2487, y=3.6393), Point(x=7.1675, y=3.6393)], span=DocumentSpan(offset=212, length=1), confidence=0.951), DocumentWord(content=of, polygon=[Point(x=7.2893, y=3.4819), Point(x=7.4416, y=3.4819), Point(x=7.4466, y=3.6393), Point(x=7.2893, y=3.6393)], span=DocumentSpan(offset=214, length=2), confidence=0.995), DocumentWord(content=1, polygon=[Point(x=7.472, y=3.4819), Point(x=7.5482, y=3.4769), Point(x=7.5532, y=3.6393), Point(x=7.4771, y=3.6393)], span=DocumentSpan(offset=217, length=1), confidence=0.989), DocumentWord(content=Patient, polygon=[Point(x=0.4721, y=3.9997), Point(x=1.0457, y=3.9946), Point(x=1.0508, y=4.1418), Point(x=0.4772, y=4.1418)], span=DocumentSpan(offset=219, length=7), confidence=0.995), DocumentWord(content=Service, polygon=[Point(x=1.4721, y=3.9895), Point(x=2.0304, y=3.9895), Point(x=2.0304, y=4.1367), Point(x=1.4771, y=4.1367)], span=DocumentSpan(offset=227, length=7), confidence=0.993), DocumentWord(content=Provided, polygon=[Point(x=2.0964, y=3.9895), Point(x=2.7715, y=3.9844), Point(x=2.7766, y=4.1418), Point(x=2.1015, y=4.1367)], span=DocumentSpan(offset=235, length=8), confidence=0.995), DocumentWord(content=Claimed, polygon=[Point(x=5.269, y=4.0047), Point(x=5.8832, y=4.0047), Point(x=5.8832, y=4.157), Point(x=5.2741, y=4.1519)], span=DocumentSpan(offset=244, length=7), confidence=0.995), DocumentWord(content=Quantity, polygon=[Point(x=6.2334, y=3.9895), Point(x=6.9187, y=3.9946), Point(x=6.9136, y=4.157), Point(x=6.2334, y=4.1519)], span=DocumentSpan(offset=252, length=8), confidence=0.995), DocumentWord(content=Amount, polygon=[Point(x=7.1624, y=3.9946), Point(x=7.7817, y=3.9946), Point(x=7.7817, y=4.1418), Point(x=7.1624, y=4.1367)], span=DocumentSpan(offset=261, length=6), confidence=0.995), DocumentWord(content=Freddie, polygon=[Point(x=0.467, y=4.2839), Point(x=1.0508, y=4.2839), Point(x=1.0508, y=4.4463), Point(x=0.4721, y=4.4463)], span=DocumentSpan(offset=268, length=7), confidence=0.994), DocumentWord(content=(ID:385654), polygon=[Point(x=1.1574, y=4.2839), Point(x=2.0812, y=4.289), Point(x=2.0812, y=4.4412), Point(x=1.1574, y=4.4463)], span=DocumentSpan(offset=276, length=11), confidence=0.98), DocumentWord(content=10/07/23, polygon=[Point(x=0.7005, y=4.4616), Point(x=1.3096, y=4.4616), Point(x=1.3147, y=4.6138), Point(x=0.7056, y=4.6138)], span=DocumentSpan(offset=288, length=8), confidence=0.994), DocumentWord(content=Consultation-, polygon=[Point(x=1.4619, y=4.4616), Point(x=2.401, y=4.4616), Point(x=2.4061, y=4.6189), Point(x=1.467, y=4.6138)], span=DocumentSpan(offset=297, length=13), confidence=0.988), DocumentWord(content=Complex, polygon=[Point(x=2.4315, y=4.4616), Point(x=3.0558, y=4.4616), Point(x=3.0558, y=4.6291), Point(x=2.4365, y=4.624)], span=DocumentSpan(offset=311, length=7), confidence=0.995), DocumentWord(content=✓, polygon=[Point(x=5.5208, y=4.5079), Point(x=5.5926, y=4.5079), Point(x=5.5926, y=4.5832), Point(x=5.5208, y=4.5832)], span=DocumentSpan(offset=319, length=1), confidence=1.0), DocumentWord(content=1, polygon=[Point(x=6.4466, y=4.4666), Point(x=6.5228, y=4.4666), Point(x=6.5177, y=4.5986), Point(x=6.4416, y=4.5935)], span=DocumentSpan(offset=321, length=1), confidence=0.975), DocumentWord(content=$, polygon=[Point(x=7.3248, y=4.4616), Point(x=7.4162, y=4.4616), Point(x=7.4213, y=4.6189), Point(x=7.3299, y=4.6189)], span=DocumentSpan(offset=323, length=1), confidence=0.948), DocumentWord(content=0.00, polygon=[Point(x=7.4568, y=4.4616), Point(x=7.7715, y=4.4616), Point(x=7.7715, y=4.6189), Point(x=7.4619, y=4.6189)], span=DocumentSpan(offset=325, length=4), confidence=0.993), DocumentWord(content=Flusapex, polygon=[Point(x=1.4568, y=4.9133), Point(x=2.0964, y=4.9082), Point(x=2.0964, y=5.0808), Point(x=1.4568, y=5.0859)], span=DocumentSpan(offset=330, length=8), confidence=0.994), DocumentWord(content=100ml, polygon=[Point(x=2.1421, y=4.9082), Point(x=2.5939, y=4.9082), Point(x=2.5939, y=5.0859), Point(x=2.1421, y=5.0808)], span=DocumentSpan(offset=339, length=5), confidence=0.995), DocumentWord(content=✓, polygon=[Point(x=5.5208, y=4.9662), Point(x=5.5926, y=4.9662), Point(x=5.5926, y=5.0415), Point(x=5.5208, y=5.0415)], span=DocumentSpan(offset=345, length=1), confidence=1.0), DocumentWord(content=1, polygon=[Point(x=6.4517, y=4.9234), Point(x=6.5177, y=4.9234), Point(x=6.5177, y=5.0554), Point(x=6.4517, y=5.0554)], span=DocumentSpan(offset=347, length=1), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.1573, y=4.9285), Point(x=7.2334, y=4.9285), Point(x=7.2385, y=5.0706), Point(x=7.1624, y=5.0706)], span=DocumentSpan(offset=349, length=1), confidence=0.961), DocumentWord(content=132.28, polygon=[Point(x=7.2944, y=4.9285), Point(x=7.7664, y=4.9184), Point(x=7.7664, y=5.0706), Point(x=7.2944, y=5.0706)], span=DocumentSpan(offset=351, length=6), confidence=0.992), DocumentWord(content=You, polygon=[Point(x=0.5076, y=5.4513), Point(x=0.7614, y=5.4513), Point(x=0.7614, y=5.6137), Point(x=0.5076, y=5.6087)], span=DocumentSpan(offset=358, length=3), confidence=0.996), DocumentWord(content=have, polygon=[Point(x=0.7919, y=5.4513), Point(x=1.1421, y=5.4462), Point(x=1.1472, y=5.6137), Point(x=0.7969, y=5.6137)], span=DocumentSpan(offset=362, length=4), confidence=0.993), DocumentWord(content=been, polygon=[Point(x=1.1777, y=5.4462), Point(x=1.5279, y=5.4462), Point(x=1.5279, y=5.6188), Point(x=1.1777, y=5.6137)], span=DocumentSpan(offset=367, length=4), confidence=0.987), DocumentWord(content=given, polygon=[Point(x=1.5837, y=5.4462), Point(x=1.9543, y=5.4462), Point(x=1.9594, y=5.6188), Point(x=1.5837, y=5.6188)], span=DocumentSpan(offset=372, length=5), confidence=0.995), DocumentWord(content=a, polygon=[Point(x=2.0101, y=5.4462), Point(x=2.0964, y=5.4462), Point(x=2.1015, y=5.6188), Point(x=2.0101, y=5.6188)], span=DocumentSpan(offset=378, length=1), confidence=0.995), DocumentWord(content=discount, polygon=[Point(x=2.132, y=5.4462), Point(x=2.736, y=5.4462), Point(x=2.736, y=5.6188), Point(x=2.132, y=5.6188)], span=DocumentSpan(offset=380, length=8), confidence=0.993), DocumentWord(content=of:, polygon=[Point(x=2.7665, y=5.4462), Point(x=2.9847, y=5.4462), Point(x=2.9898, y=5.6137), Point(x=2.7665, y=5.6188)], span=DocumentSpan(offset=389, length=3), confidence=0.993), DocumentWord(content=$, polygon=[Point(x=3.0203, y=5.4462), Point(x=3.1167, y=5.4462), Point(x=3.1167, y=5.6137), Point(x=3.0203, y=5.6137)], span=DocumentSpan(offset=393, length=1), confidence=0.948), DocumentWord(content=156.00, polygon=[Point(x=3.1827, y=5.4462), Point(x=3.7005, y=5.4462), Point(x=3.7005, y=5.6087), Point(x=3.1827, y=5.6137)], span=DocumentSpan(offset=395, length=6), confidence=0.995), DocumentWord(content=TOTAL, polygon=[Point(x=6.1015, y=5.4564), Point(x=6.5837, y=5.4513), Point(x=6.5837, y=5.6036), Point(x=6.1015, y=5.5985)], span=DocumentSpan(offset=402, length=5), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.0913, y=5.4462), Point(x=7.1776, y=5.4412), Point(x=7.1827, y=5.6137), Point(x=7.0913, y=5.6137)], span=DocumentSpan(offset=408, length=1), confidence=0.993), DocumentWord(content=132.28, polygon=[Point(x=7.2487, y=5.4412), Point(x=7.7715, y=5.4412), Point(x=7.7766, y=5.6137), Point(x=7.2487, y=5.6137)], span=DocumentSpan(offset=410, length=6), confidence=0.995), DocumentWord(content=Total, polygon=[Point(x=5.137, y=5.6391), Point(x=5.5279, y=5.6391), Point(x=5.5279, y=5.8117), Point(x=5.137, y=5.8117)], span=DocumentSpan(offset=417, length=5), confidence=0.995), DocumentWord(content=includes, polygon=[Point(x=5.5634, y=5.6391), Point(x=6.2436, y=5.634), Point(x=6.2385, y=5.8117), Point(x=5.5583, y=5.8117)], span=DocumentSpan(offset=423, length=8), confidence=0.991), DocumentWord(content=GST, polygon=[Point(x=6.2893, y=5.634), Point(x=6.5989, y=5.634), Point(x=6.5989, y=5.8117), Point(x=6.2842, y=5.8117)], span=DocumentSpan(offset=432, length=3), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.2385, y=5.6594), Point(x=7.3248, y=5.6543), Point(x=7.3299, y=5.8066), Point(x=7.2436, y=5.8066)], span=DocumentSpan(offset=436, length=1), confidence=0.95), DocumentWord(content=12.03, polygon=[Point(x=7.3857, y=5.6543), Point(x=7.7715, y=5.6493), Point(x=7.7715, y=5.8066), Point(x=7.3908, y=5.8066)], span=DocumentSpan(offset=438, length=5), confidence=0.995), DocumentWord(content=Balance, polygon=[Point(x=5.4365, y=5.8523), Point(x=6.0456, y=5.8523), Point(x=6.0507, y=6.0198), Point(x=5.4365, y=6.0096)], span=DocumentSpan(offset=444, length=7), confidence=0.995), DocumentWord(content=Owing, polygon=[Point(x=6.1015, y=5.8523), Point(x=6.5888, y=5.8523), Point(x=6.5888, y=6.0249), Point(x=6.1015, y=6.0198)], span=DocumentSpan(offset=452, length=5), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.1928, y=5.8523), Point(x=7.274, y=5.8523), Point(x=7.2791, y=6.0096), Point(x=7.1979, y=6.0096)], span=DocumentSpan(offset=458, length=1), confidence=0.953), DocumentWord(content=83.48, polygon=[Point(x=7.3451, y=5.8523), Point(x=7.7715, y=5.8523), Point(x=7.7715, y=6.0046), Point(x=7.3502, y=6.0096)], span=DocumentSpan(offset=460, length=5), confidence=0.995)], selection_marks=[DocumentSelectionMark(state=selected, span=DocumentSpan(offset=466, length=10), confidence=0.742, polygon=[Point(x=5.51, y=4.504), Point(x=5.5976, y=4.504), Point(x=5.5976, y=4.5931), Point(x=5.51, y=4.5931)]), DocumentSelectionMark(state=selected, span=DocumentSpan(offset=477, length=10), confidence=0.913, polygon=[Point(x=5.5114, y=4.9628), Point(x=5.5949, y=4.9628), Point(x=5.5949, y=5.0465), Point(x=5.5114, y=5.0465)])], spans=[DocumentSpan(offset=0, length=487)], barcodes=[], formulas=[])], paragraphs=[], tables=[DocumentTable(row_count=4, column_count=5, cells=[DocumentTableCell(kind=columnHeader, row_index=0, column_index=0, row_span=1, column_span=1, content=Patient, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=3.9268), Point(x=1.3525, y=3.9268), Point(x=1.3525, y=4.1971), Point(x=0.3639, y=4.1971)])], spans=[DocumentSpan(offset=219, length=7)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=1, row_span=1, column_span=1, content=Service Provided, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.3525, y=3.9268), Point(x=4.1714, y=3.9268), Point(x=4.1637, y=4.1971), Point(x=1.3525, y=4.1971)])], spans=[DocumentSpan(offset=227, length=16)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=2, row_span=1, column_span=1, content=Claimed, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1714, y=3.9268), Point(x=6.0559, y=3.9268), Point(x=6.0636, y=4.1971), Point(x=4.1637, y=4.1971)])], spans=[DocumentSpan(offset=244, length=7)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=3, row_span=1, column_span=1, content=Quantity, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0559, y=3.9268), Point(x=7.029, y=3.9268), Point(x=7.029, y=4.1971), Point(x=6.0636, y=4.1971)])], spans=[DocumentSpan(offset=252, length=8)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=4, row_span=1, column_span=1, content=Amount, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=3.9268), Point(x=7.8786, y=3.9268), Point(x=7.8786, y=4.1971), Point(x=7.029, y=4.1971)])], spans=[DocumentSpan(offset=261, length=6)]), DocumentTableCell(kind=content, row_index=1, column_index=0, row_span=1, column_span=2, content=Freddie (ID:385654), bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=4.1971), Point(x=4.1637, y=4.1971), Point(x=4.1637, y=4.4442), Point(x=0.3639, y=4.4442)])], spans=[DocumentSpan(offset=268, length=19)]), DocumentTableCell(kind=content, row_index=1, column_index=2, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1637, y=4.1971), Point(x=6.0636, y=4.1971), Point(x=6.0636, y=4.4364), Point(x=4.1637, y=4.4442)])], spans=[]), DocumentTableCell(kind=content, row_index=1, column_index=3, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0636, y=4.1971), Point(x=7.029, y=4.1971), Point(x=7.029, y=4.4364), Point(x=6.0636, y=4.4364)])], spans=[]), DocumentTableCell(kind=content, row_index=1, column_index=4, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=4.1971), Point(x=7.8786, y=4.1971), Point(x=7.8786, y=4.4364), Point(x=7.029, y=4.4364)])], spans=[]), DocumentTableCell(kind=content, row_index=2, column_index=0, row_span=1, column_span=1, content=10/07/23, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=4.4442), Point(x=1.3525, y=4.4442), Point(x=1.3525, y=4.7608), Point(x=0.3639, y=4.7608)])], spans=[DocumentSpan(offset=288, length=8)]), DocumentTableCell(kind=content, row_index=2, column_index=1, row_span=1, column_span=1, content=Consultation- Complex, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.3525, y=4.4442), Point(x=4.1637, y=4.4442), Point(x=4.1637, y=4.7608), Point(x=1.3525, y=4.7608)])], spans=[DocumentSpan(offset=297, length=21)]), DocumentTableCell(kind=content, row_index=2, column_index=2, row_span=1, column_span=1, content=✓\n", ":selected:, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1637, y=4.4442), Point(x=6.0636, y=4.4364), Point(x=6.0636, y=4.7608), Point(x=4.1637, y=4.7608)])], spans=[DocumentSpan(offset=319, length=1), DocumentSpan(offset=466, length=10)]), DocumentTableCell(kind=content, row_index=2, column_index=3, row_span=1, column_span=1, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0636, y=4.4364), Point(x=7.029, y=4.4364), Point(x=7.029, y=4.7608), Point(x=6.0636, y=4.7608)])], spans=[DocumentSpan(offset=321, length=1)]), DocumentTableCell(kind=content, row_index=2, column_index=4, row_span=1, column_span=1, content=$ 0.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=4.4364), Point(x=7.8786, y=4.4364), Point(x=7.8786, y=4.7608), Point(x=7.029, y=4.7608)])], spans=[DocumentSpan(offset=323, length=6)]), DocumentTableCell(kind=content, row_index=3, column_index=0, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=4.7608), Point(x=1.3525, y=4.7608), Point(x=1.3447, y=5.3862), Point(x=0.3562, y=5.3785)])], spans=[]), DocumentTableCell(kind=content, row_index=3, column_index=1, row_span=1, column_span=1, content=Flusapex 100ml, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.3525, y=4.7608), Point(x=4.1637, y=4.7608), Point(x=4.1637, y=5.3862), Point(x=1.3447, y=5.3862)])], spans=[DocumentSpan(offset=330, length=14)]), DocumentTableCell(kind=content, row_index=3, column_index=2, row_span=1, column_span=1, content=✓\n", ":selected:, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1637, y=4.7608), Point(x=6.0636, y=4.7608), Point(x=6.0636, y=5.3862), Point(x=4.1637, y=5.3862)])], spans=[DocumentSpan(offset=345, length=1), DocumentSpan(offset=477, length=10)]), DocumentTableCell(kind=content, row_index=3, column_index=3, row_span=1, column_span=1, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0636, y=4.7608), Point(x=7.029, y=4.7608), Point(x=7.0367, y=5.394), Point(x=6.0636, y=5.3862)])], spans=[DocumentSpan(offset=347, length=1)]), DocumentTableCell(kind=content, row_index=3, column_index=4, row_span=1, column_span=1, content=$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=4.7608), Point(x=7.8786, y=4.7608), Point(x=7.8786, y=5.394), Point(x=7.0367, y=5.394)])], spans=[DocumentSpan(offset=349, length=8)])], bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3691, y=3.9222), Point(x=7.898, y=3.9226), Point(x=7.8986, y=5.4012), Point(x=0.3688, y=5.4015)])], spans=[DocumentSpan(offset=219, length=101), DocumentSpan(offset=466, length=10), DocumentSpan(offset=321, length=25), DocumentSpan(offset=477, length=10), DocumentSpan(offset=347, length=10)])], key_value_pairs=[], styles=[], documents=[AnalyzedDocument(doc_type=invoice, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.0, y=0.0), Point(x=8.2639, y=0.0), Point(x=8.2639, y=11.6944), Point(x=0.0, y=11.6944)])], spans=[DocumentSpan(offset=0, length=487)], fields={'AmountDue': DocumentField(value_type=currency, value=CurrencyValue(amount=83.48, symbol=$, code=AUD), content=$ 83.48, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.1928, y=5.8523), Point(x=7.7715, y=5.8523), Point(x=7.7715, y=6.0096), Point(x=7.1928, y=6.0096)])], spans=[DocumentSpan(offset=458, length=7)], confidence=0.919), 'CustomerAddress': DocumentField(value_type=address, value=AddressValue(house_number=21, po_box=None, road=Aspendale Place, city=None, state=WA, postal_code=6025, country_region=None, street_address=21 Aspendale Place, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None), content=21 Aspendale Place\n", "<PERSON>s WA 6025, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.2843, y=2.8982), Point(x=2.6446, y=2.8982), Point(x=2.6446, y=3.2282), Point(x=1.2843, y=3.2282)])], spans=[DocumentSpan(offset=127, length=18), DocumentSpan(offset=160, length=16)], confidence=0.892), 'CustomerId': DocumentField(value_type=string, value='172386', content=172386, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.7867, y=2.6901), Point(x=7.2994, y=2.6901), Point(x=7.2994, y=2.8627), Point(x=6.7867, y=2.8576)])], spans=[DocumentSpan(offset=120, length=6)], confidence=0.982), 'CustomerName': DocumentField(value_type=string, value='<PERSON>', content=<PERSON>, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.325, y=2.7251), Point(x=2.1289, y=2.7358), Point(x=2.1268, y=2.8937), Point(x=1.3229, y=2.883)])], spans=[DocumentSpan(offset=96, length=11)], confidence=0.915), 'InvoiceDate': DocumentField(value_type=date, value=datetime.date(2023, 7, 13), content=13/07/23, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.7715, y=2.8779), Point(x=7.3959, y=2.8779), Point(x=7.3959, y=3.0556), Point(x=6.7715, y=3.0505)])], spans=[DocumentSpan(offset=151, length=8)], confidence=0.982), 'InvoiceId': DocumentField(value_type=string, value='********', content=********, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.7817, y=3.284), Point(x=7.472, y=3.2891), Point(x=7.472, y=3.4312), Point(x=6.7817, y=3.4261)])], spans=[DocumentSpan(offset=198, length=8)], confidence=0.982), 'InvoiceTotal': DocumentField(value_type=currency, value=CurrencyValue(amount=132.28, symbol=$, code=AUD), content=$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.0913, y=5.4412), Point(x=7.7766, y=5.4412), Point(x=7.7766, y=5.6137), Point(x=7.0913, y=5.6137)])], spans=[DocumentSpan(offset=408, length=8)], confidence=0.92), 'Items': DocumentField(value_type=list, value=[DocumentField(value_type=dictionary, value={'Amount': DocumentField(value_type=currency, value=CurrencyValue(amount=0.0, symbol=$, code=AUD), content=$ 0.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.3248, y=4.4616), Point(x=7.7715, y=4.4616), Point(x=7.7715, y=4.6189), Point(x=7.3248, y=4.6189)])], spans=[DocumentSpan(offset=323, length=6)], confidence=0.933), 'Date': DocumentField(value_type=date, value=datetime.date(2023, 7, 10), content=10/07/23, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.7005, y=4.4616), Point(x=1.3096, y=4.4616), Point(x=1.3147, y=4.6138), Point(x=0.7056, y=4.6138)])], spans=[DocumentSpan(offset=288, length=8)], confidence=0.818), 'Description': DocumentField(value_type=string, value='Consultation- Complex', content=Consultation- Complex, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.4619, y=4.4551), Point(x=3.0565, y=4.4616), Point(x=3.0558, y=4.6291), Point(x=1.4612, y=4.6226)])], spans=[DocumentSpan(offset=297, length=21)], confidence=0.923), 'Quantity': DocumentField(value_type=float, value=1.0, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.4466, y=4.4666), Point(x=6.5228, y=4.4666), Point(x=6.5177, y=4.5986), Point(x=6.4416, y=4.5935)])], spans=[DocumentSpan(offset=321, length=1)], confidence=0.94)}, content=10/07/23 Consultation- Complex\n", "✓\n", "1\n", "$ 0.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.7005, y=4.4616), Point(x=7.7715, y=4.4616), Point(x=7.7715, y=4.6291), Point(x=0.7005, y=4.6291)])], spans=[DocumentSpan(offset=288, length=41)], confidence=0.81), DocumentField(value_type=dictionary, value={'Amount': DocumentField(value_type=currency, value=CurrencyValue(amount=132.28, symbol=$, code=AUD), content=$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.1573, y=4.9184), Point(x=7.7664, y=4.9184), Point(x=7.7664, y=5.0706), Point(x=7.1573, y=5.0706)])], spans=[DocumentSpan(offset=349, length=8)], confidence=0.932), 'Description': DocumentField(value_type=string, value='Flusapex 100ml', content=Flusapex 100ml, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.4569, y=4.9018), Point(x=2.5949, y=4.9082), Point(x=2.5939, y=5.0923), Point(x=1.4558, y=5.0859)])], spans=[DocumentSpan(offset=330, length=14)], confidence=0.922), 'Quantity': DocumentField(value_type=float, value=1.0, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.4517, y=4.9234), Point(x=6.5177, y=4.9234), Point(x=6.5177, y=5.0554), Point(x=6.4517, y=5.0554)])], spans=[DocumentSpan(offset=347, length=1)], confidence=0.937)}, content=Flusapex 100ml\n", "✓\n", "1\n", "$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.4568, y=4.9082), Point(x=7.7664, y=4.9082), Point(x=7.7664, y=5.0859), Point(x=1.4568, y=5.0859)])], spans=[DocumentSpan(offset=330, length=27)], confidence=0.854)], content=None, bounding_regions=[], spans=[], confidence=None), 'TotalDiscount': DocumentField(value_type=currency, value=CurrencyValue(amount=156.0, symbol=$, code=AUD), content=$ 156.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=3.0203, y=5.4462), Point(x=3.7005, y=5.4462), Point(x=3.7005, y=5.6137), Point(x=3.0203, y=5.6137)])], spans=[DocumentSpan(offset=393, length=8)], confidence=0.416), 'TotalTax': DocumentField(value_type=currency, value=CurrencyValue(amount=12.03, symbol=$, code=AUD), content=$ 12.03, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.2385, y=5.6528), Point(x=7.7715, y=5.6493), Point(x=7.7725, y=5.8066), Point(x=7.2395, y=5.8101)])], spans=[DocumentSpan(offset=436, length=7)], confidence=0.648), 'VendorAddress': DocumentField(value_type=address, value=AddressValue(house_number=2, po_box=None, road=Banks Ave, city=None, state=WA, postal_code=6025, country_region=None, street_address=2 Banks Ave, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None), content=2 Banks Ave\n", "<PERSON><PERSON> WA 6025, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.9035, y=0.6598), Point(x=6.3705, y=0.6598), Point(x=6.3705, y=1.0151), Point(x=4.9035, y=1.0151)])], spans=[DocumentSpan(offset=10, length=28)], confidence=0.891), 'VendorAddressRecipient': DocumentField(value_type=string, value='Whitfords', content=Whitfords, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.9086, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.6091), Point(x=4.9086, y=0.6091)])], spans=[DocumentSpan(offset=0, length=9)], confidence=0.645), 'VendorName': DocumentField(value_type=string, value='Whitfords', content=Whitfords, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.9086, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.6091), Point(x=4.9086, y=0.6091)])], spans=[DocumentSpan(offset=0, length=9)], confidence=0.645)}, confidence=1.0)])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["invoices"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------Analyzing invoice #1--------\n", "Vendor Name: <PERSON><PERSON><PERSON><PERSON> has confidence: 0.645\n", "Vendor Address: AddressValue(house_number=2, po_box=None, road=Banks Ave, city=None, state=WA, postal_code=6025, country_region=None, street_address=2 Banks Ave, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None) has confidence: 0.891\n", "<PERSON><PERSON>or Address Recipient: <PERSON><PERSON><PERSON><PERSON> has confidence: 0.645\n", "Customer Name: <PERSON> has confidence: 0.915\n", "Customer Id: 172386 has confidence: 0.982\n", "Customer Address: AddressValue(house_number=21, po_box=None, road=Aspendale Place, city=None, state=WA, postal_code=6025, country_region=None, street_address=21 Aspendale Place, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None) has confidence: 0.892\n", "Invoice Id: ******** has confidence: 0.982\n", "Invoice Date: 2023-07-13 has confidence: 0.982\n", "Invoice Total: $132.28 has confidence: 0.92\n", "Invoice items:\n", "...Item #1\n", "......Description: Consultation- Complex has confidence: 0.923\n", "......Quantity: 1.0 has confidence: 0.94\n", "......Date: 2023-07-10 has confidence: 0.818\n", "......Amount: $0.0 has confidence: 0.933\n", "...Item #2\n", "......Description: Flusapex 100ml has confidence: 0.922\n", "......Quantity: 1.0 has confidence: 0.937\n", "......Amount: $132.28 has confidence: 0.932\n", "Total Tax: $12.03 has confidence: 0.648\n", "Amount Due: $83.48 has confidence: 0.919\n"]}], "source": ["for idx, invoice in enumerate(invoices.documents):\n", "       print(f\"--------Analyzing invoice #{idx + 1}--------\")\n", "       vendor_name = invoice.fields.get(\"VendorName\")\n", "       if vendor_name:\n", "           print(\n", "               f\"Vendor Name: {vendor_name.value} has confidence: {vendor_name.confidence}\"\n", "           )\n", "       vendor_address = invoice.fields.get(\"VendorAddress\")\n", "       if vendor_address:\n", "           print(\n", "               f\"Vendor Address: {vendor_address.value} has confidence: {vendor_address.confidence}\"\n", "           )\n", "       vendor_address_recipient = invoice.fields.get(\"VendorAddressRecipient\")\n", "       if vendor_address_recipient:\n", "           print(\n", "               f\"Vendor Address Recipient: {vendor_address_recipient.value} has confidence: {vendor_address_recipient.confidence}\"\n", "           )\n", "       customer_name = invoice.fields.get(\"CustomerName\")\n", "       if customer_name:\n", "           print(\n", "               f\"Customer Name: {customer_name.value} has confidence: {customer_name.confidence}\"\n", "           )\n", "       customer_id = invoice.fields.get(\"CustomerId\")\n", "       if customer_id:\n", "           print(\n", "               f\"Customer Id: {customer_id.value} has confidence: {customer_id.confidence}\"\n", "           )\n", "       customer_address = invoice.fields.get(\"CustomerAddress\")\n", "       if customer_address:\n", "           print(\n", "               f\"Customer Address: {customer_address.value} has confidence: {customer_address.confidence}\"\n", "           )\n", "       customer_address_recipient = invoice.fields.get(\"CustomerAddressRecipient\")\n", "       if customer_address_recipient:\n", "           print(\n", "               f\"Customer Address Recipient: {customer_address_recipient.value} has confidence: {customer_address_recipient.confidence}\"\n", "           )\n", "       invoice_id = invoice.fields.get(\"InvoiceId\")\n", "       if invoice_id:\n", "           print(\n", "               f\"Invoice Id: {invoice_id.value} has confidence: {invoice_id.confidence}\"\n", "           )\n", "       invoice_date = invoice.fields.get(\"InvoiceDate\")\n", "       if invoice_date:\n", "           print(\n", "               f\"Invoice Date: {invoice_date.value} has confidence: {invoice_date.confidence}\"\n", "           )\n", "       invoice_total = invoice.fields.get(\"InvoiceTotal\")\n", "       if invoice_total:\n", "           print(\n", "               f\"Invoice Total: {invoice_total.value} has confidence: {invoice_total.confidence}\"\n", "           )\n", "       due_date = invoice.fields.get(\"DueDate\")\n", "       if due_date:\n", "           print(f\"Due Date: {due_date.value} has confidence: {due_date.confidence}\")\n", "       purchase_order = invoice.fields.get(\"PurchaseOrder\")\n", "       if purchase_order:\n", "           print(\n", "               f\"Purchase Order: {purchase_order.value} has confidence: {purchase_order.confidence}\"\n", "           )\n", "       billing_address = invoice.fields.get(\"BillingAddress\")\n", "       if billing_address:\n", "           print(\n", "               f\"Billing Address: {billing_address.value} has confidence: {billing_address.confidence}\"\n", "           )\n", "       billing_address_recipient = invoice.fields.get(\"BillingAddressRecipient\")\n", "       if billing_address_recipient:\n", "           print(\n", "               f\"Billing Address Recipient: {billing_address_recipient.value} has confidence: {billing_address_recipient.confidence}\"\n", "           )\n", "       shipping_address = invoice.fields.get(\"ShippingAddress\")\n", "       if shipping_address:\n", "           print(\n", "               f\"Shipping Address: {shipping_address.value} has confidence: {shipping_address.confidence}\"\n", "           )\n", "       shipping_address_recipient = invoice.fields.get(\"ShippingAddressRecipient\")\n", "       if shipping_address_recipient:\n", "           print(\n", "               f\"Shipping Address Recipient: {shipping_address_recipient.value} has confidence: {shipping_address_recipient.confidence}\"\n", "           )\n", "       print(\"Invoice items:\")\n", "       for idx, item in enumerate(invoice.fields.get(\"Items\").value):\n", "           print(f\"...Item #{idx + 1}\")\n", "           item_description = item.value.get(\"Description\")\n", "           if item_description:\n", "               print(\n", "                   f\"......Description: {item_description.value} has confidence: {item_description.confidence}\"\n", "               )\n", "           item_quantity = item.value.get(\"Quantity\")\n", "           if item_quantity:\n", "               print(\n", "                   f\"......Quantity: {item_quantity.value} has confidence: {item_quantity.confidence}\"\n", "               )\n", "           unit = item.value.get(\"Unit\")\n", "           if unit:\n", "               print(f\"......Unit: {unit.value} has confidence: {unit.confidence}\")\n", "           unit_price = item.value.get(\"UnitPrice\")\n", "           if unit_price:\n", "               unit_price_code = unit_price.value.code if unit_price.value.code else \"\"\n", "               print(\n", "                   f\"......Unit Price: {unit_price.value}{unit_price_code} has confidence: {unit_price.confidence}\"\n", "               )\n", "           product_code = item.value.get(\"ProductCode\")\n", "           if product_code:\n", "               print(\n", "                   f\"......Product Code: {product_code.value} has confidence: {product_code.confidence}\"\n", "               )\n", "           item_date = item.value.get(\"Date\")\n", "           if item_date:\n", "               print(\n", "                   f\"......Date: {item_date.value} has confidence: {item_date.confidence}\"\n", "               )\n", "           tax = item.value.get(\"Tax\")\n", "           if tax:\n", "               print(f\"......Tax: {tax.value} has confidence: {tax.confidence}\")\n", "           amount = item.value.get(\"Amount\")\n", "           if amount:\n", "               print(\n", "                   f\"......Amount: {amount.value} has confidence: {amount.confidence}\"\n", "               )\n", "       subtotal = invoice.fields.get(\"SubTotal\")\n", "       if subtotal:\n", "           print(f\"Subtotal: {subtotal.value} has confidence: {subtotal.confidence}\")\n", "       total_tax = invoice.fields.get(\"TotalTax\")\n", "       if total_tax:\n", "           print(\n", "               f\"Total Tax: {total_tax.value} has confidence: {total_tax.confidence}\"\n", "           )\n", "       previous_unpaid_balance = invoice.fields.get(\"PreviousUnpaidBalance\")\n", "       if previous_unpaid_balance:\n", "           print(\n", "               f\"Previous Unpaid Balance: {previous_unpaid_balance.value} has confidence: {previous_unpaid_balance.confidence}\"\n", "           )\n", "       amount_due = invoice.fields.get(\"AmountDue\")\n", "       if amount_due:\n", "           print(\n", "               f\"Amount Due: {amount_due.value} has confidence: {amount_due.confidence}\"\n", "           )\n", "       service_start_date = invoice.fields.get(\"ServiceStartDate\")\n", "       if service_start_date:\n", "           print(\n", "               f\"Service Start Date: {service_start_date.value} has confidence: {service_start_date.confidence}\"\n", "           )\n", "       service_end_date = invoice.fields.get(\"ServiceEndDate\")\n", "       if service_end_date:\n", "           print(\n", "               f\"Service End Date: {service_end_date.value} has confidence: {service_end_date.confidence}\"\n", "           )\n", "       service_address = invoice.fields.get(\"ServiceAddress\")\n", "       if service_address:\n", "           print(\n", "               f\"Service Address: {service_address.value} has confidence: {service_address.confidence}\"\n", "           )\n", "       service_address_recipient = invoice.fields.get(\"ServiceAddressRecipient\")\n", "       if service_address_recipient:\n", "           print(\n", "               f\"Service Address Recipient: {service_address_recipient.value} has confidence: {service_address_recipient.confidence}\"\n", "           )\n", "       remittance_address = invoice.fields.get(\"RemittanceAddress\")\n", "       if remittance_address:\n", "           print(\n", "               f\"Remittance Address: {remittance_address.value} has confidence: {remittance_address.confidence}\"\n", "           )\n", "       remittance_address_recipient = invoice.fields.get(\"RemittanceAddressRecipient\")\n", "       if remittance_address_recipient:\n", "           print(\n", "               f\"Remittance Address Recipient: {remittance_address_recipient.value} has confidence: {remittance_address_recipient.confidence}\"\n", "           )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}