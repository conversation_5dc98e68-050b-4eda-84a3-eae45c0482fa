{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# construct testing invoice files"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# import fitz  # PyMuPDF\n", "\n", "# file_path = \"../../data/tmp_1728358794590598391.pdf\"\n", "# pdf_document = fitz.open(file_path)\n", "\n", "# invoice1_output_path = \"../../data/test_invoice1.pdf\"\n", "# invoice1_output_pdf = fitz.open()\n", "# invoice1_output_pdf.insert_pdf(pdf_document, from_page=0, to_page=0)\n", "# invoice1_output_pdf.save(invoice1_output_path)\n", "# invoice1_output_pdf.close()\n", "\n", "\n", "# invoice2_output_path = \"../../data/test_invoice2.pdf\"\n", "# invoice2_output_pdf = fitz.open()\n", "# invoice2_output_pdf.insert_pdf(pdf_document, from_page=1, to_page=1)\n", "# invoice2_output_pdf.save(invoice2_output_path)\n", "# invoice2_output_pdf.close()\n", "\n", "# pdf_document.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pre-Process Files for Document Intelligence"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import os\n", "from typing import List, Dict\n", "import fitz\n", "from fitz import FileDataError\n", "import glob\n", "from loguru import logger\n", "from shutil import copy2\n", "\n", "def split_pdf_file(file_path: str, target_folder_path: str) -> List[str]:\n", "    assert Path(file_path).exists and Path(file_path).is_file and Path(file_path).suffix.lower() == \".pdf\"\n", "    assert Path(target_folder_path).is_dir\n", "    if not Path(target_folder_path).exists():\n", "        logger.info(f\"target folder {target_folder_path} created\")\n", "        os.makedirs(target_folder_path)\n", "\n", "    file_stem = str(Path(file_path).stem)\n", "    try:\n", "        pdf_document = fitz.open(file_path)\n", "        num_page = pdf_document.page_count\n", "    except FileDataError as e:\n", "        logger.exception(e)\n", "        return []\n", "\n", "    output_file_name_list = []\n", "    for i in range(num_page):\n", "        target_file_path = os.path.join(target_folder_path, f\"{file_stem}-{i}.pdf\")\n", "\n", "        output_pdf = fitz.open()\n", "        output_pdf.insert_pdf(pdf_document, from_page=i, to_page=i)\n", "        output_pdf.save(target_file_path)\n", "        output_pdf.close()\n", "        output_file_name_list.append(target_file_path)\n", "\n", "    pdf_document.close()\n", "    return output_file_name_list\n", "\n", "def batch_split_pdf_files(source_folder_path: str, target_folder_path: str) -> List[str]:\n", "    assert Path(source_folder_path).is_dir and Path(source_folder_path).exists\n", "    assert Path(target_folder_path).is_dir\n", "    if not Path(target_folder_path).exists():\n", "        os.makedirs(target_folder_path)\n", "\n", "    output_file_name_list = []\n", "\n", "    for file_path in sorted(os.listdir(source_folder_path)):\n", "        suffix = str(Path(file_path).suffix)\n", "\n", "        if suffix.lower() == \".pdf\":\n", "            logger.info(f\"PDF file {file_path} get split.\")\n", "            pdf_output_file_name_list = split_pdf_file(os.path.join(source_folder_path,file_path), target_folder_path)\n", "            output_file_name_list.extend(pdf_output_file_name_list)\n", "        elif suffix.lower() in [\".jpeg\", \".jpg\", \".png\", \".bmp\", \".tiff\", \".heif\"]:\n", "            logger.info(f\"Image file {file_path} add to the output file list\")\n", "            copy2(os.path.join(source_folder_path,file_path), os.path.join(target_folder_path, file_path))\n", "            output_file_name_list.append(file_path)\n", "        else:\n", "            logger.exception(f\"file {file_path} {suffix} gets IGNORED due to unacceptable file type.\")\n", "    return output_file_name_list\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-10 02:26:51.554\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 00dcaff3-051f-4064-ae19-254264e8e242.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.559\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 08b8af75-483d-4160-9fba-f75344f4ce4f.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.564\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.565\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0ca22e7d-42bb-45b5-8ba0-212faba169ad.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.567\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.576\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.579\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 1085fe16-0014-489d-bfab-6d005298074a.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.581\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 10ec75dd-e526-444f-895b-7faac9c0cd93.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.583\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.588\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.592\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 204231ce-913e-405c-9132-20cfd100a14b.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.598\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 2836a967-2f4c-4655-bf35-c148e26d9f07.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.601\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.604\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 29eb8c55-14df-4a91-b2bb-9bc29ab69a63.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.607\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 2e5efa44-3660-40df-9701-245d015f3771.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.609\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.611\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 30d78167-a823-44da-a272-a5b36acc66aa.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.614\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 37d281d0-903c-482d-aec6-a603544de1d6.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.619\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.621\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.623\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.626\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 4234dfb8-5e66-452a-88ba-d520698fb608.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.627\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 426dd3f2-6ad7-4a93-a801-71647893c19d.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.630\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 42c562c8-ad0a-4119-9b2a-a98702470659.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.632\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 452156dd-abc3-4c1d-b31c-fde48aadf60c.PDF get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.634\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 4626aa97-e040-494f-9896-0469eeffd02f.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.636\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 46b70eea-f82b-4f9e-b453-9fbdc0104549.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.637\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 474466f5-70ca-4c63-83f8-bae13e06e70e.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.642\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 48d16278-7a91-4d47-b916-0b1360d784ab.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.644\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 4c03cf6c-0701-434b-a779-399d45ce051d.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.648\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 4c52e8fd-f13c-4a77-961a-7e69294d66e4.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.651\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.652\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.656\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 519d1e33-094f-4c75-a2a4-2aebac4455aa.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.657\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 54addd43-e63e-495d-9b59-fcd34768a072.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.659\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.662\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 56210846-cdf7-4cc4-9489-56e5946d27a4.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.666\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.667\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.668\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.670\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.673\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.677\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.680\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 70f5599a-60dd-4b2c-9f53-ecf07cc714a5.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.686\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.689\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 75d665c1-a17c-4882-9804-7574b221dd21.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.692\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.692\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file 7f6f0d3e-6892-497f-b571-57c5542830a4.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.694\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.696\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 811bf137-b832-4774-a0ba-42314e20fae2.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.698\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 89926c19-088e-49e8-9bf7-3dc947307dad.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.700\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 8d3e1857-b6c2-4b94-b757-798cf906675b.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.703\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.705\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 90683a89-d579-4d61-962c-0625189d0f5b.PDF get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.707\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 91df8902-e51d-4fe3-be8b-6af88d9e6a42.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.709\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.712\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 9356b36f-4e13-4733-9c77-dc8555add32e.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.714\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 99bababe-a109-413f-90e4-608f1eb9e293.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.716\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.719\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.723\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.724\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.726\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.731\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file a85e2454-2d4a-4813-bd81-1af1c54dcc9a.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.733\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file b1f83929-9b81-402f-8b37-13065b355db1.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.738\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.742\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file b514aa28-d6db-4360-87f9-0554233f4a86.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.744\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.747\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.748\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file c607919d-3678-491f-823f-d6c75818c995.png add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.753\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file c6282f11-1e0f-4213-9d54-9a5138f72441.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.754\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.755\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file cdc164de-ca0a-4f3d-9bde-3552f06ae74a.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.758\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.760\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.764\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d0be39b5-e383-4ffa-9a15-901af56e5baa.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.766\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file d0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.770\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d444e324-a0cd-4949-aac8-311cb8b591e7.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.772\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d757112f-9c32-4a9b-81e7-140f1d30e717.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.777\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file d80c66ae-b17d-4c47-840d-da293939f843.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.781\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file d9b8c26d-6e6d-48d3-a763-f8f45fd91477.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.784\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.788\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.791\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.794\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.797\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.801\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file e4f69236-845c-4fcb-abf9-9df336b0478f.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.802\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.805\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file eb362ff0-c953-4049-9810-0bfbffc1772c.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.809\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.812\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file ed611268-36c2-4c56-bee4-26f1005ddca2.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.814\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file f13c4fa7-0df6-4b48-816a-e11e55d5e9f0.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.816\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.824\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file f56eb768-41d9-4cdd-bbe6-4969cc51ed86.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.827\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.828\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.832\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file fa6f85ee-cd21-4eef-8805-939fbcd36712.pdf get split.\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.834\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m54\u001b[0m - \u001b[1mImage file fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg add to the output file list\u001b[0m\n", "\u001b[32m2024-10-10 02:26:51.838\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mbatch_split_pdf_files\u001b[0m:\u001b[36m50\u001b[0m - \u001b[1mPDF file fbae41f6-4864-419e-bed0-944c831570f2.pdf get split.\u001b[0m\n"]}, {"data": {"text/plain": ["['00dcaff3-051f-4064-ae19-254264e8e242.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/08b8af75-483d-4160-9fba-f75344f4ce4f-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/08b8af75-483d-4160-9fba-f75344f4ce4f-1.pdf',\n", " '0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg',\n", " '0ca22e7d-42bb-45b5-8ba0-212faba169ad.png',\n", " '0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg',\n", " '0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/1085fe16-0014-489d-bfab-6d005298074a-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/1085fe16-0014-489d-bfab-6d005298074a-1.pdf',\n", " '10ec75dd-e526-444f-895b-7faac9c0cd93.jpg',\n", " '145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg',\n", " '20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg',\n", " '204231ce-913e-405c-9132-20cfd100a14b.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/2836a967-2f4c-4655-bf35-c148e26d9f07-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/2e5efa44-3660-40df-9701-245d015f3771-0.pdf',\n", " '309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/30d78167-a823-44da-a272-a5b36acc66aa-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/37d281d0-903c-482d-aec6-a603544de1d6-0.pdf',\n", " '392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.pdf',\n", " '3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg',\n", " '4234dfb8-5e66-452a-88ba-d520698fb608.jpg',\n", " '426dd3f2-6ad7-4a93-a801-71647893c19d.jpg',\n", " '42c562c8-ad0a-4119-9b2a-a98702470659.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/452156dd-abc3-4c1d-b31c-fde48aadf60c-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4626aa97-e040-494f-9896-0469eeffd02f-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4626aa97-e040-494f-9896-0469eeffd02f-1.pdf',\n", " '46b70eea-f82b-4f9e-b453-9fbdc0104549.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/474466f5-70ca-4c63-83f8-bae13e06e70e-0.pdf',\n", " '48d16278-7a91-4d47-b916-0b1360d784ab.jpeg',\n", " '4c03cf6c-0701-434b-a779-399d45ce051d.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.pdf',\n", " '4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png',\n", " '50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg',\n", " '519d1e33-094f-4c75-a2a4-2aebac4455aa.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/54addd43-e63e-495d-9b59-fcd34768a072-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.pdf',\n", " '56210846-cdf7-4cc4-9489-56e5946d27a4.jpg',\n", " '5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg',\n", " '65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png',\n", " '67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.pdf',\n", " '6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg',\n", " '6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.pdf',\n", " '728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg',\n", " '75d665c1-a17c-4882-9804-7574b221dd21.png',\n", " '79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png',\n", " '7f6f0d3e-6892-497f-b571-57c5542830a4.png',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/811bf137-b832-4774-a0ba-42314e20fae2-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/89926c19-088e-49e8-9bf7-3dc947307dad-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/8d3e1857-b6c2-4b94-b757-798cf906675b-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/90683a89-d579-4d61-962c-0625189d0f5b-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/9356b36f-4e13-4733-9c77-dc8555add32e-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/99bababe-a109-413f-90e4-608f1eb9e293-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.pdf',\n", " 'a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg',\n", " 'a37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg',\n", " 'a6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg',\n", " 'a7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-1.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-2.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-3.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b1f83929-9b81-402f-8b37-13065b355db1-4.pdf',\n", " 'b2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/b514aa28-d6db-4360-87f9-0554233f4a86-0.pdf',\n", " 'b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg',\n", " 'ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png',\n", " 'bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png',\n", " 'c607919d-3678-491f-823f-d6c75818c995.png',\n", " 'c6282f11-1e0f-4213-9d54-9a5138f72441.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.pdf',\n", " 'ce810450-7f72-490b-9e09-c3d5ae1dad88.jpg',\n", " 'cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d0be39b5-e383-4ffa-9a15-901af56e5baa-0.pdf',\n", " 'd0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d444e324-a0cd-4949-aac8-311cb8b591e7-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d757112f-9c32-4a9b-81e7-140f1d30e717-0.pdf',\n", " 'd80c66ae-b17d-4c47-840d-da293939f843.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.pdf',\n", " 'dbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg',\n", " 'e10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg',\n", " 'e23da3d2-dfda-43d2-977e-b19ef997f205.jpeg',\n", " 'e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg',\n", " 'e4efb75c-f421-4f10-aed9-61e6004938c6.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/e4f69236-845c-4fcb-abf9-9df336b0478f-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.pdf',\n", " 'eb362ff0-c953-4049-9810-0bfbffc1772c.jpg',\n", " 'ebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/ed611268-36c2-4c56-bee4-26f1005ddca2-0.pdf',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.pdf',\n", " 'f43fba4c-2325-4151-8cfc-263fd2f04d11.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.pdf',\n", " 'f5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg',\n", " 'f8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/fa6f85ee-cd21-4eef-8805-939fbcd36712-0.pdf',\n", " 'fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg',\n", " '../../data/OCR_in_house/samples/100_samples_DI_split_pages/fbae41f6-4864-419e-bed0-944c831570f2-0.pdf']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["## LOG - preprocess pages at the same time\n", "# batch_split_pdf_files(\"../../data/OCR_in_house/samples/100_samples_DI/\", \"../../data/OCR_in_house/samples/100_samples_DI_split_pages/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# test api connection to azure document intelligence"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# import libraries\n", "import os\n", "from dotenv import load_dotenv\n", "from loguru import logger\n", "\n", "load_dotenv()\n", "from azure.ai.formrecognizer import DocumentAnalysisClient\n", "from azure.core.credentials import AzureKeyCredential\n", "from azure.core.exceptions import HttpResponseError\n", "\n", "# set `<your-endpoint>` and `<your-key>` variables with the values from the Azure portal\n", "endpoint = os.getenv(\"AZURE_DOCUMENT_INTELLIGENCE_ENDPONT\")\n", "key = os.getenv(\"AZURE_DOCUMENT_INTELLIGENCE_KEY\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["document_analysis_client = DocumentAnalysisClient(\n", "        endpoint=endpoint, credential=AzureKeyCredential(key)\n", "    )"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import glob\n", "import time\n", "\n", "\n", "DATA_FOLDER = \"../../data/samples/test_non_split_DI/\"\n", "MAX_RETRY = 3\n", "\n", "ans = []"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["doc_path_list = sorted(os.listdir(DATA_FOLDER))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'../../data/samples/test_non_split_DI/5c00e2bb-eb9d-487f-9f5b-c28612a7bbf1.pdf'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["## Test a file\n", "file_path = os.path.join(DATA_FOLDER ,doc_path_list[0])\n", "file_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Try running a file on DI\n", "with open(file_path, \"rb\") as f:\n", "    poller = document_analysis_client.begin_analyze_document(model_id=\"prebuilt-invoice\", document=f)\n", "    invoices = poller.result()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["ans.append({\n", "        \"file_path\": file_path,\n", "        \"invoice\": invoices.to_dict(),\n", "        })"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["OUPUT_DATA_FOLDER = \"../../data/samples/test_non_split_DI_res/\" \n", "if not Path(OUPUT_DATA_FOLDER).exists():\n", "    os.makedirs(OUPUT_DATA_FOLDER)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["import json\n", "import pickle as pk\n", "\n", "for file_path in sorted(os.listdir(DATA_FOLDER))[len(ans):]:\n", "    retry_num = 0\n", "\n", "    file_stem = str(Path(file_path).stem)\n", "    while retry_num < MAX_RETRY:\n", "        logger.info(f\"Precessing {file_path}... Retried {retry_num} times\")\n", "        try:\n", "            with open(os.path.join(DATA_FOLDER, file_path), \"rb\") as f:\n", "                poller = document_analysis_client.begin_analyze_document(model_id=\"prebuilt-invoice\", document=f)\n", "                invoices = poller.result()\n", "                invoice_dict = invoices.to_dict()\n", "                ans.append(\n", "                    {\n", "                        \"file_path\": file_path,\n", "                        \"invoice\": invoice_dict,\n", "                        }\n", "                    )\n", "                time.sleep(5)\n", "\n", "                # dumpt to pk file\n", "                with open(os.path.join(OUPUT_DATA_FOLDER, f\"{file_stem}.pk\"), \"wb\") as fout:\n", "                    pk.dump(invoice_dict, fout)\n", "                # dumpt to json file\n", "                with open(os.path.join(OUPUT_DATA_FOLDER, f\"{file_stem}.json\"), \"w\") as fout:\n", "                    json.dump(invoice_dict, fout, indent=4, default=str)\n", "\n", "                break\n", "        except HttpResponseError as hre:\n", "            logger.exception(hre)\n", "            retry_num += 1\n", "            time.sleep(5)\n", "        except Exception as e:\n", "            logger.exception(e)\n", "            break\n", "\n", "    if ans and ans[-1][\"file_path\"] == file_path:\n", "        # process succeed\n", "        logger.info(f\"file {file_path} processed SUCCESS\")\n", "    else:\n", "        # process failed\n", "        logger.exception(f\"file {file_path} processed FAIL\")\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# dumpt to pk file\n", "with open(os.path.join(OUPUT_DATA_FOLDER, \"ans.pk\"), \"wb\") as fout:\n", "    pk.dump(ans, fout)\n", "# dumpt to json file\n", "with open(os.path.join(OUPUT_DATA_FOLDER, \"ans.json\"), \"w\") as fout:\n", "    json.dump(ans, fout, indent=4, default=str)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# some test result. DO NOT OVERRIDE."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["AnalyzeResult(api_version=2023-07-31, model_id=prebuilt-invoice, content=Whitfords\n", "2 Banks Ave\n", "Hillarys WA 6025\n", "Ph: 08 9404 1133\n", "E: <EMAIL>\n", "Tax Invoice\n", "<PERSON>\n", "Customer ID 172386\n", "21 Aspendale Place\n", "Date\n", "13/07/23\n", "Hillarys WA 6025\n", "Reference\n", "Invoice No\n", "********\n", "Page 1 of 1\n", "Patient\n", "Service Provided\n", "Claimed\n", "Quantity\n", "Amount\n", "<PERSON> (ID:385654)\n", "10/07/23 Consultation- Complex\n", "✓\n", "1\n", "$ 0.00\n", "Flusapex 100ml\n", "✓\n", "1\n", "$ 132.28\n", "You have been given a discount of: $ 156.00\n", "TOTAL\n", "$ 132.28\n", "Total includes GST\n", "$ 12.03\n", "Balance Owing\n", "$ 83.48 :selected: :selected:, languages=[], pages=[DocumentPage(page_number=1, angle=None, width=8.2639, height=11.6944, unit=inch, lines=[DocumentLine(content=Whitfords, polygon=[Point(x=4.9035, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.604), Point(x=4.9035, y=0.5989)], spans=[DocumentSpan(offset=0, length=9)]), DocumentLine(content=2 Banks Ave, polygon=[Point(x=4.9035, y=0.6548), Point(x=5.9746, y=0.6598), Point(x=5.9746, y=0.8223), Point(x=4.9035, y=0.8172)], spans=[DocumentSpan(offset=10, length=11)]), DocumentLine(content=Hillarys WA 6025, polygon=[Point(x=4.9035, y=0.8324), Point(x=6.3857, y=0.8273), Point(x=6.3908, y=1.005), Point(x=4.9035, y=1.0101)], spans=[DocumentSpan(offset=22, length=16)]), DocumentLine(content=Ph: 08 9404 1133, polygon=[Point(x=4.9035, y=1.0253), Point(x=6.2791, y=1.0253), Point(x=6.2791, y=1.1928), Point(x=4.9086, y=1.1928)], spans=[DocumentSpan(offset=39, length=16)]), DocumentLine(content=E: <EMAIL>, polygon=[Point(x=4.9035, y=1.2131), Point(x=7.2487, y=1.2232), Point(x=7.2487, y=1.3806), Point(x=4.9035, y=1.3755)], spans=[DocumentSpan(offset=56, length=27)]), DocumentLine(content=Tax Invoice, polygon=[Point(x=5.6091, y=2.1978), Point(x=7.2182, y=2.1978), Point(x=7.2182, y=2.4313), Point(x=5.6091, y=2.4363)], spans=[DocumentSpan(offset=84, length=11)]), DocumentLine(content=Kevin Jones, polygon=[Point(x=1.3198, y=2.7257), Point(x=2.132, y=2.7307), Point(x=2.132, y=2.883), Point(x=1.3198, y=2.8779)], spans=[DocumentSpan(offset=96, length=11)]), DocumentLine(content=Customer ID 172386, polygon=[Point(x=5.7664, y=2.6851), Point(x=7.3045, y=2.6851), Point(x=7.3045, y=2.8576), Point(x=5.7664, y=2.8576)], spans=[DocumentSpan(offset=108, length=18)]), DocumentLine(content=21 Aspendale Place, polygon=[Point(x=1.2792, y=2.8982), Point(x=2.6548, y=2.8982), Point(x=2.6548, y=3.0607), Point(x=1.2792, y=3.0607)], spans=[DocumentSpan(offset=127, length=18)]), DocumentLine(content=Date, polygon=[Point(x=5.7715, y=2.9033), Point(x=6.1116, y=2.9033), Point(x=6.1116, y=3.0302), Point(x=5.7766, y=3.0302)], spans=[DocumentSpan(offset=146, length=4)]), DocumentLine(content=13/07/23, polygon=[Point(x=6.7614, y=2.8729), Point(x=7.3959, y=2.8779), Point(x=7.3959, y=3.0505), Point(x=6.7614, y=3.0454)], spans=[DocumentSpan(offset=151, length=8)]), DocumentLine(content=Hillarys WA 6025, polygon=[Point(x=1.2792, y=3.0657), Point(x=2.4771, y=3.0607), Point(x=2.4822, y=3.218), Point(x=1.2792, y=3.2231)], spans=[DocumentSpan(offset=160, length=16)]), DocumentLine(content=Reference, polygon=[Point(x=5.7766, y=3.0911), Point(x=6.4822, y=3.0911), Point(x=6.4822, y=3.2282), Point(x=5.7766, y=3.2282)], spans=[DocumentSpan(offset=177, length=9)]), DocumentLine(content=Invoice No, polygon=[Point(x=5.7715, y=3.284), Point(x=6.5076, y=3.284), Point(x=6.5076, y=3.4261), Point(x=5.7715, y=3.4261)], spans=[DocumentSpan(offset=187, length=10)]), DocumentLine(content=********, polygon=[Point(x=6.7614, y=3.284), Point(x=7.4872, y=3.284), Point(x=7.4872, y=3.4261), Point(x=6.7614, y=3.421)], spans=[DocumentSpan(offset=198, length=8)]), DocumentLine(content=Page 1 of 1, polygon=[Point(x=6.7614, y=3.4769), Point(x=7.5583, y=3.4769), Point(x=7.5532, y=3.6342), Point(x=6.7614, y=3.6342)], spans=[DocumentSpan(offset=207, length=11)]), DocumentLine(content=Patient, polygon=[Point(x=0.4518, y=3.9895), Point(x=1.0508, y=3.9895), Point(x=1.0508, y=4.1367), Point(x=0.4518, y=4.1367)], spans=[DocumentSpan(offset=219, length=7)]), DocumentLine(content=Service Provided, polygon=[Point(x=1.4619, y=3.9844), Point(x=2.7969, y=3.9844), Point(x=2.7969, y=4.1367), Point(x=1.4619, y=4.1367)], spans=[DocumentSpan(offset=227, length=16)]), DocumentLine(content=Claimed, polygon=[Point(x=5.2588, y=3.9997), Point(x=5.9035, y=3.9997), Point(x=5.9035, y=4.157), Point(x=5.2588, y=4.1519)], spans=[DocumentSpan(offset=244, length=7)]), DocumentLine(content=Quantity, polygon=[Point(x=6.2233, y=3.9844), Point(x=6.939, y=3.9895), Point(x=6.939, y=4.157), Point(x=6.2233, y=4.1469)], spans=[DocumentSpan(offset=252, length=8)]), DocumentLine(content=Amount, polygon=[Point(x=7.1471, y=3.9895), Point(x=7.7817, y=3.9895), Point(x=7.7817, y=4.1367), Point(x=7.1471, y=4.1367)], spans=[DocumentSpan(offset=261, length=6)]), DocumentLine(content=Freddie (ID:385654), polygon=[Point(x=0.467, y=4.2788), Point(x=2.0863, y=4.2788), Point(x=2.0863, y=4.4463), Point(x=0.467, y=4.4412)], spans=[DocumentSpan(offset=268, length=19)]), DocumentLine(content=10/07/23 Consultation- Complex, polygon=[Point(x=0.6903, y=4.4565), Point(x=3.0507, y=4.4616), Point(x=3.0507, y=4.624), Point(x=0.6903, y=4.6138)], spans=[DocumentSpan(offset=288, length=30)]), DocumentLine(content=✓, polygon=[Point(x=5.5208, y=4.5079), Point(x=5.5926, y=4.5079), Point(x=5.5926, y=4.5832), Point(x=5.5208, y=4.5832)], spans=[DocumentSpan(offset=319, length=1)]), DocumentLine(content=1, polygon=[Point(x=6.4416, y=4.4666), Point(x=6.5228, y=4.4666), Point(x=6.5228, y=4.5986), Point(x=6.4416, y=4.5935)], spans=[DocumentSpan(offset=321, length=1)]), DocumentLine(content=$ 0.00, polygon=[Point(x=7.3147, y=4.4616), Point(x=7.7817, y=4.4616), Point(x=7.7817, y=4.6138), Point(x=7.3147, y=4.6138)], spans=[DocumentSpan(offset=323, length=6)]), DocumentLine(content=Flusapex 100ml, polygon=[Point(x=1.4518, y=4.9082), Point(x=2.5939, y=4.9031), Point(x=2.5939, y=5.0808), Point(x=1.4518, y=5.0859)], spans=[DocumentSpan(offset=330, length=14)]), DocumentLine(content=✓, polygon=[Point(x=5.5208, y=4.9662), Point(x=5.5926, y=4.9662), Point(x=5.5926, y=5.0415), Point(x=5.5208, y=5.0415)], spans=[DocumentSpan(offset=345, length=1)]), DocumentLine(content=1, polygon=[Point(x=6.4466, y=4.9285), Point(x=6.5177, y=4.9234), Point(x=6.5177, y=5.0554), Point(x=6.4517, y=5.0503)], spans=[DocumentSpan(offset=347, length=1)]), DocumentLine(content=$ 132.28, polygon=[Point(x=7.1471, y=4.9234), Point(x=7.7817, y=4.9184), Point(x=7.7817, y=5.0656), Point(x=7.1471, y=5.0706)], spans=[DocumentSpan(offset=349, length=8)]), DocumentLine(content=You have been given a discount of: $ 156.00, polygon=[Point(x=0.4975, y=5.4462), Point(x=3.7106, y=5.4412), Point(x=3.7106, y=5.6137), Point(x=0.4975, y=5.6137)], spans=[DocumentSpan(offset=358, length=43)]), DocumentLine(content=TOTAL, polygon=[Point(x=6.0761, y=5.4462), Point(x=6.5989, y=5.4462), Point(x=6.5989, y=5.5985), Point(x=6.0761, y=5.5934)], spans=[DocumentSpan(offset=402, length=5)]), DocumentLine(content=$ 132.28, polygon=[Point(x=7.0761, y=5.4412), Point(x=7.7715, y=5.4361), Point(x=7.7766, y=5.6087), Point(x=7.0761, y=5.6087)], spans=[DocumentSpan(offset=408, length=8)]), DocumentLine(content=Total includes GST, polygon=[Point(x=5.1116, y=5.634), Point(x=6.6192, y=5.629), Point(x=6.6192, y=5.8066), Point(x=5.1116, y=5.8117)], spans=[DocumentSpan(offset=417, length=18)]), DocumentLine(content=$ 12.03, polygon=[Point(x=7.2284, y=5.6493), Point(x=7.7715, y=5.6493), Point(x=7.7715, y=5.8015), Point(x=7.2284, y=5.8015)], spans=[DocumentSpan(offset=436, length=7)]), DocumentLine(content=Balance Owing, polygon=[Point(x=5.4314, y=5.8472), Point(x=6.604, y=5.8523), Point(x=6.5989, y=6.0198), Point(x=5.4314, y=6.0096)], spans=[DocumentSpan(offset=444, length=13)]), DocumentLine(content=$ 83.48, polygon=[Point(x=7.1827, y=5.8523), Point(x=7.7867, y=5.8472), Point(x=7.7867, y=6.0046), Point(x=7.1827, y=6.0046)], spans=[DocumentSpan(offset=458, length=7)])], words=[DocumentWord(content=Whitfords, polygon=[Point(x=4.9086, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.6091), Point(x=4.9086, y=0.6091)], span=DocumentSpan(offset=0, length=9), confidence=0.995), DocumentWord(content=2, polygon=[Point(x=4.9137, y=0.6598), Point(x=5.0101, y=0.6598), Point(x=5.0152, y=0.8223), Point(x=4.9238, y=0.8223)], span=DocumentSpan(offset=10, length=1), confidence=0.965), DocumentWord(content=Banks, polygon=[Point(x=5.071, y=0.6598), Point(x=5.5837, y=0.6598), Point(x=5.5888, y=0.8172), Point(x=5.0761, y=0.8223)], span=DocumentSpan(offset=12, length=5), confidence=0.995), DocumentWord(content=Ave, polygon=[Point(x=5.6599, y=0.6598), Point(x=5.9492, y=0.6649), Point(x=5.9492, y=0.8223), Point(x=5.6599, y=0.8172)], span=DocumentSpan(offset=18, length=3), confidence=0.996), DocumentWord(content=Hillarys, polygon=[Point(x=4.9035, y=0.8324), Point(x=5.5583, y=0.8426), Point(x=5.5634, y=1.0101), Point(x=4.9086, y=1.0151)], span=DocumentSpan(offset=22, length=8), confidence=0.993), DocumentWord(content=WA, polygon=[Point(x=5.6192, y=0.8426), Point(x=5.8781, y=0.8426), Point(x=5.8832, y=1.0101), Point(x=5.6192, y=1.0101)], span=DocumentSpan(offset=31, length=2), confidence=0.996), DocumentWord(content=6025, polygon=[Point(x=5.9593, y=0.8375), Point(x=6.3654, y=0.8324), Point(x=6.3705, y=1.0101), Point(x=5.9593, y=1.0101)], span=DocumentSpan(offset=34, length=4), confidence=0.993), DocumentWord(content=Ph:, polygon=[Point(x=4.9086, y=1.0304), Point(x=5.1827, y=1.0354), Point(x=5.1878, y=1.1928), Point(x=4.9137, y=1.1979)], span=DocumentSpan(offset=39, length=3), confidence=0.996), DocumentWord(content=08, polygon=[Point(x=5.2132, y=1.0354), Point(x=5.401, y=1.0405), Point(x=5.401, y=1.1928), Point(x=5.2182, y=1.1928)], span=DocumentSpan(offset=43, length=2), confidence=0.995), DocumentWord(content=9404, polygon=[Point(x=5.4619, y=1.0405), Point(x=5.8324, y=1.0405), Point(x=5.8324, y=1.1928), Point(x=5.4619, y=1.1928)], span=DocumentSpan(offset=46, length=4), confidence=0.989), DocumentWord(content=1133, polygon=[Point(x=5.9035, y=1.0405), Point(x=6.2791, y=1.0304), Point(x=6.2791, y=1.1928), Point(x=5.9086, y=1.1928)], span=DocumentSpan(offset=51, length=4), confidence=0.993), DocumentWord(content=E:, polygon=[Point(x=4.9086, y=1.2131), Point(x=5.071, y=1.2182), Point(x=5.0761, y=1.3806), Point(x=4.9137, y=1.3806)], span=DocumentSpan(offset=56, length=2), confidence=0.995), DocumentWord(content=<EMAIL>, polygon=[Point(x=5.1066, y=1.2182), Point(x=7.2284, y=1.2232), Point(x=7.2334, y=1.3857), Point(x=5.1066, y=1.3806)], span=DocumentSpan(offset=59, length=24), confidence=0.948), DocumentWord(content=Tax, polygon=[Point(x=5.6497, y=2.2029), Point(x=6.1065, y=2.2079), Point(x=6.0964, y=2.4363), Point(x=5.6345, y=2.4363)], span=DocumentSpan(offset=84, length=3), confidence=0.995), DocumentWord(content=Invoice, polygon=[Point(x=6.2131, y=2.2079), Point(x=7.1827, y=2.1978), Point(x=7.1878, y=2.4363), Point(x=6.203, y=2.4363)], span=DocumentSpan(offset=88, length=7), confidence=0.969), DocumentWord(content=Kevin, polygon=[Point(x=1.3249, y=2.7307), Point(x=1.6903, y=2.7307), Point(x=1.6954, y=2.883), Point(x=1.3249, y=2.883)], span=DocumentSpan(offset=96, length=5), confidence=0.995), DocumentWord(content=Jones, polygon=[Point(x=1.7411, y=2.7307), Point(x=2.1269, y=2.7358), Point(x=2.1269, y=2.8881), Point(x=1.7462, y=2.883)], span=DocumentSpan(offset=102, length=5), confidence=0.993), DocumentWord(content=Customer, polygon=[Point(x=5.7715, y=2.6901), Point(x=6.4365, y=2.6901), Point(x=6.4365, y=2.8576), Point(x=5.7664, y=2.8576)], span=DocumentSpan(offset=108, length=8), confidence=0.992), DocumentWord(content=ID, polygon=[Point(x=6.472, y=2.6901), Point(x=6.6192, y=2.6901), Point(x=6.6192, y=2.8576), Point(x=6.472, y=2.8576)], span=DocumentSpan(offset=117, length=2), confidence=0.995), DocumentWord(content=172386, polygon=[Point(x=6.7867, y=2.6901), Point(x=7.2994, y=2.6901), Point(x=7.2994, y=2.8627), Point(x=6.7867, y=2.8576)], span=DocumentSpan(offset=120, length=6), confidence=0.995), DocumentWord(content=21, polygon=[Point(x=1.2893, y=2.9033), Point(x=1.4619, y=2.9033), Point(x=1.467, y=3.0607), Point(x=1.2893, y=3.0607)], span=DocumentSpan(offset=127, length=2), confidence=0.995), DocumentWord(content=Aspendale, polygon=[Point(x=1.5025, y=2.9084), Point(x=2.2335, y=2.9033), Point(x=2.2335, y=3.0607), Point(x=1.5076, y=3.0607)], span=DocumentSpan(offset=130, length=9), confidence=0.994), DocumentWord(content=Place, polygon=[Point(x=2.2741, y=2.9033), Point(x=2.6446, y=2.8982), Point(x=2.6446, y=3.0657), Point(x=2.2741, y=3.0607)], span=DocumentSpan(offset=140, length=5), confidence=0.993), DocumentWord(content=Date, polygon=[Point(x=5.7766, y=2.9033), Point(x=6.0913, y=2.9033), Point(x=6.0913, y=3.0302), Point(x=5.7766, y=3.0302)], span=DocumentSpan(offset=146, length=4), confidence=0.993), DocumentWord(content=13/07/23, polygon=[Point(x=6.7715, y=2.8779), Point(x=7.3959, y=2.8779), Point(x=7.3959, y=3.0556), Point(x=6.7715, y=3.0505)], span=DocumentSpan(offset=151, length=8), confidence=0.995), DocumentWord(content=Hillarys, polygon=[Point(x=1.2843, y=3.0657), Point(x=1.8122, y=3.0708), Point(x=1.8122, y=3.2231), Point(x=1.2843, y=3.2282)], span=DocumentSpan(offset=160, length=8), confidence=0.994), DocumentWord(content=WA, polygon=[Point(x=1.8426, y=3.0708), Point(x=2.066, y=3.0708), Point(x=2.066, y=3.2231), Point(x=1.8426, y=3.2231)], span=DocumentSpan(offset=169, length=2), confidence=0.995), DocumentWord(content=6025, polygon=[Point(x=2.1167, y=3.0708), Point(x=2.4721, y=3.0657), Point(x=2.4721, y=3.2231), Point(x=2.1167, y=3.2231)], span=DocumentSpan(offset=172, length=4), confidence=0.993), DocumentWord(content=Reference, polygon=[Point(x=5.7766, y=3.0962), Point(x=6.472, y=3.1013), Point(x=6.472, y=3.2282), Point(x=5.7817, y=3.2332)], span=DocumentSpan(offset=177, length=9), confidence=0.994), DocumentWord(content=Invoice, polygon=[Point(x=5.7715, y=3.2891), Point(x=6.2538, y=3.2891), Point(x=6.2538, y=3.4312), Point(x=5.7766, y=3.4312)], span=DocumentSpan(offset=187, length=7), confidence=0.993), DocumentWord(content=No, polygon=[Point(x=6.2994, y=3.2891), Point(x=6.4873, y=3.2891), Point(x=6.4923, y=3.4312), Point(x=6.3045, y=3.4312)], span=DocumentSpan(offset=195, length=2), confidence=0.995), DocumentWord(content=********, polygon=[Point(x=6.7817, y=3.284), Point(x=7.472, y=3.2891), Point(x=7.472, y=3.4312), Point(x=6.7817, y=3.4261)], span=DocumentSpan(offset=198, length=8), confidence=0.995), DocumentWord(content=Page, polygon=[Point(x=6.7614, y=3.4819), Point(x=7.1116, y=3.4819), Point(x=7.1167, y=3.6393), Point(x=6.7664, y=3.6342)], span=DocumentSpan(offset=207, length=4), confidence=0.993), DocumentWord(content=1, polygon=[Point(x=7.1624, y=3.4819), Point(x=7.2487, y=3.4819), Point(x=7.2487, y=3.6393), Point(x=7.1675, y=3.6393)], span=DocumentSpan(offset=212, length=1), confidence=0.951), DocumentWord(content=of, polygon=[Point(x=7.2893, y=3.4819), Point(x=7.4416, y=3.4819), Point(x=7.4466, y=3.6393), Point(x=7.2893, y=3.6393)], span=DocumentSpan(offset=214, length=2), confidence=0.995), DocumentWord(content=1, polygon=[Point(x=7.472, y=3.4819), Point(x=7.5482, y=3.4769), Point(x=7.5532, y=3.6393), Point(x=7.4771, y=3.6393)], span=DocumentSpan(offset=217, length=1), confidence=0.989), DocumentWord(content=Patient, polygon=[Point(x=0.4721, y=3.9997), Point(x=1.0457, y=3.9946), Point(x=1.0508, y=4.1418), Point(x=0.4772, y=4.1418)], span=DocumentSpan(offset=219, length=7), confidence=0.995), DocumentWord(content=Service, polygon=[Point(x=1.4721, y=3.9895), Point(x=2.0304, y=3.9895), Point(x=2.0304, y=4.1367), Point(x=1.4771, y=4.1367)], span=DocumentSpan(offset=227, length=7), confidence=0.993), DocumentWord(content=Provided, polygon=[Point(x=2.0964, y=3.9895), Point(x=2.7715, y=3.9844), Point(x=2.7766, y=4.1418), Point(x=2.1015, y=4.1367)], span=DocumentSpan(offset=235, length=8), confidence=0.995), DocumentWord(content=Claimed, polygon=[Point(x=5.269, y=4.0047), Point(x=5.8832, y=4.0047), Point(x=5.8832, y=4.157), Point(x=5.2741, y=4.1519)], span=DocumentSpan(offset=244, length=7), confidence=0.995), DocumentWord(content=Quantity, polygon=[Point(x=6.2334, y=3.9895), Point(x=6.9187, y=3.9946), Point(x=6.9136, y=4.157), Point(x=6.2334, y=4.1519)], span=DocumentSpan(offset=252, length=8), confidence=0.995), DocumentWord(content=Amount, polygon=[Point(x=7.1624, y=3.9946), Point(x=7.7817, y=3.9946), Point(x=7.7817, y=4.1418), Point(x=7.1624, y=4.1367)], span=DocumentSpan(offset=261, length=6), confidence=0.995), DocumentWord(content=Freddie, polygon=[Point(x=0.467, y=4.2839), Point(x=1.0508, y=4.2839), Point(x=1.0508, y=4.4463), Point(x=0.4721, y=4.4463)], span=DocumentSpan(offset=268, length=7), confidence=0.994), DocumentWord(content=(ID:385654), polygon=[Point(x=1.1574, y=4.2839), Point(x=2.0812, y=4.289), Point(x=2.0812, y=4.4412), Point(x=1.1574, y=4.4463)], span=DocumentSpan(offset=276, length=11), confidence=0.98), DocumentWord(content=10/07/23, polygon=[Point(x=0.7005, y=4.4616), Point(x=1.3096, y=4.4616), Point(x=1.3147, y=4.6138), Point(x=0.7056, y=4.6138)], span=DocumentSpan(offset=288, length=8), confidence=0.994), DocumentWord(content=Consultation-, polygon=[Point(x=1.4619, y=4.4616), Point(x=2.401, y=4.4616), Point(x=2.4061, y=4.6189), Point(x=1.467, y=4.6138)], span=DocumentSpan(offset=297, length=13), confidence=0.988), DocumentWord(content=Complex, polygon=[Point(x=2.4315, y=4.4616), Point(x=3.0558, y=4.4616), Point(x=3.0558, y=4.6291), Point(x=2.4365, y=4.624)], span=DocumentSpan(offset=311, length=7), confidence=0.995), DocumentWord(content=✓, polygon=[Point(x=5.5208, y=4.5079), Point(x=5.5926, y=4.5079), Point(x=5.5926, y=4.5832), Point(x=5.5208, y=4.5832)], span=DocumentSpan(offset=319, length=1), confidence=1.0), DocumentWord(content=1, polygon=[Point(x=6.4466, y=4.4666), Point(x=6.5228, y=4.4666), Point(x=6.5177, y=4.5986), Point(x=6.4416, y=4.5935)], span=DocumentSpan(offset=321, length=1), confidence=0.975), DocumentWord(content=$, polygon=[Point(x=7.3248, y=4.4616), Point(x=7.4162, y=4.4616), Point(x=7.4213, y=4.6189), Point(x=7.3299, y=4.6189)], span=DocumentSpan(offset=323, length=1), confidence=0.948), DocumentWord(content=0.00, polygon=[Point(x=7.4568, y=4.4616), Point(x=7.7715, y=4.4616), Point(x=7.7715, y=4.6189), Point(x=7.4619, y=4.6189)], span=DocumentSpan(offset=325, length=4), confidence=0.993), DocumentWord(content=Flusapex, polygon=[Point(x=1.4568, y=4.9133), Point(x=2.0964, y=4.9082), Point(x=2.0964, y=5.0808), Point(x=1.4568, y=5.0859)], span=DocumentSpan(offset=330, length=8), confidence=0.994), DocumentWord(content=100ml, polygon=[Point(x=2.1421, y=4.9082), Point(x=2.5939, y=4.9082), Point(x=2.5939, y=5.0859), Point(x=2.1421, y=5.0808)], span=DocumentSpan(offset=339, length=5), confidence=0.995), DocumentWord(content=✓, polygon=[Point(x=5.5208, y=4.9662), Point(x=5.5926, y=4.9662), Point(x=5.5926, y=5.0415), Point(x=5.5208, y=5.0415)], span=DocumentSpan(offset=345, length=1), confidence=1.0), DocumentWord(content=1, polygon=[Point(x=6.4517, y=4.9234), Point(x=6.5177, y=4.9234), Point(x=6.5177, y=5.0554), Point(x=6.4517, y=5.0554)], span=DocumentSpan(offset=347, length=1), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.1573, y=4.9285), Point(x=7.2334, y=4.9285), Point(x=7.2385, y=5.0706), Point(x=7.1624, y=5.0706)], span=DocumentSpan(offset=349, length=1), confidence=0.961), DocumentWord(content=132.28, polygon=[Point(x=7.2944, y=4.9285), Point(x=7.7664, y=4.9184), Point(x=7.7664, y=5.0706), Point(x=7.2944, y=5.0706)], span=DocumentSpan(offset=351, length=6), confidence=0.992), DocumentWord(content=You, polygon=[Point(x=0.5076, y=5.4513), Point(x=0.7614, y=5.4513), Point(x=0.7614, y=5.6137), Point(x=0.5076, y=5.6087)], span=DocumentSpan(offset=358, length=3), confidence=0.996), DocumentWord(content=have, polygon=[Point(x=0.7919, y=5.4513), Point(x=1.1421, y=5.4462), Point(x=1.1472, y=5.6137), Point(x=0.7969, y=5.6137)], span=DocumentSpan(offset=362, length=4), confidence=0.993), DocumentWord(content=been, polygon=[Point(x=1.1777, y=5.4462), Point(x=1.5279, y=5.4462), Point(x=1.5279, y=5.6188), Point(x=1.1777, y=5.6137)], span=DocumentSpan(offset=367, length=4), confidence=0.987), DocumentWord(content=given, polygon=[Point(x=1.5837, y=5.4462), Point(x=1.9543, y=5.4462), Point(x=1.9594, y=5.6188), Point(x=1.5837, y=5.6188)], span=DocumentSpan(offset=372, length=5), confidence=0.995), DocumentWord(content=a, polygon=[Point(x=2.0101, y=5.4462), Point(x=2.0964, y=5.4462), Point(x=2.1015, y=5.6188), Point(x=2.0101, y=5.6188)], span=DocumentSpan(offset=378, length=1), confidence=0.995), DocumentWord(content=discount, polygon=[Point(x=2.132, y=5.4462), Point(x=2.736, y=5.4462), Point(x=2.736, y=5.6188), Point(x=2.132, y=5.6188)], span=DocumentSpan(offset=380, length=8), confidence=0.993), DocumentWord(content=of:, polygon=[Point(x=2.7665, y=5.4462), Point(x=2.9847, y=5.4462), Point(x=2.9898, y=5.6137), Point(x=2.7665, y=5.6188)], span=DocumentSpan(offset=389, length=3), confidence=0.993), DocumentWord(content=$, polygon=[Point(x=3.0203, y=5.4462), Point(x=3.1167, y=5.4462), Point(x=3.1167, y=5.6137), Point(x=3.0203, y=5.6137)], span=DocumentSpan(offset=393, length=1), confidence=0.948), DocumentWord(content=156.00, polygon=[Point(x=3.1827, y=5.4462), Point(x=3.7005, y=5.4462), Point(x=3.7005, y=5.6087), Point(x=3.1827, y=5.6137)], span=DocumentSpan(offset=395, length=6), confidence=0.995), DocumentWord(content=TOTAL, polygon=[Point(x=6.1015, y=5.4564), Point(x=6.5837, y=5.4513), Point(x=6.5837, y=5.6036), Point(x=6.1015, y=5.5985)], span=DocumentSpan(offset=402, length=5), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.0913, y=5.4462), Point(x=7.1776, y=5.4412), Point(x=7.1827, y=5.6137), Point(x=7.0913, y=5.6137)], span=DocumentSpan(offset=408, length=1), confidence=0.993), DocumentWord(content=132.28, polygon=[Point(x=7.2487, y=5.4412), Point(x=7.7715, y=5.4412), Point(x=7.7766, y=5.6137), Point(x=7.2487, y=5.6137)], span=DocumentSpan(offset=410, length=6), confidence=0.995), DocumentWord(content=Total, polygon=[Point(x=5.137, y=5.6391), Point(x=5.5279, y=5.6391), Point(x=5.5279, y=5.8117), Point(x=5.137, y=5.8117)], span=DocumentSpan(offset=417, length=5), confidence=0.995), DocumentWord(content=includes, polygon=[Point(x=5.5634, y=5.6391), Point(x=6.2436, y=5.634), Point(x=6.2385, y=5.8117), Point(x=5.5583, y=5.8117)], span=DocumentSpan(offset=423, length=8), confidence=0.991), DocumentWord(content=GST, polygon=[Point(x=6.2893, y=5.634), Point(x=6.5989, y=5.634), Point(x=6.5989, y=5.8117), Point(x=6.2842, y=5.8117)], span=DocumentSpan(offset=432, length=3), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.2385, y=5.6594), Point(x=7.3248, y=5.6543), Point(x=7.3299, y=5.8066), Point(x=7.2436, y=5.8066)], span=DocumentSpan(offset=436, length=1), confidence=0.95), DocumentWord(content=12.03, polygon=[Point(x=7.3857, y=5.6543), Point(x=7.7715, y=5.6493), Point(x=7.7715, y=5.8066), Point(x=7.3908, y=5.8066)], span=DocumentSpan(offset=438, length=5), confidence=0.995), DocumentWord(content=Balance, polygon=[Point(x=5.4365, y=5.8523), Point(x=6.0456, y=5.8523), Point(x=6.0507, y=6.0198), Point(x=5.4365, y=6.0096)], span=DocumentSpan(offset=444, length=7), confidence=0.995), DocumentWord(content=Owing, polygon=[Point(x=6.1015, y=5.8523), Point(x=6.5888, y=5.8523), Point(x=6.5888, y=6.0249), Point(x=6.1015, y=6.0198)], span=DocumentSpan(offset=452, length=5), confidence=0.995), DocumentWord(content=$, polygon=[Point(x=7.1928, y=5.8523), Point(x=7.274, y=5.8523), Point(x=7.2791, y=6.0096), Point(x=7.1979, y=6.0096)], span=DocumentSpan(offset=458, length=1), confidence=0.953), DocumentWord(content=83.48, polygon=[Point(x=7.3451, y=5.8523), Point(x=7.7715, y=5.8523), Point(x=7.7715, y=6.0046), Point(x=7.3502, y=6.0096)], span=DocumentSpan(offset=460, length=5), confidence=0.995)], selection_marks=[DocumentSelectionMark(state=selected, span=DocumentSpan(offset=466, length=10), confidence=0.742, polygon=[Point(x=5.51, y=4.504), Point(x=5.5976, y=4.504), Point(x=5.5976, y=4.5931), Point(x=5.51, y=4.5931)]), DocumentSelectionMark(state=selected, span=DocumentSpan(offset=477, length=10), confidence=0.913, polygon=[Point(x=5.5114, y=4.9628), Point(x=5.5949, y=4.9628), Point(x=5.5949, y=5.0465), Point(x=5.5114, y=5.0465)])], spans=[DocumentSpan(offset=0, length=487)], barcodes=[], formulas=[])], paragraphs=[], tables=[DocumentTable(row_count=4, column_count=5, cells=[DocumentTableCell(kind=columnHeader, row_index=0, column_index=0, row_span=1, column_span=1, content=Patient, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=3.9268), Point(x=1.3525, y=3.9268), Point(x=1.3525, y=4.1971), Point(x=0.3639, y=4.1971)])], spans=[DocumentSpan(offset=219, length=7)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=1, row_span=1, column_span=1, content=Service Provided, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.3525, y=3.9268), Point(x=4.1714, y=3.9268), Point(x=4.1637, y=4.1971), Point(x=1.3525, y=4.1971)])], spans=[DocumentSpan(offset=227, length=16)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=2, row_span=1, column_span=1, content=Claimed, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1714, y=3.9268), Point(x=6.0559, y=3.9268), Point(x=6.0636, y=4.1971), Point(x=4.1637, y=4.1971)])], spans=[DocumentSpan(offset=244, length=7)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=3, row_span=1, column_span=1, content=Quantity, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0559, y=3.9268), Point(x=7.029, y=3.9268), Point(x=7.029, y=4.1971), Point(x=6.0636, y=4.1971)])], spans=[DocumentSpan(offset=252, length=8)]), DocumentTableCell(kind=columnHeader, row_index=0, column_index=4, row_span=1, column_span=1, content=Amount, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=3.9268), Point(x=7.8786, y=3.9268), Point(x=7.8786, y=4.1971), Point(x=7.029, y=4.1971)])], spans=[DocumentSpan(offset=261, length=6)]), DocumentTableCell(kind=content, row_index=1, column_index=0, row_span=1, column_span=2, content=Freddie (ID:385654), bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=4.1971), Point(x=4.1637, y=4.1971), Point(x=4.1637, y=4.4442), Point(x=0.3639, y=4.4442)])], spans=[DocumentSpan(offset=268, length=19)]), DocumentTableCell(kind=content, row_index=1, column_index=2, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1637, y=4.1971), Point(x=6.0636, y=4.1971), Point(x=6.0636, y=4.4364), Point(x=4.1637, y=4.4442)])], spans=[]), DocumentTableCell(kind=content, row_index=1, column_index=3, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0636, y=4.1971), Point(x=7.029, y=4.1971), Point(x=7.029, y=4.4364), Point(x=6.0636, y=4.4364)])], spans=[]), DocumentTableCell(kind=content, row_index=1, column_index=4, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=4.1971), Point(x=7.8786, y=4.1971), Point(x=7.8786, y=4.4364), Point(x=7.029, y=4.4364)])], spans=[]), DocumentTableCell(kind=content, row_index=2, column_index=0, row_span=1, column_span=1, content=10/07/23, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=4.4442), Point(x=1.3525, y=4.4442), Point(x=1.3525, y=4.7608), Point(x=0.3639, y=4.7608)])], spans=[DocumentSpan(offset=288, length=8)]), DocumentTableCell(kind=content, row_index=2, column_index=1, row_span=1, column_span=1, content=Consultation- Complex, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.3525, y=4.4442), Point(x=4.1637, y=4.4442), Point(x=4.1637, y=4.7608), Point(x=1.3525, y=4.7608)])], spans=[DocumentSpan(offset=297, length=21)]), DocumentTableCell(kind=content, row_index=2, column_index=2, row_span=1, column_span=1, content=✓\n", ":selected:, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1637, y=4.4442), Point(x=6.0636, y=4.4364), Point(x=6.0636, y=4.7608), Point(x=4.1637, y=4.7608)])], spans=[DocumentSpan(offset=319, length=1), DocumentSpan(offset=466, length=10)]), DocumentTableCell(kind=content, row_index=2, column_index=3, row_span=1, column_span=1, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0636, y=4.4364), Point(x=7.029, y=4.4364), Point(x=7.029, y=4.7608), Point(x=6.0636, y=4.7608)])], spans=[DocumentSpan(offset=321, length=1)]), DocumentTableCell(kind=content, row_index=2, column_index=4, row_span=1, column_span=1, content=$ 0.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=4.4364), Point(x=7.8786, y=4.4364), Point(x=7.8786, y=4.7608), Point(x=7.029, y=4.7608)])], spans=[DocumentSpan(offset=323, length=6)]), DocumentTableCell(kind=content, row_index=3, column_index=0, row_span=1, column_span=1, content=, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3639, y=4.7608), Point(x=1.3525, y=4.7608), Point(x=1.3447, y=5.3862), Point(x=0.3562, y=5.3785)])], spans=[]), DocumentTableCell(kind=content, row_index=3, column_index=1, row_span=1, column_span=1, content=Flusapex 100ml, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.3525, y=4.7608), Point(x=4.1637, y=4.7608), Point(x=4.1637, y=5.3862), Point(x=1.3447, y=5.3862)])], spans=[DocumentSpan(offset=330, length=14)]), DocumentTableCell(kind=content, row_index=3, column_index=2, row_span=1, column_span=1, content=✓\n", ":selected:, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.1637, y=4.7608), Point(x=6.0636, y=4.7608), Point(x=6.0636, y=5.3862), Point(x=4.1637, y=5.3862)])], spans=[DocumentSpan(offset=345, length=1), DocumentSpan(offset=477, length=10)]), DocumentTableCell(kind=content, row_index=3, column_index=3, row_span=1, column_span=1, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.0636, y=4.7608), Point(x=7.029, y=4.7608), Point(x=7.0367, y=5.394), Point(x=6.0636, y=5.3862)])], spans=[DocumentSpan(offset=347, length=1)]), DocumentTableCell(kind=content, row_index=3, column_index=4, row_span=1, column_span=1, content=$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.029, y=4.7608), Point(x=7.8786, y=4.7608), Point(x=7.8786, y=5.394), Point(x=7.0367, y=5.394)])], spans=[DocumentSpan(offset=349, length=8)])], bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.3691, y=3.9222), Point(x=7.898, y=3.9226), Point(x=7.8986, y=5.4012), Point(x=0.3688, y=5.4015)])], spans=[DocumentSpan(offset=219, length=101), DocumentSpan(offset=466, length=10), DocumentSpan(offset=321, length=25), DocumentSpan(offset=477, length=10), DocumentSpan(offset=347, length=10)])], key_value_pairs=[], styles=[], documents=[AnalyzedDocument(doc_type=invoice, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.0, y=0.0), Point(x=8.2639, y=0.0), Point(x=8.2639, y=11.6944), Point(x=0.0, y=11.6944)])], spans=[DocumentSpan(offset=0, length=487)], fields={'AmountDue': DocumentField(value_type=currency, value=CurrencyValue(amount=83.48, symbol=$, code=AUD), content=$ 83.48, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.1928, y=5.8523), Point(x=7.7715, y=5.8523), Point(x=7.7715, y=6.0096), Point(x=7.1928, y=6.0096)])], spans=[DocumentSpan(offset=458, length=7)], confidence=0.919), 'CustomerAddress': DocumentField(value_type=address, value=AddressValue(house_number=21, po_box=None, road=Aspendale Place, city=None, state=WA, postal_code=6025, country_region=None, street_address=21 Aspendale Place, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None), content=21 Aspendale Place\n", "<PERSON>s WA 6025, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.2843, y=2.8982), Point(x=2.6446, y=2.8982), Point(x=2.6446, y=3.2282), Point(x=1.2843, y=3.2282)])], spans=[DocumentSpan(offset=127, length=18), DocumentSpan(offset=160, length=16)], confidence=0.892), 'CustomerId': DocumentField(value_type=string, value='172386', content=172386, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.7867, y=2.6901), Point(x=7.2994, y=2.6901), Point(x=7.2994, y=2.8627), Point(x=6.7867, y=2.8576)])], spans=[DocumentSpan(offset=120, length=6)], confidence=0.982), 'CustomerName': DocumentField(value_type=string, value='<PERSON>', content=<PERSON>, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.325, y=2.7251), Point(x=2.1289, y=2.7358), Point(x=2.1268, y=2.8937), Point(x=1.3229, y=2.883)])], spans=[DocumentSpan(offset=96, length=11)], confidence=0.915), 'InvoiceDate': DocumentField(value_type=date, value=datetime.date(2023, 7, 13), content=13/07/23, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.7715, y=2.8779), Point(x=7.3959, y=2.8779), Point(x=7.3959, y=3.0556), Point(x=6.7715, y=3.0505)])], spans=[DocumentSpan(offset=151, length=8)], confidence=0.982), 'InvoiceId': DocumentField(value_type=string, value='********', content=********, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.7817, y=3.284), Point(x=7.472, y=3.2891), Point(x=7.472, y=3.4312), Point(x=6.7817, y=3.4261)])], spans=[DocumentSpan(offset=198, length=8)], confidence=0.982), 'InvoiceTotal': DocumentField(value_type=currency, value=CurrencyValue(amount=132.28, symbol=$, code=AUD), content=$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.0913, y=5.4412), Point(x=7.7766, y=5.4412), Point(x=7.7766, y=5.6137), Point(x=7.0913, y=5.6137)])], spans=[DocumentSpan(offset=408, length=8)], confidence=0.92), 'Items': DocumentField(value_type=list, value=[DocumentField(value_type=dictionary, value={'Amount': DocumentField(value_type=currency, value=CurrencyValue(amount=0.0, symbol=$, code=AUD), content=$ 0.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.3248, y=4.4616), Point(x=7.7715, y=4.4616), Point(x=7.7715, y=4.6189), Point(x=7.3248, y=4.6189)])], spans=[DocumentSpan(offset=323, length=6)], confidence=0.933), 'Date': DocumentField(value_type=date, value=datetime.date(2023, 7, 10), content=10/07/23, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.7005, y=4.4616), Point(x=1.3096, y=4.4616), Point(x=1.3147, y=4.6138), Point(x=0.7056, y=4.6138)])], spans=[DocumentSpan(offset=288, length=8)], confidence=0.818), 'Description': DocumentField(value_type=string, value='Consultation- Complex', content=Consultation- Complex, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.4619, y=4.4551), Point(x=3.0565, y=4.4616), Point(x=3.0558, y=4.6291), Point(x=1.4612, y=4.6226)])], spans=[DocumentSpan(offset=297, length=21)], confidence=0.923), 'Quantity': DocumentField(value_type=float, value=1.0, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.4466, y=4.4666), Point(x=6.5228, y=4.4666), Point(x=6.5177, y=4.5986), Point(x=6.4416, y=4.5935)])], spans=[DocumentSpan(offset=321, length=1)], confidence=0.94)}, content=10/07/23 Consultation- Complex\n", "✓\n", "1\n", "$ 0.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=0.7005, y=4.4616), Point(x=7.7715, y=4.4616), Point(x=7.7715, y=4.6291), Point(x=0.7005, y=4.6291)])], spans=[DocumentSpan(offset=288, length=41)], confidence=0.81), DocumentField(value_type=dictionary, value={'Amount': DocumentField(value_type=currency, value=CurrencyValue(amount=132.28, symbol=$, code=AUD), content=$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.1573, y=4.9184), Point(x=7.7664, y=4.9184), Point(x=7.7664, y=5.0706), Point(x=7.1573, y=5.0706)])], spans=[DocumentSpan(offset=349, length=8)], confidence=0.932), 'Description': DocumentField(value_type=string, value='Flusapex 100ml', content=Flusapex 100ml, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.4569, y=4.9018), Point(x=2.5949, y=4.9082), Point(x=2.5939, y=5.0923), Point(x=1.4558, y=5.0859)])], spans=[DocumentSpan(offset=330, length=14)], confidence=0.922), 'Quantity': DocumentField(value_type=float, value=1.0, content=1, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=6.4517, y=4.9234), Point(x=6.5177, y=4.9234), Point(x=6.5177, y=5.0554), Point(x=6.4517, y=5.0554)])], spans=[DocumentSpan(offset=347, length=1)], confidence=0.937)}, content=Flusapex 100ml\n", "✓\n", "1\n", "$ 132.28, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=1.4568, y=4.9082), Point(x=7.7664, y=4.9082), Point(x=7.7664, y=5.0859), Point(x=1.4568, y=5.0859)])], spans=[DocumentSpan(offset=330, length=27)], confidence=0.854)], content=None, bounding_regions=[], spans=[], confidence=None), 'TotalDiscount': DocumentField(value_type=currency, value=CurrencyValue(amount=156.0, symbol=$, code=AUD), content=$ 156.00, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=3.0203, y=5.4462), Point(x=3.7005, y=5.4462), Point(x=3.7005, y=5.6137), Point(x=3.0203, y=5.6137)])], spans=[DocumentSpan(offset=393, length=8)], confidence=0.416), 'TotalTax': DocumentField(value_type=currency, value=CurrencyValue(amount=12.03, symbol=$, code=AUD), content=$ 12.03, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=7.2385, y=5.6528), Point(x=7.7715, y=5.6493), Point(x=7.7725, y=5.8066), Point(x=7.2395, y=5.8101)])], spans=[DocumentSpan(offset=436, length=7)], confidence=0.648), 'VendorAddress': DocumentField(value_type=address, value=AddressValue(house_number=2, po_box=None, road=Banks Ave, city=None, state=WA, postal_code=6025, country_region=None, street_address=2 Banks Ave, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None), content=2 Banks Ave\n", "<PERSON><PERSON> WA 6025, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.9035, y=0.6598), Point(x=6.3705, y=0.6598), Point(x=6.3705, y=1.0151), Point(x=4.9035, y=1.0151)])], spans=[DocumentSpan(offset=10, length=28)], confidence=0.891), 'VendorAddressRecipient': DocumentField(value_type=string, value='Whitfords', content=Whitfords, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.9086, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.6091), Point(x=4.9086, y=0.6091)])], spans=[DocumentSpan(offset=0, length=9)], confidence=0.645), 'VendorName': DocumentField(value_type=string, value='Whitfords', content=Whitfords, bounding_regions=[BoundingRegion(page_number=1, polygon=[Point(x=4.9086, y=0.4111), Point(x=5.9949, y=0.4162), Point(x=5.9949, y=0.6091), Point(x=4.9086, y=0.6091)])], spans=[DocumentSpan(offset=0, length=9)], confidence=0.645)}, confidence=1.0)])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["invoices"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------Analyzing invoice #1--------\n", "Vendor Name: <PERSON><PERSON><PERSON><PERSON> has confidence: 0.645\n", "Vendor Address: AddressValue(house_number=2, po_box=None, road=Banks Ave, city=None, state=WA, postal_code=6025, country_region=None, street_address=2 Banks Ave, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None) has confidence: 0.891\n", "<PERSON><PERSON>or Address Recipient: <PERSON><PERSON><PERSON><PERSON> has confidence: 0.645\n", "Customer Name: <PERSON> has confidence: 0.915\n", "Customer Id: 172386 has confidence: 0.982\n", "Customer Address: AddressValue(house_number=21, po_box=None, road=Aspendale Place, city=None, state=WA, postal_code=6025, country_region=None, street_address=21 Aspendale Place, unit=None, city_district=None, state_district=None, suburb=Hillarys, house=None, level=None) has confidence: 0.892\n", "Invoice Id: ******** has confidence: 0.982\n", "Invoice Date: 2023-07-13 has confidence: 0.982\n", "Invoice Total: $132.28 has confidence: 0.92\n", "Invoice items:\n", "...Item #1\n", "......Description: Consultation- Complex has confidence: 0.923\n", "......Quantity: 1.0 has confidence: 0.94\n", "......Date: 2023-07-10 has confidence: 0.818\n", "......Amount: $0.0 has confidence: 0.933\n", "...Item #2\n", "......Description: Flusapex 100ml has confidence: 0.922\n", "......Quantity: 1.0 has confidence: 0.937\n", "......Amount: $132.28 has confidence: 0.932\n", "Total Tax: $12.03 has confidence: 0.648\n", "Amount Due: $83.48 has confidence: 0.919\n"]}], "source": ["for idx, invoice in enumerate(invoices.documents):\n", "       print(f\"--------Analyzing invoice #{idx + 1}--------\")\n", "       vendor_name = invoice.fields.get(\"VendorName\")\n", "       if vendor_name:\n", "           print(\n", "               f\"Vendor Name: {vendor_name.value} has confidence: {vendor_name.confidence}\"\n", "           )\n", "       vendor_address = invoice.fields.get(\"VendorAddress\")\n", "       if vendor_address:\n", "           print(\n", "               f\"Vendor Address: {vendor_address.value} has confidence: {vendor_address.confidence}\"\n", "           )\n", "       vendor_address_recipient = invoice.fields.get(\"VendorAddressRecipient\")\n", "       if vendor_address_recipient:\n", "           print(\n", "               f\"Vendor Address Recipient: {vendor_address_recipient.value} has confidence: {vendor_address_recipient.confidence}\"\n", "           )\n", "       customer_name = invoice.fields.get(\"CustomerName\")\n", "       if customer_name:\n", "           print(\n", "               f\"Customer Name: {customer_name.value} has confidence: {customer_name.confidence}\"\n", "           )\n", "       customer_id = invoice.fields.get(\"CustomerId\")\n", "       if customer_id:\n", "           print(\n", "               f\"Customer Id: {customer_id.value} has confidence: {customer_id.confidence}\"\n", "           )\n", "       customer_address = invoice.fields.get(\"CustomerAddress\")\n", "       if customer_address:\n", "           print(\n", "               f\"Customer Address: {customer_address.value} has confidence: {customer_address.confidence}\"\n", "           )\n", "       customer_address_recipient = invoice.fields.get(\"CustomerAddressRecipient\")\n", "       if customer_address_recipient:\n", "           print(\n", "               f\"Customer Address Recipient: {customer_address_recipient.value} has confidence: {customer_address_recipient.confidence}\"\n", "           )\n", "       invoice_id = invoice.fields.get(\"InvoiceId\")\n", "       if invoice_id:\n", "           print(\n", "               f\"Invoice Id: {invoice_id.value} has confidence: {invoice_id.confidence}\"\n", "           )\n", "       invoice_date = invoice.fields.get(\"InvoiceDate\")\n", "       if invoice_date:\n", "           print(\n", "               f\"Invoice Date: {invoice_date.value} has confidence: {invoice_date.confidence}\"\n", "           )\n", "       invoice_total = invoice.fields.get(\"InvoiceTotal\")\n", "       if invoice_total:\n", "           print(\n", "               f\"Invoice Total: {invoice_total.value} has confidence: {invoice_total.confidence}\"\n", "           )\n", "       due_date = invoice.fields.get(\"DueDate\")\n", "       if due_date:\n", "           print(f\"Due Date: {due_date.value} has confidence: {due_date.confidence}\")\n", "       purchase_order = invoice.fields.get(\"PurchaseOrder\")\n", "       if purchase_order:\n", "           print(\n", "               f\"Purchase Order: {purchase_order.value} has confidence: {purchase_order.confidence}\"\n", "           )\n", "       billing_address = invoice.fields.get(\"BillingAddress\")\n", "       if billing_address:\n", "           print(\n", "               f\"Billing Address: {billing_address.value} has confidence: {billing_address.confidence}\"\n", "           )\n", "       billing_address_recipient = invoice.fields.get(\"BillingAddressRecipient\")\n", "       if billing_address_recipient:\n", "           print(\n", "               f\"Billing Address Recipient: {billing_address_recipient.value} has confidence: {billing_address_recipient.confidence}\"\n", "           )\n", "       shipping_address = invoice.fields.get(\"ShippingAddress\")\n", "       if shipping_address:\n", "           print(\n", "               f\"Shipping Address: {shipping_address.value} has confidence: {shipping_address.confidence}\"\n", "           )\n", "       shipping_address_recipient = invoice.fields.get(\"ShippingAddressRecipient\")\n", "       if shipping_address_recipient:\n", "           print(\n", "               f\"Shipping Address Recipient: {shipping_address_recipient.value} has confidence: {shipping_address_recipient.confidence}\"\n", "           )\n", "       print(\"Invoice items:\")\n", "       for idx, item in enumerate(invoice.fields.get(\"Items\").value):\n", "           print(f\"...Item #{idx + 1}\")\n", "           item_description = item.value.get(\"Description\")\n", "           if item_description:\n", "               print(\n", "                   f\"......Description: {item_description.value} has confidence: {item_description.confidence}\"\n", "               )\n", "           item_quantity = item.value.get(\"Quantity\")\n", "           if item_quantity:\n", "               print(\n", "                   f\"......Quantity: {item_quantity.value} has confidence: {item_quantity.confidence}\"\n", "               )\n", "           unit = item.value.get(\"Unit\")\n", "           if unit:\n", "               print(f\"......Unit: {unit.value} has confidence: {unit.confidence}\")\n", "           unit_price = item.value.get(\"UnitPrice\")\n", "           if unit_price:\n", "               unit_price_code = unit_price.value.code if unit_price.value.code else \"\"\n", "               print(\n", "                   f\"......Unit Price: {unit_price.value}{unit_price_code} has confidence: {unit_price.confidence}\"\n", "               )\n", "           product_code = item.value.get(\"ProductCode\")\n", "           if product_code:\n", "               print(\n", "                   f\"......Product Code: {product_code.value} has confidence: {product_code.confidence}\"\n", "               )\n", "           item_date = item.value.get(\"Date\")\n", "           if item_date:\n", "               print(\n", "                   f\"......Date: {item_date.value} has confidence: {item_date.confidence}\"\n", "               )\n", "           tax = item.value.get(\"Tax\")\n", "           if tax:\n", "               print(f\"......Tax: {tax.value} has confidence: {tax.confidence}\")\n", "           amount = item.value.get(\"Amount\")\n", "           if amount:\n", "               print(\n", "                   f\"......Amount: {amount.value} has confidence: {amount.confidence}\"\n", "               )\n", "       subtotal = invoice.fields.get(\"SubTotal\")\n", "       if subtotal:\n", "           print(f\"Subtotal: {subtotal.value} has confidence: {subtotal.confidence}\")\n", "       total_tax = invoice.fields.get(\"TotalTax\")\n", "       if total_tax:\n", "           print(\n", "               f\"Total Tax: {total_tax.value} has confidence: {total_tax.confidence}\"\n", "           )\n", "       previous_unpaid_balance = invoice.fields.get(\"PreviousUnpaidBalance\")\n", "       if previous_unpaid_balance:\n", "           print(\n", "               f\"Previous Unpaid Balance: {previous_unpaid_balance.value} has confidence: {previous_unpaid_balance.confidence}\"\n", "           )\n", "       amount_due = invoice.fields.get(\"AmountDue\")\n", "       if amount_due:\n", "           print(\n", "               f\"Amount Due: {amount_due.value} has confidence: {amount_due.confidence}\"\n", "           )\n", "       service_start_date = invoice.fields.get(\"ServiceStartDate\")\n", "       if service_start_date:\n", "           print(\n", "               f\"Service Start Date: {service_start_date.value} has confidence: {service_start_date.confidence}\"\n", "           )\n", "       service_end_date = invoice.fields.get(\"ServiceEndDate\")\n", "       if service_end_date:\n", "           print(\n", "               f\"Service End Date: {service_end_date.value} has confidence: {service_end_date.confidence}\"\n", "           )\n", "       service_address = invoice.fields.get(\"ServiceAddress\")\n", "       if service_address:\n", "           print(\n", "               f\"Service Address: {service_address.value} has confidence: {service_address.confidence}\"\n", "           )\n", "       service_address_recipient = invoice.fields.get(\"ServiceAddressRecipient\")\n", "       if service_address_recipient:\n", "           print(\n", "               f\"Service Address Recipient: {service_address_recipient.value} has confidence: {service_address_recipient.confidence}\"\n", "           )\n", "       remittance_address = invoice.fields.get(\"RemittanceAddress\")\n", "       if remittance_address:\n", "           print(\n", "               f\"Remittance Address: {remittance_address.value} has confidence: {remittance_address.confidence}\"\n", "           )\n", "       remittance_address_recipient = invoice.fields.get(\"RemittanceAddressRecipient\")\n", "       if remittance_address_recipient:\n", "           print(\n", "               f\"Remittance Address Recipient: {remittance_address_recipient.value} has confidence: {remittance_address_recipient.confidence}\"\n", "           )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}