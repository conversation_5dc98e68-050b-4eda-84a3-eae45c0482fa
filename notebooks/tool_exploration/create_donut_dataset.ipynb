{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2023-11-05T12:28:35.685495300Z", "start_time": "2023-11-05T12:28:35.401476500Z"}}, "outputs": [], "source": ["import json\n", "import os.path\n", "from pathlib import Path\n", "\n", "import fitz\n", "from PIL import Image\n", "from icecream import ic\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "markdown", "source": ["# Generate metadata.jsonl for train set"], "metadata": {"collapsed": false}, "id": "5c737bf06b1c2fc"}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["ROOT_PATH = Path(os.path.abspath('.')).parent\n", "DATA_PATH = ROOT_PATH / 'data'\n", "TRAIN_PATH = DATA_PATH / 'csp_dataset' / 'train'\n", "TEST_PATH = DATA_PATH / 'csp_dataset' / 'test'"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-05T12:28:35.696494900Z", "start_time": "2023-11-05T12:28:35.687496800Z"}}, "id": "c02976e6522af5f2"}, {"cell_type": "markdown", "source": ["# Convert PNGs and JPEGs to JPGs"], "metadata": {"collapsed": false}, "id": "5df3f610b0caa4d2"}, {"cell_type": "code", "execution_count": 10, "outputs": [{"data": {"text/plain": "0it [00:00, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "86f69d878aee4bf5ad396fe46eb609d1"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/24 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "dbdf83cabcad433cb06a47e284fa48e8"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/128 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6391dbf4ac5a43b09c723a0b81752898"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/19 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ae8d4ca48094460cb14d0d874d8390ec"}}, "metadata": {}, "output_type": "display_data"}], "source": ["for r, d, f in os.walk(TEST_PATH):\n", "    for file in tqdm(f):\n", "        if file.lower().endswith(\".png\"):\n", "            file_path = os.path.join(r, file)\n", "            img = Image.open(file_path).convert('RGB')\n", "            img.save(file_path.lower().replace('.png', '.jpg'))"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-29T22:41:14.502482400Z", "start_time": "2023-10-29T22:41:04.167959300Z"}}, "id": "38968d34bdff72f4"}, {"cell_type": "code", "execution_count": 14, "outputs": [{"data": {"text/plain": "0it [00:00, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "137dd139c3484056b239e760a0e4a244"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/186 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5d72012fc7fb4ed7833fede828d0877d"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/148 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a84802ae3157487faa6db54fddbcd70f"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/86 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b4fdbf142ac447f78737ef33aefb89c9"}}, "metadata": {}, "output_type": "display_data"}], "source": ["for r, d, f in os.walk(TRAIN_PATH):\n", "    for file in tqdm(f):\n", "        if file.lower().endswith(\".jpeg\"):\n", "            file_path = os.path.join(r, file)\n", "            img = Image.open(file_path)\n", "            img.save(file_path.lower().replace('.jpeg', '.jpg'))"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-29T23:11:58.023329700Z", "start_time": "2023-10-29T23:11:50.082536800Z"}}, "id": "20dbd1d5686d9ab8"}, {"cell_type": "code", "execution_count": 6, "outputs": [{"data": {"text/plain": "  0%|          | 0/3 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "34740be66c5f4354bb3658876e3a8209"}}, "metadata": {}, "output_type": "display_data"}], "source": ["for label in tqdm(['invoice', 'medical_record', 'other']):\n", "    with open(TRAIN_PATH / label / \"metadata.jsonl\", \"w+\") as j:\n", "        for r, d, f in os.walk(TRAIN_PATH / label):\n", "            #TODO: skip if the file is metadata.jsonl itself\n", "            for file in f:\n", "                item = {\"file_name\": file, \"ground_truth\": {\"gt_parse\": {\"class\": label}}}\n", "                line = json.dumps(item)\n", "                j.write(line + '\\n')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-05T12:34:45.939358Z", "start_time": "2023-11-05T12:34:45.904696200Z"}}, "id": "4c9b7cbccf8e6074"}, {"cell_type": "markdown", "source": ["# Convert pdfs to images in test set"], "metadata": {"collapsed": false}, "id": "fd8c17e88fe64f38"}, {"cell_type": "code", "execution_count": 30, "outputs": [{"data": {"text/plain": "  0%|          | 0/100 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "aa0bbe384cc44626af862a29e7e8edec"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "  0%|          | 0/142 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "edaa859ec3374eccb13b4e33556091b8"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "0it [00:00, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "09a004fb43f64af9b8b60ea3161b370f"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "0it [00:00, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f2ee2c172a1a47cc838aacefb9f0eaf2"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "0it [00:00, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "95a1b2dc800749eb8f6e04a56c215c64"}}, "metadata": {}, "output_type": "display_data"}], "source": ["for r, d, f in os.walk(TEST_PATH):\n", "    for file in tqdm(f):\n", "        if file.endswith(\".pdf\"):\n", "            file_path = os.path.join(r, file)\n", "            doc = fitz.open(file_path)\n", "            for page in doc:\n", "                pix = page.get_pixmap(dpi=200)\n", "                pix.save(TEST_PATH / 'converted' / f\"{file}_{page.number}.png\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-10-27T00:44:18.949523400Z", "start_time": "2023-10-27T00:43:56.639482400Z"}}, "id": "a6bc8370bb2dfec7"}, {"cell_type": "markdown", "source": ["# Generate metadata.jsonl for test set"], "metadata": {"collapsed": false}, "id": "6af2b2b4c4818f1c"}, {"cell_type": "code", "execution_count": 7, "outputs": [{"data": {"text/plain": "  0%|          | 0/3 [00:00<?, ?it/s]", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "bc910718f37b41c69dec55e6596fd327"}}, "metadata": {}, "output_type": "display_data"}], "source": ["for label in tqdm(['invoice', 'medical_record', 'other']):\n", "    with open(TEST_PATH / label / \"metadata.jsonl\", \"w+\") as j:\n", "        for r, d, f in os.walk(TEST_PATH / label):\n", "            for file in f:\n", "                item = {\"file_name\": file, \"ground_truth\": {\"gt_parse\": {\"class\": label}}}\n", "                line = json.dumps(item)\n", "                j.write(line + '\\n')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-05T12:37:22.952809600Z", "start_time": "2023-11-05T12:37:22.913301300Z"}}, "id": "98a2d1fe8700921"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}