{"cells": [{"cell_type": "code", "execution_count": 1, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "from PIL import Image\n", "from paddleocr import PaddleOCR\n", "from paddleocr.tools.infer.utility import draw_ocr"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-08T02:11:25.176302900Z", "start_time": "2023-11-08T02:11:01.384415700Z"}}, "id": "e70eae729652dffa"}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["ROOT_DIR = Path(os.path.abspath('.')).parent\n", "OUTPUT_DIR = ROOT_DIR / 'output'\n", "IMG_DIR = ROOT_DIR / 'data' / 'invoices' / 'Image'\n", "PDF_DIR = ROOT_DIR / 'data' / 'invoices' / 'PDF'\n", "FONT_DIR = ROOT_DIR / 'resources' / 'fonts'"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-08T02:11:25.178313300Z", "start_time": "2023-11-08T02:11:25.172306500Z"}}, "id": "f0dd68b6e169d5f5"}, {"cell_type": "code", "execution_count": 3, "outputs": [], "source": ["image_path = IMG_DIR / '0ac805c3-4404-423c-9060-2765641cda23.jpeg'"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-08T02:11:25.193315600Z", "start_time": "2023-11-08T02:11:25.178313300Z"}}, "id": "c2df15cc1e113f90"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["ocr = PaddleOCR(use_angle_cls=True, lang='en')"], "metadata": {"collapsed": false}, "id": "92afede365575951"}, {"cell_type": "code", "execution_count": 6, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2023/11/08 13:18:41] ppocr DEBUG: dt_boxes num : 75, elapsed : 33.19753384590149\n", "[2023/11/08 13:18:53] ppocr DEBUG: cls num  : 75, elapsed : 11.284873247146606\n", "[2023/11/08 13:18:54] ppocr DEBUG: rec_res num  : 75, elapsed : 1.0500800609588623\n"]}], "source": ["result = ocr.ocr(image_path.as_posix(), cls=True)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-08T02:18:54.229506700Z", "start_time": "2023-11-08T02:18:07.938967400Z"}}, "id": "b2ddf9856cb900f6"}, {"cell_type": "code", "execution_count": 7, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[1365.0, 305.0], [1411.0, 305.0], [1411.0, 546.0], [1365.0, 546.0]], ('Amount', 0.999325692653656)]\n", "[[[1608.0, 303.0], [1671.0, 300.0], [1681.0, 513.0], [1618.0, 516.0]], ('$69.30', 0.9928831458091736)]\n", "[[[1785.0, 301.0], [1856.0, 301.0], [1856.0, 550.0], [1785.0, 550.0]], ('$119.72', 0.9921894073486328)]\n", "[[[1504.0, 314.0], [1562.0, 314.0], [1562.0, 515.0], [1504.0, 515.0]], ('$50.42', 0.9944437146186829)]\n", "[[[1991.0, 309.0], [2050.0, 309.0], [2050.0, 524.0], [1991.0, 524.0]], ('$52.20', 0.994070291519165)]\n", "[[[2108.0, 305.0], [2167.0, 305.0], [2167.0, 528.0], [2108.0, 528.0]], ('$81.68', 0.9241166114807129)]\n", "[[[2192.0, 305.0], [2251.0, 305.0], [2251.0, 576.0], [2192.0, 576.0]], ('$201.40', 0.9939795732498169)]\n", "[[[1928.0, 318.0], [1974.0, 318.0], [1974.0, 520.0], [1928.0, 520.0]], ('$29.48', 0.9941480159759521)]\n", "[[[2264.0, 314.0], [2323.0, 314.0], [2323.0, 533.0], [2264.0, 533.0]], ('$18.31', 0.9911013245582581)]\n", "[[[2339.0, 309.0], [2398.0, 309.0], [2398.0, 546.0], [2339.0, 546.0]], ('$43.80', 0.9953486323356628)]\n", "[[[2415.0, 314.0], [2474.0, 314.0], [2474.0, 576.0], [2415.0, 576.0]], ('$157.60', 0.9958735108375549)]\n", "[[[1184.0, 395.0], [1243.0, 395.0], [1243.0, 683.0], [1184.0, 683.0]], ('Page lof1', 0.9021458029747009)]\n", "[[[117.0, 439.0], [189.0, 438.0], [198.0, 1331.0], [126.0, 1332.0]], ('Benetook Veterinary Clinic', 0.97178053855896)]\n", "[[[1105.0, 438.0], [1176.0, 438.0], [1176.0, 683.0], [1105.0, 683.0]], ('7359576', 0.9983569979667664)]\n", "[[[3612.0, 434.0], [3684.0, 434.0], [3675.0, 1886.0], [3603.0, 1885.0]], ('Other Procedures [Annual Dog Health Check & Vaccination]', 0.9698763489723206)]\n", "[[[373.0, 457.0], [457.0, 455.0], [480.0, 1330.0], [395.0, 1332.0]], ('After Hours Mobile: 0455 855 400.', 0.9557557702064514)]\n", "[[[457.0, 465.0], [529.0, 463.0], [542.0, 1327.0], [471.0, 1328.0]], ('E: <EMAIL>', 0.9688034057617188)]\n", "[[[979.0, 477.0], [1037.0, 477.0], [1037.0, 683.0], [979.0, 683.0]], ('25/1/23', 0.9988629221916199)]\n", "[[[731.0, 494.0], [844.0, 494.0], [844.0, 1087.0], [731.0, 1087.0]], ('Tax Invoice', 0.9811693429946899)]\n", "[[[907.0, 503.0], [966.0, 503.0], [966.0, 687.0], [907.0, 687.0]], ('33457', 0.9991094470024109)]\n", "[[[520.0, 559.0], [592.0, 558.0], [605.0, 1322.0], [534.0, 1324.0]], ('W:www.benetookvet.com.au', 0.9812676906585693)]\n", "[[[906.0, 693.0], [965.0, 691.0], [979.0, 1025.0], [921.0, 1028.0]], ('Customer ID', 0.9673420190811157)]\n", "[[[1112.0, 757.0], [1171.0, 755.0], [1181.0, 1025.0], [1122.0, 1027.0]], ('Invoice No', 0.9637386202812195)]\n", "[[[1054.0, 769.0], [1100.0, 769.0], [1100.0, 1022.0], [1054.0, 1022.0]], ('Reference', 0.9772713780403137)]\n", "[[[201.0, 804.0], [273.0, 803.0], [277.0, 1327.0], [206.0, 1328.0]], ('634 Benetook Ave', 0.9744412302970886)]\n", "[[[600.0, 804.0], [667.0, 803.0], [677.0, 1327.0], [609.0, 1328.0]], ('ABN:72 0056 23076', 0.9248367547988892)]\n", "[[[260.0, 835.0], [327.0, 833.0], [341.0, 1326.0], [274.0, 1328.0]], ('Mildura VIC 3500', 0.9585564136505127)]\n", "[[[327.0, 826.0], [394.0, 824.0], [408.0, 1326.0], [341.0, 1328.0]], ('Ph:(**********', 0.9375272989273071)]\n", "[[[991.0, 833.0], [1033.0, 833.0], [1033.0, 1022.0], [991.0, 1022.0]], ('Datew', 0.8241551518440247)]\n", "[[[1357.0, 833.0], [1428.0, 833.0], [1428.0, 1100.0], [1357.0, 1100.0]], ('Quantity', 0.9965454339981079)]\n", "[[[1798.0, 846.0], [1856.0, 846.0], [1856.0, 1160.0], [1798.0, 1160.0]], ('Patient Total', 0.9786045551300049)]\n", "[[[2108.0, 846.0], [2167.0, 846.0], [2167.0, 1164.0], [2108.0, 1164.0]], ('Patient Total', 0.****************)]\n", "[[[2197.0, 850.0], [2243.0, 850.0], [2243.0, 1070.0], [2197.0, 1070.0]], ('TOTAL', 0.****************)]\n", "[[[2256.0, 842.0], [2327.0, 842.0], [2322.0, 1353.0], [2251.0, 1353.0]], ('Total includes GST', 0.****************)]\n", "[[[2335.0, 932.0], [2394.0, 933.0], [2390.0, 1310.0], [2331.0, 1310.0]], ('Amount paid', 0.****************)]\n", "[[[2411.0, 941.0], [2470.0, 941.0], [2470.0, 1598.0], [2411.0, 1598.0]], ('Your account balance is', 0.****************)]\n", "[[[3768.0, 1065.0], [3852.0, 1066.0], [3843.0, 2114.0], [3759.0, 2113.0]], ('\"Caring for creatures great and small.\"', 0.****************)]\n", "[[[2591.0, 1078.0], [2650.0, 1078.0], [2650.0, 2105.0], [2591.0, 2105.0]], ('Account Name: Benetook Veterinary Clinic', 0.****************)]\n", "[[[1512.0, 1254.0], [1571.0, 1254.0], [1571.0, 1873.0], [1512.0, 1873.0]], ('Vaccine Protech C5 (C4+', 0.***************)]\n", "[[[1684.0, 1246.0], [1739.0, 1246.0], [1739.0, 1869.0], [1684.0, 1869.0]], ('Parvovirus, Parainfluenza,', 0.****************)]\n", "[[[1995.0, 1254.0], [2050.0, 1255.0], [2041.0, 1873.0], [1986.0, 1872.0]], ('Distemper, Parainfluenza,', 0.****************)]\n", "[[[1621.0, 1281.0], [1676.0, 1280.0], [1685.0, 1877.0], [1630.0, 1877.0]], ('<PERSON><PERSON><PERSON>,Adenovirus,', 0.****************)]\n", "[[[2050.0, 1289.0], [2108.0, 1289.0], [2108.0, 1864.0], [2050.0, 1864.0]], ('Parvovirus, Adenovirus', 0.****************)]\n", "[[[2482.0, 1293.0], [2537.0, 1293.0], [2537.0, 1856.0], [2482.0, 1856.0]], ('Direct Deposit Details', 0.****************)]\n", "[[[1915.0, 1349.0], [1986.0, 1349.0], [1991.0, 1877.0], [1920.0, 1877.0]], ('Vaccine Protech C4', 0.****************)]\n", "[[[2537.0, 1357.0], [2596.0, 1357.0], [2596.0, 1826.0], [2537.0, 1826.0]], ('BankANZ Bank', 0.****************)]\n", "[[[2705.0, 1366.0], [2759.0, 1366.0], [2759.0, 1813.0], [2705.0, 1813.0]], ('ACC:*********', 0.****************)]\n", "[[[2759.0, 1370.0], [2814.0, 1370.0], [2814.0, 1826.0], [2759.0, 1826.0]], (\"REF:'HEALY33457\", 0.****************)]\n", "[[[3545.0, 1370.0], [3604.0, 1370.0], [3604.0, 1873.0], [3545.0, 1873.0]], ('Booster Vaccination', 0.****************)]\n", "[[[1365.0, 1405.0], [1424.0, 1405.0], [1424.0, 1881.0], [1365.0, 1881.0]], ('Service Provided', 0.****************)]\n", "[[[2646.0, 1409.0], [2701.0, 1409.0], [2701.0, 1770.0], [2646.0, 1770.0]], ('BSB:013725', 0.****************)]\n", "[[[244.0, 1576.0], [420.0, 1576.0], [420.0, 2474.0], [244.0, 2474.0]], ('<PERSON><PERSON><PERSON>', 0.****************)]\n", "[[[1743.0, 1606.0], [1789.0, 1606.0], [1789.0, 1869.0], [1743.0, 1869.0]], ('<PERSON><PERSON><PERSON><PERSON>', 0.***************)]\n", "[[[1579.0, 1757.0], [1621.0, 1757.0], [1621.0, 1877.0], [1579.0, 1877.0]], ('<PERSON>', 0.*********5334778)]\n", "[[[3612.0, 1933.0], [3671.0, 1934.0], [3662.0, 2487.0], [3603.0, 2486.0]], ('<PERSON><PERSON><PERSON><PERSON> +1 other(s)', 0.9384521245956421)]\n", "[[[1625.0, 2066.0], [1684.0, 2066.0], [1684.0, 2500.0], [1625.0, 2500.0]], ('Other Procedures', 0.9881192445755005)]\n", "[[[1986.0, 2062.0], [2045.0, 2062.0], [2050.0, 2491.0], [1991.0, 2492.0]], ('Other Procedures', 0.987877607345581)]\n", "[[[928.0, 2109.0], [1000.0, 2110.0], [995.0, 2578.0], [924.0, 2577.0]], ('Mr <PERSON>', 0.9560052752494812)]\n", "[[[1000.0, 2109.0], [1054.0, 2109.0], [1054.0, 2569.0], [1000.0, 2569.0]], ('18 Garreffa Parade', 0.9627304077148438)]\n", "[[[1041.0, 2110.0], [1113.0, 2109.0], [1117.0, 2577.0], [1046.0, 2578.0]], ('Euston NSW 2737', 0.9161977171897888)]\n", "[[[1370.0, 2126.0], [1429.0, 2127.0], [1419.0, 2505.0], [1360.0, 2503.0]], ('Product Type', 0.975990355014801)]\n", "[[[1936.0, 2195.0], [1982.0, 2195.0], [1982.0, 2496.0], [1936.0, 2496.0]], ('Medication', 0.9962364435195923)]\n", "[[[1449.0, 2217.0], [1508.0, 2216.0], [1512.0, 2590.0], [1453.0, 2590.0]], ('(ID:148082', 0.8954535722732544)]\n", "[[[1525.0, 2221.0], [1571.0, 2221.0], [1571.0, 2526.0], [1525.0, 2526.0]], ('Medication', 0.9927571415901184)]\n", "[[[1852.0, 2261.0], [1923.0, 2259.0], [1937.0, 2851.0], [1865.0, 2853.0]], ('<PERSON> (ID:173785)', 0.9161924719810486)]\n", "[[[3550.0, 2279.0], [3609.0, 2284.0], [3594.0, 2493.0], [3535.0, 2488.0]], ('<PERSON>', 0.9921216368675232)]\n", "[[[1462.0, 2569.0], [1508.0, 2569.0], [1508.0, 2856.0], [1462.0, 2856.0]], ('<PERSON><PERSON><PERSON><PERSON>', 0.9953186511993408)]\n", "[[[1520.0, 2569.0], [1567.0, 2569.0], [1567.0, 2775.0], [1520.0, 2775.0]], ('25/1/23', 0.9985703825950623)]\n", "[[[3461.0, 2568.0], [3524.0, 2570.0], [3515.0, 2849.0], [3452.0, 2846.0]], ('<PERSON><PERSON><PERSON>', 0.9975506067276001)]\n", "[[[1365.0, 2625.0], [1424.0, 2625.0], [1424.0, 2861.0], [1365.0, 2861.0]], ('Patient', 0.9866142868995667)]\n", "[[[3541.0, 2633.0], [3587.0, 2633.0], [3587.0, 2839.0], [3541.0, 2839.0]], ('18/1/23', 0.9981176257133484)]\n", "[[[3608.0, 2633.0], [3654.0, 2633.0], [3654.0, 2835.0], [3608.0, 2835.0]], ('25/1/24', 0.9974393248558044)]\n"]}], "source": ["for res in result:\n", "    for line in res:\n", "        print(line)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-08T02:19:47.929969200Z", "start_time": "2023-11-08T02:19:47.921971400Z"}}, "id": "2ef8edec5c02d6a2"}, {"cell_type": "code", "execution_count": 9, "outputs": [], "source": ["image = Image.open(image_path).convert('RGB')\n", "boxes = [line[0] for line in result[0]]\n", "txts = [line[1][0] for line in result[0]]\n", "scores = [line[1][1] for line in result[0]]\n", "im_show = draw_ocr(image, boxes, txts, scores, font_path=(FONT_DIR / 'HARMONYOS.ttf').as_posix())\n", "im_show = Image.fromarray(im_show)\n", "im_show.save(OUTPUT_DIR / 'result.jpg')"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-11-08T02:20:30.467749700Z", "start_time": "2023-11-08T02:20:29.466177900Z"}}, "id": "811b228490dc64da"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}