{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BIRI-9653_fallout_rule_design\n", "\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["#\n", "data_folder_path = \"/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["* file broken check"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["# image check\n", "import fitz  # PyMuPDF\n", "from PIL import Image\n", "from pathlib import Path\n", "from loguru import logger\n", "import os\n", "\n", "def check_pdf(filepath):\n", "    try:\n", "        with fitz.open(filepath) as doc:\n", "            doc.load_page(0)  # Try loading the first page\n", "        return True\n", "    except Exception as e:\n", "        logger.exception(f\"PDF error: {e}\")\n", "        return False\n", "\n", "def check_image(filepath):\n", "    try:\n", "        with Image.open(filepath) as img:\n", "            img.verify()  # Verifies image integrity\n", "        return True\n", "    except Exception as e:\n", "        logger.exception(f\"JPG error: {e}\")\n", "        return False\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-21 03:15:54.304\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m48d16278-7a91-4d47-b916-0b1360d784ab.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.305\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m37d281d0-903c-482d-aec6-a603544de1d6.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.306\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md444e324-a0cd-4949-aac8-311cb8b591e7.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m89926c19-088e-49e8-9bf7-3dc947307dad.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.310\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m67dc83a3-e15d-4869-8168-659a8a50cd9e.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.311\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8d3e1857-b6c2-4b94-b757-798cf906675b.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.312\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mb2eee775-e155-4b3f-8dd9-2a1a6f41e285.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.315\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mf8ebb75c-b0b0-4209-8f4e-e37370507f40.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.317\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m5601aa3e-b487-4e8d-9ae1-0feaacfd0be8.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.318\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2e5efa44-3660-40df-9701-245d015f3771.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.319\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mfa6f85ee-cd21-4eef-8805-939fbcd36712.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.320\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m91df8902-e51d-4fe3-be8b-6af88d9e6a42.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.321\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m99bababe-a109-413f-90e4-608f1eb9e293.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.322\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m3e463fdf-fbf8-4366-8b07-0ba07f62df6c.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.326\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m08b8af75-483d-4160-9fba-f75344f4ce4f.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.327\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb514aa28-d6db-4360-87f9-0554233f4a86.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.329\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m4e9ff370-bc9c-4d1e-aa33-2313d90139b8.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.329\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1ma6742d14-2825-4ec5-9f60-c180daaf0b78.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.331\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m7f7ce38b-533e-46c7-a7dd-618fd83c2f5e.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.332\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md757112f-9c32-4a9b-81e7-140f1d30e717.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.333\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mc9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.334\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m0ca22e7d-42bb-45b5-8ba0-212faba169ad.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.337\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m42c562c8-ad0a-4119-9b2a-a98702470659.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.339\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.342\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1ma37382de-b1cc-4425-8f2f-bb6cae59a0f8.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.344\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m20238f33-c059-4eeb-8544-0bfd9eb555ef.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.346\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mc607919d-3678-491f-823f-d6c75818c995.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.348\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m474466f5-70ca-4c63-83f8-bae13e06e70e.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.348\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.370\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mcheck_image\u001b[0m:\u001b[36m23\u001b[0m - \u001b[31m\u001b[1mJPG error: cannot identify image file '/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI/fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg'\u001b[0m\n", "\u001b[33m\u001b[1mTraceback (most recent call last):\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 196, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "           │         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "           │         └ <code object <module> at 0x7fb883349000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "           └ <function _run_code at 0x7fb8831813f0>\n", "  File \"/usr/local/lib/python3.10/runpy.py\", line 86, in _run_code\n", "    exec(code, run_globals)\n", "         │     └ {'__name__': '__main__', '__doc__': 'Entry point for launching an IPython kernel.\\n\\nThis is separate from the ipykernel pack...\n", "         └ <code object <module> at 0x7fb883349000, file \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 1>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "    │   └ <bound method Application.launch_instance of <class 'ipykernel.kernelapp.IPKernelApp'>>\n", "    └ <module 'ipykernel.kernelapp' from '/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py'>\n", "  File \"/usr/local/lib/python3.10/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "    │   └ <function IPKernelApp.start at 0x7fb880b79ab0>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7fb8832b0dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "    │    │       └ <function BaseAsyncIOLoop.start at 0x7fb8809c97e0>\n", "    │    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7fb8809ba950>\n", "    └ <ipykernel.kernelapp.IPKernelApp object at 0x7fb8832b0dc0>\n", "  File \"/usr/local/lib/python3.10/site-packages/tornado/platform/asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "    │    │            └ <function BaseEventLoop.run_forever at 0x7fb882268310>\n", "    │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "    └ <tornado.platform.asyncio.AsyncIOMainLoop object at 0x7fb8809ba950>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 603, in run_forever\n", "    self._run_once()\n", "    │    └ <function BaseEventLoop._run_once at 0x7fb882269e10>\n", "    └ <_UnixSelectorEventLoop running=True closed=False debug=False>\n", "  File \"/usr/local/lib/python3.10/asyncio/base_events.py\", line 1909, in _run_once\n", "    handle._run()\n", "    │      └ <function Handle._run at 0x7fb8824497e0>\n", "    └ <Handle Task.task_wakeup(<Future finis...6B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "    │    │            │    │           │    └ <member '_args' of 'Handle' objects>\n", "    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...6B)>, ...],))>)>\n", "    │    │            │    └ <member '_callback' of 'Handle' objects>\n", "    │    │            └ <Handle Task.task_wakeup(<Future finis...6B)>, ...],))>)>\n", "    │    └ <member '_context' of 'Handle' objects>\n", "    └ <Handle Task.task_wakeup(<Future finis...6B)>, ...],))>)>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "          │    └ <function Kernel.process_one at 0x7fb880b34280>\n", "          └ <ipykernel.ipkernel.IPythonKernel object at 0x7fb8809baf20>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "          │         └ ([<zmq.<PERSON>ame(b'209d4bda-ad3'...36B)>, <zmq.<PERSON>ame(b'<IDS|MSG>')>, <zmq.<PERSON>ame(b'c6c46dd0c5cc'...64B)>, <zmq.<PERSON>ame(b'{\"date\":\"20...\n", "          └ <bound method Kernel.dispatch_shell of <ipykernel.ipkernel.IPythonKernel object at 0x7fb8809baf20>>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "          └ <coroutine object IPythonKernel.execute_request at 0x7fb87eb4ccf0>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "                                  │       │      └ {'header': {'date': datetime.datetime(2024, 10, 21, 3, 15, 54, 293000, tzinfo=tzutc()), 'msg_id': '605bbe1c-4563-4045-a744-1a...\n", "                                  │       └ [b'209d4bda-ad3b-4a9d-b0c2-8471755afcca']\n", "                                  └ <zmq.eventloop.zmqstream.ZMQStream object at 0x7fb8809ba680>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "                          └ <coroutine object IPythonKernel.do_execute at 0x7fb87c13b300>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "          │     └ <function ZMQInteractiveShell.run_cell at 0x7fb880b630a0>\n", "          └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7fb87eb68070>\n", "  File \"/usr/local/lib/python3.10/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "                             │       └ {'store_history': True, 'silent': False, 'cell_id': 'vscode-notebook-cell://dev-container%2B7b22686f737450617468223a222f686f6...\n", "                             └ ('\\nfor file_name in os.listdir(data_folder_path):\\n    suffix = str(Path(file_name).suffix)\\n    if suffix.lower() == \".pdf\"...\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3075, in run_cell\n", "    result = self._run_cell(\n", "             │    └ <function InteractiveShell._run_cell at 0x7fb8816b1ea0>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7fb87eb68070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3130, in _run_cell\n", "    result = runner(coro)\n", "             │      └ <coroutine object InteractiveShell.run_cell_async at 0x7fb87d7f1770>\n", "             └ <function _pseudo_sync_runner at 0x7fb88169d7e0>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "    │    └ <method 'send' of 'coroutine' objects>\n", "    └ <coroutine object InteractiveShell.run_cell_async at 0x7fb87d7f1770>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3334, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "                       │    │             │        │     └ '/tmp/ipykernel_30853/2123814658.py'\n", "                       │    │             │        └ [<ast.For object at 0x7fb87d707940>]\n", "                       │    │             └ <ast.Module object at 0x7fb87d706a40>\n", "                       │    └ <function InteractiveShell.run_ast_nodes at 0x7fb8816b2170>\n", "                       └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7fb87eb68070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3517, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "             │    │        │     │              └ False\n", "             │    │        │     └ <ExecutionResult object at 7fb87d7079d0, execution_count=3 error_before_exec=None error_in_exec=None info=<ExecutionInfo obje...\n", "             │    │        └ <code object <module> at 0x7fb877f0b680, file \"/tmp/ipykernel_30853/2123814658.py\", line 1>\n", "             │    └ <function InteractiveShell.run_code at 0x7fb8816b2200>\n", "             └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7fb87eb68070>\n", "  File \"/usr/local/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3577, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "         │         │    │               │    └ {'__name__': '__main__', '__doc__': 'Automatically created module for IPython interactive environment', '__package__': None, ...\n", "         │         │    │               └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7fb87eb68070>\n", "         │         │    └ <property object at 0x7fb8816a4860>\n", "         │         └ <ipykernel.zmqshell.ZMQInteractiveShell object at 0x7fb87eb68070>\n", "         └ <code object <module> at 0x7fb877f0b680, file \"/tmp/ipykernel_30853/2123814658.py\", line 1>\n", "\n", "  File \"\u001b[32m/tmp/ipykernel_30853/\u001b[0m\u001b[32m\u001b[1m2123814658.py\u001b[0m\", line \u001b[33m6\u001b[0m, in \u001b[35m<module>\u001b[0m\n", "    \u001b[1mlogger\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1minfo\u001b[0m\u001b[1m(\u001b[0m\u001b[36mf\"{file_name} Image format verification: {check_image(os.path.join(data_folder_path, file_name))}\"\u001b[0m\u001b[1m)\u001b[0m\n", "    \u001b[36m│      └ \u001b[0m\u001b[36m\u001b[1m<function Logger.info at 0x7fb877f11630>\u001b[0m\n", "    \u001b[36m└ \u001b[0m\u001b[36m\u001b[1m<loguru.logger handlers=[(id=0, level=10, sink=stderr)]>\u001b[0m\n", "\n", "> File \"\u001b[32m/tmp/ipykernel_30853/\u001b[0m\u001b[32m\u001b[1m1584147433.py\u001b[0m\", line \u001b[33m19\u001b[0m, in \u001b[35mcheck_image\u001b[0m\n", "    \u001b[35m\u001b[1mwith\u001b[0m \u001b[1mImage\u001b[0m\u001b[35m\u001b[1m.\u001b[0m\u001b[1mopen\u001b[0m\u001b[1m(\u001b[0m\u001b[1mfilepath\u001b[0m\u001b[1m)\u001b[0m \u001b[35m\u001b[1mas\u001b[0m \u001b[1mimg\u001b[0m\u001b[1m:\u001b[0m\n", "    \u001b[36m     │     │    └ \u001b[0m\u001b[36m\u001b[1m'/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI/fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg'\u001b[0m\n", "    \u001b[36m     │     └ \u001b[0m\u001b[36m\u001b[1m<function open at 0x7fb87c1a7eb0>\u001b[0m\n", "    \u001b[36m     └ \u001b[0m\u001b[36m\u001b[1m<module 'PIL.Image' from '/usr/local/lib/python3.10/site-packages/PIL/Image.py'>\u001b[0m\n", "\n", "  File \"/usr/local/lib/python3.10/site-packages/PIL/Image.py\", line 3498, in open\n", "    raise UnidentifiedImageError(msg)\n", "          │                      └ \"cannot identify image file '/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI/fa970016-e3b5-4013-a5e4-cca5d4...\n", "          └ <class 'PIL.UnidentifiedImageError'>\n", "\n", "\u001b[31m\u001b[1mPIL.UnidentifiedImageError\u001b[0m:\u001b[1m cannot identify image file '/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI/fa970016-e3b5-4013-a5e4-cca5d49418ad.jpg'\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.398\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mfa970016-e3b5-4013-a5e4-cca5d49418ad.jpg Image format verification: False\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.400\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m519d1e33-094f-4c75-a2a4-2aebac4455aa.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.401\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mce810450-7f72-490b-9e09-c3d5ae1dad88.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.402\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1ma7f99b6b-8c72-44b0-9550-46cae60ee18c.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.403\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1me4efb75c-f421-4f10-aed9-61e6004938c6.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.405\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.406\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1me10d92e1-0995-49c9-ab4d-950e04f11c8e.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.407\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m46b70eea-f82b-4f9e-b453-9fbdc0104549.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.409\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.410\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m75d665c1-a17c-4882-9804-7574b221dd21.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.411\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mc6282f11-1e0f-4213-9d54-9a5138f72441.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.412\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mdbd5b507-08c8-47bd-b98b-e030fb9b468b.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.413\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m56210846-cdf7-4cc4-9489-56e5946d27a4.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.415\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf56eb768-41d9-4cdd-bbe6-4969cc51ed86.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.415\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m6da16dc2-eb22-4e29-830a-2aa9db3bd599.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.417\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m00dcaff3-051f-4064-ae19-254264e8e242.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.418\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m29eb8c55-14df-4a91-b2bb-9bc29ab69a63.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.420\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m54addd43-e63e-495d-9b59-fcd34768a072.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.421\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m6bf46030-cb4a-4add-bbf3-fbd57d3e9248.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.422\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m4c52e8fd-f13c-4a77-961a-7e69294d66e4.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.425\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m4c03cf6c-0701-434b-a779-399d45ce051d.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.427\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m2836a967-2f4c-4655-bf35-c148e26d9f07.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.428\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m4626aa97-e040-494f-9896-0469eeffd02f.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.429\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m92ceb1a1-2fce-4468-abd8-e477b7b5d317.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.430\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m79796bd8-ae1a-4771-bab8-ff65fa35a4f7.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.431\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m65e2706d-f5b0-4bd3-96ad-13a16bb710e0.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.433\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m7f6f0d3e-6892-497f-b571-57c5542830a4.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.436\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mbbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.437\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1me2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.439\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m204231ce-913e-405c-9132-20cfd100a14b.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.440\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m1085fe16-0014-489d-bfab-6d005298074a.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.441\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m5686cd7a-c2a6-45cc-95a5-902caa94daa4.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.442\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m0f88ab75-a51e-4799-a0fb-49eb3f2a9161.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.446\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.447\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m30d78167-a823-44da-a272-a5b36acc66aa.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.448\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1ma85e2454-2d4a-4813-bd81-1af1c54dcc9a.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.450\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m392f63c9-1dc3-48ba-a563-f90b8c923058.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.450\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mf13c4fa7-0df6-4b48-816a-e11e55d5e9f0.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.452\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m4234dfb8-5e66-452a-88ba-d520698fb608.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.453\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1md0e3a9af-80ec-4b89-b87b-26869dbaeed8.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.454\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1ma32b55fe-0d0b-4a0f-88a1-48d9af852ff0.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.455\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mb7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.456\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.457\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.458\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mf5ff435f-9fc1-467a-97a9-bd304891ebec.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.460\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m728e11b5-5923-456e-8c13-b857e7b11a3d.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.461\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mb1f83929-9b81-402f-8b37-13065b355db1.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.462\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m70f5599a-60dd-4b2c-9f53-ecf07cc714a5.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.463\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mcdc164de-ca0a-4f3d-9bde-3552f06ae74a.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.465\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1med611268-36c2-4c56-bee4-26f1005ddca2.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.466\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mcfdcf6cd-0ad0-4db5-abf9-197f9e33a711.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.467\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m3d59fa71-6b7c-4c5a-93af-cc5bc87036fd.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.468\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1mfbae41f6-4864-419e-bed0-944c831570f2.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.469\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m811bf137-b832-4774-a0ba-42314e20fae2.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.470\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m10ec75dd-e526-444f-895b-7faac9c0cd93.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.471\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mf43fba4c-2325-4151-8cfc-263fd2f04d11.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.472\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md9b8c26d-6e6d-48d3-a763-f8f45fd91477.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.473\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1me23da3d2-dfda-43d2-977e-b19ef997f205.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.474\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1me4f69236-845c-4fcb-abf9-9df336b0478f.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.475\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m9356b36f-4e13-4733-9c77-dc8555add32e.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.476\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1md80c66ae-b17d-4c47-840d-da293939f843.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.478\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9.pdf PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.479\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.481\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mebb628ae-1660-46f1-8aed-e674bc2fa512.jpeg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.483\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1meb362ff0-c953-4049-9810-0bfbffc1772c.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.485\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1mba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.png Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.487\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m90683a89-d579-4d61-962c-0625189d0f5b.PDF PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.488\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m6\u001b[0m - \u001b[1m426dd3f2-6ad7-4a93-a801-71647893c19d.jpg Image format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.489\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1m452156dd-abc3-4c1d-b31c-fde48aadf60c.PDF PDF format verification: True\u001b[0m\n", "\u001b[32m2024-10-21 03:15:54.490\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m4\u001b[0m - \u001b[1md0be39b5-e383-4ffa-9a15-901af56e5baa.pdf PDF format verification: True\u001b[0m\n"]}], "source": ["\n", "for file_name in os.listdir(data_folder_path):\n", "    suffix = str(Path(file_name).suffix)\n", "    if suffix.lower() == \".pdf\":\n", "        logger.info(f\"{file_name} PDF format verification: {check_pdf(os.path.join(data_folder_path, file_name))}\")\n", "    elif suffix.lower() in [\".jpeg\", \".jpg\", \".png\", \".bmp\", \".tiff\"]:\n", "        logger.info(f\"{file_name} Image format verification: {check_image(os.path.join(data_folder_path, file_name))}\")\n", "    else:\n", "        logger.exception(f\"{file_name} has unsupported file format\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* ABN extraction and validation rule"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["import re \n", "from typing import List\n", "\n", "abn_extract_regex = r\"(?:\\d *){11}\"\n", "abn_extract_regex1 = r\"\\d{2}-\\d{3}-\\d{3}-\\d{3}\"\n", "\n", "\n", "def validate_abn(nums: List[int]) -> bool:\n", "    if len(nums) != 11:\n", "        return False\n", "    if not all(isinstance(x, int) for x in nums):\n", "        return False\n", "    if any(x>9 for x in nums):\n", "        return False\n", "    if any(x<0 for x in nums):\n", "        return False\n", "\n", "    s = sum(w*(n - int(i==0)) for i, (n, w) in enumerate(zip(nums, [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19])))\n", "    return s%89 == 0\n", "\n", "\n", "def get_abn(content: str) -> List[str]:\n", "    matches = re.findall(abn_extract_regex, content)\n", "    matches1 = re.findall(abn_extract_regex1, content)\n", "    ans = []\n", "    for match in matches + matches1:\n", "        match_num = []\n", "        for c in match:\n", "            try:\n", "                int_c = int(c)\n", "                match_num.append(int_c)\n", "            except ValueError:\n", "                continue\n", "        if validate_abn(match_num):\n", "            ans.append(match_num)\n", "\n", "    ans = list({\"\".join([str(x) for x in abn]) for abn in ans})\n", "    return ans"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["['***********', '***********', '***********']"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["get_abn(\"ABN Number: 89-*********** ABN: *********** ABN 79 ***********\\nM<PERSON> <PERSON>\\nABN 79 ***********\\nInvoice Date\\n22/09/2024\\n27 Yating Ave\\n5990855\\nSCHOFIELDS NSW 2762\\nTransaction No\\nPatient\\nDudu\\nReference\\nDr. Grace Ma N8150\\nDetails\\nTax Invoice for Professional Services\\nService Provided on 22/09/2024\\nNo\\nAmount\\nVeterinary Consultation & Examination\\n1.00\\n150.50\\nMedications\\nMetrogyl 200mg Tablets\\n6.00\\n38.50\\nPro-Kolin Syringe 30ml\\n1.00\\n46.00\\nFoods, Nutrition & Supplements\\nHills I/d Canine Dry\\n1.00\\n99.00\\nTotal:\\n334.00\\nIncludes Tax of:\\n30.36\\nAmount Paid\\n334.00\\nPAID\\nBalance remaining\\n0.00\\nRef - 195767 - 22/09/2024 - 09:37:17\")"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-22 00:54:09.817\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: e2b44cd9-f38b-4337-a537-19d7c39bfc4b.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.821\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.826\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 20238f33-c059-4eeb-8544-0bfd9eb555ef.json, has ABN mentioned: <PERSON><PERSON><PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.833\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.836\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 4234dfb8-5e66-452a-88ba-d520698fb608.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.838\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.839\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.842\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 6da16dc2-eb22-4e29-830a-2aa9db3bd599.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.850\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.854\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.858\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: d444e324-a0cd-4949-aac8-311cb8b591e7-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.863\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.869\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.875\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 54addd43-e63e-495d-9b59-fcd34768a072-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.880\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 67dc83a3-e15d-4869-8168-659a8a50cd9e.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.883\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-4.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.887\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.j<PERSON>, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.894\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.909\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.917\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.j<PERSON>, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.925\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 56210846-cdf7-4cc4-9489-56e5946d27a4.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.930\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.937\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.941\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: f8ebb75c-b0b0-4209-8f4e-e37370507f40.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.944\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.json, has ABN mentioned: True, ABNs: ['***********', '***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.947\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: d757112f-9c32-4a9b-81e7-140f1d30e717-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.951\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 89926c19-088e-49e8-9bf7-3dc947307dad-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.953\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 08b8af75-483d-4160-9fba-f75344f4ce4f-1.json, has ABN mentioned: <PERSON><PERSON><PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.956\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.958\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: ed611268-36c2-4c56-bee4-26f1005ddca2-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.964\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.968\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 728e11b5-5923-456e-8c13-b857e7b11a3d.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.972\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: ebb628ae-1660-46f1-8aed-e674bc2fa512.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.976\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 1085fe16-0014-489d-bfab-6d005298074a-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.981\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 9356b36f-4e13-4733-9c77-dc8555add32e-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.983\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 90683a89-d579-4d61-962c-0625189d0f5b-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.985\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.987\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 46b70eea-f82b-4f9e-b453-9fbdc0104549.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:09.996\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: f43fba4c-2325-4151-8cfc-263fd2f04d11.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.001\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 5686cd7a-c2a6-45cc-95a5-902caa94daa4.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.002\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.j<PERSON>, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.006\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 08b8af75-483d-4160-9fba-f75344f4ce4f-0.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.010\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: b2eee775-e155-4b3f-8dd9-2a1a6f41e285.json, has ABN mentioned: True, ABNs: ['***********', '***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.015\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: dbd5b507-08c8-47bd-b98b-e030fb9b468b.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.022\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 4c03cf6c-0701-434b-a779-399d45ce051d.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.024\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.j<PERSON>, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.027\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.031\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.036\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-1.j<PERSON>, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.040\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 426dd3f2-6ad7-4a93-a801-71647893c19d.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.046\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 2836a967-2f4c-4655-bf35-c148e26d9f07-0.json, has ABN mentioned: True, ABNs: ['***********', '***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.051\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: fbae41f6-4864-419e-bed0-944c831570f2-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.056\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 10ec75dd-e526-444f-895b-7faac9c0cd93.json, has ABN mentioned: <PERSON><PERSON><PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.063\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: c6282f11-1e0f-4213-9d54-9a5138f72441.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.068\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 204231ce-913e-405c-9132-20cfd100a14b.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.072\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 8d3e1857-b6c2-4b94-b757-798cf906675b-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.075\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.080\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.083\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: eb362ff0-c953-4049-9810-0bfbffc1772c.json, has ABN mentioned: True, ABNs: ['***********', '***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.086\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 99bababe-a109-413f-90e4-608f1eb9e293-0.j<PERSON>, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.091\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.094\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.100\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.102\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: fa6f85ee-cd21-4eef-8805-939fbcd36712-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.105\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 37d281d0-903c-482d-aec6-a603544de1d6-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.111\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 519d1e33-094f-4c75-a2a4-2aebac4455aa.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.118\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: c607919d-3678-491f-823f-d6c75818c995.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.122\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 75d665c1-a17c-4882-9804-7574b221dd21.json, has ABN mentioned: <PERSON><PERSON><PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.126\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: f5ff435f-9fc1-467a-97a9-bd304891ebec.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.130\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.133\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.140\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 392f63c9-1dc3-48ba-a563-f90b8c923058.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.144\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-2.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.146\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.149\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.154\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 0ca22e7d-42bb-45b5-8ba0-212faba169ad.json, has ABN mentioned: <PERSON>als<PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.159\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 0f3de264-9a93-45b5-a56a-1696edceb3ce.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.162\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: b514aa28-d6db-4360-87f9-0554233f4a86-0.json, has ABN mentioned: <PERSON><PERSON><PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.165\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.167\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.170\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.j<PERSON>, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.174\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: d80c66ae-b17d-4c47-840d-da293939f843.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.177\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: e4efb75c-f421-4f10-aed9-61e6004938c6.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.180\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 811bf137-b832-4774-a0ba-42314e20fae2-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.183\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 4626aa97-e040-494f-9896-0469eeffd02f-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.186\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.j<PERSON>, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.188\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 452156dd-abc3-4c1d-b31c-fde48aadf60c-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.191\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: e10d92e1-0995-49c9-ab4d-950e04f11c8e.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.195\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.200\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 00dcaff3-051f-4064-ae19-254264e8e242.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.204\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: d0e3a9af-80ec-4b89-b87b-26869dbaeed8.json, has ABN mentioned: True, ABNs: ['***********', '***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.206\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 42c562c8-ad0a-4119-9b2a-a98702470659.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.214\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: a7f99b6b-8c72-44b0-9550-46cae60ee18c.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.216\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.json, has ABN mentioned: Fals<PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.219\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 1085fe16-0014-489d-bfab-6d005298074a-1.json, has ABN mentioned: True, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.221\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.223\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 7f6f0d3e-6892-497f-b571-57c5542830a4.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.225\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.228\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.235\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 48d16278-7a91-4d47-b916-0b1360d784ab.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.239\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: fa970016-e3b5-4013-a5e4-cca5d49418ad.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.243\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: a6742d14-2825-4ec5-9f60-c180daaf0b78.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.247\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: ce810450-7f72-490b-9e09-c3d5ae1dad88.json, has ABN mentioned: <PERSON>als<PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.250\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 2e5efa44-3660-40df-9701-245d015f3771-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.253\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 474466f5-70ca-4c63-83f8-bae13e06e70e-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.257\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: d0be39b5-e383-4ffa-9a15-901af56e5baa-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.260\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: a37382de-b1cc-4425-8f2f-bb6cae59a0f8.json, has ABN mentioned: <PERSON>als<PERSON>, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.264\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: e23da3d2-dfda-43d2-977e-b19ef997f205.json, has ABN mentioned: True, ABNs: ['***********', '***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.266\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.json, has ABN mentioned: False, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.269\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-3.j<PERSON>, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.272\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.json, has ABN mentioned: False, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.274\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: 4626aa97-e040-494f-9896-0469eeffd02f-1.json, has ABN mentioned: True, ABNs: []\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.276\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m14\u001b[0m - \u001b[1mfile: 30d78167-a823-44da-a272-a5b36acc66aa-0.json, has ABN mentioned: True, ABNs: ['***********']\u001b[0m\n", "\u001b[32m2024-10-22 00:54:10.279\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m16\u001b[0m - \u001b[31m\u001b[1mfile: e4f69236-845c-4fcb-abf9-9df336b0478f-0.json, has ABN mentioned: True, ABNs: []\u001b[0m\n"]}], "source": ["import json\n", "DI_res_folder_path = \"/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI_res\"\n", "\n", "for file_name in os.listdir(DI_res_folder_path):\n", "    suffix = str(Path(file_name).suffix)\n", "\n", "    if suffix == \".json\" and file_name != \"ans.json\":\n", "        with open(os.path.join(DI_res_folder_path, file_name), \"r\") as f:\n", "            res = json.load(f)\n", "        content = res.get(\"content\", \"\")\n", "        abn = get_abn(content)\n", "        abn_mentioned = \"abn\" in content.lower() or \"a.b.n.\" in content.lower()\n", "        if (abn_mentioned and len(abn) == 1) or (not abn_mentioned and len(abn) == 0):\n", "            logger.info(f\"file: {file_name}, has ABN mentioned: {abn_mentioned}, ABNs: {abn}\")\n", "        else:\n", "            logger.error(f\"file: {file_name}, has ABN mentioned: {abn_mentioned}, ABNs: {abn}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* invoice no fuzzy match from PaddleOCR result"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_path</th>\n", "      <th>content</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1,450.00 99.24 275.00 51.48 FREE 2,634.57 239....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.pdf</td>\n", "      <td>GREATER SPRINGFIELD VETERINARY Excellence with...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg</td>\n", "      <td>Gungurru Veterinary Centre Wyatt Grove Shoppin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0ca22e7d-42bb-45b5-8ba0-212faba169ad.png</td>\n", "      <td>2:08 PM Sat 14 Sep 100%  Tm 7 117 Chapel St St...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg</td>\n", "      <td>Tax Invoice $648.10 000$ $648.10 000$ $606.48 ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_path  \\\n", "0  00dcaff3-051f-4064-ae19-254264e8e242.jpeg   \n", "1   08b8af75-483d-4160-9fba-f75344f4ce4f.pdf   \n", "2   0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg   \n", "3   0ca22e7d-42bb-45b5-8ba0-212faba169ad.png   \n", "4   0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg   \n", "\n", "                                             content  \n", "0  1,450.00 99.24 275.00 51.48 FREE 2,634.57 239....  \n", "1  GREATER SPRINGFIELD VETERINARY Excellence with...  \n", "2  Gungurru Veterinary Centre Wyatt Grove Shoppin...  \n", "3  2:08 PM Sat 14 Sep 100%  Tm 7 117 Chapel St St...  \n", "4  Tax Invoice $648.10 000$ $648.10 000$ $606.48 ...  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "PADDLEOCR_res_path = \"/workspaces/OCR_in_house/data/OCR_in_house/data/100_samples_DI_paddleocr.csv\"\n", "paddleocr_df = pd.read_csv(PADDLEOCR_res_path)\n", "paddleocr_df.head()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_path</th>\n", "      <th>content</th>\n", "      <th>file_stem</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242.jpeg</td>\n", "      <td>1,450.00 99.24 275.00 51.48 FREE 2,634.57 239....</td>\n", "      <td>00dcaff3-051f-4064-ae19-254264e8e242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f.pdf</td>\n", "      <td>GREATER SPRINGFIELD VETERINARY Excellence with...</td>\n", "      <td>08b8af75-483d-4160-9fba-f75344f4ce4f</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg</td>\n", "      <td>Gungurru Veterinary Centre Wyatt Grove Shoppin...</td>\n", "      <td>0b2c5c86-90a8-4490-af46-2cb4a460b5bd</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0ca22e7d-42bb-45b5-8ba0-212faba169ad.png</td>\n", "      <td>2:08 PM Sat 14 Sep 100%  Tm 7 117 Chapel St St...</td>\n", "      <td>0ca22e7d-42bb-45b5-8ba0-212faba169ad</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg</td>\n", "      <td>Tax Invoice $648.10 000$ $648.10 000$ $606.48 ...</td>\n", "      <td>0f3de264-9a93-45b5-a56a-1696edceb3ce</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_path  \\\n", "0  00dcaff3-051f-4064-ae19-254264e8e242.jpeg   \n", "1   08b8af75-483d-4160-9fba-f75344f4ce4f.pdf   \n", "2   0b2c5c86-90a8-4490-af46-2cb4a460b5bd.jpg   \n", "3   0ca22e7d-42bb-45b5-8ba0-212faba169ad.png   \n", "4   0f3de264-9a93-45b5-a56a-1696edceb3ce.jpg   \n", "\n", "                                             content  \\\n", "0  1,450.00 99.24 275.00 51.48 FREE 2,634.57 239....   \n", "1  GREATER SPRINGFIELD VETERINARY Excellence with...   \n", "2  Gungurru Veterinary Centre Wyatt Grove Shoppin...   \n", "3  2:08 PM Sat 14 Sep 100%  Tm 7 117 Chapel St St...   \n", "4  Tax Invoice $648.10 000$ $648.10 000$ $606.48 ...   \n", "\n", "                              file_stem  \n", "0  00dcaff3-051f-4064-ae19-254264e8e242  \n", "1  08b8af75-483d-4160-9fba-f75344f4ce4f  \n", "2  0b2c5c86-90a8-4490-af46-2cb4a460b5bd  \n", "3  0ca22e7d-42bb-45b5-8ba0-212faba169ad  \n", "4  0f3de264-9a93-45b5-a56a-1696edceb3ce  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from pathlib import Path\n", "paddleocr_df[\"file_stem\"] = paddleocr_df[\"file_path\"].apply(lambda x: str(Path(x).stem))\n", "paddleocr_df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def find_similar_substring(content: str, target: str, max_diff: int = 2):\n", "    \"\"\"\n", "    Find a substring in the content that has the same length as the target substring,\n", "    with only one or two different characters or digits.\n", "\n", "    Args:\n", "        content (str): The string to search within.\n", "        target (str): The substring to compare with.\n", "        max_diff (int): Maximum number of allowed differences. Default is 2.\n", "\n", "    Returns:\n", "        list: A list of matching substrings that differ by at most max_diff characters.\n", "    \"\"\"\n", "    target_len = len(target)\n", "    result = []\n", "\n", "    # Loop through content to get every possible substring of the same length as target\n", "    for i in range(len(content) - target_len + 1):\n", "        sub_str = content[i:i + target_len]\n", "        if i-1 >= 0 and content[i-1].isalnum():\n", "            continue\n", "        if i + target_len < len(content) and content[i + target_len].isalnum():\n", "            continue\n", "\n", "        # Count how many characters are different between sub_str and target\n", "        # di_invoice_no: 1012538, invoice_no_fuzzy_res: ['.00 58.', '. 1/21 ', '1012540'\n", "        diff_count = 0\n", "        for a, b in zip(sub_str.lower(), target.lower()):\n", "            if a!=b:\n", "                if a.isalnum() and b.isalnum():\n", "                    diff_count += 1\n", "                else:\n", "                    diff_count += max_diff+1\n", "                    break\n", "                # if b.isalnum() and not a.isalnum():\n", "                #     diff_count += max_diff\n", "                #     break\n", "                # elif not b.isalnum():\n", "                #     diff_count += max_diff\n", "                #     break\n", "        # If the difference is within the allowed limit, add to the result\n", "        if diff_count <= max_diff:\n", "            result.append(sub_str)\n", "\n", "    return list(set(result))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-28 02:54:23.266\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 00dcaff3-051f-4064-ae19-254264e8e242.json, NO extra invoice,invoice_no: 1/928537\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.302\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 08b8af75-483d-4160-9fba-f75344f4ce4f-0.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 1012538, invoice_no_fuzzy_res: ['1012540', '1012538']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 08b8af75-483d-4160-9fba-f75344f4ce4f-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.320\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.json, NO extra invoice,invoice_no: 316135\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.334\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 0ca22e7d-42bb-45b5-8ba0-212faba169ad.json, NO extra invoice,invoice_no: 180125\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.345\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 0f3de264-9a93-45b5-a56a-1696edceb3ce.json, NO extra invoice,invoice_no: 800513\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.354\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.json, #invoice_fuzzy: 1, di_invoice_no in invoice_fuzzy: False di_invoice_no: PAWaa4e22c41643, invoice_no_fuzzy_res: ['PAWaa4e22c41b43']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.366\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 1085fe16-0014-489d-bfab-6d005298074a-0.json, NO extra invoice,invoice_no: 296121\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.381\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 1085fe16-0014-489d-bfab-6d005298074a-1.json, NO extra invoice,invoice_no: 07 3207 6041\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.391\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 10ec75dd-e526-444f-895b-7faac9c0cd93.json, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.400\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.json, NO extra invoice,invoice_no: 942780\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.408\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 20238f33-c059-4eeb-8544-0bfd9eb555ef.json, NO extra invoice,invoice_no: 600140\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.424\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 204231ce-913e-405c-9132-20cfd100a14b.json, NO extra invoice,invoice_no: 1/928537\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.434\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 2836a967-2f4c-4655-bf35-c148e26d9f07-0.json, NO extra invoice,invoice_no: 91415\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.442\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.json, NO extra invoice,invoice_no: 2000011801\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.451\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.467\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.json, NO extra invoice,invoice_no: 738300\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.477\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 2e5efa44-3660-40df-9701-245d015f3771-0.json, NO extra invoice,invoice_no: HEV1681252256083\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.484\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.json, NO extra invoice,invoice_no: 31217706948419748\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.532\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 30d78167-a823-44da-a272-a5b36acc66aa-0.json, NO extra invoice,invoice_no: 480103\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.542\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 37d281d0-903c-482d-aec6-a603544de1d6-0.json, NO extra invoice,invoice_no: 781632\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.552\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 392f63c9-1dc3-48ba-a563-f90b8c923058.json, NO extra invoice,invoice_no: 548133\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.562\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.json, NO extra invoice,invoice_no: 478504\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.570\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.json, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.577\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 4234dfb8-5e66-452a-88ba-d520698fb608.json, NO extra invoice,invoice_no: 1078914\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.590\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 426dd3f2-6ad7-4a93-a801-71647893c19d.json, NO extra invoice,invoice_no: 468255\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.601\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 42c562c8-ad0a-4119-9b2a-a98702470659.json, NO extra invoice,invoice_no: 517905\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.610\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 452156dd-abc3-4c1d-b31c-fde48aadf60c-0.json, NO extra invoice,invoice_no: 1/195925\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.620\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 4626aa97-e040-494f-9896-0469eeffd02f-0.json, NO extra invoice,invoice_no: 284516\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.632\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 4626aa97-e040-494f-9896-0469eeffd02f-1.json, NO extra invoice,invoice_no: 3207 6041\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.639\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 46b70eea-f82b-4f9e-b453-9fbdc0104549.json, NO extra invoice,invoice_no: 1261715\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.648\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 474466f5-70ca-4c63-83f8-bae13e06e70e-0.json, NO extra invoice,invoice_no: 1470403\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.663\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 48d16278-7a91-4d47-b916-0b1360d784ab.json, NO extra invoice,invoice_no: 10018\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.675\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 4c03cf6c-0701-434b-a779-399d45ce051d.json, NO extra invoice,invoice_no: 1/928537\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.685\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 764948, invoice_no_fuzzy_res: ['764948', '764979']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.697\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 764979, invoice_no_fuzzy_res: ['764948', '764979']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.705\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.json, NO extra invoice,invoice_no: 1021793951\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.717\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.json, NO extra invoice,invoice_no: 21216171\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.729\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 519d1e33-094f-4c75-a2a4-2aebac4455aa.json, NO extra invoice,invoice_no: 636711\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.739\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 54addd43-e63e-495d-9b59-fcd34768a072-0.json, #invoice_fuzzy: 1, di_invoice_no in invoice_fuzzy: False di_invoice_no: T867edd8795a0, invoice_no_fuzzy_res: ['T8b7edd8795a0']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.752\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.json, NO extra invoice,invoice_no: 706637\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.760\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.768\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 56210846-cdf7-4cc4-9489-56e5946d27a4.json, NO extra invoice,invoice_no: 439974\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.777\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 5686cd7a-c2a6-45cc-95a5-902caa94daa4.json, NO extra invoice,invoice_no: 600084\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.788\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.json, NO extra invoice,invoice_no: 1402381\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.796\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 67dc83a3-e15d-4869-8168-659a8a50cd9e.json, NO extra invoice,invoice_no: 1041038\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.805\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.json, NO extra invoice,invoice_no: 397781\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.814\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.json, NO extra invoice,invoice_no: 600050\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.822\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 6da16dc2-eb22-4e29-830a-2aa9db3bd599.json, NO extra invoice,invoice_no: 74852\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.833\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.json, NO extra invoice,invoice_no: 20633\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.840\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.849\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 728e11b5-5923-456e-8c13-b857e7b11a3d.json, NO extra invoice,invoice_no: 524050\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.859\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 75d665c1-a17c-4882-9804-7574b221dd21.json, NO extra invoice,invoice_no: 489248\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.868\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.json, NO extra invoice,invoice_no: C68e8dfe29f09\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.875\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 7f6f0d3e-6892-497f-b571-57c5542830a4.json, NO extra invoice,invoice_no: 5890264\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.884\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.json, NO extra invoice,invoice_no: 612098\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.892\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 811bf137-b832-4774-a0ba-42314e20fae2-0.json, NO extra invoice,invoice_no: 189652\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.903\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 89926c19-088e-49e8-9bf7-3dc947307dad-0.json, NO extra invoice,invoice_no: 1821375\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.912\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 8d3e1857-b6c2-4b94-b757-798cf906675b-0.json, NO extra invoice,invoice_no: 89981237540988608\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.919\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.json, NO extra invoice,invoice_no: 5990855\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.926\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 90683a89-d579-4d61-962c-0625189d0f5b-0.json, NO extra invoice,invoice_no: 1437928\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.935\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.json, NO extra invoice,invoice_no: 386029\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.942\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.json, NO extra invoice,invoice_no: 2528535\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.947\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: 92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.955\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 9356b36f-4e13-4733-9c77-dc8555add32e-0.json, #invoice_fuzzy: 1, di_invoice_no in invoice_fuzzy: False di_invoice_no: 106643482599362400, invoice_no_fuzzy_res: ['106543492599362400']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.963\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: 99bababe-a109-413f-90e4-608f1eb9e293-0.json, NO extra invoice,invoice_no: 738240\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.974\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 188202, invoice_no_fuzzy_res: ['188202', '188201']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.982\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 311307, invoice_no_fuzzy_res: ['311308', '311307']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:23.990\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: a37382de-b1cc-4425-8f2f-bb6cae59a0f8.json, NO extra invoice,invoice_no: 646216\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.001\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: a6742d14-2825-4ec5-9f60-c180daaf0b78.json, NO extra invoice,invoice_no: 44983\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.008\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: a7f99b6b-8c72-44b0-9550-46cae60ee18c.json, NO extra invoice,invoice_no: 240146\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.014\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.json, NO extra invoice,invoice_no: DGT-246\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.022\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-0.json, NO extra invoice,invoice_no: 1/458705\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.029\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-1.<PERSON><PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.036\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-2.<PERSON><PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.044\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-3.json, NO extra invoice,invoice_no: 1/470230\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.055\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-4.json, NO extra invoice,invoice_no: 1/471883\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.062\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: b2eee775-e155-4b3f-8dd9-2a1a6f41e285.json, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.070\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: b514aa28-d6db-4360-87f9-0554233f4a86-0.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.078\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.json, NO extra invoice,invoice_no: 638434\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.087\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.json, NO extra invoice,invoice_no: 3431116\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.094\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.json, NO extra invoice,invoice_no: 628881\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.101\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: c607919d-3678-491f-823f-d6c75818c995.json, NO extra invoice,invoice_no: 1/907643\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.108\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: c6282f11-1e0f-4213-9d54-9a5138f72441.json, NO extra invoice,invoice_no: S249160406\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.116\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.json, NO extra invoice,invoice_no: 61330\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.124\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.json, NO extra invoice,invoice_no: 207219\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.131\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.139\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: ce810450-7f72-490b-9e09-c3d5ae1dad88.json, NO extra invoice,invoice_no: 754675\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.150\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.json, NO extra invoice,invoice_no: 6014163\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.158\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: d0be39b5-e383-4ffa-9a15-901af56e5baa-0.json, NO extra invoice,invoice_no: 166638\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.166\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: d0e3a9af-80ec-4b89-b87b-26869dbaeed8.json, NO extra invoice,invoice_no: 411019\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.175\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: d444e324-a0cd-4949-aac8-311cb8b591e7-0.json, NO extra invoice,invoice_no: 857098\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.182\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: d757112f-9c32-4a9b-81e7-140f1d30e717-0.json, NO extra invoice,invoice_no: 6000006\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.193\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: d80c66ae-b17d-4c47-840d-da293939f843.json, NO extra invoice,invoice_no: 600223\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.203\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.json, NO extra invoice,invoice_no: 1070666\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.209\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.j<PERSON>, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.221\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: dbd5b507-08c8-47bd-b98b-e030fb9b468b.json, NO extra invoice,invoice_no: 426793\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.230\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: e10d92e1-0995-49c9-ab4d-950e04f11c8e.json, NO extra invoice,invoice_no: 704116\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.246\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: e23da3d2-dfda-43d2-977e-b19ef997f205.json, NO extra invoice,invoice_no: 2395634\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.254\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m21\u001b[0m - \u001b[1mfile: e2b44cd9-f38b-4337-a537-19d7c39bfc4b.json, FALL OUT by len(invoice_no)\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.262\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: e4efb75c-f421-4f10-aed9-61e6004938c6.json, #invoice_fuzzy: 1, di_invoice_no in invoice_fuzzy: False di_invoice_no: 198473, invoice_no_fuzzy_res: ['198474']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.270\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: e4f69236-845c-4fcb-abf9-9df336b0478f-0.json, NO extra invoice,invoice_no: 25021147427242440\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.279\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 725191, invoice_no_fuzzy_res: ['725191', '725187']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.287\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: eb362ff0-c953-4049-9810-0bfbffc1772c.json, #invoice_fuzzy: 2, di_invoice_no in invoice_fuzzy: True di_invoice_no: 411047, invoice_no_fuzzy_res: ['411060', '411047']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.296\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: ebb628ae-1660-46f1-8aed-e674bc2fa512.json, NO extra invoice,invoice_no: 1457046\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: ed611268-36c2-4c56-bee4-26f1005ddca2-0.json, NO extra invoice,invoice_no: 1014423\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.320\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m28\u001b[0m - \u001b[31m\u001b[1mfile: f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.json, #invoice_fuzzy: 1, di_invoice_no in invoice_fuzzy: False di_invoice_no: DEVff03d86228d1, invoice_no_fuzzy_res: ['DEVff03d8b228d1']\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.332\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: f43fba4c-2325-4151-8cfc-263fd2f04d11.json, NO extra invoice,invoice_no: 1/928537\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.340\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.json, NO extra invoice,invoice_no: 2024-05895\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.349\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: f5ff435f-9fc1-467a-97a9-bd304891ebec.json, NO extra invoice,invoice_no: 1/369950\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.364\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: f8ebb75c-b0b0-4209-8f4e-e37370507f40.json, NO extra invoice,invoice_no: 209862\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.371\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: fa6f85ee-cd21-4eef-8805-939fbcd36712-0.json, NO extra invoice,invoice_no: 73106698576455648\u001b[0m\n", "\u001b[32m2024-10-28 02:54:24.413\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mfile: fbae41f6-4864-419e-bed0-944c831570f2-0.json, NO extra invoice,invoice_no: 688318\u001b[0m\n"]}], "source": ["import os\n", "import json\n", "from loguru import logger\n", "\n", "DI_res_folder_path = \"/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI_res\"\n", "\n", "for file_name in sorted(os.listdir(DI_res_folder_path)):\n", "    suffix = str(Path(file_name).suffix)\n", "    stem = str(Path(file_name).stem)\n", "    if stem[-2] == \"-\" and stem[-1].isdigit():\n", "        stem = stem[:-2]\n", "    if stem.startswith(\"fa970016\"):\n", "        continue\n", "\n", "    if suffix == \".json\" and file_name != \"ans.json\":\n", "        with open(os.path.join(DI_res_folder_path, file_name), \"r\") as f:\n", "            res = json.load(f)\n", "        invoice_no = res[\"documents\"][0][\"fields\"].get(\"InvoiceId\", {}).get(\"value\", \"\")\n", "        invoice_no = invoice_no.strip(\"#) \")\n", "        if len(invoice_no)<=4 or len(invoice_no)>=20:\n", "            logger.info(f\"file: {file_name}, FALL OUT by len(invoice_no)\")\n", "            continue\n", "        paddle_ocr_content = paddleocr_df[paddleocr_df[\"file_stem\"]==stem][\"content\"].to_list()[0]\n", "        invoice_no_fuzzy_res = find_similar_substring(paddle_ocr_content, invoice_no, max_diff=2)\n", "        if len(invoice_no_fuzzy_res) == 0  or (len(invoice_no_fuzzy_res) == 1 and invoice_no == invoice_no_fuzzy_res[0]):\n", "            logger.info(f\"file: {file_name}, NO extra invoice,invoice_no: {invoice_no}\")\n", "        else:\n", "            logger.error(f\"file: {file_name}, #invoice_fuzzy: {len(invoice_no_fuzzy_res)}, di_invoice_no in invoice_fuzzy: {invoice_no in invoice_no_fuzzy_res} di_invoice_no: {invoice_no}, invoice_no_fuzzy_res: {invoice_no_fuzzy_res}\")\n", "\n", "# 08b8af75 FP -> invoice no vs receipt no, need gpt\n", "# 0f88ab75 TP\n", "# 2836a967 FP -> invoice content should be space TODO\n", "# 426dd3f2 FP -> Fallout by multi pets\n", "# 4c52e8fd TP \n", "# 54addd43 TP\n", "# 9356b36f TP\n", "# 9ac1b519 TP\n", "# a32b55fe FP -> invoice no vs receipt no, need gpt\n", "# e4efb75c FP -> invoice no vs receipt no, need gpt\n", "# ea2f6b7d FP -> invoice no vs receipt no, need gpt \n", "# eb362ff0 FP -> invoice no vs receipt no, need gpt \n", "# f13c4fa7 TP\n", "# 70f5599a-60dd-4b2c-9f53-ecf07cc714a5.pdf FN -> invoice no vs receipt no. different len of them.\n", "# e2b44cd9-f38b-4337-a537-19d7c39bfc4b.jpg FN -> len(invoice_no) invoice no in treatment line and not extracted by DI potential RISK\n", "# 10ec75dd-e526-444f-895b-7faac9c0cd93.jpg FN -> len(invoice_no) fallout cropped file"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* check receipt no and invoice no both appear in the content. If so, direct to gpt for verification."]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["def if_need_invoice_no_verification(content: str) -> bool:\n", "    # return (\"invoice\" in content.lower()) and (\"receipt\" in content.lower())\n", "    return (\"invoice no\" in content.lower() or \"invoice number\" in content.lower()) and (\"receipt no\" in content.lower() or \"receipt number\" in content.lower())"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-10-22 06:24:13.921\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 00dcaff3-051f-4064-ae19-254264e8e242.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.926\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 08b8af75-483d-4160-9fba-f75344f4ce4f-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.929\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 08b8af75-483d-4160-9fba-f75344f4ce4f-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.934\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 0b2c5c86-90a8-4490-af46-2cb4a460b5bd.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.937\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 0ca22e7d-42bb-45b5-8ba0-212faba169ad.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.942\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 0f3de264-9a93-45b5-a56a-1696edceb3ce.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.953\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 0f88ab75-a51e-4799-a0fb-49eb3f2a9161.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.957\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 1085fe16-0014-489d-bfab-6d005298074a-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.964\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 1085fe16-0014-489d-bfab-6d005298074a-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.967\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 10ec75dd-e526-444f-895b-7faac9c0cd93.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.969\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 145b63cb-f8d0-41ff-8c23-fe4b1a4ba7b8.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.971\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 20238f33-c059-4eeb-8544-0bfd9eb555ef.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.979\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 204231ce-913e-405c-9132-20cfd100a14b.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.983\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 2836a967-2f4c-4655-bf35-c148e26d9f07-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.985\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.987\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 28ba0bd8-eaf3-4b1c-a057-d41a3eb681e7-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:13.994\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 29eb8c55-14df-4a91-b2bb-9bc29ab69a63-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.001\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 2e5efa44-3660-40df-9701-245d015f3771-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.004\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 309e19b3-e8c2-4569-a8fe-dc8c57e67ac4.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.006\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 30d78167-a823-44da-a272-a5b36acc66aa-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.009\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 37d281d0-903c-482d-aec6-a603544de1d6-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.013\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 392f63c9-1dc3-48ba-a563-f90b8c923058.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.017\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 3d59fa71-6b7c-4c5a-93af-cc5bc87036fd-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.019\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 3e463fdf-fbf8-4366-8b07-0ba07f62df6c.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.021\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4234dfb8-5e66-452a-88ba-d520698fb608.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.027\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 426dd3f2-6ad7-4a93-a801-71647893c19d.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.029\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 42c562c8-ad0a-4119-9b2a-a98702470659.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.033\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 452156dd-abc3-4c1d-b31c-fde48aadf60c-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.040\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4626aa97-e040-494f-9896-0469eeffd02f-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.058\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4626aa97-e040-494f-9896-0469eeffd02f-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.067\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 46b70eea-f82b-4f9e-b453-9fbdc0104549.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.070\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 474466f5-70ca-4c63-83f8-bae13e06e70e-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.090\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 48d16278-7a91-4d47-b916-0b1360d784ab.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.100\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4c03cf6c-0701-434b-a779-399d45ce051d.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.107\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4c52e8fd-f13c-4a77-961a-7e69294d66e4-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.110\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4c52e8fd-f13c-4a77-961a-7e69294d66e4-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.116\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 4e9ff370-bc9c-4d1e-aa33-2313d90139b8.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.120\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 50d4bc5f-ad09-48f4-a2d0-5a3d836ab8ed.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.126\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 519d1e33-094f-4c75-a2a4-2aebac4455aa.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.129\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 54addd43-e63e-495d-9b59-fcd34768a072-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.133\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.135\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 5601aa3e-b487-4e8d-9ae1-0feaacfd0be8-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.138\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 56210846-cdf7-4cc4-9489-56e5946d27a4.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.142\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 5686cd7a-c2a6-45cc-95a5-902caa94daa4.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.145\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 65e2706d-f5b0-4bd3-96ad-13a16bb710e0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.149\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 67dc83a3-e15d-4869-8168-659a8a50cd9e.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.152\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 6adb2e29-cf4e-4e13-b7e1-e47db06c9ec9-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.157\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 6bf46030-cb4a-4add-bbf3-fbd57d3e9248.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.159\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 6da16dc2-eb22-4e29-830a-2aa9db3bd599.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.161\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.162\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 70f5599a-60dd-4b2c-9f53-ecf07cc714a5-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.164\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 728e11b5-5923-456e-8c13-b857e7b11a3d.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.167\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 75d665c1-a17c-4882-9804-7574b221dd21.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.169\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 79796bd8-ae1a-4771-bab8-ff65fa35a4f7.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.171\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 7f6f0d3e-6892-497f-b571-57c5542830a4.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.173\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 7f7ce38b-533e-46c7-a7dd-618fd83c2f5e-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.175\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 811bf137-b832-4774-a0ba-42314e20fae2-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.178\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 89926c19-088e-49e8-9bf7-3dc947307dad-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.181\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 8d3e1857-b6c2-4b94-b757-798cf906675b-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.184\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 8d82d2fe-f2dc-47c7-a107-f28ddc82e3d4-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.185\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 90683a89-d579-4d61-962c-0625189d0f5b-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.188\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 91df8902-e51d-4fe3-be8b-6af88d9e6a42-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.190\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 92ceb1a1-2fce-4468-abd8-e477b7b5d317-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.192\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 92ceb1a1-2fce-4468-abd8-e477b7b5d317-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.195\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 9356b36f-4e13-4733-9c77-dc8555add32e-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.197\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: 99bababe-a109-413f-90e4-608f1eb9e293-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.200\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m15\u001b[0m - \u001b[31m\u001b[1mfile: 9ac1b519-ec6a-49e4-822d-bc2b3e8cf2f0-0.json, if_need_invoice_verification: True\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.204\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: a32b55fe-0d0b-4a0f-88a1-48d9af852ff0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.207\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: a37382de-b1cc-4425-8f2f-bb6cae59a0f8.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.211\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: a6742d14-2825-4ec5-9f60-c180daaf0b78.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.214\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: a7f99b6b-8c72-44b0-9550-46cae60ee18c.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.217\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: a85e2454-2d4a-4813-bd81-1af1c54dcc9a-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.219\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.222\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.225\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-2.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.227\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-3.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.229\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b1f83929-9b81-402f-8b37-13065b355db1-4.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.232\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b2eee775-e155-4b3f-8dd9-2a1a6f41e285.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.234\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b514aa28-d6db-4360-87f9-0554233f4a86-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.238\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: b7cd3e45-4bd3-4a90-a945-4bb7f43bbaa1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.241\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: ba5eb15b-ab12-4dd5-b385-4b0dbcdeedb1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.245\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: bbf8debf-6c28-4dfd-be52-9d24bd0c2dfb.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.247\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: c607919d-3678-491f-823f-d6c75818c995.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.250\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: c6282f11-1e0f-4213-9d54-9a5138f72441.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.253\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: c9d88bfa-43ec-4acb-bb89-a4aeb22b3dd5-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.256\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: cdc164de-ca0a-4f3d-9bde-3552f06ae74a-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.259\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: cdc164de-ca0a-4f3d-9bde-3552f06ae74a-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.262\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: ce810450-7f72-490b-9e09-c3d5ae1dad88.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.264\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: cfdcf6cd-0ad0-4db5-abf9-197f9e33a711.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.267\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d0be39b5-e383-4ffa-9a15-901af56e5baa-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.269\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d0e3a9af-80ec-4b89-b87b-26869dbaeed8.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.272\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d444e324-a0cd-4949-aac8-311cb8b591e7-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.274\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d757112f-9c32-4a9b-81e7-140f1d30e717-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.277\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d80c66ae-b17d-4c47-840d-da293939f843.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.279\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d9b8c26d-6e6d-48d3-a763-f8f45fd91477-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.280\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: d9b8c26d-6e6d-48d3-a763-f8f45fd91477-1.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.282\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: dbd5b507-08c8-47bd-b98b-e030fb9b468b.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.285\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: e10d92e1-0995-49c9-ab4d-950e04f11c8e.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.288\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: e23da3d2-dfda-43d2-977e-b19ef997f205.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.290\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: e2b44cd9-f38b-4337-a537-19d7c39bfc4b.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.292\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: e4efb75c-f421-4f10-aed9-61e6004938c6.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.300\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: e4f69236-845c-4fcb-abf9-9df336b0478f-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.305\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m15\u001b[0m - \u001b[31m\u001b[1mfile: ea2f6b7d-c7fe-479b-a8d3-52d3ce1563f6-0.json, if_need_invoice_verification: True\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.311\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: eb362ff0-c953-4049-9810-0bfbffc1772c.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.316\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: ebb628ae-1660-46f1-8aed-e674bc2fa512.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.320\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: ed611268-36c2-4c56-bee4-26f1005ddca2-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.327\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: f13c4fa7-0df6-4b48-816a-e11e55d5e9f0-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.331\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: f43fba4c-2325-4151-8cfc-263fd2f04d11.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.334\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: f56eb768-41d9-4cdd-bbe6-4969cc51ed86-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.336\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: f5ff435f-9fc1-467a-97a9-bd304891ebec.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.340\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: f8ebb75c-b0b0-4209-8f4e-e37370507f40.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.342\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: fa6f85ee-cd21-4eef-8805-939fbcd36712-0.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.346\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: fa970016-e3b5-4013-a5e4-cca5d49418ad.json, if_need_invoice_verification: False\u001b[0m\n", "\u001b[32m2024-10-22 06:24:14.350\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36m<module>\u001b[0m:\u001b[36m13\u001b[0m - \u001b[1mfile: fbae41f6-4864-419e-bed0-944c831570f2-0.json, if_need_invoice_verification: False\u001b[0m\n"]}], "source": ["import json\n", "DI_res_folder_path = \"/workspaces/OCR_in_house/data/OCR_in_house/samples/100_samples_DI_res\"\n", "\n", "for file_name in sorted(os.listdir(DI_res_folder_path)):\n", "    suffix = str(Path(file_name).suffix)\n", "\n", "    if suffix == \".json\" and file_name != \"ans.json\":\n", "        with open(os.path.join(DI_res_folder_path, file_name), \"r\") as f:\n", "            res = json.load(f)\n", "        content = res.get(\"content\", \"\")\n", "        if_need_invoice_verification = if_need_invoice_no_verification(content)\n", "        if not if_need_invoice_verification:\n", "            logger.info(f\"file: {file_name}, if_need_invoice_verification: {if_need_invoice_verification}\")\n", "        else:\n", "            logger.error(f\"file: {file_name}, if_need_invoice_verification: {if_need_invoice_verification}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}