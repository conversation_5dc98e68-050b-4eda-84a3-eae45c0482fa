{"cells": [{"cell_type": "code", "execution_count": 2, "id": "e70eae729652dffa", "metadata": {"ExecuteTime": {"end_time": "2023-11-08T02:11:25.176302900Z", "start_time": "2023-11-08T02:11:01.384415700Z"}, "collapsed": false}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from PIL import Image\n", "from paddleocr import PaddleOCR\n", "from paddleocr.tools.infer.utility import draw_ocr\n", "import pandas as pd\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from datetime import datetime, timedelta\n", "from pdf2image import convert_from_path"]}, {"cell_type": "code", "execution_count": 2, "id": "5b0d486e", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/incorrect_amount.xlsx')"]}, {"cell_type": "code", "execution_count": 3, "id": "f5ed05cb", "metadata": {}, "outputs": [], "source": ["invoice08 = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/GPT4_vision_100_invoices_0.8_v4.xlsx')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download required files"]}, {"cell_type": "code", "execution_count": 3, "id": "406b1f48", "metadata": {}, "outputs": [], "source": ["# define credentials\n", "    \n", "source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='\n", "\n", "source_account_name = 'p3storageprod'\n", "\n", "sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,\n", "                                     resource_types=ResourceTypes(\n", "                                         service=True, container=True, object=True),\n", "permission=AccountSasPermissions(read=True),\n", "                                     expiry=datetime.utcnow() + <PERSON><PERSON><PERSON>(hours=1))\n", "\n", "source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)\n", "\n", "# Create download function\n", "def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "# Download documents from blob storage to local folder called pdf             \n", "for idx in df.index:\n", "    path =  'C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/incorrect_amount/'+df['DocFile'][idx]\n", "    download(source_blob_service_client,df['DocContainer'].iloc[idx],df['DocFile'].iloc[idx],path)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Convert pdf files to images"]}, {"cell_type": "code", "execution_count": 5, "id": "8cc86c0a", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def extract_filename(file_path):\n", "    filename_with_extension = os.path.basename(file_path)\n", "    filename = filename_with_extension.split('.')[0]\n", "    return filename\n", "\n", "def convert_pdf_to_images(pdf_path):\n", "    filename = extract_filename(pdf_path)\n", "    output_folder = os.path.dirname(pdf_path)  # Get the folder path where the PDF is located\n", "\n", "    # Convert PDF to a list of images\n", "    images = convert_from_path(pdf_path, poppler_path='E:\\\\claryt\\\\poppler-23.11.0\\\\Library\\\\bin')\n", "\n", "    # Save each image as a separate file\n", "    for i, image in enumerate(images):\n", "        image_path = os.path.join(output_folder, f'{filename}_p{i+1}.jpg')\n", "        image.save(image_path, 'JPEG')\n", "\n", "def convert_pdfs_in_folder(folder_path):\n", "    for filename in os.listdir(folder_path):\n", "        if filename.lower().endswith('.pdf'):\n", "            pdf_path = os.path.join(folder_path, filename)\n", "            convert_pdf_to_images(pdf_path)\n", "\n", "# Example usage\n", "# convert_pdfs_in_folder('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/incorrect_amount')\n", "convert_pdfs_in_folder('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices2_0.8_paddleocr/100_invoices2_0.8')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Paddle OCR Extraction"]}, {"cell_type": "code", "execution_count": 7, "id": "4aa9849b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024/02/29 01:49:22] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, image_dir=None, page_num=0, det_algorithm='DB', det_model_dir='C:\\\\Users\\\\<USER>\\\\det\\\\en\\\\en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='C:\\\\Users\\\\<USER>\\\\rec\\\\en\\\\en_PP-OCRv3_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='c:\\\\Users\\\\<USER>\\\\.conda\\\\envs\\\\claryt\\\\lib\\\\site-packages\\\\paddleocr\\\\ppocr\\\\utils\\\\en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='C:\\\\Users\\\\<USER>\\\\cls\\\\ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv3', structure_version='PP-StructureV2')\n", "[2024/02/29 01:49:25] ppocr DEBUG: dt_boxes num : 47, elapse : 0.8145139217376709\n", "[2024/02/29 01:49:26] ppocr DEBUG: cls num  : 47, elapse : 0.8277325630187988\n", "[2024/02/29 01:49:36] ppocr DEBUG: rec_res num  : 47, elapse : 10.677335023880005\n", "Results for 01068e9e-424f-4692-9637-2683f93a465d.jpg:\n", "[2024/02/29 01:49:37] ppocr DEBUG: dt_boxes num : 56, elapse : 0.7531862258911133\n", "[2024/02/29 01:49:39] ppocr DEBUG: cls num  : 56, elapse : 0.9791803359985352\n", "[2024/02/29 01:49:49] ppocr DEBUG: rec_res num  : 56, elapse : 10.378504276275635\n", "Results for 11b14979-b7dc-4bfc-a8c4-43e862cabb2e.jpg:\n", "[2024/02/29 01:49:50] ppocr DEBUG: dt_boxes num : 57, elapse : 0.8617212772369385\n", "[2024/02/29 01:49:51] ppocr DEBUG: cls num  : 57, elapse : 1.166121482849121\n", "[2024/02/29 01:50:04] ppocr DEBUG: rec_res num  : 57, elapse : 12.456125020980835\n", "Results for 15e96415-e023-4ad1-9ee1-f051b1b06293_p1.jpg:\n", "[2024/02/29 01:50:05] ppocr DEBUG: dt_boxes num : 97, elapse : 0.8601117134094238\n", "[2024/02/29 01:50:07] ppocr DEBUG: cls num  : 97, elapse : 1.7035839557647705\n", "[2024/02/29 01:50:24] ppocr DEBUG: rec_res num  : 97, elapse : 17.875962495803833\n", "Results for 17e2458e-78d4-4f21-9887-74f5b3e85000.jpg:\n", "[2024/02/29 01:50:25] ppocr DEBUG: dt_boxes num : 50, elapse : 0.7506954669952393\n", "[2024/02/29 01:50:26] ppocr DEBUG: cls num  : 50, elapse : 0.8916566371917725\n", "[2024/02/29 01:50:40] ppocr DEBUG: rec_res num  : 50, elapse : 13.232132196426392\n", "Results for 27f1ebeb-39eb-4855-8de6-3a317c139e48.jpg:\n", "[2024/02/29 01:50:41] ppocr DEBUG: dt_boxes num : 44, elapse : 0.9133219718933105\n", "[2024/02/29 01:50:42] ppocr DEBUG: cls num  : 44, elapse : 0.8738155364990234\n", "[2024/02/29 01:50:55] ppocr DEBUG: rec_res num  : 44, elapse : 13.902396440505981\n", "Results for 39c9a3bb-5e2d-48bd-8ce8-cca5bd367e39_p1.jpg:\n", "[2024/02/29 01:50:56] ppocr DEBUG: dt_boxes num : 3, elapse : 0.806340217590332\n", "[2024/02/29 01:50:56] ppocr DEBUG: cls num  : 3, elapse : 0.08834409713745117\n", "[2024/02/29 01:50:59] ppocr DEBUG: rec_res num  : 3, elapse : 2.9452385902404785\n", "Results for 39c9a3bb-5e2d-48bd-8ce8-cca5bd367e39_p2.jpg:\n", "[2024/02/29 01:51:01] ppocr DEBUG: dt_boxes num : 67, elapse : 1.1640729904174805\n", "[2024/02/29 01:51:02] ppocr DEBUG: cls num  : 67, elapse : 1.4391076564788818\n", "[2024/02/29 01:51:18] ppocr DEBUG: rec_res num  : 67, elapse : 15.704240560531616\n", "Results for 3de4847f-a37a-4ebe-af3d-cde221c5a10d.jpeg:\n", "[2024/02/29 01:51:20] ppocr DEBUG: dt_boxes num : 49, elapse : 1.0511078834533691\n", "[2024/02/29 01:51:21] ppocr DEBUG: cls num  : 49, elapse : 0.9216742515563965\n", "[2024/02/29 01:51:32] ppocr DEBUG: rec_res num  : 49, elapse : 11.249669313430786\n", "Results for 453066c5-a3cf-439c-bac6-e33d79502914.jpg:\n", "[2024/02/29 01:51:33] ppocr DEBUG: dt_boxes num : 52, elapse : 0.776447057723999\n", "[2024/02/29 01:51:34] ppocr DEBUG: cls num  : 52, elapse : 0.9323112964630127\n", "[2024/02/29 01:51:44] ppocr DEBUG: rec_res num  : 52, elapse : 10.576972007751465\n", "Results for 4a27efe0-a868-42f3-8048-92581d4e7ec4.jpg:\n", "[2024/02/29 01:51:45] ppocr DEBUG: dt_boxes num : 56, elapse : 0.5074112415313721\n", "[2024/02/29 01:51:46] ppocr DEBUG: cls num  : 56, elapse : 1.004535436630249\n", "[2024/02/29 01:51:57] ppocr DEBUG: rec_res num  : 56, elapse : 11.036803722381592\n", "Results for 4eb60602-06eb-41c4-8936-a921b6a8698c.jpg:\n", "[2024/02/29 01:51:59] ppocr DEBUG: dt_boxes num : 37, elapse : 0.8207015991210938\n", "[2024/02/29 01:51:59] ppocr DEBUG: cls num  : 37, elapse : 0.6704347133636475\n", "[2024/02/29 01:52:08] ppocr DEBUG: rec_res num  : 37, elapse : 8.544058799743652\n", "Results for 4ee9d17f-35fd-4983-b84d-7392b4f163b5.jpeg:\n", "[2024/02/29 01:52:09] ppocr DEBUG: dt_boxes num : 86, elapse : 0.7670140266418457\n", "[2024/02/29 01:52:10] ppocr DEBUG: cls num  : 86, elapse : 1.5735809803009033\n", "[2024/02/29 01:52:36] ppocr DEBUG: rec_res num  : 86, elapse : 26.055572509765625\n", "Results for 5babf3b6-5a76-4aea-bde8-6856cda57a06_p1.jpg:\n", "[2024/02/29 01:52:37] ppocr DEBUG: dt_boxes num : 49, elapse : 0.7953798770904541\n", "[2024/02/29 01:52:39] ppocr DEBUG: cls num  : 49, elapse : 1.0281236171722412\n", "[2024/02/29 01:52:49] ppocr DEBUG: rec_res num  : 49, elapse : 10.359772682189941\n", "Results for 61e6eb96-4a01-4496-a41d-c06ee91ca33f.jpg:\n", "[2024/02/29 01:52:50] ppocr DEBUG: dt_boxes num : 51, elapse : 0.9772875308990479\n", "[2024/02/29 01:52:51] ppocr DEBUG: cls num  : 51, elapse : 0.8997743129730225\n", "[2024/02/29 01:53:02] ppocr DEBUG: rec_res num  : 51, elapse : 10.393525123596191\n", "Results for 660887b2-cb83-43b7-9862-4bc6d0b57c1e.jpg:\n", "[2024/02/29 01:53:03] ppocr DEBUG: dt_boxes num : 47, elapse : 0.8918626308441162\n", "[2024/02/29 01:53:04] ppocr DEBUG: cls num  : 47, elapse : 0.8291387557983398\n", "[2024/02/29 01:53:12] ppocr DEBUG: rec_res num  : 47, elapse : 8.676337242126465\n", "Results for 6fbbe213-876d-4aba-96b2-35a92ccdd732.jpg:\n", "[2024/02/29 01:53:13] ppocr DEBUG: dt_boxes num : 40, elapse : 0.6470398902893066\n", "[2024/02/29 01:53:14] ppocr DEBUG: cls num  : 40, elapse : 0.9845695495605469\n", "[2024/02/29 01:53:21] ppocr DEBUG: rec_res num  : 40, elapse : 6.951641082763672\n", "Results for 80620c54-1181-4f10-99c2-64efbab9bc7d.png:\n", "[2024/02/29 01:53:22] ppocr DEBUG: dt_boxes num : 61, elapse : 0.556633710861206\n", "[2024/02/29 01:53:23] ppocr DEBUG: cls num  : 61, elapse : 1.0460460186004639\n", "[2024/02/29 01:53:35] ppocr DEBUG: rec_res num  : 61, elapse : 12.283552408218384\n", "Results for 84f21673-0617-48c9-b4b9-051f1c6f5236.jpg:\n", "[2024/02/29 01:53:36] ppocr DEBUG: dt_boxes num : 48, elapse : 0.7656567096710205\n", "[2024/02/29 01:53:37] ppocr DEBUG: cls num  : 48, elapse : 0.8212490081787109\n", "[2024/02/29 01:53:46] ppocr DEBUG: rec_res num  : 48, elapse : 8.82806658744812\n", "Results for 94700dc4-283e-4b4d-91bb-54267604e8b6.jpeg:\n", "[2024/02/29 01:53:47] ppocr DEBUG: dt_boxes num : 44, elapse : 0.7893986701965332\n", "[2024/02/29 01:53:48] ppocr DEBUG: cls num  : 44, elapse : 0.7759709358215332\n", "[2024/02/29 01:54:00] ppocr DEBUG: rec_res num  : 44, elapse : 12.816916227340698\n", "Results for 9a4c1766-4267-4be7-85ff-dac76a805c05_p1.jpg:\n", "[2024/02/29 01:54:01] ppocr DEBUG: dt_boxes num : 3, elapse : 0.7938776016235352\n", "[2024/02/29 01:54:01] ppocr DEBUG: cls num  : 3, elapse : 0.09194302558898926\n", "[2024/02/29 01:54:03] ppocr DEBUG: rec_res num  : 3, elapse : 2.1303091049194336\n", "Results for 9a4c1766-4267-4be7-85ff-dac76a805c05_p2.jpg:\n", "[2024/02/29 01:54:05] ppocr DEBUG: dt_boxes num : 61, elapse : 0.7687363624572754\n", "[2024/02/29 01:54:06] ppocr DEBUG: cls num  : 61, elapse : 1.0488934516906738\n", "[2024/02/29 01:54:20] ppocr DEBUG: rec_res num  : 61, elapse : 14.060239553451538\n", "Results for 9f9aa3ab-85c2-4e1e-8920-86880b78615a.jpg:\n", "[2024/02/29 01:54:20] ppocr DEBUG: dt_boxes num : 42, elapse : 0.5606343746185303\n", "[2024/02/29 01:54:21] ppocr DEBUG: cls num  : 42, elapse : 0.7235639095306396\n", "[2024/02/29 01:54:31] ppocr DEBUG: rec_res num  : 42, elapse : 10.419668912887573\n", "Results for a5f649cc-463d-4782-8770-3a8a7ea83c8f.jpg:\n", "[2024/02/29 01:54:33] ppocr DEBUG: dt_boxes num : 58, elapse : 0.7793807983398438\n", "[2024/02/29 01:54:34] ppocr DEBUG: cls num  : 58, elapse : 1.0004920959472656\n", "[2024/02/29 01:54:44] ppocr DEBUG: rec_res num  : 58, elapse : 10.641173124313354\n", "Results for a6a7f046-ac2b-4aac-9d21-d271c3e9f287.jpeg:\n", "[2024/02/29 01:54:45] ppocr DEBUG: dt_boxes num : 42, elapse : 0.7658576965332031\n", "[2024/02/29 01:54:46] ppocr DEBUG: cls num  : 42, elapse : 0.7375576496124268\n", "[2024/02/29 01:54:55] ppocr DEBUG: rec_res num  : 42, elapse : 8.384149312973022\n", "Results for ac79a90c-7118-49b3-b749-fc4044984a29.jpg:\n", "[2024/02/29 01:54:55] ppocr DEBUG: dt_boxes num : 30, elapse : 0.7310259342193604\n", "[2024/02/29 01:54:56] ppocr DEBUG: cls num  : 30, elapse : 0.5335962772369385\n", "[2024/02/29 01:55:05] ppocr DEBUG: rec_res num  : 30, elapse : 9.21457576751709\n", "Results for b199900d-610b-4ad6-be42-af798d672edf_p1.jpg:\n", "[2024/02/29 01:55:06] ppocr DEBUG: dt_boxes num : 61, elapse : 0.5594892501831055\n", "[2024/02/29 01:55:07] ppocr DEBUG: cls num  : 61, elapse : 1.0353541374206543\n", "[2024/02/29 01:55:19] ppocr DEBUG: rec_res num  : 61, elapse : 12.319133520126343\n", "Results for bc877ee1-967a-4c58-8187-74c75c2b8fc2.jpg:\n", "[2024/02/29 01:55:20] ppocr DEBUG: dt_boxes num : 44, elapse : 0.823535680770874\n", "[2024/02/29 01:55:21] ppocr DEBUG: cls num  : 44, elapse : 0.7706151008605957\n", "[2024/02/29 01:55:34] ppocr DEBUG: rec_res num  : 44, elapse : 12.70227336883545\n", "Results for c0e870ec-3cbb-4aab-9bd8-02be233b7c25_p1.jpg:\n", "[2024/02/29 01:55:34] ppocr DEBUG: dt_boxes num : 3, elapse : 0.8121864795684814\n", "[2024/02/29 01:55:35] ppocr DEBUG: cls num  : 3, elapse : 0.08477210998535156\n", "[2024/02/29 01:55:37] ppocr DEBUG: rec_res num  : 3, elapse : 2.1357290744781494\n", "Results for c0e870ec-3cbb-4aab-9bd8-02be233b7c25_p2.jpg:\n", "[2024/02/29 01:55:38] ppocr DEBUG: dt_boxes num : 81, elapse : 0.781437873840332\n", "[2024/02/29 01:55:39] ppocr DEBUG: cls num  : 81, elapse : 1.3940362930297852\n", "[2024/02/29 01:55:54] ppocr DEBUG: rec_res num  : 81, elapse : 14.499084234237671\n", "Results for db4f47e1-a4b2-462b-bb4c-b30ec8ba492d.jpeg:\n", "[2024/02/29 01:55:55] ppocr DEBUG: dt_boxes num : 52, elapse : 0.7826640605926514\n", "[2024/02/29 01:55:56] ppocr DEBUG: cls num  : 52, elapse : 0.9142520427703857\n", "[2024/02/29 01:56:06] ppocr DEBUG: rec_res num  : 52, elapse : 10.729694128036499\n", "Results for deb513fe-8cf1-47d4-aaa2-a1f3c71a3873.jpeg:\n", "[2024/02/29 01:56:07] ppocr DEBUG: dt_boxes num : 44, elapse : 0.8090939521789551\n", "[2024/02/29 01:56:08] ppocr DEBUG: cls num  : 44, elapse : 0.7762305736541748\n", "[2024/02/29 01:56:21] ppocr DEBUG: rec_res num  : 44, elapse : 12.878253698348999\n", "Results for e4e7e28e-f0ea-4842-801b-994077cf18f3_p1.jpg:\n", "[2024/02/29 01:56:22] ppocr DEBUG: dt_boxes num : 3, elapse : 0.7853410243988037\n", "[2024/02/29 01:56:22] ppocr DEBUG: cls num  : 3, elapse : 0.08528256416320801\n", "[2024/02/29 01:56:24] ppocr DEBUG: rec_res num  : 3, elapse : 2.1489617824554443\n", "Results for e4e7e28e-f0ea-4842-801b-994077cf18f3_p2.jpg:\n", "[2024/02/29 01:56:25] ppocr DEBUG: dt_boxes num : 67, elapse : 0.7802040576934814\n", "[2024/02/29 01:56:26] ppocr DEBUG: cls num  : 67, elapse : 1.1770410537719727\n", "[2024/02/29 01:56:43] ppocr DEBUG: rec_res num  : 67, elapse : 16.757316827774048\n", "Results for e70b8af2-f4d4-4610-b4c6-f72ff19daa36.jpg:\n", "[2024/02/29 01:56:45] ppocr DEBUG: dt_boxes num : 85, elapse : 1.077317714691162\n", "[2024/02/29 01:56:46] ppocr DEBUG: cls num  : 85, elapse : 1.4917631149291992\n", "[2024/02/29 01:57:01] ppocr DEBUG: rec_res num  : 85, elapse : 14.910332441329956\n", "Results for ecd3aacf-6e79-4cce-8ca0-6563fe81faed.jpg:\n", "[2024/02/29 01:57:02] ppocr DEBUG: dt_boxes num : 62, elapse : 0.7896595001220703\n", "[2024/02/29 01:57:03] ppocr DEBUG: cls num  : 62, elapse : 1.0801498889923096\n", "[2024/02/29 01:57:15] ppocr DEBUG: rec_res num  : 62, elapse : 11.672317028045654\n", "Results for eeba9d98-2408-48e1-91b8-6489b98b89f3.jpeg:\n", "[2024/02/29 01:57:16] ppocr DEBUG: dt_boxes num : 41, elapse : 0.7891066074371338\n", "[2024/02/29 01:57:17] ppocr DEBUG: cls num  : 41, elapse : 0.7262334823608398\n", "[2024/02/29 01:57:28] ppocr DEBUG: rec_res num  : 41, elapse : 10.788394927978516\n", "Results for f51b3f23-b230-4451-9831-cba3e44e2948.jpg:\n", "[2024/02/29 01:57:28] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, image_dir=None, page_num=0, det_algorithm='DB', det_model_dir='C:\\\\Users\\\\<USER>\\\\det\\\\en\\\\en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='C:\\\\Users\\\\<USER>\\\\rec\\\\en\\\\en_PP-OCRv3_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='c:\\\\Users\\\\<USER>\\\\.conda\\\\envs\\\\claryt\\\\lib\\\\site-packages\\\\paddleocr\\\\ppocr\\\\utils\\\\en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=True, cls_model_dir='C:\\\\Users\\\\<USER>\\\\cls\\\\ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv3', structure_version='PP-StructureV2')\n", "[2024/02/29 01:57:30] ppocr DEBUG: dt_boxes num : 47, elapse : 0.728304386138916\n", "[2024/02/29 01:57:31] ppocr DEBUG: cls num  : 47, elapse : 0.7915835380554199\n", "[2024/02/29 01:57:41] ppocr DEBUG: rec_res num  : 47, elapse : 10.137785911560059\n", "Results for 01068e9e-424f-4692-9637-2683f93a465d.jpg:\n", "[2024/02/29 01:57:42] ppocr DEBUG: dt_boxes num : 50, elapse : 0.8079855442047119\n", "[2024/02/29 01:57:43] ppocr DEBUG: cls num  : 50, elapse : 0.8781089782714844\n", "[2024/02/29 01:57:53] ppocr DEBUG: rec_res num  : 50, elapse : 10.139878273010254\n", "Results for 022b4988-0313-49d4-abb3-0b1b353fdbe4_p1.jpg:\n", "[2024/02/29 01:57:54] ppocr DEBUG: dt_boxes num : 54, elapse : 0.6867470741271973\n", "[2024/02/29 01:57:55] ppocr DEBUG: cls num  : 54, elapse : 0.9024448394775391\n", "[2024/02/29 01:58:07] ppocr DEBUG: rec_res num  : 54, elapse : 12.600600719451904\n", "Results for 0595a961-c475-4db1-a497-db379dd305b7_p1.jpg:\n", "[2024/02/29 01:58:08] ppocr DEBUG: dt_boxes num : 33, elapse : 0.7458131313323975\n", "[2024/02/29 01:58:09] ppocr DEBUG: cls num  : 33, elapse : 0.5840654373168945\n", "[2024/02/29 01:58:16] ppocr DEBUG: rec_res num  : 33, elapse : 7.693539619445801\n", "Results for 071c8a25-13d2-496b-a385-f0b95e30ae71_p1.jpg:\n", "[2024/02/29 01:58:17] ppocr DEBUG: dt_boxes num : 32, elapse : 0.7443122863769531\n", "[2024/02/29 01:58:18] ppocr DEBUG: cls num  : 32, elapse : 0.5679149627685547\n", "[2024/02/29 01:58:25] ppocr DEBUG: rec_res num  : 32, elapse : 7.064157009124756\n", "Results for 07b2ab23-9023-479b-b42d-992fdf06e210_p1.jpg:\n", "[2024/02/29 01:58:26] ppocr DEBUG: dt_boxes num : 40, elapse : 0.7602612972259521\n", "[2024/02/29 01:58:26] ppocr DEBUG: cls num  : 40, elapse : 0.7159125804901123\n", "[2024/02/29 01:58:35] ppocr DEBUG: rec_res num  : 40, elapse : 8.874870300292969\n", "Results for 07be201d-6994-409b-8531-b5dc0bffcd03_p1.jpg:\n", "[2024/02/29 01:58:36] ppocr DEBUG: dt_boxes num : 142, elapse : 0.7865686416625977\n", "[2024/02/29 01:58:39] ppocr DEBUG: cls num  : 142, elapse : 2.421077251434326\n", "[2024/02/29 01:59:04] ppocr DEBUG: rec_res num  : 142, elapse : 25.320054531097412\n", "Results for 0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3_p1.jpg:\n", "[2024/02/29 01:59:05] ppocr DEBUG: dt_boxes num : 40, elapse : 0.7420334815979004\n", "[2024/02/29 01:59:05] ppocr DEBUG: cls num  : 40, elapse : 0.7114109992980957\n", "[2024/02/29 01:59:14] ppocr DEBUG: rec_res num  : 40, elapse : 8.702357053756714\n", "Results for 0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3_p2.jpg:\n", "[2024/02/29 01:59:15] ppocr DEBUG: dt_boxes num : 27, elapse : 0.814100980758667\n", "[2024/02/29 01:59:16] ppocr DEBUG: cls num  : 27, elapse : 0.48828983306884766\n", "[2024/02/29 01:59:22] ppocr DEBUG: rec_res num  : 27, elapse : 6.104620933532715\n", "Results for 0c10731f-8f4e-48dd-ad64-41ccdb6a0acb_p1.jpg:\n", "[2024/02/29 01:59:22] ppocr DEBUG: dt_boxes num : 46, elapse : 0.7602136135101318\n", "[2024/02/29 01:59:23] ppocr DEBUG: cls num  : 46, elapse : 0.7981321811676025\n", "[2024/02/29 01:59:32] ppocr DEBUG: rec_res num  : 46, elapse : 8.962798118591309\n", "Results for 0dd29c4b-a21d-4a0f-bc49-064c34a1ae82_p1.jpg:\n", "[2024/02/29 01:59:33] ppocr DEBUG: dt_boxes num : 56, elapse : 0.7945055961608887\n", "[2024/02/29 01:59:34] ppocr DEBUG: cls num  : 56, elapse : 0.979830265045166\n", "[2024/02/29 01:59:44] ppocr DEBUG: rec_res num  : 56, elapse : 9.801833152770996\n", "Results for 11b14979-b7dc-4bfc-a8c4-43e862cabb2e.jpg:\n", "[2024/02/29 01:59:45] ppocr DEBUG: dt_boxes num : 107, elapse : 0.7774689197540283\n", "[2024/02/29 01:59:47] ppocr DEBUG: cls num  : 107, elapse : 1.8265442848205566\n", "[2024/02/29 02:00:10] ppocr DEBUG: rec_res num  : 107, elapse : 23.35982847213745\n", "Results for 1d560a5f-8b5f-4be1-b4c4-d26511abbed7_p1.jpg:\n", "[2024/02/29 02:00:11] ppocr DEBUG: dt_boxes num : 30, elapse : 0.8155651092529297\n", "[2024/02/29 02:00:12] ppocr DEBUG: cls num  : 30, elapse : 0.5494594573974609\n", "[2024/02/29 02:00:19] ppocr DEBUG: rec_res num  : 30, elapse : 7.277760028839111\n", "Results for 216a66a5-cabc-4ad0-8352-44af3fe255f5_p1.jpg:\n", "[2024/02/29 02:00:20] ppocr DEBUG: dt_boxes num : 50, elapse : 0.7810854911804199\n", "[2024/02/29 02:00:21] ppocr DEBUG: cls num  : 50, elapse : 0.8958852291107178\n", "[2024/02/29 02:00:31] ppocr DEBUG: rec_res num  : 50, elapse : 10.008509397506714\n", "Results for 27f1ebeb-39eb-4855-8de6-3a317c139e48.jpg:\n", "[2024/02/29 02:00:32] ppocr DEBUG: dt_boxes num : 52, elapse : 0.7459046840667725\n", "[2024/02/29 02:00:33] ppocr DEBUG: cls num  : 52, elapse : 0.9150526523590088\n", "[2024/02/29 02:00:46] ppocr DEBUG: rec_res num  : 52, elapse : 13.384400606155396\n", "Results for 296e38c2-e045-4da0-87b3-312953318f68_p1.jpg:\n", "[2024/02/29 02:00:47] ppocr DEBUG: dt_boxes num : 75, elapse : 0.7660939693450928\n", "[2024/02/29 02:00:48] ppocr DEBUG: cls num  : 75, elapse : 1.301480770111084\n", "[2024/02/29 02:01:03] ppocr DEBUG: rec_res num  : 75, elapse : 14.762317657470703\n", "Results for 29ad5cc4-5874-43fe-903d-a1dcb1140c8a_p1.jpg:\n", "[2024/02/29 02:01:04] ppocr DEBUG: dt_boxes num : 59, elapse : 0.5296139717102051\n", "[2024/02/29 02:01:05] ppocr DEBUG: cls num  : 59, elapse : 1.0155282020568848\n", "[2024/02/29 02:01:15] ppocr DEBUG: rec_res num  : 59, elapse : 10.142672300338745\n", "Results for 29b1a318-67a0-4012-bbe0-efad74307c70.png:\n", "[2024/02/29 02:01:16] ppocr DEBUG: dt_boxes num : 49, elapse : 0.7246208190917969\n", "[2024/02/29 02:01:17] ppocr DEBUG: cls num  : 49, elapse : 0.8543837070465088\n", "[2024/02/29 02:01:28] ppocr DEBUG: rec_res num  : 49, elapse : 11.30529522895813\n", "Results for 2afa6260-a4b1-4939-a348-432af7a16462_p1.jpg:\n", "[2024/02/29 02:01:29] ppocr DEBUG: dt_boxes num : 49, elapse : 0.7542915344238281\n", "[2024/02/29 02:01:30] ppocr DEBUG: cls num  : 49, elapse : 0.8681519031524658\n", "[2024/02/29 02:01:40] ppocr DEBUG: rec_res num  : 49, elapse : 10.07604169845581\n", "Results for 312d96c7-a0a0-4551-895a-8438d2bed7bb_p1.jpg:\n", "[2024/02/29 02:01:41] ppocr DEBUG: dt_boxes num : 93, elapse : 0.8437843322753906\n", "[2024/02/29 02:01:42] ppocr DEBUG: cls num  : 93, elapse : 1.593834638595581\n", "[2024/02/29 02:01:58] ppocr DEBUG: rec_res num  : 93, elapse : 15.752460718154907\n", "Results for 36248a5b-4e90-44bf-acff-8ad1b5dbd262_p1.jpg:\n", "[2024/02/29 02:01:59] ppocr DEBUG: dt_boxes num : 55, elapse : 0.534048318862915\n", "[2024/02/29 02:01:59] ppocr DEBUG: cls num  : 55, elapse : 0.9681370258331299\n", "[2024/02/29 02:02:09] ppocr DEBUG: rec_res num  : 55, elapse : 9.790262699127197\n", "Results for 3981ff1e-817c-4877-aba1-43b5d4763f09.jpg:\n", "[2024/02/29 02:02:10] ppocr DEBUG: dt_boxes num : 49, elapse : 0.7589752674102783\n", "[2024/02/29 02:02:11] ppocr DEBUG: cls num  : 49, elapse : 0.864558219909668\n", "[2024/02/29 02:02:21] ppocr DEBUG: rec_res num  : 49, elapse : 10.068588495254517\n", "Results for 3be45a79-4e3b-42d3-9b98-80b8887d2c46_p1.jpg:\n", "[2024/02/29 02:02:22] ppocr DEBUG: dt_boxes num : 78, elapse : 0.7585163116455078\n", "[2024/02/29 02:02:23] ppocr DEBUG: cls num  : 78, elapse : 1.440412998199463\n", "[2024/02/29 02:02:39] ppocr DEBUG: rec_res num  : 78, elapse : 15.303855895996094\n", "Results for 3cdc59be-cbca-40b1-af13-32c320637384_p1.jpg:\n", "[2024/02/29 02:02:40] ppocr DEBUG: dt_boxes num : 55, elapse : 0.7557916641235352\n", "[2024/02/29 02:02:40] ppocr DEBUG: cls num  : 55, elapse : 0.9645993709564209\n", "[2024/02/29 02:02:51] ppocr DEBUG: rec_res num  : 55, elapse : 10.68999695777893\n", "Results for 3e9b6445-e869-4791-9c93-47531a88b6d4_p1.jpg:\n", "[2024/02/29 02:02:52] ppocr DEBUG: dt_boxes num : 47, elapse : 0.7517788410186768\n", "[2024/02/29 02:02:53] ppocr DEBUG: cls num  : 47, elapse : 0.830730676651001\n", "[2024/02/29 02:03:02] ppocr DEBUG: rec_res num  : 47, elapse : 9.623046875\n", "Results for 3e9b6445-e869-4791-9c93-47531a88b6d4_p2.jpg:\n", "[2024/02/29 02:03:03] ppocr DEBUG: dt_boxes num : 37, elapse : 0.7509441375732422\n", "[2024/02/29 02:03:04] ppocr DEBUG: cls num  : 37, elapse : 0.6531124114990234\n", "[2024/02/29 02:03:11] ppocr DEBUG: rec_res num  : 37, elapse : 6.730187177658081\n", "Results for 404d3df3-5770-40dc-bc3c-e2a776f6905c_p1.jpg:\n", "[2024/02/29 02:03:12] ppocr DEBUG: dt_boxes num : 54, elapse : 0.7588558197021484\n", "[2024/02/29 02:03:13] ppocr DEBUG: cls num  : 54, elapse : 0.9387812614440918\n", "[2024/02/29 02:03:26] ppocr DEBUG: rec_res num  : 54, elapse : 13.745692729949951\n", "Results for 462b7e05-4def-4f61-9d3e-e261baea24f7_p1.jpg:\n", "[2024/02/29 02:03:27] ppocr DEBUG: dt_boxes num : 29, elapse : 0.8151247501373291\n", "[2024/02/29 02:03:28] ppocr DEBUG: cls num  : 29, elapse : 0.4933292865753174\n", "[2024/02/29 02:03:35] ppocr DEBUG: rec_res num  : 29, elapse : 7.433209419250488\n", "Results for 47001773-eab5-4f57-be4f-cee225b57375_p1.jpg:\n", "[2024/02/29 02:03:36] ppocr DEBUG: dt_boxes num : 61, elapse : 0.6998898983001709\n", "[2024/02/29 02:03:37] ppocr DEBUG: cls num  : 61, elapse : 1.0297365188598633\n", "[2024/02/29 02:03:50] ppocr DEBUG: rec_res num  : 61, elapse : 12.994606494903564\n", "Results for 4a87efd6-777f-477b-922f-9bff049f32bd_p1.jpg:\n", "[2024/02/29 02:03:51] ppocr DEBUG: dt_boxes num : 66, elapse : 0.7140905857086182\n", "[2024/02/29 02:03:52] ppocr DEBUG: cls num  : 66, elapse : 1.113781452178955\n", "[2024/02/29 02:04:04] ppocr DEBUG: rec_res num  : 66, elapse : 12.37113618850708\n", "Results for 4cb08fc9-25da-4f28-80f9-2e3756174837_p1.jpg:\n", "[2024/02/29 02:04:05] ppocr DEBUG: dt_boxes num : 60, elapse : 0.721839189529419\n", "[2024/02/29 02:04:06] ppocr DEBUG: cls num  : 60, elapse : 1.0083274841308594\n", "[2024/02/29 02:04:21] ppocr DEBUG: rec_res num  : 60, elapse : 14.802335262298584\n", "Results for 4ecd26c9-82e4-41a5-b740-f5782bfa4ff7.jpg:\n", "[2024/02/29 02:04:22] ppocr DEBUG: dt_boxes num : 55, elapse : 0.6785256862640381\n", "[2024/02/29 02:04:23] ppocr DEBUG: cls num  : 55, elapse : 1.028892993927002\n", "[2024/02/29 02:04:34] ppocr DEBUG: rec_res num  : 55, elapse : 11.052035331726074\n", "Results for 4ee826d7-46e0-4bc5-9e5f-2717fd4432a6_p1.jpg:\n", "[2024/02/29 02:04:35] ppocr DEBUG: dt_boxes num : 58, elapse : 0.7246143817901611\n", "[2024/02/29 02:04:36] ppocr DEBUG: cls num  : 58, elapse : 0.9658858776092529\n", "[2024/02/29 02:04:50] ppocr DEBUG: rec_res num  : 58, elapse : 13.973527669906616\n", "Results for 54e0a48a-57ae-4e9d-9fd7-1a0bacaacb37.jpeg:\n", "[2024/02/29 02:04:51] ppocr DEBUG: dt_boxes num : 52, elapse : 0.6972534656524658\n", "[2024/02/29 02:04:51] ppocr DEBUG: cls num  : 52, elapse : 0.8834645748138428\n", "[2024/02/29 02:05:01] ppocr DEBUG: rec_res num  : 52, elapse : 9.70617938041687\n", "Results for 55c02a9b-94da-4b39-a2b8-cdf514be32f5_p1.jpg:\n", "[2024/02/29 02:05:02] ppocr DEBUG: dt_boxes num : 38, elapse : 0.7335805892944336\n", "[2024/02/29 02:05:03] ppocr DEBUG: cls num  : 38, elapse : 0.6421008110046387\n", "[2024/02/29 02:05:10] ppocr DEBUG: rec_res num  : 38, elapse : 7.069906234741211\n", "Results for 5f1f3e85-f1a5-419d-82c0-6454ffff824e.jpg:\n", "[2024/02/29 02:05:11] ppocr DEBUG: dt_boxes num : 70, elapse : 0.5037071704864502\n", "[2024/02/29 02:05:12] ppocr DEBUG: cls num  : 70, elapse : 1.1921744346618652\n", "[2024/02/29 02:05:26] ppocr DEBUG: rec_res num  : 70, elapse : 13.783395528793335\n", "Results for 6432a29f-6fb4-44da-96c1-32d1a224d7a4.png:\n", "[2024/02/29 02:05:27] ppocr DEBUG: dt_boxes num : 51, elapse : 0.7335636615753174\n", "[2024/02/29 02:05:27] ppocr DEBUG: cls num  : 51, elapse : 0.8576149940490723\n", "[2024/02/29 02:05:38] ppocr DEBUG: rec_res num  : 51, elapse : 10.160290241241455\n", "Results for 660887b2-cb83-43b7-9862-4bc6d0b57c1e.jpg:\n", "[2024/02/29 02:05:38] ppocr DEBUG: dt_boxes num : 55, elapse : 0.706916093826294\n", "[2024/02/29 02:05:39] ppocr DEBUG: cls num  : 55, elapse : 0.931556224822998\n", "[2024/02/29 02:05:50] ppocr DEBUG: rec_res num  : 55, elapse : 10.278471946716309\n", "Results for 66d62ef4-692f-407c-a1c2-cd00f55b116a_p1.jpg:\n", "[2024/02/29 02:05:50] ppocr DEBUG: dt_boxes num : 45, elapse : 0.6876223087310791\n", "[2024/02/29 02:05:51] ppocr DEBUG: cls num  : 45, elapse : 0.7547867298126221\n", "[2024/02/29 02:06:01] ppocr DEBUG: rec_res num  : 45, elapse : 9.808526754379272\n", "Results for 67a40cd5-e9c4-41b7-adfc-d76046cfa94e_p1.jpg:\n", "[2024/02/29 02:06:02] ppocr DEBUG: dt_boxes num : 51, elapse : 0.6900312900543213\n", "[2024/02/29 02:06:03] ppocr DEBUG: cls num  : 51, elapse : 0.8454380035400391\n", "[2024/02/29 02:06:14] ppocr DEBUG: rec_res num  : 51, elapse : 11.020891427993774\n", "Results for 6be648cc-9f97-4bb8-b35a-45d2a52a84ec_p1.jpg:\n", "[2024/02/29 02:06:14] ppocr DEBUG: dt_boxes num : 9, elapse : 0.6743247509002686\n", "[2024/02/29 02:06:14] ppocr DEBUG: cls num  : 9, elapse : 0.13936853408813477\n", "[2024/02/29 02:06:17] ppocr DEBUG: rec_res num  : 9, elapse : 2.619659900665283\n", "Results for 6be648cc-9f97-4bb8-b35a-45d2a52a84ec_p2.jpg:\n", "[2024/02/29 02:06:18] ppocr DEBUG: dt_boxes num : 38, elapse : 0.6759116649627686\n", "[2024/02/29 02:06:18] ppocr DEBUG: cls num  : 38, elapse : 0.6484243869781494\n", "[2024/02/29 02:06:26] ppocr DEBUG: rec_res num  : 38, elapse : 7.455394983291626\n", "Results for 6db1d5dc-87b3-4805-8185-0f3df5deeb14_p1.jpg:\n", "[2024/02/29 02:06:27] ppocr DEBUG: dt_boxes num : 47, elapse : 0.7217864990234375\n", "[2024/02/29 02:06:28] ppocr DEBUG: cls num  : 47, elapse : 0.7889919281005859\n", "[2024/02/29 02:06:36] ppocr DEBUG: rec_res num  : 47, elapse : 8.667058229446411\n", "Results for 6fbbe213-876d-4aba-96b2-35a92ccdd732.jpg:\n", "[2024/02/29 02:06:37] ppocr DEBUG: dt_boxes num : 28, elapse : 0.6738927364349365\n", "[2024/02/29 02:06:38] ppocr DEBUG: cls num  : 28, elapse : 0.45836639404296875\n", "[2024/02/29 02:06:44] ppocr DEBUG: rec_res num  : 28, elapse : 6.0094592571258545\n", "Results for 7043e5ce-aea4-491e-92f8-33745eff0388_p1.jpg:\n", "[2024/02/29 02:06:44] ppocr DEBUG: dt_boxes num : 40, elapse : 0.692826509475708\n", "[2024/02/29 02:06:45] ppocr DEBUG: cls num  : 40, elapse : 0.6766085624694824\n", "[2024/02/29 02:06:52] ppocr DEBUG: rec_res num  : 40, elapse : 7.3250792026519775\n", "Results for 7bec3d04-a892-4d5f-a684-6ccac66af51d_p1.jpg:\n", "[2024/02/29 02:06:53] ppocr DEBUG: dt_boxes num : 56, elapse : 0.7108330726623535\n", "[2024/02/29 02:06:54] ppocr DEBUG: cls num  : 56, elapse : 0.9429612159729004\n", "[2024/02/29 02:07:05] ppocr DEBUG: rec_res num  : 56, elapse : 10.671324014663696\n", "Results for 7d36bf27-9b4b-4910-9a23-8ba2c9ac2b6c_p1.jpg:\n", "[2024/02/29 02:07:06] ppocr DEBUG: dt_boxes num : 60, elapse : 0.6980524063110352\n", "[2024/02/29 02:07:07] ppocr DEBUG: cls num  : 60, elapse : 1.0059068202972412\n", "[2024/02/29 02:07:23] ppocr DEBUG: rec_res num  : 60, elapse : 16.421985864639282\n", "Results for 7db317c3-ec41-46c2-aaff-4150b20db07a_p1.jpg:\n", "[2024/02/29 02:07:24] ppocr DEBUG: dt_boxes num : 99, elapse : 0.7472820281982422\n", "[2024/02/29 02:07:26] ppocr DEBUG: cls num  : 99, elapse : 1.7254064083099365\n", "[2024/02/29 02:07:44] ppocr DEBUG: rec_res num  : 99, elapse : 18.05333924293518\n", "Results for 7f6d5661-c96e-4ee4-bf5b-74bf4080f756.jpg:\n", "[2024/02/29 02:07:45] ppocr DEBUG: dt_boxes num : 38, elapse : 0.7587449550628662\n", "[2024/02/29 02:07:45] ppocr DEBUG: cls num  : 38, elapse : 0.6865274906158447\n", "[2024/02/29 02:07:52] ppocr DEBUG: rec_res num  : 38, elapse : 7.103133916854858\n", "Results for 8325a864-0d0e-4166-929a-551177eef8f1_p1.jpg:\n", "[2024/02/29 02:07:53] ppocr DEBUG: dt_boxes num : 61, elapse : 0.5685262680053711\n", "[2024/02/29 02:07:54] ppocr DEBUG: cls num  : 61, elapse : 1.0576770305633545\n", "[2024/02/29 02:08:06] ppocr DEBUG: rec_res num  : 61, elapse : 12.314300537109375\n", "Results for 84f21673-0617-48c9-b4b9-051f1c6f5236.jpg:\n", "[2024/02/29 02:08:07] ppocr DEBUG: dt_boxes num : 55, elapse : 0.829474925994873\n", "[2024/02/29 02:08:08] ppocr DEBUG: cls num  : 55, elapse : 0.9644832611083984\n", "[2024/02/29 02:08:22] ppocr DEBUG: rec_res num  : 55, elapse : 13.303642749786377\n", "Results for 86321323-efb7-4461-8a41-0dba9881753f_p1.jpg:\n", "[2024/02/29 02:08:22] ppocr DEBUG: dt_boxes num : 42, elapse : 0.7550394535064697\n", "[2024/02/29 02:08:23] ppocr DEBUG: cls num  : 42, elapse : 0.7306478023529053\n", "[2024/02/29 02:08:31] ppocr DEBUG: rec_res num  : 42, elapse : 8.074620962142944\n", "Results for 8682281b-27da-4ffc-99eb-df419bfd92c4_p1.jpg:\n", "[2024/02/29 02:08:32] ppocr DEBUG: dt_boxes num : 51, elapse : 0.5320460796356201\n", "[2024/02/29 02:08:33] ppocr DEBUG: cls num  : 51, elapse : 0.8926737308502197\n", "[2024/02/29 02:08:42] ppocr DEBUG: rec_res num  : 51, elapse : 8.759127855300903\n", "Results for 87d06fa0-7d81-429b-aafe-decfcd3d9507.png:\n", "[2024/02/29 02:08:42] ppocr DEBUG: dt_boxes num : 62, elapse : 0.7568826675415039\n", "[2024/02/29 02:08:43] ppocr DEBUG: cls num  : 62, elapse : 1.0824549198150635\n", "[2024/02/29 02:08:55] ppocr DEBUG: rec_res num  : 62, elapse : 11.78819727897644\n", "Results for 8c720108-7a90-46db-936b-fda50b1418f7_p1.jpg:\n", "[2024/02/29 02:08:56] ppocr DEBUG: dt_boxes num : 22, elapse : 0.7349634170532227\n", "[2024/02/29 02:08:56] ppocr DEBUG: cls num  : 22, elapse : 0.3918008804321289\n", "[2024/02/29 02:09:01] ppocr DEBUG: rec_res num  : 22, elapse : 4.377992153167725\n", "Results for 8c720108-7a90-46db-936b-fda50b1418f7_p2.jpg:\n", "[2024/02/29 02:09:01] ppocr DEBUG: dt_boxes num : 62, elapse : 0.5422313213348389\n", "[2024/02/29 02:09:02] ppocr DEBUG: cls num  : 62, elapse : 1.0650935173034668\n", "[2024/02/29 02:09:16] ppocr DEBUG: rec_res num  : 62, elapse : 13.453810691833496\n", "Results for 90148748-dc5f-48e3-bdec-501271a1525b.png:\n", "[2024/02/29 02:09:17] ppocr DEBUG: dt_boxes num : 47, elapse : 0.7613790035247803\n", "[2024/02/29 02:09:18] ppocr DEBUG: cls num  : 47, elapse : 0.8243029117584229\n", "[2024/02/29 02:09:26] ppocr DEBUG: rec_res num  : 47, elapse : 8.43688702583313\n", "Results for 91247579-b111-42bd-bce8-f2f6cd61b062_p1.jpg:\n", "[2024/02/29 02:09:27] ppocr DEBUG: dt_boxes num : 28, elapse : 0.7574739456176758\n", "[2024/02/29 02:09:27] ppocr DEBUG: cls num  : 28, elapse : 0.5044665336608887\n", "[2024/02/29 02:09:33] ppocr DEBUG: rec_res num  : 28, elapse : 5.64298677444458\n", "Results for 91247579-b111-42bd-bce8-f2f6cd61b062_p2.jpg:\n", "[2024/02/29 02:09:34] ppocr DEBUG: dt_boxes num : 73, elapse : 0.793903112411499\n", "[2024/02/29 02:09:35] ppocr DEBUG: cls num  : 73, elapse : 1.2677874565124512\n", "[2024/02/29 02:09:48] ppocr DEBUG: rec_res num  : 73, elapse : 13.016034841537476\n", "Results for 92479878-7f0e-4454-b0a8-4da2a03e4dfc.png:\n", "[2024/02/29 02:09:49] ppocr DEBUG: dt_boxes num : 81, elapse : 0.7947297096252441\n", "[2024/02/29 02:09:51] ppocr DEBUG: cls num  : 81, elapse : 1.4062469005584717\n", "[2024/02/29 02:10:06] ppocr DEBUG: rec_res num  : 81, elapse : 15.136675357818604\n", "Results for 95596884-2b3b-4ef3-8b2b-eeba5cdee5a0.jpeg:\n", "[2024/02/29 02:10:07] ppocr DEBUG: dt_boxes num : 33, elapse : 0.6404645442962646\n", "[2024/02/29 02:10:07] ppocr DEBUG: cls num  : 33, elapse : 0.5851316452026367\n", "[2024/02/29 02:10:16] ppocr DEBUG: rec_res num  : 33, elapse : 8.786611318588257\n", "Results for 98a5f25c-ec33-44b7-b8ba-5921060fc8ae.jpeg:\n", "[2024/02/29 02:10:17] ppocr DEBUG: dt_boxes num : 43, elapse : 0.746973991394043\n", "[2024/02/29 02:10:18] ppocr DEBUG: cls num  : 43, elapse : 0.7671599388122559\n", "[2024/02/29 02:10:27] ppocr DEBUG: rec_res num  : 43, elapse : 9.233808994293213\n", "Results for 9a101a43-a207-4de4-a673-73cda4aaef88_p1.jpg:\n", "[2024/02/29 02:10:28] ppocr DEBUG: dt_boxes num : 33, elapse : 0.7483408451080322\n", "[2024/02/29 02:10:28] ppocr DEBUG: cls num  : 33, elapse : 0.5867066383361816\n", "[2024/02/29 02:10:35] ppocr DEBUG: rec_res num  : 33, elapse : 7.014911890029907\n", "Results for 9bea0f04-dbde-4885-a7d6-34d397a2b6de_p1.jpg:\n", "[2024/02/29 02:10:36] ppocr DEBUG: dt_boxes num : 48, elapse : 0.7712697982788086\n", "[2024/02/29 02:10:37] ppocr DEBUG: cls num  : 48, elapse : 0.8476145267486572\n", "[2024/02/29 02:10:48] ppocr DEBUG: rec_res num  : 48, elapse : 11.084608793258667\n", "Results for 9ca118aa-eb78-472d-a63a-30533ed74a45_p1.jpg:\n", "[2024/02/29 02:10:49] ppocr DEBUG: dt_boxes num : 61, elapse : 0.777674674987793\n", "[2024/02/29 02:10:50] ppocr DEBUG: cls num  : 61, elapse : 1.0802226066589355\n", "[2024/02/29 02:11:04] ppocr DEBUG: rec_res num  : 61, elapse : 13.950879573822021\n", "Results for 9f9aa3ab-85c2-4e1e-8920-86880b78615a.jpg:\n", "[2024/02/29 02:11:05] ppocr DEBUG: dt_boxes num : 33, elapse : 0.8068399429321289\n", "[2024/02/29 02:11:06] ppocr DEBUG: cls num  : 33, elapse : 0.5975186824798584\n", "[2024/02/29 02:11:12] ppocr DEBUG: rec_res num  : 33, elapse : 6.703188180923462\n", "Results for a38027b1-2f0c-4a01-99f3-57bff6d3bfc0_p1.jpg:\n", "[2024/02/29 02:11:13] ppocr DEBUG: dt_boxes num : 39, elapse : 0.7314693927764893\n", "[2024/02/29 02:11:14] ppocr DEBUG: cls num  : 39, elapse : 0.7273886203765869\n", "[2024/02/29 02:11:24] ppocr DEBUG: rec_res num  : 39, elapse : 9.751662969589233\n", "Results for a3e3802c-cad7-4a58-985e-9579dc9a9b16_p1.jpg:\n", "[2024/02/29 02:11:25] ppocr DEBUG: dt_boxes num : 56, elapse : 0.7543916702270508\n", "[2024/02/29 02:11:26] ppocr DEBUG: cls num  : 56, elapse : 0.9858744144439697\n", "[2024/02/29 02:11:38] ppocr DEBUG: rec_res num  : 56, elapse : 12.141565561294556\n", "Results for a64872c2-d019-4d54-8bb6-37c30b4d8233_p1.jpg:\n", "[2024/02/29 02:11:39] ppocr DEBUG: dt_boxes num : 58, elapse : 0.7934942245483398\n", "[2024/02/29 02:11:40] ppocr DEBUG: cls num  : 58, elapse : 1.0158650875091553\n", "[2024/02/29 02:11:51] ppocr DEBUG: rec_res num  : 58, elapse : 10.66518759727478\n", "Results for a6a7f046-ac2b-4aac-9d21-d271c3e9f287.jpeg:\n", "[2024/02/29 02:11:51] ppocr DEBUG: dt_boxes num : 44, elapse : 0.7485437393188477\n", "[2024/02/29 02:11:52] ppocr DEBUG: cls num  : 44, elapse : 0.7797348499298096\n", "[2024/02/29 02:12:02] ppocr DEBUG: rec_res num  : 44, elapse : 10.111829042434692\n", "Results for a8a94d76-3cfe-4862-9e1a-4f44777192c4.jpg:\n", "[2024/02/29 02:12:03] ppocr DEBUG: dt_boxes num : 62, elapse : 0.7645845413208008\n", "[2024/02/29 02:12:04] ppocr DEBUG: cls num  : 62, elapse : 1.0751349925994873\n", "[2024/02/29 02:12:17] ppocr DEBUG: rec_res num  : 62, elapse : 12.76183795928955\n", "Results for aab10367-0cb1-49bf-b96e-6d0895844fd0_p1.jpg:\n", "[2024/02/29 02:12:18] ppocr DEBUG: dt_boxes num : 42, elapse : 0.7849493026733398\n", "[2024/02/29 02:12:19] ppocr DEBUG: cls num  : 42, elapse : 0.745969295501709\n", "[2024/02/29 02:12:27] ppocr DEBUG: rec_res num  : 42, elapse : 8.50872802734375\n", "Results for ac79a90c-7118-49b3-b749-fc4044984a29.jpg:\n", "[2024/02/29 02:12:28] ppocr DEBUG: dt_boxes num : 46, elapse : 0.7855381965637207\n", "[2024/02/29 02:12:29] ppocr DEBUG: cls num  : 46, elapse : 0.8117284774780273\n", "[2024/02/29 02:12:38] ppocr DEBUG: rec_res num  : 46, elapse : 8.361035108566284\n", "Results for b3babc54-5efd-48a3-9551-9425519d40e5.jpg:\n", "[2024/02/29 02:12:39] ppocr DEBUG: dt_boxes num : 68, elapse : 0.8010389804840088\n", "[2024/02/29 02:12:40] ppocr DEBUG: cls num  : 68, elapse : 1.187398910522461\n", "[2024/02/29 02:12:57] ppocr DEBUG: rec_res num  : 68, elapse : 16.510577917099\n", "Results for b6eea512-4bcd-43a7-a9f2-f3846941db1f_p1.jpg:\n", "[2024/02/29 02:12:58] ppocr DEBUG: dt_boxes num : 43, elapse : 0.754981279373169\n", "[2024/02/29 02:12:58] ppocr DEBUG: cls num  : 43, elapse : 0.7483024597167969\n", "[2024/02/29 02:13:10] ppocr DEBUG: rec_res num  : 43, elapse : 11.735451459884644\n", "Results for b718ce87-3f1b-4ab4-b381-6b6947fa5986_p1.jpg:\n", "[2024/02/29 02:13:11] ppocr DEBUG: dt_boxes num : 51, elapse : 0.76462721824646\n", "[2024/02/29 02:13:12] ppocr DEBUG: cls num  : 51, elapse : 0.8943872451782227\n", "[2024/02/29 02:13:22] ppocr DEBUG: rec_res num  : 51, elapse : 10.224589109420776\n", "Results for b94fb84b-1a96-413f-a771-296c92f51a5c_p1.jpg:\n", "[2024/02/29 02:13:23] ppocr DEBUG: dt_boxes num : 61, elapse : 0.5662891864776611\n", "[2024/02/29 02:13:24] ppocr DEBUG: cls num  : 61, elapse : 1.061551570892334\n", "[2024/02/29 02:13:36] ppocr DEBUG: rec_res num  : 61, elapse : 12.26786208152771\n", "Results for bc877ee1-967a-4c58-8187-74c75c2b8fc2.jpg:\n", "[2024/02/29 02:13:37] ppocr DEBUG: dt_boxes num : 73, elapse : 0.7414264678955078\n", "[2024/02/29 02:13:38] ppocr DEBUG: cls num  : 73, elapse : 1.270634412765503\n", "[2024/02/29 02:13:54] ppocr DEBUG: rec_res num  : 73, elapse : 15.464111566543579\n", "Results for bf418c97-0a35-4db3-a14d-668aebcb8b08.jpeg:\n", "[2024/02/29 02:13:55] ppocr DEBUG: dt_boxes num : 53, elapse : 0.7573635578155518\n", "[2024/02/29 02:13:56] ppocr DEBUG: cls num  : 53, elapse : 0.926466703414917\n", "[2024/02/29 02:14:07] ppocr DEBUG: rec_res num  : 53, elapse : 11.298056364059448\n", "Results for bfc6e989-ea13-474c-9843-ed1ad71d6445_p1.jpg:\n", "[2024/02/29 02:14:08] ppocr DEBUG: dt_boxes num : 54, elapse : 0.7593002319335938\n", "[2024/02/29 02:14:09] ppocr DEBUG: cls num  : 54, elapse : 0.9333374500274658\n", "[2024/02/29 02:14:22] ppocr DEBUG: rec_res num  : 54, elapse : 13.712708950042725\n", "Results for c07cf126-082a-4889-8e13-f888656da7ba_p1.jpg:\n", "[2024/02/29 02:14:23] ppocr DEBUG: dt_boxes num : 36, elapse : 0.7469449043273926\n", "[2024/02/29 02:14:24] ppocr DEBUG: cls num  : 36, elapse : 0.6367487907409668\n", "[2024/02/29 02:14:31] ppocr DEBUG: rec_res num  : 36, elapse : 7.61319375038147\n", "Results for c492f75d-90cc-4f22-9a96-a39e15dc9e28_p1.jpg:\n", "[2024/02/29 02:14:32] ppocr DEBUG: dt_boxes num : 33, elapse : 0.8167846202850342\n", "[2024/02/29 02:14:33] ppocr DEBUG: cls num  : 33, elapse : 0.6012294292449951\n", "[2024/02/29 02:14:40] ppocr DEBUG: rec_res num  : 33, elapse : 7.219823837280273\n", "Results for c884cc7e-05d5-41db-a38c-d9ef0e7cc7e2_p1.jpg:\n", "[2024/02/29 02:14:41] ppocr DEBUG: dt_boxes num : 43, elapse : 0.7511787414550781\n", "[2024/02/29 02:14:42] ppocr DEBUG: cls num  : 43, elapse : 0.7606000900268555\n", "[2024/02/29 02:14:55] ppocr DEBUG: rec_res num  : 43, elapse : 12.81801724433899\n", "Results for c8e2450e-543e-40c3-90da-5d140be2bb42_p1.jpg:\n", "[2024/02/29 02:14:55] ppocr DEBUG: dt_boxes num : 51, elapse : 0.7532355785369873\n", "[2024/02/29 02:14:56] ppocr DEBUG: cls num  : 51, elapse : 0.8901236057281494\n", "[2024/02/29 02:15:06] ppocr DEBUG: rec_res num  : 51, elapse : 9.693172931671143\n", "Results for ca4460c2-fb3b-48c4-a232-93bf8bee663d_p1.jpg:\n", "[2024/02/29 02:15:07] ppocr DEBUG: dt_boxes num : 83, elapse : 0.697542667388916\n", "[2024/02/29 02:15:08] ppocr DEBUG: cls num  : 83, elapse : 1.430438756942749\n", "[2024/02/29 02:15:24] ppocr DEBUG: rec_res num  : 83, elapse : 16.016533851623535\n", "Results for ca742817-3906-4058-824e-0fe3e9237259.jpg:\n", "[2024/02/29 02:15:25] ppocr DEBUG: dt_boxes num : 56, elapse : 0.8379826545715332\n", "[2024/02/29 02:15:26] ppocr DEBUG: cls num  : 56, elapse : 0.9834990501403809\n", "[2024/02/29 02:15:37] ppocr DEBUG: rec_res num  : 56, elapse : 10.41982889175415\n", "Results for d0b57c66-a818-407b-8d26-8a6b6d664e6b_p1.jpg:\n", "[2024/02/29 02:15:38] ppocr DEBUG: dt_boxes num : 43, elapse : 0.7573468685150146\n", "[2024/02/29 02:15:38] ppocr DEBUG: cls num  : 43, elapse : 0.7580492496490479\n", "[2024/02/29 02:15:47] ppocr DEBUG: rec_res num  : 43, elapse : 8.208630800247192\n", "Results for d2683023-9422-4d06-8783-26460be7cd70.jpg:\n", "[2024/02/29 02:15:47] ppocr DEBUG: dt_boxes num : 32, elapse : 0.7510800361633301\n", "[2024/02/29 02:15:48] ppocr DEBUG: cls num  : 32, elapse : 0.5797052383422852\n", "[2024/02/29 02:15:56] ppocr DEBUG: rec_res num  : 32, elapse : 7.605438232421875\n", "Results for d273e58b-9d6a-4ad4-8bdf-1e32817b2d4f_p1.jpg:\n", "[2024/02/29 02:15:57] ppocr DEBUG: dt_boxes num : 65, elapse : 0.7603895664215088\n", "[2024/02/29 02:15:58] ppocr DEBUG: cls num  : 65, elapse : 1.1294403076171875\n", "[2024/02/29 02:16:10] ppocr DEBUG: rec_res num  : 65, elapse : 12.470157623291016\n", "Results for d4fe44a6-804e-4ad7-bbae-e6ad17fe4aa6_p1.jpg:\n", "[2024/02/29 02:16:11] ppocr DEBUG: dt_boxes num : 46, elapse : 0.7405924797058105\n", "[2024/02/29 02:16:12] ppocr DEBUG: cls num  : 46, elapse : 0.8160626888275146\n", "[2024/02/29 02:16:23] ppocr DEBUG: rec_res num  : 46, elapse : 11.641164541244507\n", "Results for d552b2d5-68da-4be4-bc28-c26f9f2244fa_p1.jpg:\n", "[2024/02/29 02:16:25] ppocr DEBUG: dt_boxes num : 56, elapse : 0.7813277244567871\n", "[2024/02/29 02:16:26] ppocr DEBUG: cls num  : 56, elapse : 0.9912416934967041\n", "[2024/02/29 02:16:36] ppocr DEBUG: rec_res num  : 56, elapse : 10.173790693283081\n", "Results for dd14b463-c448-47cb-b3c6-88ea55ce2469.jpg:\n", "[2024/02/29 02:16:37] ppocr DEBUG: dt_boxes num : 47, elapse : 0.7467820644378662\n", "[2024/02/29 02:16:37] ppocr DEBUG: cls num  : 47, elapse : 0.8196935653686523\n", "[2024/02/29 02:16:47] ppocr DEBUG: rec_res num  : 47, elapse : 9.433367490768433\n", "Results for ddf8bdca-86a7-4670-9275-ab1a1627868b_p1.jpg:\n", "[2024/02/29 02:16:48] ppocr DEBUG: dt_boxes num : 52, elapse : 0.7741680145263672\n", "[2024/02/29 02:16:49] ppocr DEBUG: cls num  : 52, elapse : 0.9231481552124023\n", "[2024/02/29 02:17:00] ppocr DEBUG: rec_res num  : 52, elapse : 10.71051287651062\n", "Results for deb513fe-8cf1-47d4-aaa2-a1f3c71a3873.jpeg:\n", "[2024/02/29 02:17:00] ppocr DEBUG: dt_boxes num : 53, elapse : 0.7405416965484619\n", "[2024/02/29 02:17:01] ppocr DEBUG: cls num  : 53, elapse : 0.9275391101837158\n", "[2024/02/29 02:17:13] ppocr DEBUG: rec_res num  : 53, elapse : 11.990991830825806\n", "Results for df050402-cd97-44b6-8876-ed27816e7907_p1.jpg:\n", "[2024/02/29 02:17:14] ppocr DEBUG: dt_boxes num : 2, elapse : 0.7296390533447266\n", "[2024/02/29 02:17:14] ppocr DEBUG: cls num  : 2, elapse : 0.07192087173461914\n", "[2024/02/29 02:17:15] ppocr DEBUG: rec_res num  : 2, elapse : 0.6748380661010742\n", "Results for df050402-cd97-44b6-8876-ed27816e7907_p2.jpg:\n", "[2024/02/29 02:17:16] ppocr DEBUG: dt_boxes num : 67, elapse : 0.80177903175354\n", "[2024/02/29 02:17:17] ppocr DEBUG: cls num  : 67, elapse : 1.1724348068237305\n", "[2024/02/29 02:17:34] ppocr DEBUG: rec_res num  : 67, elapse : 16.832194566726685\n", "Results for e70b8af2-f4d4-4610-b4c6-f72ff19daa36.jpg:\n", "[2024/02/29 02:17:35] ppocr DEBUG: dt_boxes num : 46, elapse : 0.7445662021636963\n", "[2024/02/29 02:17:36] ppocr DEBUG: cls num  : 46, elapse : 0.8077630996704102\n", "[2024/02/29 02:17:47] ppocr DEBUG: rec_res num  : 46, elapse : 10.873619079589844\n", "Results for e7f8df71-5411-4c8e-a2a3-7e9732ec0dc6.jpeg:\n", "[2024/02/29 02:17:48] ppocr DEBUG: dt_boxes num : 31, elapse : 0.7445590496063232\n", "[2024/02/29 02:17:48] ppocr DEBUG: cls num  : 31, elapse : 0.5602729320526123\n", "[2024/02/29 02:18:02] ppocr DEBUG: rec_res num  : 31, elapse : 13.63241195678711\n", "Results for e945140e-fde8-4d3e-be16-0200f03045fb_p1.jpg:\n", "[2024/02/29 02:18:03] ppocr DEBUG: dt_boxes num : 107, elapse : 0.7884490489959717\n", "[2024/02/29 02:18:04] ppocr DEBUG: cls num  : 107, elapse : 1.7948925495147705\n", "[2024/02/29 02:18:35] ppocr DEBUG: rec_res num  : 107, elapse : 30.41474676132202\n", "Results for eafb55be-1507-4775-bb37-bd899bf611a9_p1.jpg:\n", "[2024/02/29 02:18:36] ppocr DEBUG: dt_boxes num : 58, elapse : 0.7028598785400391\n", "[2024/02/29 02:18:37] ppocr DEBUG: cls num  : 58, elapse : 0.9748311042785645\n", "[2024/02/29 02:18:49] ppocr DEBUG: rec_res num  : 58, elapse : 12.536564111709595\n", "Results for eafb55be-1507-4775-bb37-bd899bf611a9_p2.jpg:\n", "[2024/02/29 02:18:50] ppocr DEBUG: dt_boxes num : 72, elapse : 0.7289645671844482\n", "[2024/02/29 02:18:52] ppocr DEBUG: cls num  : 72, elapse : 1.2224781513214111\n", "[2024/02/29 02:19:07] ppocr DEBUG: rec_res num  : 72, elapse : 15.081356048583984\n", "Results for efbd2cc9-eeba-488e-b730-cfcc0579df09.jpeg:\n", "[2024/02/29 02:19:07] ppocr DEBUG: dt_boxes num : 81, elapse : 0.7016065120697021\n", "[2024/02/29 02:19:09] ppocr DEBUG: cls num  : 81, elapse : 1.3579893112182617\n", "[2024/02/29 02:19:25] ppocr DEBUG: rec_res num  : 81, elapse : 16.05777931213379\n", "Results for f192f7f1-4b91-449e-954a-ba53723221da_p1.jpg:\n", "[2024/02/29 02:19:25] ppocr DEBUG: dt_boxes num : 12, elapse : 0.6718242168426514\n", "[2024/02/29 02:19:26] ppocr DEBUG: cls num  : 12, elapse : 0.19297122955322266\n", "[2024/02/29 02:19:29] ppocr DEBUG: rec_res num  : 12, elapse : 3.6827523708343506\n", "Results for f192f7f1-4b91-449e-954a-ba53723221da_p2.jpg:\n", "[2024/02/29 02:19:30] ppocr DEBUG: dt_boxes num : 39, elapse : 0.6661474704742432\n", "[2024/02/29 02:19:31] ppocr DEBUG: cls num  : 39, elapse : 0.6502718925476074\n", "[2024/02/29 02:19:39] ppocr DEBUG: rec_res num  : 39, elapse : 7.906988143920898\n", "Results for f19a7500-5568-47ad-9084-672c528bb1ee_p1.jpg:\n", "[2024/02/29 02:19:40] ppocr DEBUG: dt_boxes num : 57, elapse : 0.7167251110076904\n", "[2024/02/29 02:19:41] ppocr DEBUG: cls num  : 57, elapse : 0.9646077156066895\n", "[2024/02/29 02:19:53] ppocr DEBUG: rec_res num  : 57, elapse : 12.032525777816772\n", "Results for f203a16a-e971-4b51-9cdf-cc68479b9193.jpg:\n", "[2024/02/29 02:19:54] ppocr DEBUG: dt_boxes num : 41, elapse : 0.7212953567504883\n", "[2024/02/29 02:19:55] ppocr DEBUG: cls num  : 41, elapse : 0.6934545040130615\n", "[2024/02/29 02:20:05] ppocr DEBUG: rec_res num  : 41, elapse : 10.779725313186646\n", "Results for f51b3f23-b230-4451-9831-cba3e44e2948.jpg:\n", "[2024/02/29 02:20:06] ppocr DEBUG: dt_boxes num : 52, elapse : 0.7177448272705078\n", "[2024/02/29 02:20:07] ppocr DEBUG: cls num  : 52, elapse : 0.8742356300354004\n", "[2024/02/29 02:20:19] ppocr DEBUG: rec_res num  : 52, elapse : 11.443884134292603\n", "Results for fb0903ab-5702-4356-ba82-65520d2567ab.jpg:\n", "[2024/02/29 02:20:20] ppocr DEBUG: dt_boxes num : 41, elapse : 0.9517803192138672\n", "[2024/02/29 02:20:20] ppocr DEBUG: cls num  : 41, elapse : 0.6876239776611328\n", "[2024/02/29 02:20:29] ppocr DEBUG: rec_res num  : 41, elapse : 8.858017683029175\n", "Results for fd7c9079-4173-4ce0-99fe-9b88d2bd8e65.jpeg:\n"]}], "source": ["def run_ocr_on_folder(folder_path):\n", "    # Initialize the OCR engine\n", "    ocr = PaddleOCR(use_angle_cls=True, lang='en')\n", "    paddle_df = pd.DataFrame()\n", "    filename_ls = []\n", "    box_ls = []\n", "    text_ls = []\n", "    score_ls = []\n", "\n", "    # Loop through all files in the folder\n", "    for filename in os.listdir(folder_path):\n", "        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):\n", "            image_path = os.path.join(folder_path, filename)\n", "            filename_ls.append(filename)\n", "\n", "            # Perform OCR on the image\n", "            result = ocr.ocr(image_path, cls=True)\n", "\n", "            # Process the result\n", "            if result:\n", "                boxes = [line[0] for line in result[0]]\n", "                txts = [line[1][0] for line in result[0]]\n", "                scores = [line[1][1] for line in result[0]]\n", "\n", "                box_ls.append(boxes)\n", "                text_ls.append(txts)\n", "                score_ls.append(scores)\n", "\n", "            else: \n", "                box_ls.append(None)\n", "                text_ls.append(None)\n", "                score_ls.append(None)\n", "\n", "            print(f\"Results for {filename}:\")\n", "\n", "    paddle_df['FileName'] = filename_ls\n", "    paddle_df['BoundingBox'] = box_ls\n", "    paddle_df['Text'] = text_ls\n", "    paddle_df['Score'] = score_ls\n", "\n", "    paddle_df.to_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/invoices_0.8_paddle_ocr.xlsx')\n", "\n", "    return paddle_df\n", "\n", "# Example usage\n", "paddle_df = run_ocr_on_folder('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/incorrect_amount')\n", "paddle_df = run_ocr_on_folder('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices2_0.8_paddleocr/100_invoices2_0.8')\n"]}, {"cell_type": "markdown", "id": "7c0682de", "metadata": {}, "source": ["#### Single Image Test"]}, {"cell_type": "code", "execution_count": null, "id": "fb5ceae2", "metadata": {}, "outputs": [], "source": ["image_path = Path('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/test/0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3_p1.jpg')\n", "ocr = PaddleOCR(use_angle_cls=True, lang='en')\n", "result = ocr.ocr(image_path.as_posix(), cls=True)\n", "\n", "image = Image.open(image_path).convert('RGB')\n", "boxes = [line[0] for line in result[0]]\n", "txts = [line[1][0] for line in result[0]]\n", "scores = [line[1][1] for line in result[0]]"]}, {"cell_type": "code", "execution_count": null, "id": "a5dd38b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[2380.0, 106.0], [2839.0, 86.0], [2844.0, 198.0], [2386.0, 218.0]], ('FUR LIFE<Vet', 0.9762704968452454)]\n", "[[[1439.0, 138.0], [1899.0, 116.0], [1901.0, 162.0], [1442.0, 184.0]], ('KYABRAM VETERINARY CLINIC', 0.9824627637863159)]\n", "[[[1115.0, 201.0], [2235.0, 132.0], [2238.0, 186.0], [1119.0, 255.0]], ('KYABRAM 77 McCormick Rd (PO Box 74O) Kyabram VIC 362O P:O3 5852 46OO', 0.9716672301292419)]\n", "[[[651.0, 217.0], [974.0, 208.0], [977.0, 300.0], [654.0, 309.0]], ('<PERSON><PERSON><PERSON>', 0.9981611371040344)]\n", "[[[2510.0, 217.0], [2736.0, 217.0], [2736.0, 262.0], [2510.0, 262.0]], ('faltg petaylives', 0.5585842132568359)]\n", "[[[1269.0, 268.0], [2077.0, 220.0], [2081.0, 274.0], [1272.0, 322.0]], (\"'E: <EMAIL> W: kyabramvets.com.au\", 0.9804844856262207)]\n", "[[[732.0, 301.0], [961.0, 287.0], [964.0, 332.0], [735.0, 347.0]], ('animal health', 0.9779835343360901)]\n", "[[[1546.0, 292.0], [1805.0, 278.0], [1808.0, 320.0], [1548.0, 334.0]], ('ACN: ***********', 0.9096922874450684)]\n", "[[[1576.0, 542.0], [1929.0, 528.0], [1931.0, 583.0], [1578.0, 597.0]], ('18 ***********', 0.9896293878555298)]\n", "[[[2036.0, 642.0], [2278.0, 633.0], [2281.0, 691.0], [2039.0, 701.0]], ('10/09/2023', 0.9999893307685852)]\n", "[[[1571.0, 672.0], [1838.0, 653.0], [1842.0, 711.0], [1575.0, 730.0]], (' Invoice Date', 0.9818316102027893)]\n", "[[[2032.0, 701.0], [2248.0, 691.0], [2251.0, 749.0], [2035.0, 759.0]], ('159662AI', 0.9682921767234802)]\n", "[[[600.0, 722.0], [867.0, 707.0], [870.0, 766.0], [603.0, 780.0]], ('<PERSON><PERSON>', 0.9944767355918884)]\n", "[[[1575.0, 730.0], [1894.0, 711.0], [1898.0, 770.0], [1579.0, 789.0]], (' Transaction No', 0.9672589898109436)]\n", "[[[600.0, 772.0], [973.0, 749.0], [977.0, 807.0], [603.0, 830.0]], ('147 Kiernan Street', 0.9749683737754822)]\n", "[[[2041.0, 767.0], [2161.0, 767.0], [2161.0, 812.0], [2041.0, 812.0]], ('<PERSON>', 0.9983987808227539)]\n", "[[[1585.0, 783.0], [1734.0, 783.0], [1734.0, 829.0], [1585.0, 829.0]], ('Patient', 0.9989080429077148)]\n", "[[[591.0, 822.0], [915.0, 807.0], [917.0, 862.0], [594.0, 876.0]], ('Bearii VIC 3641', 0.9985793828964233)]\n", "[[[2040.0, 814.0], [2435.0, 791.0], [2439.0, 849.0], [2044.0, 872.0]], ('985113000521017', 0.9990093111991882)]\n", "[[[34.0, 842.0], [141.0, 842.0], [141.0, 933.0], [34.0, 933.0]], ('W', 0.9438310265541077)]\n", "[[[1576.0, 834.0], [1788.0, 824.0], [1791.0, 882.0], [1579.0, 893.0]], ('Microchip', 0.999531090259552)]\n", "[[[2036.0, 868.0], [2470.0, 845.0], [2473.0, 899.0], [2039.0, 922.0]], ('<PERSON> <PERSON> CRESWELL', 0.9999172687530518)]\n", "[[[1585.0, 892.0], [1803.0, 892.0], [1803.0, 938.0], [1585.0, 938.0]], ('Reference', 0.9979234337806702)]\n", "[[[499.0, 1100.0], [665.0, 1100.0], [665.0, 1158.0], [499.0, 1158.0]], ('Details', 0.9959777593612671)]\n", "[[[489.0, 1284.0], [1307.0, 1258.0], [1309.0, 1312.0], [491.0, 1338.0]], ('TAX Invoice for Professional Services', 0.9844065308570862)]\n", "[[[2500.0, 1305.0], [2700.0, 1295.0], [2703.0, 1353.0], [2503.0, 1363.0]], ('Amount', 0.9999679923057556)]\n", "[[[2246.0, 1317.0], [2327.0, 1317.0], [2327.0, 1371.0], [2246.0, 1371.0]], ('No', 0.9944678544998169)]\n", "[[[472.0, 1371.0], [1166.0, 1349.0], [1168.0, 1404.0], [474.0, 1426.0]], (' Service Provided on 10/09/2023', 0.9756045341491699)]\n", "[[[477.0, 1429.0], [750.0, 1429.0], [750.0, 1475.0], [477.0, 1475.0]], ('Medications', 0.9979377388954163)]\n", "[[[2224.0, 1438.0], [2339.0, 1438.0], [2339.0, 1500.0], [2224.0, 1500.0]], ('1.00', 0.999178409576416)]\n", "[[[2557.0, 1429.0], [2714.0, 1429.0], [2714.0, 1475.0], [2557.0, 1475.0]], ('150.20', 0.999468982219696)]\n", "[[[523.0, 1492.0], [1235.0, 1470.0], [1236.0, 1524.0], [525.0, 1546.0]], (' Cytopoint 40mg Injection (6 Pack)', 0.9839292168617249)]\n", "[[[2570.0, 1483.0], [2723.0, 1483.0], [2723.0, 1542.0], [2570.0, 1542.0]], ('-45.10', 0.9868943095207214)]\n", "[[[464.0, 1546.0], [673.0, 1546.0], [673.0, 1592.0], [464.0, 1592.0]], ('Discount', 0.9985095262527466)]\n", "[[[2570.0, 1575.0], [2736.0, 1575.0], [2736.0, 1633.0], [2570.0, 1633.0]], ('105.10', 0.9998223185539246)]\n", "[[[2199.0, 1592.0], [2348.0, 1592.0], [2348.0, 1650.0], [2199.0, 1650.0]], ('Total:', 0.9994522929191589)]\n", "[[[2629.0, 1654.0], [2749.0, 1654.0], [2749.0, 1708.0], [2629.0, 1708.0]], ('9.55', 0.999626636505127)]\n", "[[[1981.0, 1667.0], [2346.0, 1653.0], [2349.0, 1712.0], [1983.0, 1726.0]], ('Includes TAX of:', 0.9925000667572021)]\n", "[[[2608.0, 1875.0], [2774.0, 1875.0], [2774.0, 1933.0], [2608.0, 1933.0]], ('105.10', 0.9982404708862305)]\n", "[[[1832.0, 1896.0], [2129.0, 1887.0], [2131.0, 1945.0], [1834.0, 1955.0]], ('Amount Paid', 0.9998462796211243)]\n", "[[[1398.0, 1971.0], [1564.0, 1971.0], [1564.0, 2033.0], [1398.0, 2033.0]], ('PAID', 0.9987487196922302)]\n", "[[[2672.0, 2012.0], [2791.0, 2012.0], [2791.0, 2067.0], [2672.0, 2067.0]], ('0.00', 0.9999979138374329)]\n", "[[[1837.0, 2025.0], [2267.0, 2025.0], [2267.0, 2079.0], [1837.0, 2079.0]], ('Balance remaining', 0.9980061650276184)]\n", "[[[426.0, 2159.0], [898.0, 2145.0], [900.0, 2204.0], [427.0, 2217.0]], ('Special Instructions', 0.9927398562431335)]\n", "[[[434.0, 2221.0], [2561.0, 2200.0], [2561.0, 2271.0], [435.0, 2292.0]], ('If you elect to pay by direct deposit, please include your name and your Client Reference Number', 0.9813652038574219)]\n", "[[[439.0, 2279.0], [2407.0, 2258.0], [2408.0, 2316.0], [439.0, 2338.0]], (' located on the bottom left of this page along with your payment (eg. <PERSON> 123456ABCD)', 0.9797039031982422)]\n", "[[[482.0, 3162.0], [1249.0, 3171.0], [1248.0, 3225.0], [481.0, 3216.0]], ('Ref - 2750AI - 10/09/2023 - 12:37:18', 0.9513755440711975)]\n", "[[[593.0, 3279.0], [1172.0, 3283.0], [1172.0, 3342.0], [592.0, 3337.0]], ('Kyabram Veterinary Clinic', 0.982596218585968)]\n", "[[[597.0, 3337.0], [1283.0, 3350.0], [1282.0, 3405.0], [596.0, 3391.0]], ('T7 McCormick Road, Kyabram VIC 3620', 0.9716882109642029)]\n", "[[[1735.0, 3341.0], [2327.0, 3350.0], [2326.0, 3421.0], [1734.0, 3412.0]], (' 9 Blake Street, Nathalia VIC 368', 0.9694464802742004)]\n", "[[[636.0, 3416.0], [900.0, 3426.0], [898.0, 3484.0], [634.0, 3474.0]], ('Mastercard', 0.**************)]\n", "[[[1342.0, 3438.0], [1692.0, 3438.0], [1692.0, 3492.0], [1342.0, 3492.0]], (\"Cardholder's Name:\", 0.****************)]\n", "[[[643.0, 3488.0], [797.0, 3488.0], [797.0, 3533.0], [643.0, 3533.0]], ('Visa', 0.***************)]\n", "[[[1340.0, 3503.0], [1603.0, 3513.0], [1601.0, 3572.0], [1337.0, 3562.0]], ('Card Number:', 0.***************)]\n", "[[[641.0, 3533.0], [883.0, 3542.0], [880.0, 3601.0], [638.0, 3591.0]], ('Bankcard', 0.****************)]\n", "[[[1348.0, 3574.0], [1663.0, 3588.0], [1660.0, 3647.0], [1346.0, 3633.0]], ('Card Expiry Date:', 0.****************)]\n", "[[[650.0, 3586.0], [815.0, 3597.0], [811.0, 3655.0], [646.0, 3645.0]], ('Other', 0.***************)]\n", "[[[2382.0, 3604.0], [2582.0, 3604.0], [2582.0, 3662.0], [2382.0, 3662.0]], ('CCV Code:', 0.****************)]\n", "[[[1355.0, 3671.0], [1543.0, 3671.0], [1543.0, 3717.0], [1355.0, 3717.0]], ('Signature:', 0.****************)]\n", "[[[631.0, 3729.0], [1044.0, 3734.0], [1044.0, 3792.0], [630.0, 3787.0]], ('Direct Deposit Details', 0.****************)]\n", "[[[1079.0, 3729.0], [2817.0, 3776.0], [2816.0, 3842.0], [1077.0, 3795.0]], ('Bank: NABBSB: 083-543Bank Acc:40 707 6373Using your Client Number as reference.', 0.***************)]\n"]}], "source": ["for res in result:\n", "    for line in res:\n", "        print(line)"]}, {"cell_type": "markdown", "id": "2d30fbc9", "metadata": {}, "source": ["#### Multiple File Test"]}, {"cell_type": "code", "execution_count": 4, "id": "84bbe33c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024/02/23 04:50:36] ppocr DEBUG: Namespace(help='==SUPPRESS==', use_gpu=False, use_xpu=False, use_npu=False, ir_optim=True, use_tensorrt=False, min_subgraph_size=15, precision='fp32', gpu_mem=500, image_dir=None, page_num=0, det_algorithm='DB', det_model_dir='C:\\\\Users\\\\<USER>\\\\det\\\\en\\\\en_PP-OCRv3_det_infer', det_limit_side_len=960, det_limit_type='max', det_box_type='quad', det_db_thresh=0.3, det_db_box_thresh=0.6, det_db_unclip_ratio=1.5, max_batch_size=10, use_dilation=False, det_db_score_mode='fast', det_east_score_thresh=0.8, det_east_cover_thresh=0.1, det_east_nms_thresh=0.2, det_sast_score_thresh=0.5, det_sast_nms_thresh=0.2, det_pse_thresh=0, det_pse_box_thresh=0.85, det_pse_min_area=16, det_pse_scale=1, scales=[8, 16, 32], alpha=1.0, beta=1.0, fourier_degree=5, rec_algorithm='SVTR_LCNet', rec_model_dir='C:\\\\Users\\\\<USER>\\\\rec\\\\en\\\\en_PP-OCRv3_rec_infer', rec_image_inverse=True, rec_image_shape='3, 48, 320', rec_batch_num=6, max_text_length=25, rec_char_dict_path='c:\\\\Users\\\\<USER>\\\\.conda\\\\envs\\\\claryt\\\\lib\\\\site-packages\\\\paddleocr\\\\ppocr\\\\utils\\\\en_dict.txt', use_space_char=True, vis_font_path='./doc/fonts/simfang.ttf', drop_score=0.5, e2e_algorithm='PGNet', e2e_model_dir=None, e2e_limit_side_len=768, e2e_limit_type='max', e2e_pgnet_score_thresh=0.5, e2e_char_dict_path='./ppocr/utils/ic15_dict.txt', e2e_pgnet_valid_set='totaltext', e2e_pgnet_mode='fast', use_angle_cls=False, cls_model_dir='C:\\\\Users\\\\<USER>\\\\cls\\\\ch_ppocr_mobile_v2.0_cls_infer', cls_image_shape='3, 48, 192', label_list=['0', '180'], cls_batch_num=6, cls_thresh=0.9, enable_mkldnn=False, cpu_threads=10, use_pdserving=False, warmup=False, sr_model_dir=None, sr_image_shape='3, 32, 128', sr_batch_num=1, draw_img_save_dir='./inference_results', save_crop_res=False, crop_res_save_dir='./output', use_mp=False, total_process_num=1, process_id=0, benchmark=False, save_log_path='./log_output/', show_log=True, use_onnx=False, output='./output', table_max_len=488, table_algorithm='TableAttn', table_model_dir=None, merge_no_span_structure=True, table_char_dict_path=None, layout_model_dir=None, layout_dict_path=None, layout_score_threshold=0.5, layout_nms_threshold=0.5, kie_algorithm='LayoutXLM', ser_model_dir=None, re_model_dir=None, use_visual_backbone=True, ser_dict_path='../train_data/XFUND/class_list_xfun.txt', ocr_order_method=None, mode='structure', image_orientation=False, layout=True, table=True, ocr=True, recovery=False, use_pdf2docx_api=False, lang='en', det=True, rec=True, type='ocr', ocr_version='PP-OCRv3', structure_version='PP-StructureV2')\n", "C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\100_invoices_0.7_paddleocr\\test\\3de4847f-a37a-4ebe-af3d-cde221c5a10d.jpeg\n", "[2024/02/23 04:50:37] ppocr WARNING: Since the angle classifier is not initialized, the angle classifier will not be uesd during the forward process\n", "[2024/02/23 04:50:38] ppocr DEBUG: dt_boxes num : 67, elapse : 0.8489987850189209\n", "[2024/02/23 04:50:54] ppocr DEBUG: rec_res num  : 67, elapse : 15.427002668380737\n", "C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\100_invoices_0.7_paddleocr\\test\\3de4847f-a37a-4ebe-af3d-cde221c5a10d.jpeg\n", "[2024/02/23 04:50:56] ppocr WARNING: Since the angle classifier is not initialized, the angle classifier will not be uesd during the forward process\n", "[2024/02/23 04:50:57] ppocr DEBUG: dt_boxes num : 67, elapse : 0.7629985809326172\n", "[2024/02/23 04:51:12] ppocr DEBUG: rec_res num  : 67, elapse : 14.51600432395935\n", "Processing complete.\n"]}], "source": ["# Directories\n", "input_dir = Path('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices_0.7_paddleocr/test')\n", "output_dir = Path('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices_0.7_paddleocr/output')\n", "font_path = Path('C:/Users/<USER>/Documents/Projects/OCR_inhouse/notebook/font/HarmonyOS_Sans_Bold.ttf').as_posix()\n", "\n", "# Ensure output directory exists\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Initialize PaddleOCR\n", "ocr = PaddleOCR(use_angle_cls=False, lang='en')\n", "\n", "file_patterns = ['*.jpg', '*.jpeg', '*.png', '*.PNG', '*.JPEG', '*.JPG']\n", "\n", "# Loop through each file in the input directory\n", "for file_pattern in file_patterns:\n", "    for image_path in input_dir.glob(file_pattern):\n", "        print(image_path)\n", "        # Perform OCR\n", "        result = ocr.ocr(image_path.as_posix(), cls=True)\n", "\n", "    # Process and save the image\n", "        if result:\n", "            image = Image.open(image_path).convert('RGB')\n", "            boxes = [line[0] for line in result[0]]\n", "            txts = [line[1][0] for line in result[0]]\n", "            scores = [line[1][1] for line in result[0]]\n", "            im_show = draw_ocr(image, boxes, txts, scores, font_path=font_path)\n", "            im_show = Image.fromarray(im_show) \n", "\n", "            # Save to output directory with the same filename\n", "            output_file_path = output_dir / image_path.name\n", "            im_show.save(output_file_path.as_posix())\n", "\n", "print(\"Processing complete.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}