{"cells": [{"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read Documents"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["json_gpt = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_invoice_0.8.xlsx')\n", "json_gpt['JsonResponse'] = json_gpt['JsonResponse'].apply(lambda x: json.loads(x))\n", "ocr_df = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/sep_top100_invoice_0.8.xlsx')"]}, {"cell_type": "code", "execution_count": 203, "metadata": {}, "outputs": [], "source": ["json_gpt_ocr = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_ocr_invoice_0.8.xlsx')\n", "json_gpt_ocr['JsonResponse'] = json_gpt_ocr['JsonResponse'].apply(lambda x: json.loads(x))"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [], "source": ["# json_pdf = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_invoice_pdf3.xlsx')\n", "# json_img = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_invoice3.xlsx')\n", "\n", "# json_pdf = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/json_invoice_0.7.xlsx')\n", "# ocr_df = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/sep_top100_invoice_0.7.xlsx')\n"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [], "source": ["invoiceno_df = pd.read_csv('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/sep_invoiceno.csv')\n", "invoiceno_df = invoiceno_df [['Invoice No_','ClaimNo']]\n", "invoiceno_df['Invoice No_'] = invoiceno_df['Invoice No_'].str.replace('#', '')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading of Json Object Debug"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(json_gpt_ocr)):\n", "    print(i)\n", "    x = json_gpt_ocr['JsonResponse'][i]\n", "    # print(x)\n", "    if x != 'Null':\n", "        # y = json.dumps(x)\n", "        a = json.loads(x)\n", "\n", "\n", "b = json_gpt_ocr['JsonResponse'][85]\n", "b[102:110]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Json Preprocessing"]}, {"cell_type": "code", "execution_count": 196, "metadata": {}, "outputs": [], "source": ["def process_json_responses(df):\n", "    # Assuming df has a column 'JsonResponse' containing the JSON data\n", "    \n", "    df['gpt_Service_Provider'] = df['JsonResponse'].apply(lambda x: x['VetXmlClaim']['InfoFromVet']['Vet']['PracticeName'])\n", "    df['gpt_Total_Amount'] = df['JsonResponse'].apply(lambda x: x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['TotalIncVat'])\n", "    df['gpt_Total_Amount'] = df['gpt_Total_Amount'].apply(lambda x: float(x.replace(',', '').replace('$', '')) if isinstance(x, str) else x)\n", "    df['gpt_Invoice_Number'] = df['JsonResponse'].apply(lambda x: x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['Invoices'][0]['InvoiceNumber']).astype(str)\n", "    df['gpt_Invoice_count'] = df['JsonResponse'].apply(lambda x: len(x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['Invoices']))\n", "    df['gpt_Treatment_count'] = df['JsonResponse'].apply(lambda x: len(x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['Invoices'][0]['Items']))\n", "    df['gpt_Service_Provider'] = df['gpt_Service_Provider'].str.lower()\n", "\n", "    return df\n", "\n", "# Example of using the function\n", "json_gpt_df = process_json_responses(json_gpt)\n", "json_gpt_df['filename'] = json_gpt_df['FileName'].apply(lambda x: x.split('.')[0])\n", "json_gpt_df['filename'] = json_gpt_df['filename'].apply(lambda x: x.split('_')[0])"]}, {"cell_type": "code", "execution_count": 211, "metadata": {}, "outputs": [], "source": ["def process_json_responses(df):\n", "    # Assuming df has a column 'JsonResponse' containing the JSON data\n", "    \n", "    df['gpt2_Service_Provider'] = df['JsonResponse'].apply(lambda x: x['VetXmlClaim']['InfoFromVet']['Vet']['PracticeName'])\n", "    df['gpt2_Total_Amount'] = df['JsonResponse'].apply(lambda x: x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['TotalIncVat'])\n", "    df['gpt2_Total_Amount'] = df['gpt2_Total_Amount'].apply(lambda x: float(x.replace(',', '').replace('$', '')) if isinstance(x, str) else x)\n", "    df['gpt2_Invoice_Number'] = df['JsonResponse'].apply(lambda x: x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['Invoices'][0]['InvoiceNumber']).astype(str)\n", "    df['gpt2_Invoice_count'] = df['JsonResponse'].apply(lambda x: len(x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['Invoices']))\n", "    df['gpt2_Treatment_count'] = df['JsonResponse'].apply(lambda x: len(x['VetXmlClaim']['InfoFromVet']['Conditions'][0]['Financial']['Invoices'][0]['Items']))\n", "    df['gpt2_Service_Provider'] = df['gpt2_Service_Provider'].str.lower()\n", "\n", "    return df\n", "\n", "# Example of using the function\n", "json_gpt_ocr_df = process_json_responses(json_gpt_ocr)\n", "json_gpt_ocr_df['filename'] = json_gpt_ocr_df['FileName'].apply(lambda x: x.split('.')[0])\n", "json_gpt_ocr_df['filename'] = json_gpt_ocr_df['filename'].apply(lambda x: x.split('_')[0])\n", "\n", "json_gpt_ocr_df = json_gpt_ocr_df.rename(columns={'JsonResponse':'JsonResponse2'})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### File Merging"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [], "source": ["json_df = pd.merge(json_gpt_df,json_gpt_ocr_df, on='filename')"]}, {"cell_type": "code", "execution_count": 217, "metadata": {}, "outputs": [], "source": ["ocr_df['UPM_ServiceProviderName'] = ocr_df['UPM_ServiceProviderName'].str.lower()\n", "ocr_df['filename'] = ocr_df['DocFile'].apply(lambda x: x.split('.')[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json_merge = pd.merge(json_df, ocr_df, on='filename')\n", "json_merge = pd.merge(json_merge, invoiceno_df, on='ClaimNo', how='left')\n", "json_merge"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Check File Duplicates"]}, {"cell_type": "code", "execution_count": 228, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName_x</th>\n", "      <th>JsonResponse</th>\n", "      <th>gpt_Service_Provider</th>\n", "      <th>gpt_Total_Amount</th>\n", "      <th>gpt_Invoice_Number</th>\n", "      <th>gpt_Invoice_count</th>\n", "      <th>gpt_Treatment_count</th>\n", "      <th>filename</th>\n", "      <th>Unnamed: 0_x</th>\n", "      <th>FileName_y</th>\n", "      <th>...</th>\n", "      <th>check_total_amount</th>\n", "      <th>check_invoice_count</th>\n", "      <th>check_treatment_count</th>\n", "      <th>check_invoice_no</th>\n", "      <th>check_service_provider2</th>\n", "      <th>check_total_amount_diff2</th>\n", "      <th>check_total_amount2</th>\n", "      <th>check_invoice_count2</th>\n", "      <th>check_treatment_count2</th>\n", "      <th>check_invoice_no2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3e9b6445-e869-4791-9c93-47531a88b6d4.pdf</td>\n", "      <td>{'VetXmlClaim': {'InfoFromPolicyHolder': {'Ani...</td>\n", "      <td>clyde veterinary hospital</td>\n", "      <td>395.34</td>\n", "      <td>453335</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>3e9b6445-e869-4791-9c93-47531a88b6d4</td>\n", "      <td>22</td>\n", "      <td>3e9b6445-e869-4791-9c93-47531a88b6d4</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-144.18</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>91247579-b111-42bd-bce8-f2f6cd61b062.pdf</td>\n", "      <td>{'VetXmlClaim': {'InfoFromPolicyHolder': {'Ani...</td>\n", "      <td>boyne tannum veterinary surgery</td>\n", "      <td>272.70</td>\n", "      <td>753045</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>91247579-b111-42bd-bce8-f2f6cd61b062</td>\n", "      <td>52</td>\n", "      <td>91247579-b111-42bd-bce8-f2f6cd61b062</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>-30.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 60 columns</p>\n", "</div>"], "text/plain": ["                                  FileName_x  \\\n", "7   3e9b6445-e869-4791-9c93-47531a88b6d4.pdf   \n", "21  91247579-b111-42bd-bce8-f2f6cd61b062.pdf   \n", "\n", "                                         JsonResponse  \\\n", "7   {'VetXmlClaim': {'InfoFromPolicyHolder': {'Ani...   \n", "21  {'VetXmlClaim': {'InfoFromPolicyHolder': {'Ani...   \n", "\n", "               gpt_Service_Provider  gpt_Total_Amount gpt_Invoice_Number  \\\n", "7         clyde veterinary hospital            395.34             453335   \n", "21  boyne tannum veterinary surgery            272.70             753045   \n", "\n", "    gpt_Invoice_count  gpt_Treatment_count  \\\n", "7                   2                    4   \n", "21                  2                    7   \n", "\n", "                                filename  Unnamed: 0_x  \\\n", "7   3e9b6445-e869-4791-9c93-47531a88b6d4            22   \n", "21  91247579-b111-42bd-bce8-f2f6cd61b062            52   \n", "\n", "                              FileName_y  ... check_total_amount  \\\n", "7   3e9b6445-e869-4791-9c93-47531a88b6d4  ...                  1   \n", "21  91247579-b111-42bd-bce8-f2f6cd61b062  ...                  1   \n", "\n", "   check_invoice_count  check_treatment_count check_invoice_no  \\\n", "7                    1                      0                0   \n", "21                   1                      0                0   \n", "\n", "    check_service_provider2  check_total_amount_diff2  check_total_amount2  \\\n", "7                         1                   -144.18                    1   \n", "21                        0                    -30.00                    1   \n", "\n", "    check_invoice_count2 check_treatment_count2 check_invoice_no2  \n", "7                      1                      0                 0  \n", "21                     1                      0                 0  \n", "\n", "[2 rows x 60 columns]"]}, "execution_count": 228, "metadata": {}, "output_type": "execute_result"}], "source": ["json_merge[json_merge.duplicated('filename')]"]}, {"cell_type": "code", "execution_count": 224, "metadata": {}, "outputs": [], "source": ["json_merge['check_service_provider'] = (json_merge['gpt_Service_Provider']== json_merge['UPM_ServiceProviderName']).astype(int)\n", "# json_pdf_merge['check_total_amount'] = (json_pdf_merge['gpt_Total_Amount']== json_pdf_merge['Amount Claimed (UPM)']).astype(int)\n", "json_merge['check_total_amount_diff'] = (json_merge['gpt_Total_Amount'] -  json_merge['Amount Claimed (UPM)'])\n", "json_merge['check_total_amount'] = json_merge['check_total_amount_diff'].apply(lambda x: 1 if x < 0.05 else 0)\n", "\n", "json_merge['check_invoice_count'] = (json_merge['gpt_Invoice_count']== json_merge['Invoice Count (UPM)']).astype(int)\n", "json_merge['check_treatment_count'] = (json_merge['gpt_Treatment_count']== json_merge['Treatment Count (UPM)']).astype(int)\n", "json_merge['check_invoice_no'] = (json_merge['gpt_Invoice_Number']== json_merge['Invoice No_']).astype(int)"]}, {"cell_type": "code", "execution_count": 225, "metadata": {}, "outputs": [], "source": ["json_merge['check_service_provider2'] = (json_merge['gpt2_Service_Provider']== json_merge['UPM_ServiceProviderName']).astype(int)\n", "# json_pdf_merge['check_total_amount'] = (json_pdf_merge['gpt_Total_Amount']== json_pdf_merge['Amount Claimed (UPM)']).astype(int)\n", "json_merge['check_total_amount_diff2'] = (json_merge['gpt2_Total_Amount'] -  json_merge['Amount Claimed (UPM)'])\n", "json_merge['check_total_amount2'] = json_merge['check_total_amount_diff2'].apply(lambda x: 1 if x < 0.05 else 0)\n", "\n", "json_merge['check_invoice_count2'] = (json_merge['gpt2_Invoice_count']== json_merge['Invoice Count (UPM)']).astype(int)\n", "json_merge['check_treatment_count2'] = (json_merge['gpt2_Treatment_count']== json_merge['Treatment Count (UPM)']).astype(int)\n", "json_merge['check_invoice_no2'] = (json_merge['gpt2_Invoice_Number']== json_merge['Invoice No_']).astype(int)"]}, {"cell_type": "code", "execution_count": 227, "metadata": {}, "outputs": [], "source": ["json_merge.to_excel(('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/GPT4_vision_ocr_100_invoices_0.8.xlsx'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Incorrect Extraction Analysis"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [], "source": ["inc_amount = json_pdf_merge[json_pdf_merge['check_total_amount']==0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inc_invoiceno = json_pdf_merge[json_pdf_merge['check_invoice_no']==0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Extract Files from Folder"]}, {"cell_type": "code", "execution_count": 177, "metadata": {}, "outputs": [], "source": ["# Extract files from folder\n", "\n", "import os\n", "import shutil\n", "\n", "def extract_files_from_folders(parent_directory):\n", "    # Iterate over all the entries in the directory\n", "    for folder_name in os.listdir(parent_directory):\n", "        folder_path = os.path.join(parent_directory, folder_name)\n", "        \n", "        # Check if it is a folder\n", "        if os.path.isdir(folder_path):\n", "            # Iterate over all the files in the folder\n", "            for file_name in os.listdir(folder_path):\n", "                file_path = os.path.join(folder_path, file_name)\n", "                \n", "                # Check if it is a file and not a folder\n", "                if os.path.isfile(file_path):\n", "                    # Move file to the parent_directory\n", "                    shutil.copy(file_path, parent_directory)\n", "\n", "# Directory containing folders\n", "parent_directory = r\"C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\100_invoices_0.7_extraction\"\n", "\n", "# Extract files from the folders in the specified directory\n", "extract_files_from_folders(parent_directory)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Copy Files from Folder"]}, {"cell_type": "code", "execution_count": 182, "metadata": {}, "outputs": [], "source": ["import shutil\n", "import os\n", "\n", "def copy_files(filenames, source_dir, target_dir):\n", "    # Ensure the target directory exists, create it if it doesn't\n", "    if not os.path.exists(target_dir):\n", "        os.makedirs(target_dir)\n", "\n", "    # Copy each file from the source to the target directory\n", "    for filename in filenames:\n", "        source_file = os.path.join(source_dir, filename)\n", "        target_file = os.path.join(target_dir, filename)\n", "\n", "        # Check if the file exists before copying\n", "        if os.path.exists(source_file):\n", "            shutil.copy(source_file, target_file)\n", "        else:\n", "            print(f\"File not found: {source_file}\")\n", "\n", "# Example usage\n", "source_dir = 'C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices_0.7_extraction'  # Replace with your source directory path\n", "target_dir = 'C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices_0.7_paddleocr/incorrect_invoiceno'   # Replace with your target directory path\n", "\n", "filenames = list(inc_invoiceno['FileName'])\n", "copy_files(filenames, source_dir, target_dir)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> Matching"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import ast\n", "import pandas as pd\n", "from fuzzywuzzy import fuzz\n", "from fuzzywuzzy import process"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["amount_df = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/incorrect_amount.xlsx')\n", "amount_paddle = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/incorrect_amount_paddle_ocr.xlsx')\n", "invoice08_paddle = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/invoices_0.8_paddle_ocr.xlsx')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df = invoice08_paddle\n", "df['filename'] = df['FileName'].apply(lambda x: x.split('.')[0])\n", "df['filename'] = df['filename'].apply(lambda x: x.split('_')[0])\n", "df['Text']= df['Text'].apply(ast.literal_eval)\n", "df['Score']= df['Score'].apply(ast.literal_eval)\n", "df['BoundingBox']= df['BoundingBox'].apply(ast.literal_eval)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["\n", "# Define a custom function to concatenate lists\n", "def concatenate_lists(series):\n", "    return [item for sublist in series for item in sublist]\n", "\n", "# Group by 'filename' and apply the concatenate_lists function to each group\n", "grouped = df.groupby('filename')\n", "concatenated = grouped.agg({\n", "    'Text': concatenate_lists,\n", "    'Score': concatenate_lists,\n", "    'BoundingBox': concatenate_lists\n", "})\n", "\n", "# Reset index if you want 'filename' back as a column\n", "concatenated = concatenated.reset_index()\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["concatenated.to_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/invoices_0.8_paddle_ocr_clean.xlsx')"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["['MEADOW HEIGHTS VETERINARY CLINIC',\n", " '80 Taggerty Cr, Meadow Heights 3048',\n", " 'Ph:03 9309 3199 Ph/Fax:03 9309 6051',\n", " '<PERSON> (Paul) Bhatti & Associates',\n", " 'Meadow Heights',\n", " 'Vet Clinic',\n", " 'ABN **************',\n", " 'Like us on',\n", " 'facebook',\n", " '<PERSON><PERSON>',\n", " '16 Colchester Circuit',\n", " 'ROXBURGH PARK 3064',\n", " 'TAX INVOICE 16 Feb 2023',\n", " 'Invoice #E232160727',\n", " 'ears retained canine upper retained',\n", " 'Patient',\n", " 'Date',\n", " 'Item',\n", " \"Q'tity\",\n", " 'Total',\n", " '<PERSON>',\n", " '16/2/2023',\n", " 'CONSULTATION STANDARD',\n", " '1',\n", " '$75.00',\n", " '<PERSON>',\n", " '16/2/2023',\n", " 'NEXGARD SPECTRA 7.6-15kg SINGLE(pkt)',\n", " '1',\n", " '$33.00',\n", " 'TOTAL NOW DUE',\n", " '$108.00',\n", " 'includes GST of',\n", " '$9.82',\n", " 'The following payments have been received with thanks',\n", " 'Inv.Date',\n", " 'Payment Method',\n", " 'Card/Chq#',\n", " 'Payment',\n", " '16/2/2023',\n", " 'EFTPOS',\n", " '$108.00',\n", " '$0.00',\n", " 'ACCOUNT BALANCE OUTSTANDING',\n", " 'Bank Details: (CBA)',\n", " 'Northern Suburbs Veterinary Clinics']"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["concatenated['Text'].values[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["col = ['filename', 'Amount Claimed (OCR)','Amount Claimed (UPM)', 'gpt_Total_Amount']\n", "merged_df = pd.merge(concatenated, df[col], how='left', on = 'filename')\n", "merged_df['gpt_Total_Amount'] = merged_df['gpt_Total_Amount'].apply(lambda x: str(x))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["fuzzy_score_ls = []\n", "fuzzy_score_row = []\n", "fuzzy_value_ls = []\n", "fuzzy_max_score_ls = []\n", "\n", "for i in range(len(merged_df)):\n", "    for text in merged_df.iloc[i]['Text']:\n", "        \n", "        fuzzy_score = fuzz.token_set_ratio(merged_df.iloc[i]['gpt_Total_Amount'],text)\n", "        fuzzy_score_ls.append(fuzzy_score)\n", "    # print(fuzzy_score_ls)\n", "    fuzzy_max_score = max(fuzzy_score_ls)\n", "    # print(len(merged.iloc[i]['Text']))\n", "    # print(len(fuzzy_score_ls))\n", "    fuzzy_value = merged_df.iloc[i]['Text'][fuzzy_score_ls.index(fuzzy_max_score )]\n", "    # print(fuzzy_value)\n", "    # print(fuzzy_score_ls.index(fuzzy_max_score ))\n", "    # print(fuzzy_score_ls[21])\n", "\n", "    fuzzy_score_row.append(fuzzy_score_ls)\n", "    fuzzy_max_score_ls.append(fuzzy_max_score)\n", "    fuzzy_value_ls.append(fuzzy_value)\n", "\n", "    fuzzy_score_ls = []\n", "    \n", "    \n", "\n", "merged_df['fuzzy_score'] = fuzzy_score_row\n", "merged_df['fuzzy_max_score'] = fuzzy_max_score_ls\n", "merged_df['fuzzy_value'] = fuzzy_value_ls"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["merged_df['Amount Claimed (UPM)'] =merged_df['Amount Claimed (UPM)'].astype(float)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def try_convert_to_float(x):\n", "    try:\n", "        # Attempt to convert the string to float after replacing ',' and '$'\n", "        return float(x.replace(',', '').replace('$', ''))\n", "    except ValueError:\n", "        # If conversion fails, return the original value or handle as needed\n", "        return x\n", "\n", "# Apply this function to the 'fuzzy_value' column\n", "merged_df['fuzzy_value'] = merged_df['fuzzy_value'].apply(lambda x: try_convert_to_float(x) if isinstance(x, str) else x)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["merged_df['Is_fuzzy_correct'] = (merged_df['fuzzy_value'] == merged_df['Amount Claimed (UPM)']).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df[merged_df['Is_fuzzy_correct']==1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_df[merged_df['Is_fuzzy_correct']==0]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["merged_df.to_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/incorrect_amount_fuzzy_wuzy.xlsx')"]}], "metadata": {"kernelspec": {"display_name": "claryt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}