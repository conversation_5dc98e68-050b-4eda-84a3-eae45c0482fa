{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pyodbc\n", "import sqlalchemy as sa\n", "import urllib.parse\n", "from sqlalchemy.sql import text\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "from azure.storage.blob import ResourceTypes, AccountSasPermissions, generate_account_sas, BlobServiceClient\n", "from datetime import datetime, timedelta"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read Selected Sample Invoices from Spreadsheet"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["sep = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/september_invoices.xlsx')\n", "claimfile = pd.read_csv('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/ClaimFile_prod.csv')\n", "doc = pd.read_csv('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/Document_prod.csv')\n", "jan = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/january_invoices.xlsx')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python39\\site-packages\\openpyxl\\styles\\stylesheet.py:226: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n"]}], "source": ["tabular_error = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/sep_tabular_error.xlsx', skiprows=1)\n", "tabular_error_100 = tabular_error.head(100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merge Document to get relevant fields"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["doc_new = doc[['DocumentId', 'ClaimRefNumber', 'DocumentName', 'DocumentType','DocumentPath']]\n", "doc_new = doc_new[doc_new['DocumentType']== 'ClaimInvoice']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merge_error = pd.merge(tabular_error_100, doc_new, left_on='CSP Reference No', right_on='ClaimRefNumber')\n", "merge_error['DocContainer'] = merge_error['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "merge_error['DocFile'] = merge_error['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merge_sep = pd.merge(sep, claimfile[['ClaimNo','CSP_ReferenceNo']], left_on='Claim Number', right_on='ClaimNo')\n", "merge_sep_doc = pd.merge(merge_sep,doc_new,left_on='CSP_ReferenceNo', right_on='ClaimRefNumber' )\n", "merge_jan = pd.merge(jan, claimfile[['ClaimNo','CSP_ReferenceNo']], left_on='Claim Number', right_on='ClaimNo')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_9140\\2687857845.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  merge_final['DocContainer'] = merge_final['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_9140\\2687857845.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  merge_final['DocFile'] = merge_final['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]\n"]}], "source": ["merge_final = merge_sep_doc.drop_duplicates()\n", "merge_final['DocContainer'] = merge_final['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "merge_final['DocFile'] = merge_final['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analysis of Result"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["merge_final.to_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/september_invoices_merged.xlsx')\n", "merge_final = pd.read_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/september_invoices_merged.xlsx')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merge_final_07 = merge_final[(merge_final['Document Confidence']<0.8)].head(100)\n", "merge_final_07[merge_final_07['Amount Claimed (Difference)'] == 0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merge_final_8 = merge_final[(merge_final['Document Confidence']>=0.8) & (merge_final['AI Type']==1)].head(100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merge_final_8_other = merge_final[(merge_final['Document Confidence']>=0.8) & (merge_final['AI Type']==1)].iloc[100:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merge_final_8[merge_final_8['Amount Claimed (OCR)']!= merge_final_8['Amount Claimed (UPM)']]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["df = merge_final_07\n", "df = df.reset_index()\n", "df['DocContainer'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[0]\n", "df['DocFile'] = df['DocumentPath'].str.split(pat=\"/\", n=1, expand = True)[1]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["df.to_excel('C:/Users/<USER>/Documents/Projects/OCR_inhouse/data/sep_top100_invoice_0.7.xlsx')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = merge_error"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download Document with Incorrect Extraction"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# define credentials\n", "    \n", "source_key = 'ju9EsJDjAagISO4SHE+y9aaFFm80hb51pVn2vAog4IHeA4lJ4IhhvR2kUYweeknCjfwQMd618JYqHENoWz4moQ=='\n", "\n", "source_account_name = 'p3storageprod'\n", "\n", "sas_token = generate_account_sas(account_name=source_account_name, account_key=source_key,\n", "                                     resource_types=ResourceTypes(\n", "                                         service=True, container=True, object=True),\n", "permission=AccountSasPermissions(read=True),\n", "                                     expiry=datetime.utcnow() + <PERSON><PERSON><PERSON>(hours=1))\n", "\n", "source_blob_service_client = BlobServiceClient(account_url=f'https://{source_account_name}.blob.core.windows.net/', credential=source_key)\n", "\n", "# Create download function\n", "def download(blob_service_client: BlobServiceClient, container_name: str, file_name: str, dest_path):\n", "        container_client = blob_service_client.get_container_client(container_name)\n", "        blob_client = container_client.get_blob_client(file_name)\n", "        with open(dest_path, \"wb\") as f:\n", "            f.write(blob_client.download_blob().readall())\n", "\n", "# Download documents from blob storage to local folder called pdf             \n", "for idx in df.index:\n", "    path =  'C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/100_invoices_0.7/'+df['DocFile'][idx]\n", "    download(source_blob_service_client,df['DocContainer'].iloc[idx],df['DocFile'].iloc[idx],path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test function"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["'jpg'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["import os \n", "def extract_filename(file_path):\n", "    # Split the path by '/' and get the last part (filename with extension)\n", "    filename_with_extension = file_path.split('/')[-1]\n", "    # Split the filename by '.' and get the first part (filename without extension)\n", "    filename = filename_with_extension.split('.')[1]\n", "    return filename.lower()\n", "\n", "extract_filename('C:/Users/<USER>/Documents/Projects/OCR_inhouse/sample/test/0bb58606-c684-4662-b849-14f1eb45b75d.jpg')"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["'C:\\\\users\\\\<USER>\\\\Documents\\\\Projects\\\\OCR_inhouse\\\\sample\\\\test\\\\0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "def extract_filename(file_path):\n", "    # Split the path by '/' and get the last part (filename with extension)\n", "    filename_with_extension = file_path.split('/')[-1]\n", "    # Split the filename by '.' and get the first part (filename without extension)\n", "    filename = filename_with_extension.split('.')[0]\n", "    return filename\n", "\n", "extract_filename(r'C:\\users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\test\\0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3.pdf')"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3\n"]}], "source": ["import os\n", "\n", "def extract_filename(file_path):\n", "    # Get the base name (filename with extension)\n", "    filename_with_extension = os.path.basename(file_path)\n", "    # Split the filename by '.' and get the first part (filename without extension)\n", "    filename = filename_with_extension.split('.')[0]\n", "    return filename\n", "\n", "filename = extract_filename(r'C:\\users\\<USER>\\Documents\\Projects\\OCR_inhouse\\sample\\test\\0aa9a0b7-d4d1-458d-8a23-c8f9f3d9bcc3.pdf')\n", "print(filename)\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def get_files_from_folder(folder_path, extensions=['.pdf', '.jpg','.png','.jpeg']):\n", "    file_paths = []\n", "    for root, dirs, files in os.walk(folder_path):\n", "        for file in files:\n", "            if any(file.lower().endswith(ext) for ext in extensions):\n", "                file_paths.append(os.path.join(root, file))\n", "    return file_paths\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["folder_path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\OCR_inhouse\\\\sample\\\\100_invoices2_0.8_extraction\" \n", "file_paths = get_files_from_folder(folder_path)\n"]}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}