{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import json \n", "import re\n", "from dateutil import parser\n", "from datetime import datetime\n", "import os\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mar_invoice = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_merged.xlsx')\n", "mar_invoice['FileName'] = mar_invoice['DocFile'].apply(lambda x: x.split('.')[0])\n", "raw_treatment = pd.read_csv(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\UPM_treatment.csv')\n", "inv = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_gpt_ocr_json.xlsx')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preprocessing"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Function to clean phone numbers and ABN\n", "def clean_alpha_chars(x):\n", "    if x:\n", "        return re.sub(r'[^\\d]', '', x)  # Remove all non-digit characters\n", "    return ''\n", "\n", "# Attempt to convert the string to float after replacing ',' and '$'\n", "def clean_amount(x):\n", "    if x is None:\n", "        return None\n", "    try:\n", "        return float(x.replace(',', '').replace('$', ''))\n", "    except ValueError:\n", "        # If conversion fails, return the original value or handle as needed\n", "        return x\n", "\n", "def standardize_date(date_str):\n", "    if date_str is None:\n", "        return None\n", "    try:\n", "        # Parse the date\n", "        parsed_date = parser.parse(date_str, dayfirst=True)\n", "        \n", "        # Format the date as dd/mm/yy\n", "        standardized_date = parsed_date.strftime('%d/%m/%y')\n", "        return standardized_date\n", "    except (ValueErro<PERSON>, TypeError):\n", "        # Return None or some default value if the date is invalid or empty\n", "        return None\n", "    \n", "def standardize_and_extract_date(date_str):\n", "    if date_str is None:\n", "        return None\n", "    try:\n", "        # Parse the date\n", "        parsed_date = parser.parse(date_str, dayfirst=True)\n", "\n", "        # If the input is a full datetime string, this will convert it to a date\n", "        if hasattr(parsed_date, 'hour'):\n", "            date_str = parsed_date.strftime('%Y-%m-%d %H:%M:%S.%f')\n", "            dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S.%f')\n", "            return dt.strftime('%Y-%m-%d')\n", "        else:\n", "            # Format the date as YYYY-MM-DD\n", "            return parsed_date.strftime('%Y-%m-%d')\n", "    except (ValueErro<PERSON>, TypeError):\n", "        # Return None or some default value if the date is invalid or empty\n", "        return None\n", "    \n", "def clean_invoiceno(x):\n", "    if x:\n", "        return x.replace('#', '')  # Remove all non-digit characters\n", "    return ''\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extraction and validation of json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the expected schema\n", "expected_schema = {\n", "    \"vetxmlclaim\": {\n", "        \"infofrompolicyholder\": {\n", "            \"animaldetails\": {\n", "                \"name\": None\n", "            }\n", "        },\n", "        \"infofromvet\": {\n", "            \"vet\": {\n", "                \"vetname\": None,\n", "                \"practicename\": None,\n", "                \"practiceaddress\": None,\n", "                \"practiceabn\": None,\n", "                \"practicephonenumber\": None,\n", "                \"practicefaxnumber\": None,\n", "                \"practiceemailaddress\": None,\n", "                \"practicewebsite\": None\n", "            },\n", "            \"conditions\": [\n", "                {\n", "                    \"financial\": {\n", "                        \"totalexvat\": None,\n", "                        \"vat\": None,\n", "                        \"totalincvat\": None,\n", "                        \"invoices\": [\n", "                            {\n", "                                \"invoicenumber\": None,\n", "                                \"invoicedate\": None,\n", "                                \"totalexvat\": None,\n", "                                \"vat\": None,\n", "                                \"totalincvat\": None,\n", "                                \"ismultipetpresent\": None,\n", "                                \"items\": [\n", "                                    {\n", "                                        \"treatmentdate\": None,\n", "                                        \"itemcode\": None,\n", "                                        \"itemtype\": None,\n", "                                        \"sequence\": None,\n", "                                        \"description\": None,\n", "                                        \"amountexvat\": None,\n", "                                        \"discountexvat\": None,\n", "                                        \"vat\": None,\n", "                                        \"quantity\": None,\n", "                                        \"totalincvat\": None\n", "                                    }\n", "                                ]\n", "                            }\n", "                        ]\n", "                    }\n", "                }\n", "            ],\n", "            \"documentmetadata\": {}\n", "        }\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Final\n", "\n", "def validate_json(json_string):\n", "    try:\n", "        input_json = json.loads(json_string)\n", "    except json.JSONDecodeError as e:\n", "        print(f\"Invalid JSON string: {e}\")\n", "        return False\n", "    return True\n", "    \n", "\n", "def check_keys(expected, actual, path=\"\"):\n", "    if isinstance(expected, dict):\n", "        if not isinstance(actual, dict):\n", "            print(f\"Type mismatch at '{path}': Expected dict, got {type(actual).__name__}\")\n", "            return False\n", "        actual_lower = {k.lower(): v for k, v in actual.items()}\n", "        for key in expected:\n", "            if key.lower() not in actual_lower:\n", "                print(f\"Missing key '{key}' at path '{path}'\")\n", "                return False\n", "            if not check_keys(expected[key], actual_lower[key.lower()], f\"{path}.{key if path else key}\"):\n", "                return False\n", "    elif isinstance(expected, list):\n", "        if not isinstance(actual, list):\n", "            print(f\"Type mismatch at '{path}': Expected list, got {type(actual).__name__}\")\n", "            return False\n", "        for index, item in enumerate(actual):\n", "            if not check_keys(expected[0], item, f\"{path}[{index}]\"):\n", "                return False\n", "    return True\n", "\n", "    # return check_keys(expected_schema, input_json)\n", "\n", "\n", "def validate_and_correct_json(json_string):\n", "    # Removing comments from JSON string using regular expression\n", "    corrected_json = re.sub(r'(?<!:)//.*', '', json_string)\n", "    return corrected_json\n", "\n", "def to_lower_keys(x):\n", "    if isinstance(x, dict):\n", "        return {k.lower(): to_lower_keys(v) for k, v in x.items()}\n", "    elif isinstance(x, list):\n", "        return [to_lower_keys(i) for i in x]\n", "    else:\n", "        return x\n", "\n", "def extract_vet_data(json_string, file_name):\n", "    # Parse the JSON string\n", "    data = to_lower_keys(json.loads(json_string))\n", "    \n", "    # Initialize a list to store the extracted data\n", "    extracted_data = []\n", "\n", "    # Extract JSON header for Policy Holder information\n", "    animal_name = data['vetxmlclaim']['infofrompolicyholder']['animaldetails']['name']\n", "\n", "    # Extract JSON header for Vet information\n", "    vet_info = data['vetxmlclaim']['infofromvet']['vet']\n", "\n", "    # Extract Document Metadata\n", "    document_metadata = data['vetxmlclaim']['infofromvet']['documentmetadata']\n", "\n", "\n", "    # Clean PracticeABN, PracticePhoneNumber, and PracticeFaxNumber\n", "    practice_abn = clean_alpha_chars(vet_info.get('practiceabn', ''))\n", "    practice_phone = clean_alpha_chars(vet_info.get('practicephonenumber', ''))\n", "    practice_fax = clean_alpha_chars(vet_info.get('practicefaxnumber', ''))\n", "    \n", "\n", "    # Loop through each condition in the JSON\n", "    for condition in data['vetxmlclaim']['infofromvet']['conditions']:\n", "\n", "        # Extract JSON header for Claim Financial information\n", "        financial_info = condition['financial']\n", "        claim_total_incvat_clean = clean_amount(financial_info.get('totalincvat'))\n", "        claim_vat_clean = clean_amount(financial_info.get('vat'))\n", "        claim_total_exvat_clean = clean_amount(financial_info.get('totalexvat'))\n", "        \n", "    \n", "        # Loop through each invoice\n", "        for i, invoice in enumerate(financial_info.get('invoices', [])):\n", "            invoice_number_clean = clean_invoiceno(invoice.get('invoicenumber'))\n", "            invoice_date_clean = standardize_and_extract_date(invoice.get('invoicedate'))\n", "\n", "            invoice_total_incvat_clean = clean_amount(invoice.get('totalincvat'))\n", "            invoice_vat_clean = clean_amount(invoice.get('vat'))\n", "            invoice_total_exvat_clean = clean_amount(invoice.get('totalexvat'))\n", "            \n", "            \n", "\n", "            invoice_info = {\n", "\n", "                # File info\n", "                'FileName': file_name,\n", "                'DocumentMetadata': document_metadata,\n", "\n", "                # Policy Holder info\n", "                'AnimalName': animal_name,\n", "\n", "                # Vet info\n", "                'VetName': vet_info.get('vetname', ''),\n", "                'PracticeName': vet_info.get('practicename'),\n", "                'PracticeAddress': vet_info.get('practiceaddress'),\n", "                'PracticeABN': practice_abn,\n", "                'PracticePhoneNumber': practice_phone,\n", "                'PracticeFaxNumber': practice_fax,\n", "                'PracticeEmailAddress': vet_info.get('practiceemailaddress'),\n", "                'PracticeWebsite': vet_info.get('practicewebsite'),\n", "\n", "                # Claim info\n", "                'ClaimTotalExVAT': claim_total_exvat_clean,\n", "                'ClaimInvoiceVAT':claim_vat_clean,\n", "                'ClaimInvoiceTotalIncVAT': claim_total_incvat_clean,\n", "\n", "\n", "                # Invoice Info\n", "                'InvoiceIndex': i+1,\n", "                'InvoiceNo': invoice_number_clean,\n", "                'InvoiceDate': invoice_date_clean,\n", "                'IsMultipetPresent': invoice.get('ismultipetpresent'),\n", "\n", "                'InvoiceTotalExVAT': invoice_total_exvat_clean,\n", "                'InvoiceVAT':invoice_vat_clean,\n", "                'InvoiceTotalIncVAT': invoice_total_incvat_clean,\n", "\n", "            }\n", "\n", "            # Loop through each treatment item in the invoice\n", "            for j, item in enumerate(invoice.get('items', [])):\n", "                item_info = invoice_info.copy()\n", "\n", "                treatment_date_clean = standardize_and_extract_date(item.get('treatmentdate'))\n", "            \n", "                treatment_total_incvat_clean = clean_amount(item.get('totalincvat'))\n", "                treatment_vat_clean = clean_amount(item.get('vat'))\n", "                treatment_total_exvat_clean = clean_amount(item.get('amountexvat'))\n", "                treatment_discount_exvat_clean = clean_amount(item.get('discountexvat'))\n", "                \n", "                \n", "                item_info.update({\n", "                    'TreatmentIndex': j+1,\n", "                    'TreatmentDate': treatment_date_clean ,\n", "                    'ItemCode': item.get('itemcode', ''),\n", "                    'ItemType': item.get('itemtype', ''),\n", "                    'Sequence': item.get('sequence', ''),\n", "                    'Description': item.get('description'),\n", "                    'Quantity': item.get('quantity'),\n", "\n", "                    'TreatmentAmountExVAT': treatment_total_exvat_clean,\n", "                    'TreatmentVAT':treatment_vat_clean,\n", "                    'TreatmentAmountIncVAT': treatment_total_incvat_clean,\n", "                    'TreatmentDiscountExVAT': treatment_discount_exvat_clean\n", "                })\n", "                extracted_data.append(item_info)\n", "\n", "    # Create DataFrame\n", "    df = pd.DataFrame(extracted_data)\n", "    return df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Process JSON"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_json_responses(df):\n", "    # Define all expected columns including those from extract_vet_data plus 'is_json_valid'\n", "    columns = [\n", "        'FileName','DocumentMetadata',\n", "        'AnimalName', 'VetName','PracticeName', 'PracticeAddress', 'PracticeABN','PracticePhoneNumber', 'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "        'ClaimTotalExVAT','ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "        'InvoiceIndex', 'InvoiceNo','InvoiceDate','IsMultipetPresent', \n", "        'InvoiceTotalExVAT', 'InvoiceVAT','InvoiceTotalIncVAT' ,\n", "        'TreatmentIndex','TreatmentDate','ItemCode','ItemType','Sequence','Description','Quantity', \n", "        'TreatmentAmountExVAT','TreatmentVAT','TreatmentAmountIncVAT','TreatmentDiscountExVAT','is_Jsonvalid'\n", "    ]\n", "\n", "    # Initialize an empty DataFrame to store the results with the defined columns\n", "    all_data = pd.DataFrame(columns=columns)\n", "\n", "    # Iterate over each JSON string and corresponding FileName in the DataFrame\n", "    for json_string, file_name in zip(df['JsonResponse'], df['FileName']):\n", "        if pd.isna(json_string) or not validate_and_correct_json(json_string) or not validate_json(json_string):\n", "            # If JsonResponse is null or invalid, append a row with null values except for FileName\n", "            null_data = {col: None for col in columns}\n", "            null_data['FileName'] = file_name\n", "            null_data['is_Jsonvalid'] = False\n", "            all_data = all_data.append(null_data, ignore_index=True)\n", "            continue\n", "\n", "        # Since JSON is valid, process it\n", "        corrected_json = validate_and_correct_json(json_string)\n", "        extracted_data = extract_vet_data(corrected_json, file_name)\n", "        extracted_data['is_Jsonvalid'] = True\n", "\n", "        # Concatenate the results\n", "        all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "\n", "    return all_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## draft\n", "def process_json_responses(df):\n", "    # Define all expected columns including those from extract_vet_data plus 'is_json_valid'\n", "    columns = [\n", "        'FileName','DocumentMetadata',\n", "        'AnimalName', 'VetName','PracticeName', 'PracticeAddress', 'PracticeABN','PracticePhoneNumber', 'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "        'ClaimTotalExVAT','ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "        'InvoiceIndex', 'InvoiceNo','InvoiceDate','IsMultipetPresent', \n", "        'InvoiceTotalExVAT', 'InvoiceVAT','InvoiceTotalIncVAT' ,\n", "        'TreatmentIndex','TreatmentDate','ItemCode','ItemType','Sequence','Description','Quantity', \n", "        'TreatmentAmountExVAT','TreatmentVAT','TreatmentAmountIncVAT','TreatmentDiscountExVAT','is_Jsonvalid'\n", "    ]\n", "\n", "    # Initialize an empty DataFrame to store the results with the defined columns\n", "    all_data = pd.DataFrame(columns=columns)\n", "\n", "    # Iterate over each JSON string and corresponding FileName in the DataFrame\n", "    for json_string, file_name in zip(df['JsonResponse'], df['FileName']):\n", "        if pd.isna(json_string):\n", "            null_data = {col: None for col in columns}\n", "            null_data['FileName'] = file_name\n", "            null_data['is_Jsonvalid'] = False\n", "            all_data = all_data.append(null_data, ignore_index=True)\n", "            continue\n", "        else: \n", "            if not validate_json(json_string):\n", "                json_string = validate_and_correct_json(json_string)\n", "\n", "                # If JsonResponse is null or invalid, append a row with null values except for FileName\n", "                if not validate_json(json_string):\n", "                    null_data = {col: None for col in columns}\n", "                    null_data['FileName'] = file_name\n", "                    null_data['is_Jsonvalid'] = False\n", "                    all_data = all_data.append(null_data, ignore_index=True)\n", "                    continue\n", "\n", "        # Since JSON is valid, process it\n", "        corrected_json = validate_and_correct_json(json_string)\n", "        extracted_data = extract_vet_data(corrected_json, file_name)\n", "        extracted_data['is_Jsonvalid'] = True\n", "\n", "        # Concatenate the results\n", "        all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "\n", "    return all_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## final\n", "\n", "def process_json_responses(df):\n", "    # Define all expected columns including those from extract_vet_data plus 'is_json_valid'\n", "    columns = [\n", "        'FileName','DocumentMetadata',\n", "        'AnimalName', 'VetName','PracticeName', 'PracticeAddress', 'PracticeABN','PracticePhoneNumber', 'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "        'ClaimTotalExVAT','ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "        'InvoiceIndex', 'InvoiceNo','InvoiceDate','IsMultipetPresent', \n", "        'InvoiceTotalExVAT', 'InvoiceVAT','InvoiceTotalIncVAT' ,\n", "        'TreatmentIndex','TreatmentDate','ItemCode','ItemType','Sequence','Description','Quantity', \n", "        'TreatmentAmountExVAT','TreatmentVAT','TreatmentAmountIncVAT','TreatmentDiscountExVAT','is_Jsonvalid','is_JsonModified','is_KeySame'\n", "    ]\n", "\n", "    # Initialize an empty DataFrame to store the results with the defined columns\n", "    all_data = pd.DataFrame(columns=columns)\n", "\n", "    # Iterate over each JSON string and corresponding FileName in the DataFrame\n", "    for json_string, file_name in zip(df['JsonResponse'], df['FileName']):\n", "        if pd.isna(json_string):\n", "            null_data = {col: None for col in columns}\n", "            null_data['FileName'] = file_name\n", "            null_data['is_Jsonvalid'] = None\n", "            null_data['is_Keyvalid'] = None\n", "            null_data['is_JsonModified'] = None\n", "            all_data = all_data.append(null_data, ignore_index=True)\n", "            continue\n", "        else: \n", "            if not validate_json(json_string):\n", "                corrected_json = validate_and_correct_json(json_string)\n", "\n", "\n", "                # If JsonResponse is null or invalid, append a row with null values except for FileName\n", "                if not validate_json(corrected_json):\n", "                    null_data = {col: None for col in columns}\n", "                    null_data['FileName'] = file_name\n", "                    null_data['is_Jsonvalid'] = False\n", "                    null_data['is_Keyvalid'] = None\n", "\n", "                    if corrected_json == json_string:\n", "                        null_data['is_JsonModified'] = False\n", "                    else:\n", "                        null_data['is_JsonModified'] = True\n", "\n", "\n", "                    all_data = all_data.append(null_data, ignore_index=True)\n", "                    continue\n", "\n", "        # Since JSON is valid, process it\n", "               \n", "        corrected_json = validate_and_correct_json(json_string)\n", "        check_key = check_keys(corrected_json,expected_schema) \n", "        extracted_data = extract_vet_data(corrected_json, file_name)\n", "        extracted_data['is_Jsonvalid'] = True\n", "        extracted_data['is_Keyvalid'] = check_key\n", "\n", "        if corrected_json == json_string:\n", "            extracted_data['is_JsonModified'] = False\n", "        else:\n", "            extracted_data['is_JsonModified'] = True\n", "\n", "        # Concatenate the results\n", "        all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "\n", "    return all_data\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Call the function with your DataFrame\n", "result_df = process_json_responses(inv)\n", "result_df_merge= pd.merge(result_df, mar_invoice[['FileName', 'ClaimNo', 'CspReferenceNo']], on='FileName', how='left')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Treatment Level"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Aggregate to treatment level - UPM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def group_and_concatenate_unique(df, group_col, concat_col):\n", "    # Group by the specified column and concatenate unique values of the other column as a list\n", "    result_df = df.groupby(group_col)[concat_col].agg(lambda x: list(set(x))).reset_index()\n", "    return result_df\n", "\n", "def preprocess_upm_raw_treatment(df):\n", "    df['DateTreatment'] = df['DateTreatment'].apply(lambda x: standardize_and_extract_date(x))\n", "    upm_invoice_no = group_and_concatenate_unique(df, 'ClaimNo', 'InvoiceNo')\n", "    upm_tmt= group_and_concatenate_unique(df, ['ClaimNo','InvoiceNo'], ['TreatmentDrugRaw','AmountInclVat','DateTreatment'])\n", "\n", "    return upm_invoice_no, upm_tmt\n", "\n", "upm_invoice_no, upm_tmt = preprocess_upm_raw_treatment(raw_treatment)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Compare Treatment Info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_and_indicate(df1, df2):\n", "    # Initialize new columns for the indicators\n", "    df1['TreatmentIndicator'] = np.nan\n", "    df1['AmountIndicator'] = np.nan\n", "    df1['TreatmentDateIndicator'] = np.nan\n", "\n", "    # Iterate over the rows of df1 (result_df_merge)\n", "    for idx, row in df1.iterrows():\n", "        # Find the corresponding rows in df2 (upm_tmt)\n", "        corresponding_rows = df2[(df2['ClaimNo'] == row['ClaimNo']) & (df2['InvoiceNo'] == row['InvoiceNumber']) ]\n", "\n", "        if corresponding_rows.empty:\n", "            continue  # Skip to next row if no matching rows found\n", "\n", "        # Check if any corresponding row meets the criteria for treatment and amount\n", "        for _, match_row in corresponding_rows.iterrows():\n", "            # Check for Treatment match\n", "            if row['Description'] in match_row['TreatmentDrugRaw']:\n", "                df1.at[idx, 'TreatmentIndicator'] = True\n", "\n", "            else:\n", "               df1.at[idx, 'TreatmentIndicator'] = False \n", "\n", "            # Convert TreatmentTotalIncVAT to the correct type if necessary\n", "            treatment_total = None \n", "            if row['TreatmentTotalIncVAT']:\n", "                try:\n", "                    treatment_total = float(row['TreatmentTotalIncVAT'])\n", "                except ValueError:\n", "                    pass  # Handle or log the error as needed\n", "\n", "            # Check for Amount match\n", "            if treatment_total is not None:\n", "                if any(amount == treatment_total for amount in match_row['AmountInclVat']):\n", "                    df1.at[idx, 'AmountIndicator'] = True\n", "\n", "                else:\n", "                    df1.at[idx, 'AmountIndicator'] = False\n", "\n", "\n", "\n", "             # Check for Treatment Date match\n", "            treatment_date = row['TreatmentDate']\n", "            if treatment_date:\n", "                if any(date == treatment_date for date in match_row['DateTreatment']):\n", "                    df1.at[idx, 'TreatmentDateIndicator'] = True\n", "\n", "                else:\n", "                    df1.at[idx, 'TreatmentDateIndicator'] = False\n", "            \n", "    return df1\n", "\n", "# Usage\n", "result_df_merge_updated = compare_and_indicate(result_df_merge, upm_tmt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_multiple_columns_on_key(df1, df2, key, cols_df1, cols_df2):\n", "    # Validate input\n", "    if not all(k in df1.columns and k in df2.columns for k in key):\n", "        raise ValueError(f\"One or more key columns '{key}' not found in the dataframes\")\n", "    if len(cols_df1) != len(cols_df2):\n", "        raise ValueError(\"Lists cols_df1 and cols_df2 must have the same length\")\n", "\n", "    # Validate column existence\n", "    for col in cols_df1:\n", "        if col not in df1.columns:\n", "            raise ValueError(f\"Column '{col}' not found in df1\")\n", "    for col in cols_df2:\n", "        if col not in df2.columns:\n", "            raise ValueError(f\"Column '{col}' not found in df2\")\n", "\n", "    # Merge df1 and df2 on the key column(s)\n", "    merged_df = pd.merge(df1, df2[key + cols_df2], on=key, how='left')\n", "\n", "    # Iterate over the column pairs and perform comparisons\n", "    for col_df1, col_df2 in zip(cols_df1, cols_df2):\n", "        # Comparison function\n", "        def compare(row):\n", "            df1_val = row[col_df1]\n", "            df2_val = row[col_df2]\n", "            \n", "            # Handle null or None values in df1\n", "            if pd.isnull(df1_val):\n", "                return None\n", "            return df1_val in df2_val if isinstance(df2_val, list) else df1_val == df2_val\n", "\n", "        # Apply comparison for each row and store the result\n", "        df1[f'{col_df1}_check'] = merged_df.apply(compare, axis=1)\n", "\n", "    return df1\n", "\n", "# Example usage\n", "# result_df_merge and upm_tmt are your dataframes\n", "keys = ['ClaimNo', 'InvoiceNo']\n", "df1_check = ['TreatmentDate', 'Description', 'TreatmentAmountIncVAT', 'TreatmentAmountExVAT', 'TreatmentVAT', 'TreatmentDiscountExVAT']\n", "df2_check = ['DateTreatment', 'TreatmentDrugRaw', 'AmountInclVat', 'AmountExclVat', 'AmountVat', 'AmountDiscount']\n", "compare_df = compare_multiple_columns_on_key(result_df_merge, upm_tmt, keys, df1_check, df2_check)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Invoice Level (Ammend amount)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def compare_multiple_columns_on_key(df1, df2, key, cols_df1, cols_df2, amount_cols=None):\n", "    if amount_cols is None:\n", "        amount_cols = []\n", "\n", "    # Validate input\n", "    if not all(k in df1.columns and k in df2.columns for k in key):\n", "        raise ValueError(f\"One or more key columns '{key}' not found in the dataframes\")\n", "    if len(cols_df1) != len(cols_df2):\n", "        raise ValueError(\"Lists cols_df1 and cols_df2 must have the same length\")\n", "\n", "    # Validate column existence\n", "    for col in cols_df1:\n", "        if col not in df1.columns:\n", "            raise ValueError(f\"Column '{col}' not found in df1\")\n", "    for col in cols_df2:\n", "        if col not in df2.columns:\n", "            raise ValueError(f\"Column '{col}' not found in df2\")\n", "\n", "    # Merge df1 and df2 on the key column(s)\n", "    merged_df = pd.merge(df1, df2[key + cols_df2], on=key, how='left')\n", "\n", "    # Function to compare amounts with tolerance\n", "    def compare_amounts(val1, val2):\n", "        try:\n", "            # Check if val2 is a list and compare each element in val2 to val1\n", "            if isinstance(val2, list):\n", "                return any(abs(float(val1) - float(item)) <= 0.05 for item in val2)\n", "            # Otherwise, just compare val1 and val2 directly\n", "            else:\n", "                return abs(float(val1) - float(val2)) < 0.05\n", "\n", "        except (ValueErro<PERSON>, TypeError):\n", "            return False\n", "\n", "    # Iterate over the column pairs and perform comparisons\n", "    for col_df1, col_df2 in zip(cols_df1, cols_df2):\n", "        if col_df1 in amount_cols:\n", "            # Special comparison for amount columns\n", "            compare = lambda row: compare_amounts(row[col_df1], row[col_df2])\n", "            # print( lambda row:  row[col_df1], row[col_df2])\n", "        else:\n", "            # Standard comparison for other columns\n", "            compare = lambda row: row[col_df1] in row[col_df2] if isinstance(row[col_df2], list) else row[col_df1] == row[col_df2]\n", "\n", "        # Apply comparison for each row and store the result\n", "        df1[f'{col_df1}_check'] = merged_df.apply(compare, axis=1)\n", "\n", "    return df1\n", "\n", "# Example usage\n", "keys = ['ClaimNo']\n", "df1_check = ['InvoiceNo', 'InvoiceTotalIncVAT']\n", "df2_check = ['InvoiceNo_UPM','Amount Inc Vat']\n", "amount_cols = ['InvoiceTotalIncVAT'] # Specify which columns are amount columns\n", "compare_df = compare_multiple_columns_on_key(result_df_merge, inv_amt_result_concat, keys, df1_check, df2_check, amount_cols)\n"]}], "metadata": {"kernelspec": {"display_name": "claryt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}