{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import re"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["invoice = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_gpt_ocr_json.xlsx')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Process raw json"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting ',' delimiter: line 92 column 29 (char 4398)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting ',' delimiter: line 246 column 18 (char 13205)\n", "Invalid JSON string: Expecting ',' delimiter: line 246 column 18 (char 13205)\n", "Invalid JSON string: Expecting value: line 58 column 37 (char 2741)\n", "Invalid JSON string: Expecting ',' delimiter: line 242 column 38 (char 13463)\n", "Invalid JSON string: Expecting ',' delimiter: line 242 column 38 (char 13463)\n", "Invalid JSON string: Expecting ',' delimiter: line 92 column 15 (char 3033)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting ',' delimiter: line 249 column 38 (char 13993)\n", "Invalid JSON string: Expecting ',' delimiter: line 249 column 38 (char 13993)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting value: line 70 column 37 (char 3479)\n", "Invalid JSON string: Expecting value: line 71 column 33 (char 3512)\n", "Invalid JSON string: Expecting ',' delimiter: line 92 column 15 (char 3033)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:254: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:240: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:240: FutureWarning: In a future version, object-dtype columns with all-bool values will not be included in reductions with bool_only=True. Explicitly cast to bool dtype instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting ',' delimiter: line 228 column 38 (char 12696)\n", "Invalid JSON string: Expecting ',' delimiter: line 228 column 38 (char 12696)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting ',' delimiter: line 148 column 15 (char 5046)\n", "Invalid JSON string: Expecting ',' delimiter: line 230 column 18 (char 12411)\n", "Invalid JSON string: Expecting ',' delimiter: line 230 column 18 (char 12411)\n", "Invalid JSON string: Expecting ',' delimiter: line 247 column 30 (char 13630)\n", "Invalid JSON string: Expecting ',' delimiter: line 247 column 30 (char 13630)\n", "Invalid JSON string: Expecting ',' delimiter: line 20 column 14 (char 724)\n", "Invalid JSON string: Expecting ',' delimiter: line 20 column 14 (char 724)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting value: line 58 column 37 (char 2743)\n", "Invalid JSON string: Expecting ',' delimiter: line 126 column 29 (char 6266)\n", "Invalid JSON string: Expecting ',' delimiter: line 126 column 15 (char 4266)\n", "Invalid JSON string: Expecting ',' delimiter: line 92 column 15 (char 3026)\n", "Invalid JSON string: Expecting ',' delimiter: line 82 column 37 (char 4018)\n", "Invalid JSON string: Expecting ',' delimiter: line 92 column 29 (char 4398)\n", "Invalid JSON string: Expecting ',' delimiter: line 237 column 38 (char 13589)\n", "Invalid JSON string: Expecting ',' delimiter: line 237 column 38 (char 13589)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting value: line 220 column 37 (char 11805)\n", "Invalid JSON string: Expecting value: line 221 column 33 (char 11838)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\2\\ipykernel_10928\\552521143.py:262: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  all_data = all_data.append(null_data, ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Invalid JSON string: Expecting ',' delimiter: line 126 column 15 (char 4266)\n", "Invalid JSON string: Expecting ',' delimiter: line 126 column 15 (char 4266)\n", "Invalid JSON string: Expecting ',' delimiter: line 70 column 15 (char 2249)\n", "Invalid JSON string: Expecting ',' delimiter: line 92 column 15 (char 3026)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>DocumentMetadata</th>\n", "      <th>AnimalName</th>\n", "      <th>VetName</th>\n", "      <th>PracticeName</th>\n", "      <th><PERSON><PERSON>ddress</th>\n", "      <th>PracticeABN</th>\n", "      <th>PracticePhoneNumber</th>\n", "      <th>PracticeFaxNumber</th>\n", "      <th>PracticeEmailAddress</th>\n", "      <th>...</th>\n", "      <th>Description</th>\n", "      <th>Quantity</th>\n", "      <th>TreatmentAmountExVAT</th>\n", "      <th>TreatmentVAT</th>\n", "      <th>TreatmentAmountIncVAT</th>\n", "      <th>TreatmentDiscountExVAT</th>\n", "      <th>is_InitialJsonValid</th>\n", "      <th>is_FinalJsonValid</th>\n", "      <th>is_JsonModified</th>\n", "      <th>is_KeyValid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>12915f61-35ca-4af6-976f-80250f39d018</td>\n", "      <td>{'header': 'EVERVET SOUTH YARRA', 'body': ['In...</td>\n", "      <td><PERSON><PERSON><PERSON> (Pipo)</td>\n", "      <td><PERSON></td>\n", "      <td>EVERVET</td>\n", "      <td>66 Toorak Road, South Yarra, Victoria 3141</td>\n", "      <td>0490796561</td>\n", "      <td>Ph:9510 1335</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Consultation / Examination</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>$104.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>12915f61-35ca-4af6-976f-80250f39d018</td>\n", "      <td>{'header': 'EVERVET SOUTH YARRA', 'body': ['In...</td>\n", "      <td><PERSON><PERSON><PERSON> (Pipo)</td>\n", "      <td><PERSON></td>\n", "      <td>EVERVET</td>\n", "      <td>66 Toorak Road, South Yarra, Victoria 3141</td>\n", "      <td>0490796561</td>\n", "      <td>Ph:9510 1335</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Metacam Antiinflammatory Oral Suspension 10ml</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>$58.50</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>12a700b3-8405-40fa-99a6-1ea2f0f81134</td>\n", "      <td>{'header': 'Kardinia Veterinary Clinic &amp; Anima...</td>\n", "      <td>Bonnie</td>\n", "      <td>Dr <PERSON> B<PERSON></td>\n", "      <td>Kardinia Veterinary Clinic &amp; Animal Hospital</td>\n", "      <td>355 Moorabool Street, Geelong Victoria 3220</td>\n", "      <td>None</td>\n", "      <td>03 5221 5122</td>\n", "      <td>03 5222 3930</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Canine Annual Vaccination And Health Check</td>\n", "      <td>1.00</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>153.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>12ace777-0681-44c8-a267-85f27217e8fe</td>\n", "      <td>{'header': 'PAYMENT RECEIPT', 'body': ['Receip...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>None</td>\n", "      <td>My Family Vet</td>\n", "      <td>141 Canvey Road Upper Kedron, Queensland 4055</td>\n", "      <td>None</td>\n", "      <td>07 3518 4418</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Consultation - Standard</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>$95.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>12ace777-0681-44c8-a267-85f27217e8fe</td>\n", "      <td>{'header': 'PAYMENT RECEIPT', 'body': ['Receip...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>None</td>\n", "      <td>My Family Vet</td>\n", "      <td>141 Canvey Road Upper Kedron, Queensland 4055</td>\n", "      <td>None</td>\n", "      <td>07 3518 4418</td>\n", "      <td>None</td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Cephalexin 600mg (Per tablet)</td>\n", "      <td>14</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>$58.56</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2616</th>\n", "      <td>11c06a3c-1980-46b8-8795-7a4c1a189b3f</td>\n", "      <td>{'header': 'ANIMAL EMERGENCY SERVICE', 'body':...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr <PERSON></td>\n", "      <td>ANIMAL EMERGENCY SERVICE</td>\n", "      <td>Cnr Lexington &amp; Logan Roads, Underwood QLD 4119</td>\n", "      <td>**************</td>\n", "      <td>***********</td>\n", "      <td>***********</td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Otoflush 125mL</td>\n", "      <td>1.00</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>34.65</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2617</th>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3</td>\n", "      <td>{'header': 'WAVES (WA Vet Emergency &amp; Specialt...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>WAVES (WA Vet Emergency &amp; Specialty)</td>\n", "      <td>1/640 Beeliar Drive, Success, WA 6164</td>\n", "      <td>**************</td>\n", "      <td>9412 5700</td>\n", "      <td></td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Catheterisation IV</td>\n", "      <td>1.00</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>87.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2618</th>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3</td>\n", "      <td>{'header': 'WAVES (WA Vet Emergency &amp; Specialt...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>WAVES (WA Vet Emergency &amp; Specialty)</td>\n", "      <td>1/640 Beeliar Drive, Success, WA 6164</td>\n", "      <td>**************</td>\n", "      <td>9412 5700</td>\n", "      <td></td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>DM Hymenoptera injection + monitoring</td>\n", "      <td>1.00</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>82.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2619</th>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3</td>\n", "      <td>{'header': 'WAVES (WA Vet Emergency &amp; Specialt...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>WAVES (WA Vet Emergency &amp; Specialty)</td>\n", "      <td>1/640 Beeliar Drive, Success, WA 6164</td>\n", "      <td>**************</td>\n", "      <td>9412 5700</td>\n", "      <td></td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Hospitalisation - Gen Ward B (12hrs)</td>\n", "      <td>1.00</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>75.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2620</th>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3</td>\n", "      <td>{'header': 'WAVES (WA Vet Emergency &amp; Specialt...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>WAVES (WA Vet Emergency &amp; Specialty)</td>\n", "      <td>1/640 Beeliar Drive, Success, WA 6164</td>\n", "      <td>**************</td>\n", "      <td>9412 5700</td>\n", "      <td></td>\n", "      <td><EMAIL></td>\n", "      <td>...</td>\n", "      <td>Gabapentin /CPD C50mg</td>\n", "      <td>1.00</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18.00</td>\n", "      <td>None</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2621 rows × 36 columns</p>\n", "</div>"], "text/plain": ["                                  FileName  \\\n", "0     12915f61-35ca-4af6-976f-80250f39d018   \n", "1     12915f61-35ca-4af6-976f-80250f39d018   \n", "2     12a700b3-8405-40fa-99a6-1ea2f0f81134   \n", "3     12ace777-0681-44c8-a267-85f27217e8fe   \n", "4     12ace777-0681-44c8-a267-85f27217e8fe   \n", "...                                    ...   \n", "2616  11c06a3c-1980-46b8-8795-7a4c1a189b3f   \n", "2617  12268c80-e3fe-4485-a607-5091d9bb82b3   \n", "2618  12268c80-e3fe-4485-a607-5091d9bb82b3   \n", "2619  12268c80-e3fe-4485-a607-5091d9bb82b3   \n", "2620  12268c80-e3fe-4485-a607-5091d9bb82b3   \n", "\n", "                                       DocumentMetadata   AnimalName  \\\n", "0     {'header': 'EVERVET SOUTH YARRA', 'body': ['In...  <PERSON><PERSON>o (Pipo)   \n", "1     {'header': 'EVERVET SOUTH YARRA', 'body': ['In...  <PERSON><PERSON><PERSON> (Pipo)   \n", "2     {'header': 'Kardinia Veterinary Clinic & Anima...       Bonnie   \n", "3     {'header': 'PAYMENT RECEIPT', 'body': ['Receip...      Katness   \n", "4     {'header': 'PAYMENT RECEIPT', 'body': ['Receip...      Katness   \n", "...                                                 ...          ...   \n", "2616  {'header': 'ANIMAL EMERGENCY SERVICE', 'body':...         Yogi   \n", "2617  {'header': 'WAVES (WA Vet Emergency & Specialt...       <PERSON><PERSON>   \n", "2618  {'header': 'WAVES (WA Vet Emergency & Specialt...       <PERSON><PERSON>   \n", "2619  {'header': 'WAVES (WA Vet Emergency & Specialt...       <PERSON><PERSON>   \n", "2620  {'header': 'WAVES (WA Vet Emergency & Specialt...       <PERSON><PERSON>   \n", "\n", "                     VetName                                  PracticeName  \\\n", "0                 May Warner                                       EVERVET   \n", "1                 May Warner                                       EVERVET   \n", "2     Dr <PERSON> BVSc  Kardinia Veterinary Clinic & Animal Hospital   \n", "3                       None                                 My Family Vet   \n", "4                       None                                 My Family Vet   \n", "...                      ...                                           ...   \n", "2616       Dr <PERSON>                      ANIMAL EMERGENCY SERVICE   \n", "2617        Dr. <PERSON><PERSON>          WAVES (WA Vet Emergency & Specialty)   \n", "2618        Dr. <PERSON><PERSON>          WAVES (WA Vet Emergency & Specialty)   \n", "2619        <PERSON>. <PERSON><PERSON>          WAVES (WA Vet Emergency & Specialty)   \n", "2620        Dr. <PERSON><PERSON>          WAVES (WA Vet Emergency & Specialty)   \n", "\n", "                                      PracticeAddress     PracticeABN  \\\n", "0          66 Toorak Road, South Yarra, Victoria 3141      0490796561   \n", "1          66 Toorak Road, South Yarra, Victoria 3141      0490796561   \n", "2         355 Moorabool Street, Geelong Victoria 3220            None   \n", "3       141 Canvey Road Upper Kedron, Queensland 4055            None   \n", "4       141 Canvey Road Upper Kedron, Queensland 4055            None   \n", "...                                               ...             ...   \n", "2616  Cnr Lexington & Logan Roads, Underwood QLD 4119  **************   \n", "2617            1/640 Beeliar Drive, Success, WA 6164  **************   \n", "2618            1/640 Beeliar Drive, Success, WA 6164  **************   \n", "2619            1/640 Beeliar Drive, Success, WA 6164  **************   \n", "2620            1/640 Beeliar Drive, Success, WA 6164  **************   \n", "\n", "     PracticePhoneNumber PracticeFaxNumber    PracticeEmailAddress  ...  \\\n", "0           Ph:9510 1335              None  <EMAIL>  ...   \n", "1           Ph:9510 1335              None  <EMAIL>  ...   \n", "2           03 5221 5122      03 5222 3930                    None  ...   \n", "3           07 3518 4418              None    <EMAIL>  ...   \n", "4           07 3518 4418              None    <EMAIL>  ...   \n", "...                  ...               ...                     ...  ...   \n", "2616         ***********       ***********     <EMAIL>  ...   \n", "2617           9412 5700                    <EMAIL>  ...   \n", "2618           9412 5700                    <EMAIL>  ...   \n", "2619           9412 5700                    <EMAIL>  ...   \n", "2620           9412 5700                    <EMAIL>  ...   \n", "\n", "                                        Description Quantity  \\\n", "0                        Consultation / Examination        1   \n", "1     Metacam Antiinflammatory Oral Suspension 10ml        1   \n", "2        Canine Annual Vaccination And Health Check     1.00   \n", "3                           Consultation - Standard        1   \n", "4                     Cephalexin 600mg (Per tablet)       14   \n", "...                                             ...      ...   \n", "2616                                 Otoflush 125mL     1.00   \n", "2617                             Catheterisation IV     1.00   \n", "2618          DM Hymenoptera injection + monitoring     1.00   \n", "2619           Hospitalisation - Gen Ward B (12hrs)     1.00   \n", "2620                          Gabapentin /CPD C50mg     1.00   \n", "\n", "     TreatmentAmountExVAT TreatmentVAT TreatmentAmountIncVAT  \\\n", "0                    None         None               $104.00   \n", "1                    None         None                $58.50   \n", "2                    None         None                153.00   \n", "3                    None         None                $95.00   \n", "4                    None         None                $58.56   \n", "...                   ...          ...                   ...   \n", "2616                 None         None                 34.65   \n", "2617                 None         None                 87.00   \n", "2618                 None         None                 82.00   \n", "2619                 None         None                 75.00   \n", "2620                 None         None                 18.00   \n", "\n", "     TreatmentDiscountExVAT is_InitialJsonValid is_FinalJsonValid  \\\n", "0                      None                True              True   \n", "1                      None                True              True   \n", "2                      None                True              True   \n", "3                      None                True              True   \n", "4                      None                True              True   \n", "...                     ...                 ...               ...   \n", "2616                   None                True              True   \n", "2617                   None                True              True   \n", "2618                   None                True              True   \n", "2619                   None                True              True   \n", "2620                   None                True              True   \n", "\n", "     is_JsonModified is_KeyValid  \n", "0              False        True  \n", "1              False        True  \n", "2              False        True  \n", "3              False        True  \n", "4              False        True  \n", "...              ...         ...  \n", "2616           False        True  \n", "2617           False        True  \n", "2618           False        True  \n", "2619           False        True  \n", "2620           False        True  \n", "\n", "[2621 rows x 36 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define the expected schema\n", "expected_schema = {\n", "    \"vetxmlclaim\": {\n", "        \"infofrompolicyholder\": {\n", "            \"animaldetails\": {\n", "                \"name\": None\n", "            }\n", "        },\n", "        \"infofromvet\": {\n", "            \"vet\": {\n", "                \"vetname\": None,\n", "                \"practicename\": None,\n", "                \"practiceaddress\": None,\n", "                \"practiceabn\": None,\n", "                \"practicephonenumber\": None,\n", "                \"practicefaxnumber\": None,\n", "                \"practiceemailaddress\": None,\n", "                \"practicewebsite\": None\n", "            },\n", "            \"conditions\": [\n", "                {\n", "                    \"financial\": {\n", "                        \"totalexvat\": None,\n", "                        \"vat\": None,\n", "                        \"totalincvat\": None,\n", "                        \"invoices\": [\n", "                            {\n", "                                \"invoicenumber\": None,\n", "                                \"invoicedate\": None,\n", "                                \"totalexvat\": None,\n", "                                \"vat\": None,\n", "                                \"totalincvat\": None,\n", "                                \"ismultipetpresent\": None,\n", "                                \"items\": [\n", "                                    {\n", "                                        \"treatmentdate\": None,\n", "                                        \"itemcode\": None,\n", "                                        \"itemtype\": None,\n", "                                        \"sequence\": None,\n", "                                        \"description\": None,\n", "                                        \"amountexvat\": None,\n", "                                        \"discountexvat\": None,\n", "                                        \"vat\": None,\n", "                                        \"quantity\": None,\n", "                                        \"totalincvat\": None\n", "                                    }\n", "                                ]\n", "                            }\n", "                        ]\n", "                    }\n", "                }\n", "            ],\n", "            \"documentmetadata\": {}\n", "        }\n", "    }\n", "}\n", "\n", "\n", "\n", "def validate_json(json_string):\n", "    try:\n", "        input_json = json.loads(json_string)\n", "    except json.JSONDecodeError as e:\n", "        print(f\"Invalid JSON string: {e}\")\n", "        return False\n", "    return True\n", "    \n", "\n", "def check_keys(expected, actual, path=\"\"):\n", "    if isinstance(expected, dict):\n", "        if not isinstance(actual, dict):\n", "            print(f\"Type mismatch at '{path}': Expected dict, got {type(actual).__name__}\")\n", "            return False\n", "        actual_lower = {k.lower(): v for k, v in actual.items()}\n", "        for key in expected:\n", "            if key.lower() not in actual_lower:\n", "                print(f\"Missing key '{key}' at path '{path}'\")\n", "                return False\n", "            if not check_keys(expected[key], actual_lower[key.lower()], f\"{path}.{key if path else key}\"):\n", "                return False\n", "    elif isinstance(expected, list):\n", "        if not isinstance(actual, list):\n", "            print(f\"Type mismatch at '{path}': Expected list, got {type(actual).__name__}\")\n", "            return False\n", "        for index, item in enumerate(actual):\n", "            if not check_keys(expected[0], item, f\"{path}[{index}]\"):\n", "                return False\n", "    return True\n", "\n", "\n", "def validate_and_correct_json(json_string):\n", "    # Removing comments from JSON string using regular expression\n", "    corrected_json = re.sub(r'(?<!:)//.*', '', json_string)\n", "    return corrected_json\n", "\n", "def to_lower_keys(x):\n", "    if isinstance(x, dict):\n", "        return {k.lower(): to_lower_keys(v) for k, v in x.items()}\n", "    elif isinstance(x, list):\n", "        return [to_lower_keys(i) for i in x]\n", "    else:\n", "        return x\n", "\n", "def extract_vet_data(json_string, file_name):\n", "    # Parse the JSON string\n", "    data = to_lower_keys(json.loads(json_string))\n", "    \n", "    # Initialize a list to store the extracted data\n", "    extracted_data = []\n", "\n", "    # Extract JSON header for Policy Holder information\n", "    animal_name = data['vetxmlclaim']['infofrompolicyholder']['animaldetails']['name']\n", "\n", "    # Extract JSON header for Vet information\n", "    vet_info = data['vetxmlclaim']['infofromvet']['vet']\n", "\n", "    # Extract Document Metadata\n", "    document_metadata = data['vetxmlclaim']['infofromvet']['documentmetadata']\n", "\n", "\n", "    # Clean PracticeABN, PracticePhoneNumber, and PracticeFaxNumber\n", "    practice_abn = vet_info.get('practiceabn', '')\n", "    practice_phone = vet_info.get('practicephonenumber', '')\n", "    practice_fax = vet_info.get('practicefaxnumber', '')\n", "    \n", "\n", "    # Loop through each condition in the JSON\n", "    for condition in data['vetxmlclaim']['infofromvet']['conditions']:\n", "\n", "        # Extract JSON header for Claim Financial information\n", "        financial_info = condition['financial']\n", "        claim_total_incvat_clean = financial_info.get('totalincvat')\n", "        claim_vat_clean = financial_info.get('vat')\n", "        claim_total_exvat_clean = financial_info.get('totalexvat')\n", "        \n", "    \n", "        # Loop through each invoice\n", "        for i, invoice in enumerate(financial_info.get('invoices', [])):\n", "            invoice_number_clean = invoice.get('invoicenumber')\n", "            invoice_date_clean = invoice.get('invoicedate')\n", "\n", "            invoice_total_incvat_clean = invoice.get('totalincvat')\n", "            invoice_vat_clean = invoice.get('vat')\n", "            invoice_total_exvat_clean = invoice.get('totalexvat')\n", "            \n", "            \n", "\n", "            invoice_info = {\n", "\n", "                # File info\n", "                'FileName': file_name,\n", "                'DocumentMetadata': document_metadata,\n", "\n", "                # Policy Holder info\n", "                'AnimalName': animal_name,\n", "\n", "                # Vet info\n", "                'VetName': vet_info.get('vetname', ''),\n", "                'PracticeName': vet_info.get('practicename'),\n", "                'PracticeAddress': vet_info.get('practiceaddress'),\n", "                'PracticeABN': practice_abn,\n", "                'PracticePhoneNumber': practice_phone,\n", "                'PracticeFaxNumber': practice_fax,\n", "                'PracticeEmailAddress': vet_info.get('practiceemailaddress'),\n", "                'PracticeWebsite': vet_info.get('practicewebsite'),\n", "\n", "                # Claim info\n", "                'ClaimTotalExVAT': claim_total_exvat_clean,\n", "                'ClaimInvoiceVAT':claim_vat_clean,\n", "                'ClaimInvoiceTotalIncVAT': claim_total_incvat_clean,\n", "\n", "\n", "                # Invoice Info\n", "                'InvoiceIndex': i+1,\n", "                'InvoiceNo': invoice_number_clean,\n", "                'InvoiceDate': invoice_date_clean,\n", "                'IsMultipetPresent': invoice.get('ismultipetpresent'),\n", "\n", "                'InvoiceTotalExVAT': invoice_total_exvat_clean,\n", "                'InvoiceVAT':invoice_vat_clean,\n", "                'InvoiceTotalIncVAT': invoice_total_incvat_clean,\n", "\n", "            }\n", "\n", "            # Loop through each treatment item in the invoice\n", "            for j, item in enumerate(invoice.get('items', [])):\n", "                item_info = invoice_info.copy()\n", "\n", "                treatment_date_clean = item.get('treatmentdate')\n", "            \n", "                treatment_total_incvat_clean = item.get('totalincvat')\n", "                treatment_vat_clean = item.get('vat')\n", "                treatment_total_exvat_clean = item.get('amountexvat')\n", "                treatment_discount_exvat_clean = item.get('discountexvat')\n", "                \n", "                \n", "                item_info.update({\n", "                    'TreatmentIndex': j+1,\n", "                    'TreatmentDate': treatment_date_clean ,\n", "                    'ItemCode': item.get('itemcode', ''),\n", "                    'ItemType': item.get('itemtype', ''),\n", "                    'Sequence': item.get('sequence', ''),\n", "                    'Description': item.get('description'),\n", "                    'Quantity': item.get('quantity'),\n", "\n", "                    'TreatmentAmountExVAT': treatment_total_exvat_clean,\n", "                    'TreatmentVAT':treatment_vat_clean,\n", "                    'TreatmentAmountIncVAT': treatment_total_incvat_clean,\n", "                    'TreatmentDiscountExVAT': treatment_discount_exvat_clean\n", "                })\n", "                extracted_data.append(item_info)\n", "\n", "    # Create DataFrame\n", "    df = pd.DataFrame(extracted_data)\n", "    return df\n", "\n", "\n", "def process_json_responses(df):\n", "    columns = [\n", "        'FileName','DocumentMetadata',\n", "        'AnimalName', 'VetName','PracticeName', 'PracticeAddress', 'PracticeABN','PracticePhoneNumber', 'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "        'ClaimTotalExVAT','ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "        'InvoiceIndex', 'InvoiceNo','InvoiceDate','IsMultipetPresent', \n", "        'InvoiceTotalExVAT', 'InvoiceVAT','InvoiceTotalIncVAT' ,\n", "        'TreatmentIndex','TreatmentDate','ItemCode','ItemType','Sequence','Description','Quantity', \n", "        'TreatmentAmountExVAT','TreatmentVAT','TreatmentAmountIncVAT','TreatmentDiscountExVAT','is_InitialJsonValid','is_FinalJsonValid','is_JsonModified','is_KeyValid'\n", "    ]\n", "\n", "\n", "    all_data = pd.DataFrame(columns=columns)\n", "\n", "    for json_string, file_name in zip(df['JsonResponse'], df['FileName']):\n", "        if pd.isna(json_string):\n", "            null_data = {col: None for col in columns}\n", "            null_data['FileName'] = file_name\n", "            null_data['is_InitialJsonValid'] = None\n", "            null_data['is_FinalJsonValid'] = None\n", "            null_data['is_KeyValid'] = None\n", "            null_data['is_JsonModified'] = None\n", "            all_data = all_data.append(null_data, ignore_index=True)\n", "            continue\n", "\n", "        initial_validity = validate_json(json_string)\n", "        corrected_json = validate_and_correct_json(json_string) if not initial_validity else json_string\n", "        final_validity = validate_json(corrected_json)\n", "        check_key = check_keys(corrected_json, expected_schema)\n", "\n", "        if final_validity:\n", "            extracted_data = extract_vet_data(corrected_json, file_name)\n", "            extracted_data['is_InitialJsonValid'] = initial_validity\n", "            extracted_data['is_FinalJsonValid'] = final_validity\n", "            extracted_data['is_KeyValid'] = check_key\n", "            extracted_data['is_JsonModified'] = corrected_json != json_string\n", "            all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "        else:\n", "            null_data = {col: None for col in columns}\n", "            null_data['FileName'] = file_name\n", "            null_data['is_InitialJsonValid'] = initial_validity\n", "            null_data['is_FinalJsonValid'] = final_validity\n", "            null_data['is_KeyValid'] = None\n", "            null_data['is_JsonModified'] = corrected_json != json_string\n", "            all_data = all_data.append(null_data, ignore_index=True)\n", "\n", "    return all_data\n", "\n", "result_df = process_json_responses(invoice)\n", "result_df"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [], "source": ["# def process_json_responses(df):\n", "#     # Define all expected columns including those from extract_vet_data plus 'is_json_valid'\n", "#     columns = [\n", "#         'FileName','DocumentMetadata',\n", "#         'AnimalName', 'VetName','PracticeName', 'PracticeAddress', 'PracticeABN','PracticePhoneNumber', 'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "#         'ClaimTotalExVAT','ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "#         'InvoiceIndex', 'InvoiceNo','InvoiceDate','IsMultipetPresent', \n", "#         'InvoiceTotalExVAT', 'InvoiceVAT','InvoiceTotalIncVAT' ,\n", "#         'TreatmentIndex','TreatmentDate','ItemCode','ItemType','Sequence','Description','Quantity', \n", "#         'TreatmentAmountExVAT','TreatmentVAT','TreatmentAmountIncVAT','TreatmentDiscountExVAT','is_InitialJsonvalid','is_FinalJsonvalid','is_JsonModified','is_Keyvalid'\n", "#     ]\n", "\n", "#     # Initialize an empty DataFrame to store the results with the defined columns\n", "#     all_data = pd.DataFrame(columns=columns)\n", "\n", "#     # Iterate over each JSON string and corresponding FileName in the DataFrame\n", "#     for json_string, file_name in zip(df['JsonResponse'], df['FileName']):\n", "#         if pd.isna(json_string):\n", "#             null_data = {col: None for col in columns}\n", "#             null_data['FileName'] = file_name\n", "#             null_data['is_InitialJsonvalid'] = None\n", "#             null_data['is_FinalJsonvalid'] = None\n", "#             null_data['is_Keyvalid'] = None\n", "#             null_data['is_JsonModified'] = None\n", "#             all_data = all_data.append(null_data, ignore_index=True)\n", "#             continue\n", "#         else: \n", "#             if not validate_json(json_string):\n", "#                 corrected_json = validate_and_correct_json(json_string)\n", "\n", "\n", "#                 # If JsonResponse is null or invalid, append a row with null values except for FileName\n", "#                 if not validate_json(corrected_json):\n", "#                     null_data = {col: None for col in columns}\n", "#                     null_data['FileName'] = file_name\n", "#                     null_data['is_InitialJsonvalid'] = False\n", "#                     null_data['is_Keyvalid'] = None\n", "\n", "#                     if corrected_json == json_string:\n", "#                         null_data['is_JsonModified'] = False\n", "#                     else:\n", "#                         null_data['is_JsonModified'] = True\n", "\n", "\n", "#                     all_data = all_data.append(null_data, ignore_index=True)\n", "#                     continue\n", "\n", "#         # Since JSON is valid, process it\n", "               \n", "#         corrected_json = validate_and_correct_json(json_string)\n", "#         check_key = check_keys(corrected_json,expected_schema) \n", "#         extracted_data = extract_vet_data(corrected_json, file_name)\n", "#         extracted_data['is_InitialJsonvalid'] = True\n", "#         extracted_data['is_FinalJsonvalid'] = True\n", "#         extracted_data['is_Keyvalid'] = check_key\n", "\n", "#         if corrected_json == json_string:\n", "#             extracted_data['is_JsonModified'] = False\n", "#         else:\n", "#             extracted_data['is_JsonModified'] = True\n", "\n", "#         # Concatenate the results\n", "#         all_data = pd.concat([all_data, extracted_data], ignore_index=True)\n", "\n", "#     return all_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Change Variable -- Filter invalid json"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [], "source": ["result_df = result_df[result_df['is_InitialJsonValid']==True]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check Columns"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def check_column_difference(df, column1, column2):\n", "    \"\"\"\n", "    Compares the difference between two columns in a DataFrame and returns 1 if the difference\n", "    is between -0.05 and 0.05 (inclusive), otherwise returns 0.\n", "\n", "    Parameters:\n", "    df (pd.DataFrame): The DataFrame to process.\n", "    column1 (str): The name of the first column to compare.\n", "    column2 (str): The name of the second column to compare.\n", "\n", "    Returns:\n", "    pd.Series: A Series with 1 where the condition is met, and 0 otherwise.\n", "    \"\"\"\n", "    # Ensure the columns are numeric, replacing non-numeric values with NaN\n", "    df[column1] = pd.to_numeric(df[column1], errors='coerce')\n", "    df[column2] = pd.to_numeric(df[column2], errors='coerce')\n", "\n", "    # Calculate the absolute difference\n", "    difference = (df[column1] - df[column2]).abs()\n", "\n", "    # Check if the difference is within the specified range and return 1 or 0\n", "    return (difference <= 0.05).astype(int)\n", "\n", "def extract_amount(x):\n", "    if x is None:\n", "        return None\n", "    try:\n", "        # Find all occurrences of numbers, which may include commas and decimal points\n", "        match = re.search(r'-?\\d+[\\d,]*\\.?\\d*', x)\n", "\n", "        if match:\n", "                # Extract the matched number, remove commas, convert to float, and format to two decimal places\n", "                number = float(match.group().replace(',', ''))\n", "                formatted_number = round(number, 2)\n", "                return formatted_number\n", "        else:\n", "            return None\n", "\n", "    except ValueError:\n", "        # If conversion fails, return the original value or handle as needed\n", "        return x\n", "   \n", "    \n", "    \n", "result_df['TreatmentAmountIncVAT_clean'] = result_df['TreatmentAmountIncVAT'].apply(lambda x: extract_amount(x))\n", "# result_df['TreatmentAmountIncVAT_clean'] = result_df['TreatmentAmountIncVAT_clean'].fillna(0)\n", "result_df['TreatmentAmountIncVAT_clean'] = pd.to_numeric(result_df['TreatmentAmountIncVAT_clean'], errors='coerce')\n", "\n", "result_df['InvoiceTotalIncVAT_clean'] = result_df['InvoiceTotalIncVAT'].apply(lambda x: extract_amount(x))\n", "result_df['ClaimInvoiceTotalIncVAT_clean'] = result_df['ClaimInvoiceTotalIncVAT'].apply(lambda x: extract_amount(x))\n", "\n", "# Step 1 & 2: Group by 'InvoiceNo' and sum 'TreatmentAmountIncVAT'\n", "grouped_inv = result_df.groupby('FileName')['InvoiceTotalIncVAT_clean'].sum().reset_index(name='InvoiceSum')\n", "grouped_tmt = result_df.groupby('FileName')['TreatmentAmountIncVAT_clean'].sum().reset_index(name='TreatmentSum')\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\3\\ipykernel_2156\\3964445282.py:18: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  result_df['TreatmentAmountIncVAT_clean'] = result_df['TreatmentAmountIncVAT'].apply(lambda x: clean_amount(x))\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\3\\ipykernel_2156\\3964445282.py:20: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  result_df['TreatmentAmountIncVAT_clean'] = pd.to_numeric(result_df['TreatmentAmountIncVAT_clean'], errors='coerce')\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\3\\ipykernel_2156\\3964445282.py:22: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  result_df['InvoiceTotalIncVAT_clean'] = result_df['InvoiceTotalIncVAT'].apply(lambda x: clean_amount(x))\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\3\\ipykernel_2156\\3964445282.py:23: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  result_df['ClaimInvoiceTotalIncVAT_clean'] = result_df['ClaimInvoiceTotalIncVAT'].apply(lambda x: clean_amount(x))\n"]}], "source": ["# import pandas as pd\n", "# import re\n", "\n", "# # Attempt to convert the string to float after replacing ',' and '$'\n", "# def clean_amount(x):\n", "#     if x is None:\n", "#         return None\n", "#     try:\n", "#         return float(x.replace(',', '').replace('$', ''))\n", "#     except ValueError:\n", "#         # If conversion fails, return the original value or handle as needed\n", "#         return x\n", "\n", "\n", "# result_df['TreatmentAmountIncVAT_clean'] = result_df['TreatmentAmountIncVAT'].apply(lambda x: clean_amount(x))\n", "# # result_df['TreatmentAmountIncVAT_clean'] = result_df['TreatmentAmountIncVAT_clean'].fillna(0)\n", "# result_df['TreatmentAmountIncVAT_clean'] = pd.to_numeric(result_df['TreatmentAmountIncVAT_clean'], errors='coerce')\n", "\n", "# result_df['InvoiceTotalIncVAT_clean'] = result_df['InvoiceTotalIncVAT'].apply(lambda x: clean_amount(x))\n", "# result_df['ClaimInvoiceTotalIncVAT_clean'] = result_df['ClaimInvoiceTotalIncVAT'].apply(lambda x: clean_amount(x))\n", "\n", "# # Step 1 & 2: Group by 'InvoiceNo' and sum 'TreatmentAmountIncVAT'\n", "# grouped_inv = result_df.groupby('FileName')['InvoiceTotalIncVAT_clean'].sum().reset_index(name='InvoiceSum')\n", "# grouped_tmt = result_df.groupby('FileName')['TreatmentAmountIncVAT_clean'].sum().reset_index(name='TreatmentSum')\n", "\n", "# # Merging the summed values back to the original DataFrame\n", "# result_df_merge = result_df.merge(grouped_inv, on='FileName', how='left')\n", "\n", "# # result_df_merge['InvoiceTotalCheck'] = result_df_merge ['InvoiceSum'] == result_df_merge['InvoiceTotalIncVAT_clean']\n", "# # result_df_merge['InvoiceTotalCheck'] = check_column_difference(result_df_merge, 'InvoiceSum', 'InvoiceTotalIncVAT_clean')\n", "\n", "# grouped_claim = result_df.groupby('FileName')['ClaimInvoiceTotalIncVAT_clean'].sum().reset_index(name='ClaimSum')\n", "# result_df_final = result_df_merge.merge(grouped_claim, on='FileName', how='left')\n", "# result_df_final = result_df_final.merge(grouped_tmt, on='FileName', how='left')\n", "\n", "# # result_df_final['ClaimTotalCheck'] = result_df_final['ClaimSum'] == result_df_final['ClaimInvoiceTotalIncVAT_clean']\n", "# # result_df_final['ClaimTotalCheck'] = check_column_difference(result_df_final, 'ClaimSum', 'ClaimInvoiceTotalIncVAT_clean')\n", "\n", "# # result_df_final['ClaimTotalCheck_tmt'] = check_column_difference(result_df_final, 'TreatmentSum', 'ClaimInvoiceTotalIncVAT_clean')\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["\n", "# Merging the summed values back to the original DataFrame\n", "result_df_merge = result_df.merge(grouped_inv, on='FileName', how='left')\n", "\n", "grouped_claim = result_df.groupby('FileName')['ClaimInvoiceTotalIncVAT_clean'].sum().reset_index(name='ClaimSum')\n", "result_df_final = result_df_merge.merge(grouped_claim, on='FileName', how='left')\n", "result_df_final = result_df_final.merge(grouped_tmt, on='FileName', how='left')\n", "\n", "invoice_lvl = result_df_final.drop_duplicates('InvoiceNo').reset_index()\n", "claim_lvl = result_df_final.drop_duplicates('FileName').reset_index()\n", "\n", "true_invoice = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_merged.xlsx')\n", "true_invoice['FileName'] = true_invoice['DocFile'].apply(lambda x: x.split('.')[0])\n", "claim_lvl_merge = claim_lvl.merge(true_invoice,on='FileName', how='left')\n", "\n", "result_series = check_column_difference(claim_lvl_merge, 'ClaimInvoiceTotalIncVAT_clean', 'Amount Claimed (UPM)')\n", "claim_lvl_merge['AmountCheck_1'] = result_series\n", "\n", "result_series = check_column_difference(claim_lvl_merge, 'ClaimSum', 'Amount Claimed (UPM)')\n", "claim_lvl_merge['AmountCheck_2'] = result_series\n", "\n", "result_series = check_column_difference(claim_lvl_merge, 'InvoiceSum', 'Amount Claimed (UPM)')\n", "claim_lvl_merge['AmountCheck_3'] = result_series\n", "\n", "result_series = check_column_difference(claim_lvl_merge, 'TreatmentSum', 'Amount Claimed (UPM)')\n", "claim_lvl_merge['AmountCheck_4'] = result_series\n", "\n", "result_series = check_column_difference(claim_lvl_merge, 'ClaimInvoiceTotalIncVAT_clean', 'TreatmentSum')\n", "claim_lvl_merge['ClaimTmtEqual_check'] = result_series\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Change Variable"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [], "source": ["claim_lvl_merge = claim_lvl_merge[claim_lvl_merge['IsMultipetPresent']==0]"]}, {"cell_type": "code", "execution_count": 207, "metadata": {}, "outputs": [{"data": {"text/plain": ["770"]}, "execution_count": 207, "metadata": {}, "output_type": "execute_result"}], "source": ["len(claim_lvl_merge)"]}, {"cell_type": "code", "execution_count": 208, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>FileName</th>\n", "      <th>DocumentMetadata</th>\n", "      <th>AnimalName</th>\n", "      <th>VetName</th>\n", "      <th>PracticeName</th>\n", "      <th><PERSON><PERSON>ddress</th>\n", "      <th>PracticeABN</th>\n", "      <th>PracticePhoneNumber</th>\n", "      <th>PracticeFaxNumber</th>\n", "      <th>...</th>\n", "      <th>DocumentName</th>\n", "      <th>DocumentPath</th>\n", "      <th>VetPracticeId</th>\n", "      <th>DocContainer</th>\n", "      <th>DocFile</th>\n", "      <th>AmountCheck_1</th>\n", "      <th>AmountCheck_2</th>\n", "      <th>AmountCheck_3</th>\n", "      <th>AmountCheck_4</th>\n", "      <th>ClaimTmtEqual_check</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>12915f61-35ca-4af6-976f-80250f39d018</td>\n", "      <td>{'header': 'EVERVET SOUTH YARRA', 'body': ['In...</td>\n", "      <td><PERSON><PERSON><PERSON> (Pipo)</td>\n", "      <td><PERSON></td>\n", "      <td>EVERVET</td>\n", "      <td>66 Toorak Road, South Yarra, Victoria 3141</td>\n", "      <td>0490796561</td>\n", "      <td>Ph:9510 1335</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/12915f61-35ca-4af6-976...</td>\n", "      <td>NaN</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>12915f61-35ca-4af6-976f-80250f39d018.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>12a700b3-8405-40fa-99a6-1ea2f0f81134</td>\n", "      <td>{'header': 'Kardinia Veterinary Clinic &amp; Anima...</td>\n", "      <td>Bonnie</td>\n", "      <td>Dr <PERSON> B<PERSON></td>\n", "      <td>Kardinia Veterinary Clinic &amp; Animal Hospital</td>\n", "      <td>355 Moorabool Street, Geelong Victoria 3220</td>\n", "      <td>None</td>\n", "      <td>03 5221 5122</td>\n", "      <td>03 5222 3930</td>\n", "      <td>...</td>\n", "      <td>Invoice.jpg</td>\n", "      <td>csp-prod-claim-20240301/12a700b3-8405-40fa-99a...</td>\n", "      <td>CT0002750</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>12a700b3-8405-40fa-99a6-1ea2f0f81134.jpg</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>12ace777-0681-44c8-a267-85f27217e8fe</td>\n", "      <td>{'header': 'PAYMENT RECEIPT', 'body': ['Receip...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>None</td>\n", "      <td>My Family Vet</td>\n", "      <td>141 Canvey Road Upper Kedron, Queensland 4055</td>\n", "      <td>None</td>\n", "      <td>07 3518 4418</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/12ace777-0681-44c8-a26...</td>\n", "      <td>NaN</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>12ace777-0681-44c8-a267-85f27217e8fe.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8</td>\n", "      <td>12ae6ded-0fbf-4073-8b05-794d8e8b8e51</td>\n", "      <td>{'header': 'Vets on Balwyn logo, Practice deta...</td>\n", "      <td>O<PERSON></td>\n", "      <td>Dr <PERSON>c DVM</td>\n", "      <td>Vets on Balwyn</td>\n", "      <td>341 Balwyn Road, North Balwyn VIC 3104</td>\n", "      <td>**************</td>\n", "      <td>P(03) 9857 8100</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/12ae6ded-0fbf-4073-8b0...</td>\n", "      <td>CT0006410</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>12ae6ded-0fbf-4073-8b05-794d8e8b8e51.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10</td>\n", "      <td>12b3b224-33c7-416f-8499-52b3bae91115</td>\n", "      <td>{'header': 'Niddrie Vet Clinic, 577 Keilor Rd,...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Niddrie Vet Clinic</td>\n", "      <td>577 Keilor Rd, Niddrie VIC 3042</td>\n", "      <td>**************</td>\n", "      <td>(03) 9379 8913</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/12b3b224-33c7-416f-849...</td>\n", "      <td>NaN</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>12b3b224-33c7-416f-8499-52b3bae91115.pdf</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>763</th>\n", "      <td>2590</td>\n", "      <td>11212af0-c1ce-47f5-bf31-d1d5e0cad331</td>\n", "      <td>{'header': 'NORTHERN RIVERS VETERINARY SERVICE...</td>\n", "      <td>Luna</td>\n", "      <td>Mr <PERSON></td>\n", "      <td>NORTHERN RIVERS VETERINARY SERVICE CASINO</td>\n", "      <td>57 Johnston St, Casino NSW 2470</td>\n", "      <td>***********</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Invoice.jpeg</td>\n", "      <td>csp-prod-claim-20240301/11212af0-c1ce-47f5-bf3...</td>\n", "      <td>NaN</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>11212af0-c1ce-47f5-bf31-d1d5e0cad331.jpeg</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>765</th>\n", "      <td>2604</td>\n", "      <td>1151d46d-890a-4202-8c8f-00c98bd49ff8</td>\n", "      <td>{'header': 'Wellness Centre', 'body': 'Tax Inv...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr <PERSON></td>\n", "      <td>Wellness Centre</td>\n", "      <td>24 PRAIANO AVENUE Berwick VIC 3806</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/1151d46d-890a-4202-8c8...</td>\n", "      <td>NaN</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>1151d46d-890a-4202-8c8f-00c98bd49ff8.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>767</th>\n", "      <td>2609</td>\n", "      <td>11b0c020-8d5a-4db4-b89d-edb322d6ea26</td>\n", "      <td>{'header': 'Greencross Vets', 'body': 'Invoice...</td>\n", "      <td>Odin</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>Greencross Vets</td>\n", "      <td>13 Marshall Lane, Kenmore QLD 4069</td>\n", "      <td>**************</td>\n", "      <td>07 3378 7188</td>\n", "      <td>07 3378 7171</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/11b0c020-8d5a-4db4-b89...</td>\n", "      <td>CT0661341</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>11b0c020-8d5a-4db4-b89d-edb322d6ea26.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>768</th>\n", "      <td>2611</td>\n", "      <td>11c06a3c-1980-46b8-8795-7a4c1a189b3f</td>\n", "      <td>{'header': 'ANIMAL EMERGENCY SERVICE', 'body':...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr <PERSON></td>\n", "      <td>ANIMAL EMERGENCY SERVICE</td>\n", "      <td>Cnr Lexington &amp; Logan Roads, Underwood QLD 4119</td>\n", "      <td>**************</td>\n", "      <td>***********</td>\n", "      <td>***********</td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/11c06a3c-1980-46b8-879...</td>\n", "      <td>CT0007592</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>11c06a3c-1980-46b8-8795-7a4c1a189b3f.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>769</th>\n", "      <td>2617</td>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3</td>\n", "      <td>{'header': 'WAVES (WA Vet Emergency &amp; Specialt...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>WAVES (WA Vet Emergency &amp; Specialty)</td>\n", "      <td>1/640 Beeliar Drive, Success, WA 6164</td>\n", "      <td>**************</td>\n", "      <td>9412 5700</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>Invoice.pdf</td>\n", "      <td>csp-prod-claim-20240301/12268c80-e3fe-4485-a60...</td>\n", "      <td>NaN</td>\n", "      <td>csp-prod-claim-20240301</td>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3.pdf</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>618 rows × 82 columns</p>\n", "</div>"], "text/plain": ["     index                              FileName  \\\n", "0        0  12915f61-35ca-4af6-976f-80250f39d018   \n", "1        2  12a700b3-8405-40fa-99a6-1ea2f0f81134   \n", "2        3  12ace777-0681-44c8-a267-85f27217e8fe   \n", "3        8  12ae6ded-0fbf-4073-8b05-794d8e8b8e51   \n", "4       10  12b3b224-33c7-416f-8499-52b3bae91115   \n", "..     ...                                   ...   \n", "763   2590  11212af0-c1ce-47f5-bf31-d1d5e0cad331   \n", "765   2604  1151d46d-890a-4202-8c8f-00c98bd49ff8   \n", "767   2609  11b0c020-8d5a-4db4-b89d-edb322d6ea26   \n", "768   2611  11c06a3c-1980-46b8-8795-7a4c1a189b3f   \n", "769   2617  12268c80-e3fe-4485-a607-5091d9bb82b3   \n", "\n", "                                      DocumentMetadata   AnimalName  \\\n", "0    {'header': 'EVERVET SOUTH YARRA', 'body': ['In...  <PERSON><PERSON>o (Pipo)   \n", "1    {'header': 'Kardinia Veterinary Clinic & Anima...       Bonnie   \n", "2    {'header': 'PAYMENT RECEIPT', 'body': ['Receip...      Katness   \n", "3    {'header': 'Vets on Balwyn logo, Practice deta...        Ollie   \n", "4    {'header': 'Niddrie Vet Clinic, 577 Keilor Rd,...      <PERSON>   \n", "..                                                 ...          ...   \n", "763  {'header': 'NORTHERN RIVERS VETERINARY SERVICE...         Luna   \n", "765  {'header': 'Wellness Centre', 'body': 'Tax Inv...         Ludo   \n", "767  {'header': 'Greencross Vets', 'body': 'Invoice...         Odin   \n", "768  {'header': 'ANIMAL EMERGENCY SERVICE', 'body':...         Yogi   \n", "769  {'header': 'WAVES (WA Vet Emergency & Specialt...       <PERSON><PERSON>   \n", "\n", "                         VetName  \\\n", "0                     May <PERSON>   \n", "1         <PERSON> <PERSON>   \n", "2                           None   \n", "3    <PERSON> <PERSON> BSc DVM   \n", "4                           None   \n", "..                           ...   \n", "763           Mr <PERSON>   \n", "765          <PERSON> <PERSON>   \n", "767                 <PERSON>   \n", "768            <PERSON> <PERSON>   \n", "769             <PERSON><PERSON> <PERSON><PERSON>   \n", "\n", "                                     PracticeName  \\\n", "0                                         EVERVET   \n", "1    Kardinia Veterinary Clinic & Animal Hospital   \n", "2                                   My Family Vet   \n", "3                                  Vets on Balwyn   \n", "4                              Niddrie Vet Clinic   \n", "..                                            ...   \n", "763     NORTHERN RIVERS VETERINARY SERVICE CASINO   \n", "765                               Wellness Centre   \n", "767                               Greencross Vets   \n", "768                      ANIMAL EMERGENCY SERVICE   \n", "769          WAVES (WA Vet Emergency & Specialty)   \n", "\n", "                                     PracticeAddress     PracticeABN  \\\n", "0         66 Toorak Road, South Yarra, Victoria 3141      0490796561   \n", "1        355 Moorabool Street, Geelong Victoria 3220            None   \n", "2      141 Canvey Road Upper Kedron, Queensland 4055            None   \n", "3             341 Balwyn Road, North Balwyn VIC 3104  **************   \n", "4                    577 Keilor Rd, Niddrie VIC 3042  **************   \n", "..                                               ...             ...   \n", "763                  57 Johnston St, Casino NSW 2470     ***********   \n", "765               24 PRAIANO AVENUE Berwick VIC 3806            None   \n", "767               13 <PERSON>, Kenmore QLD 4069  **************   \n", "768  Cnr Lexington & Logan Roads, Underwood QLD 4119  **************   \n", "769            1/640 Beeliar Drive, Success, WA 6164  **************   \n", "\n", "    PracticePhoneNumber PracticeFaxNumber  ...  DocumentName  \\\n", "0          Ph:9510 1335              None  ...   Invoice.pdf   \n", "1          03 5221 5122      03 5222 3930  ...   Invoice.jpg   \n", "2          07 3518 4418              None  ...   Invoice.pdf   \n", "3       P(03) 9857 8100              None  ...   Invoice.pdf   \n", "4        (03) 9379 8913              None  ...   Invoice.pdf   \n", "..                  ...               ...  ...           ...   \n", "763                None              None  ...  Invoice.jpeg   \n", "765                None              None  ...   Invoice.pdf   \n", "767        07 3378 7188      07 3378 7171  ...   Invoice.pdf   \n", "768         ***********       ***********  ...   Invoice.pdf   \n", "769           9412 5700                    ...   Invoice.pdf   \n", "\n", "                                          DocumentPath VetPracticeId  \\\n", "0    csp-prod-claim-20240301/12915f61-35ca-4af6-976...           NaN   \n", "1    csp-prod-claim-20240301/12a700b3-8405-40fa-99a...     CT0002750   \n", "2    csp-prod-claim-20240301/12ace777-0681-44c8-a26...           NaN   \n", "3    csp-prod-claim-20240301/12ae6ded-0fbf-4073-8b0...     CT0006410   \n", "4    csp-prod-claim-20240301/12b3b224-33c7-416f-849...           NaN   \n", "..                                                 ...           ...   \n", "763  csp-prod-claim-20240301/11212af0-c1ce-47f5-bf3...           NaN   \n", "765  csp-prod-claim-20240301/1151d46d-890a-4202-8c8...           NaN   \n", "767  csp-prod-claim-20240301/11b0c020-8d5a-4db4-b89...     CT0661341   \n", "768  csp-prod-claim-20240301/11c06a3c-1980-46b8-879...     CT0007592   \n", "769  csp-prod-claim-20240301/12268c80-e3fe-4485-a60...           NaN   \n", "\n", "                DocContainer                                    DocFile  \\\n", "0    csp-prod-claim-20240301   12915f61-35ca-4af6-976f-80250f39d018.pdf   \n", "1    csp-prod-claim-20240301   12a700b3-8405-40fa-99a6-1ea2f0f81134.jpg   \n", "2    csp-prod-claim-20240301   12ace777-0681-44c8-a267-85f27217e8fe.pdf   \n", "3    csp-prod-claim-20240301   12ae6ded-0fbf-4073-8b05-794d8e8b8e51.pdf   \n", "4    csp-prod-claim-20240301   12b3b224-33c7-416f-8499-52b3bae91115.pdf   \n", "..                       ...                                        ...   \n", "763  csp-prod-claim-20240301  11212af0-c1ce-47f5-bf31-d1d5e0cad331.jpeg   \n", "765  csp-prod-claim-20240301   1151d46d-890a-4202-8c8f-00c98bd49ff8.pdf   \n", "767  csp-prod-claim-20240301   11b0c020-8d5a-4db4-b89d-edb322d6ea26.pdf   \n", "768  csp-prod-claim-20240301   11c06a3c-1980-46b8-8795-7a4c1a189b3f.pdf   \n", "769  csp-prod-claim-20240301   12268c80-e3fe-4485-a607-5091d9bb82b3.pdf   \n", "\n", "    AmountCheck_1 AmountCheck_2 AmountCheck_3 AmountCheck_4  \\\n", "0               1             0             0             1   \n", "1               1             1             1             1   \n", "2               1             0             0             1   \n", "3               1             0             0             1   \n", "4               1             1             1             1   \n", "..            ...           ...           ...           ...   \n", "763             1             0             0             1   \n", "765             1             0             0             1   \n", "767             1             0             0             1   \n", "768             1             0             0             1   \n", "769             1             0             0             1   \n", "\n", "    ClaimTmtEqual_check  \n", "0                     1  \n", "1                     1  \n", "2                     1  \n", "3                     1  \n", "4                     1  \n", "..                  ...  \n", "763                   1  \n", "765                   1  \n", "767                   1  \n", "768                   1  \n", "769                   1  \n", "\n", "[618 rows x 82 columns]"]}, "execution_count": 208, "metadata": {}, "output_type": "execute_result"}], "source": ["# claim_lvl_merge[(claim_lvl_merge['InvoiceTotalCheck']==1) & (claim_lvl_merge['AmountCheck_1']==0)]\n", "\n", "# claim_lvl_merge[(claim_lvl_merge['ClaimTotalCheck']==1) & (claim_lvl_merge['AmountCheck_1']==0)]\n", "\n", "claim_lvl_merge[(claim_lvl_merge['AmountCheck_4']==1)]\n", "\n", "claim_lvl_merge[(claim_lvl_merge['ClaimTmtEqual_check']==1) & (claim_lvl_merge['AmountCheck_1']==1)]\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON> over 0.8 confidence"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["273"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# claim_lvl_merge_08 = claim_lvl_merge[(claim_lvl_merge['Document Confidence']>=0.8) & (claim_lvl_merge['AI Type']>=1) ]\n", "claim_lvl_merge_08 = claim_lvl_merge[(claim_lvl_merge['Document Confidence']>=0.8) & (claim_lvl_merge['AI Type']>=1)]\n", "len(claim_lvl_merge_08)"]}, {"cell_type": "code", "execution_count": 211, "metadata": {}, "outputs": [{"data": {"text/plain": ["273"]}, "execution_count": 211, "metadata": {}, "output_type": "execute_result"}], "source": ["len(claim_lvl_merge_08)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>Amount Claimed (OCR)</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "      <th>ClaimInvoiceTotalIncVAT</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>12a700b3-8405-40fa-99a6-1ea2f0f81134</td>\n", "      <td>153.00</td>\n", "      <td>153.00</td>\n", "      <td>153.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>12ae6ded-0fbf-4073-8b05-794d8e8b8e51</td>\n", "      <td>137.80</td>\n", "      <td>137.80</td>\n", "      <td>137.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1308dbb6-e5c1-46a5-90d1-1694cdf145f8</td>\n", "      <td>227.70</td>\n", "      <td>227.70</td>\n", "      <td>227.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>14075600-1a42-4a5c-8e19-3757ef966018</td>\n", "      <td>255.00</td>\n", "      <td>255.00</td>\n", "      <td>255.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>1451437a-b4e3-411d-bea9-035754733985</td>\n", "      <td>639.25</td>\n", "      <td>639.25</td>\n", "      <td>639.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745</th>\n", "      <td>a73fc9ba-6a2b-4a0a-b902-c5d44fb9345f</td>\n", "      <td>154.13</td>\n", "      <td>154.13</td>\n", "      <td>$154.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>746</th>\n", "      <td>a76f4e08-a28c-43f0-bc00-0593bff5d17d</td>\n", "      <td>424.00</td>\n", "      <td>424.00</td>\n", "      <td>424.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>755</th>\n", "      <td>0f56ef52-3f4f-4729-81b0-22acf8a100af</td>\n", "      <td>72.95</td>\n", "      <td>72.95</td>\n", "      <td>$72.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>758</th>\n", "      <td>0fce4b79-b6e8-4262-bd2f-0aa0bf501474</td>\n", "      <td>230.10</td>\n", "      <td>230.10</td>\n", "      <td>230.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>768</th>\n", "      <td>11c06a3c-1980-46b8-8795-7a4c1a189b3f</td>\n", "      <td>473.85</td>\n", "      <td>473.85</td>\n", "      <td>473.85</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>247 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                                 FileName  Amount Claimed (OCR)  \\\n", "1    12a700b3-8405-40fa-99a6-1ea2f0f81134                153.00   \n", "3    12ae6ded-0fbf-4073-8b05-794d8e8b8e51                137.80   \n", "7    1308dbb6-e5c1-46a5-90d1-1694cdf145f8                227.70   \n", "11   14075600-1a42-4a5c-8e19-3757ef966018                255.00   \n", "12   1451437a-b4e3-411d-bea9-035754733985                639.25   \n", "..                                    ...                   ...   \n", "745  a73fc9ba-6a2b-4a0a-b902-c5d44fb9345f                154.13   \n", "746  a76f4e08-a28c-43f0-bc00-0593bff5d17d                424.00   \n", "755  0f56ef52-3f4f-4729-81b0-22acf8a100af                 72.95   \n", "758  0fce4b79-b6e8-4262-bd2f-0aa0bf501474                230.10   \n", "768  11c06a3c-1980-46b8-8795-7a4c1a189b3f                473.85   \n", "\n", "     Amount Claimed (UPM) ClaimInvoiceTotalIncVAT  \n", "1                  153.00                  153.00  \n", "3                  137.80                  137.80  \n", "7                  227.70                  227.70  \n", "11                 255.00                  255.00  \n", "12                 639.25                  639.25  \n", "..                    ...                     ...  \n", "745                154.13                 $154.13  \n", "746                424.00                  424.00  \n", "755                 72.95                  $72.95  \n", "758                230.10                  230.10  \n", "768                473.85                  473.85  \n", "\n", "[247 rows x 4 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["claim_lvl_merge_08[(claim_lvl_merge_08['AmountCheck_4']==1)]\n", "\n", "claim_lvl_merge_08[(claim_lvl_merge_08['ClaimTmtEqual_check']==1) & (claim_lvl_merge_08['AmountCheck_1']==1)][['FileName', 'Amount Claimed (OCR)', 'Amount Claimed (UPM)','ClaimInvoiceTotalIncVAT']]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["col=['FileName','InvoiceNo','is_InitialJsonValid', 'is_FinalJsonValid', 'is_JsonModified','is_KeyValid','InvoiceSum','InvoiceTotalIncVAT_clean']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### OCR Output"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["ocr = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_ocr_output_clean.xlsx')\n", "invoice_merge = pd.merge(claim_lvl_merge,ocr,left_on='FileName', right_on='filename', how='left')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['index', 'FileName', 'DocumentMetadata', 'AnimalName', 'VetName',\n", "       'PracticeName', 'PracticeAddress', 'PracticeABN', 'PracticePhoneNumber',\n", "       'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "       'ClaimTotalExVAT', 'ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "       'InvoiceIndex', 'InvoiceNo', 'InvoiceDate', 'IsMultipetPresent',\n", "       'InvoiceTotalExVAT', 'InvoiceVAT', 'InvoiceTotalIncVAT',\n", "       'TreatmentIndex', 'TreatmentDate', 'ItemCode', 'ItemType', 'Sequence',\n", "       'Description', 'Quantity', 'TreatmentAmountExVAT', 'TreatmentVAT',\n", "       'TreatmentAmountIncVAT', 'TreatmentDiscountExVAT',\n", "       'is_InitialJsonValid', 'is_FinalJsonValid', 'is_JsonModified',\n", "       'is_KeyValid', 'TreatmentAmountIncVAT_clean',\n", "       'InvoiceTotalIncVAT_clean', 'ClaimInvoiceTotalIncVAT_clean',\n", "       'InvoiceSum', 'ClaimSum', 'TreatmentSum', 'Unnamed: 0_x',\n", "       'Claim Number', 'Claim Date Created', 'Document Confidence', 'AI Type',\n", "       'Is GapOnly', 'Amou<PERSON> (OCR)', 'Amount <PERSON> (UPM)',\n", "       'Amount Claimed (Difference)', 'Invoice Count (OCR)',\n", "       'Invoice Count (UPM)', 'Invoice Count (Difference)',\n", "       'Are Invoice Numbers Different', 'Treatment Count (OCR)',\n", "       'Treatment Count (UPM)', 'Treatment Count (Difference)',\n", "       'Is Service Provider Different', 'Audit Category', 'Risk Category',\n", "       'OCR_ServiceProviderName', 'UPM_ServiceProviderName', 'ClaimNo',\n", "       'CspReferenceNo', 'AnimalNo', 'PolicyNo', 'InsuredContactNo',\n", "       'DateFirstTreatment', 'AmountClaimed', 'DocumentId', 'DocumentName',\n", "       'DocumentPath', 'VetPracticeId', 'DocContainer', 'DocFile',\n", "       'AmountCheck_1', 'AmountCheck_2', 'AmountCheck_3', 'AmountCheck_4',\n", "       'ClaimTmtEqual_check', 'Unnamed: 0_y', 'filename', 'Text', 'Score',\n", "       'BoundingBox'],\n", "      dtype='object')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["invoice_merge.columns\n", "# col = ['AnimalName', 'VetName',\n", "#        'PracticeName', 'PracticeAddress', 'PracticeABN', 'PracticePhoneNumber',\n", "#        'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "#        'ClaimInvoiceTotalIncVAT',\n", "#        'InvoiceNo', 'InvoiceDate','InvoiceTotalIncVAT',\n", "#        'Description', 'TreatmentAmountIncVAT']"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import ast\n", "invoice_merge['Text'] =invoice_merge['Text'].apply(ast.literal_eval)\n", "invoice_merge['Score'] = invoice_merge['Score'].apply(ast.literal_eval)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fuzzy Matching Rows for AnimalName: 100%|██████████| 770/770 [00:01<00:00, 565.87it/s]\n", "Fuzzy Matching Rows for VetName: 100%|██████████| 770/770 [00:01<00:00, 749.41it/s]\n", "Fuzzy Matching Rows for ClaimInvoiceTotalIncVAT: 100%|██████████| 770/770 [00:01<00:00, 576.63it/s]\n", "Fuzzy Matching Rows for InvoiceNo: 100%|██████████| 770/770 [00:01<00:00, 629.49it/s]\n"]}], "source": ["from fuzzywuzzy import process\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from fuzzywuzzy import fuzz\n", "\n", "def fuzzy_match_multiple_columns(df, compare_cols, text_col, score_col):\n", "    \"\"\"\n", "    Conducts fuzzy matching between values in multiple columns ('compare_cols') and a list of values in 'text_col'.\n", "    Creates separate 'BestMatch', 'MatchConfidenceScore', and 'FuzzyMatchScore' columns for each column in 'compare_cols'.\n", "    \n", "    Parameters:\n", "    df (pd.DataFrame): The DataFrame to process.\n", "    compare_cols (list of str): List of column names to be compared with 'text_col'.\n", "    text_col (str): Column name with the list of text values.\n", "    score_col (str): Column name with the list of associated confidence scores.\n", "\n", "    Returns:\n", "    pd.DataFrame: The modified DataFrame with new columns for each compare_col.\n", "    \"\"\"\n", "    def get_best_match(compare_value, texts, scores):\n", "        best_match = None\n", "        best_fuzzy_score = -1\n", "        best_confidence_score = None\n", "\n", "        if isinstance(compare_value, list):\n", "            compare_value = compare_value[0] if compare_value else \"\"\n", "        compare_value = str(compare_value) if pd.notnull(compare_value) else \"\"\n", "\n", "        if not compare_value.strip():\n", "            return None, None, None\n", "\n", "        for text, score in zip(texts, scores):\n", "            match = process.extractOne(compare_value, [text], score_cutoff=0, scorer=fuzz.partial_ratio) #fuzz.token_set_ratio \n", "            if match and match[1] > best_fuzzy_score:\n", "                best_match = text\n", "                best_fuzzy_score = match[1]\n", "                best_confidence_score = score\n", "\n", "        return best_match, best_confidence_score, best_fuzzy_score\n", "\n", "    for col in compare_cols:\n", "        tqdm.pandas(desc=f\"Fuzzy Matching Rows for {col}\")\n", "        match_results = df.progress_apply(lambda row: get_best_match(row[col], row[text_col], row[score_col]), axis=1)\n", "\n", "        df[f'{col}_BestMatch'] = [match[0] for match in match_results]\n", "        df[f'{col}_MatchConfidenceScore'] = [match[1] for match in match_results]\n", "        df[f'{col}_FuzzyMatchScore'] = [match[2] for match in match_results]\n", "\n", "    return df\n", "\n", "# Example usage\n", "fuzzy_df = fuzzy_match_multiple_columns(invoice_merge, ['AnimalName', 'VetName','ClaimInvoiceTotalIncVAT','InvoiceNo'], 'Text', 'Score')\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Exact Matching Rows for ClaimInvoiceTotalIncVAT_clean: 100%|██████████| 770/770 [00:00<00:00, 5811.77it/s]\n"]}], "source": ["import pandas as pd\n", "from tqdm import tqdm\n", "\n", "def exact_match_multiple_columns(df, compare_cols, text_col, score_col):\n", "    \"\"\"\n", "    Conducts exact matching between values in multiple columns ('compare_cols') and a list of values in 'text_col'.\n", "    Creates separate 'ExactMatch' and 'MatchConfidenceScore' columns for each column in 'compare_cols'.\n", "    \n", "    Parameters:\n", "    df (pd.DataFrame): The DataFrame to process.\n", "    compare_cols (list of str): List of column names to be compared with 'text_col'.\n", "    text_col (str): Column name with the list of text values.\n", "    score_col (str): Column name with the list of associated confidence scores.\n", "\n", "    Returns:\n", "    pd.DataFrame: The modified DataFrame with new columns for each compare_col.\n", "    \"\"\"\n", "    def get_exact_match(compare_value, texts, scores):\n", "        compare_value = str(compare_value) if pd.notnull(compare_value) else \"\"\n", "        if not compare_value.strip():\n", "            return None, None,False\n", "        # print('this:',compare_value)\n", "        \n", "        for text, score in zip(texts, scores):\n", "        \n", "            if str(compare_value)== str(extract_amount(text)):\n", "                # print(extract_amount(text))\n", "                return text, score, True\n", "\n", "        return None, None, False\n", "\n", "    for col in compare_cols:\n", "        tqdm.pandas(desc=f\"Exact Matching Rows for {col}\")\n", "        match_results = df.progress_apply(lambda row: get_exact_match(row[col], row[text_col], row[score_col]), axis=1)\n", "\n", "        df[f'{col}_ExactMatch'] = [match[0] for match in match_results]\n", "        df[f'{col}_ExactMatchConfidenceScore'] = [match[1] for match in match_results]\n", "        df[f'{col}_ExactMatchIndicator'] = [match[2] for match in match_results]\n", "\n", "    return df\n", "\n", "# Example usage\n", "final_df = exact_match_multiple_columns(fuzzy_df, ['ClaimInvoiceTotalIncVAT_clean'], 'Text', 'Score')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scoring System"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["final_df['Amount_Score'] = final_df['ClaimInvoiceTotalIncVAT_clean_ExactMatchIndicator'] *  final_df['ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore']"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['index', 'FileName', 'DocumentMetadata', 'AnimalName', 'VetName',\n", "       'PracticeName', 'PracticeAddress', 'PracticeABN', 'PracticePhoneNumber',\n", "       'PracticeFaxNumber', 'PracticeEmailAddress', 'PracticeWebsite',\n", "       'ClaimTotalExVAT', 'ClaimInvoiceVAT', 'ClaimInvoiceTotalIncVAT',\n", "       'InvoiceIndex', 'InvoiceNo', 'InvoiceDate', 'IsMultipetPresent',\n", "       'InvoiceTotalExVAT', 'InvoiceVAT', 'InvoiceTotalIncVAT',\n", "       'TreatmentIndex', 'TreatmentDate', 'ItemCode', 'ItemType', 'Sequence',\n", "       'Description', 'Quantity', 'TreatmentAmountExVAT', 'TreatmentVAT',\n", "       'TreatmentAmountIncVAT', 'TreatmentDiscountExVAT',\n", "       'is_InitialJsonValid', 'is_FinalJsonValid', 'is_JsonModified',\n", "       'is_KeyValid', 'TreatmentAmountIncVAT_clean',\n", "       'InvoiceTotalIncVAT_clean', 'ClaimInvoiceTotalIncVAT_clean',\n", "       'InvoiceSum', 'ClaimSum', 'TreatmentSum', 'Unnamed: 0_x',\n", "       'Claim Number', 'Claim Date Created', 'Document Confidence', 'AI Type',\n", "       'Is GapOnly', 'Amou<PERSON> (OCR)', 'Amount <PERSON> (UPM)',\n", "       'Amount Claimed (Difference)', 'Invoice Count (OCR)',\n", "       'Invoice Count (UPM)', 'Invoice Count (Difference)',\n", "       'Are Invoice Numbers Different', 'Treatment Count (OCR)',\n", "       'Treatment Count (UPM)', 'Treatment Count (Difference)',\n", "       'Is Service Provider Different', 'Audit Category', 'Risk Category',\n", "       'OCR_ServiceProviderName', 'UPM_ServiceProviderName', 'ClaimNo',\n", "       'CspReferenceNo', 'AnimalNo', 'PolicyNo', 'InsuredContactNo',\n", "       'DateFirstTreatment', 'AmountClaimed', 'DocumentId', 'DocumentName',\n", "       'DocumentPath', 'VetPracticeId', 'DocContainer', 'DocFile',\n", "       'AmountCheck_1', 'AmountCheck_2', 'AmountCheck_3', 'AmountCheck_4',\n", "       'ClaimTmtEqual_check', 'Unnamed: 0_y', 'filename', 'Text', 'Score',\n", "       'BoundingBox', 'AnimalName_BestMatch',\n", "       'AnimalName_MatchConfidenceScore', 'AnimalName_FuzzyMatchScore',\n", "       'VetName_BestMatch', 'VetName_MatchConfidenceScore',\n", "       'VetName_FuzzyMatchScore', 'ClaimInvoiceTotalIncVAT_BestMatch',\n", "       'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_FuzzyMatchScore', 'InvoiceNo_BestMatch',\n", "       'InvoiceNo_MatchConfidenceScore', 'InvoiceNo_FuzzyMatchScore'],\n", "      dtype='object')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df.columns\n", "# ['Invoice Count (UPM)','Invoice Count (UPM)','InvoiceSum','Are Invoice Numbers Different','InvoiceNo',]\n", "# ['OCR_ServiceProviderName', 'UPM_ServiceProviderName','PracticeName']"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>AnimalName</th>\n", "      <th>AnimalName_BestMatch</th>\n", "      <th>AnimalName_MatchConfidenceScore</th>\n", "      <th>AnimalName_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>13bd981b-e785-43b0-a817-3dc7196c7d21</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON> <PERSON><PERSON></td>\n", "      <td>0.934622</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>264fc6d4-c540-49bb-9075-241ff1806af9</td>\n", "      <td>Maxx</td>\n", "      <td>tax:</td>\n", "      <td>0.991597</td>\n", "      <td>57.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>03eda42d-e0fd-4063-bb56-ad481d186098</td>\n", "      <td>Ali</td>\n", "      <td>Corio Veterinary Clinic.</td>\n", "      <td>0.982857</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>339b87f3-1cb8-4add-8106-0bacb1d18eac</td>\n", "      <td><PERSON></td>\n", "      <td>DATE</td>\n", "      <td>0.998342</td>\n", "      <td>45.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225</th>\n", "      <td>3ab5ffb7-d8cf-40be-b797-03b50a1f3459</td>\n", "      <td>Beth</td>\n", "      <td>Receipt #37646 for Visit with <PERSON> 29/02/2024</td>\n", "      <td>0.991063</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>285</th>\n", "      <td>45bcdb83-538f-4c88-b8d4-8a40149dba5f</td>\n", "      <td>MSE. T. WORLOLINE</td>\n", "      <td>ESYWORLOLINE</td>\n", "      <td>0.752347</td>\n", "      <td>70.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>489db92b-725e-4abf-be50-2624e2700e7f</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.537948</td>\n", "      <td>67.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>336</th>\n", "      <td>51c10067-07f0-4444-8c12-faacf643d509</td>\n", "      <td>Mia</td>\n", "      <td>Como Veterinary Clinic</td>\n", "      <td>0.961402</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>393</th>\n", "      <td>5c3faf2f-61cc-40b9-ad39-72ba22d22ab1</td>\n", "      <td>#27</td>\n", "      <td>Please quote your Pet's name, Surname and #27</td>\n", "      <td>0.974359</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>449</th>\n", "      <td>67214a25-7d17-4dd9-ac7a-2650de60984b</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Solensia 7mg/ml</td>\n", "      <td>0.968516</td>\n", "      <td>57.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>529</th>\n", "      <td>765b6c0f-2388-4cb8-8f4f-a8dbe7d5734f</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON> <PERSON><PERSON></td>\n", "      <td>0.934622</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>561</th>\n", "      <td>7dfa55e2-001b-4b65-b657-75c0c68084c2</td>\n", "      <td><PERSON> &amp; <PERSON><PERSON></td>\n", "      <td>INJECTIONS</td>\n", "      <td>0.997472</td>\n", "      <td>46.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>562</th>\n", "      <td>7e538ff2-33db-468b-85af-f742972134a5</td>\n", "      <td><PERSON></td>\n", "      <td>Winstor</td>\n", "      <td>0.998576</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>572</th>\n", "      <td>827b77b0-c00c-4b92-afa5-01be685a9a2d</td>\n", "      <td>Cash</td>\n", "      <td>CLINICAL #:</td>\n", "      <td>0.983690</td>\n", "      <td>51.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>716</th>\n", "      <td>a0fed668-de97-4c78-ad56-245faf36224d</td>\n", "      <td><PERSON></td>\n", "      <td>Reminders for <PERSON></td>\n", "      <td>0.999170</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>725</th>\n", "      <td>a2195150-1e69-4eb4-b5ed-7fc2f02c1686</td>\n", "      <td>For Farrer</td>\n", "      <td>Client Ref:KING7A ForFarrer</td>\n", "      <td>0.953270</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>749</th>\n", "      <td>a7b7fbe5-4611-40a4-87be-eb439de44d23</td>\n", "      <td><PERSON></td>\n", "      <td>317 CONSULT-.REVISIT</td>\n", "      <td>0.973832</td>\n", "      <td>45.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 FileName         AnimalName  \\\n", "9    13bd981b-e785-43b0-a817-3dc7196c7d21        <PERSON><PERSON>   \n", "120  264fc6d4-c540-49bb-9075-241ff1806af9               Maxx   \n", "142  03eda42d-e0fd-4063-bb56-ad481d186098                Ali   \n", "187  339b87f3-1cb8-4add-8106-0bacb1d18eac             Harley   \n", "225  3ab5ffb7-d8cf-40be-b797-03b50a1f3459               Beth   \n", "285  45bcdb83-538f-4c88-b8d4-8a40149dba5f  MSE. T. WORLOLINE   \n", "291  489db92b-725e-4abf-be50-2624e2700e7f              <PERSON>zie   \n", "336  51c10067-07f0-4444-8c12-faacf643d509                Mia   \n", "393  5c3faf2f-61cc-40b9-ad39-72ba22d22ab1                #27   \n", "449  67214a25-7d17-4dd9-ac7a-2650de60984b              <PERSON><PERSON>   \n", "529  765b6c0f-2388-4cb8-8f4f-a8dbe7d5734f        <PERSON><PERSON>   \n", "561  7dfa55e2-001b-4b65-b657-75c0c68084c2      Oscar & Ken<PERSON>   \n", "562  7e538ff2-33db-468b-85af-f742972134a5            Winston   \n", "572  827b77b0-c00c-4b92-afa5-01be685a9a2d               Cash   \n", "716  a0fed668-de97-4c78-ad56-245faf36224d       <PERSON>   \n", "725  a2195150-1e69-4eb4-b5ed-7fc2f02c1686         For Farrer   \n", "749  a7b7fbe5-4611-40a4-87be-eb439de44d23               Lulu   \n", "\n", "                              AnimalName_BestMatch  \\\n", "9                               <PERSON> <PERSON><PERSON><PERSON>   \n", "120                                           tax:   \n", "142                       Corio Veterinary Clinic.   \n", "187                                           DATE   \n", "225  Receipt #37646 for Visit with <PERSON> 29/02/2024   \n", "285                                   ESYWORLOLINE   \n", "291                                           <PERSON><PERSON>   \n", "336                         Como Veterinary Clinic   \n", "393  Please quote your Pet's name, Surname and #27   \n", "449                                Solensia 7mg/ml   \n", "529                             <PERSON><PERSON>   \n", "561                                     INJECTIONS   \n", "562                                        Winstor   \n", "572                                    CLINICAL #:   \n", "716                           Reminders for <PERSON>   \n", "725                    Client Ref:KING7A ForFarrer   \n", "749                           317 CONSULT-.REVISIT   \n", "\n", "     AnimalName_MatchConfidenceScore  AnimalName_FuzzyMatchScore  \n", "9                           0.934622                        86.0  \n", "120                         0.991597                        57.0  \n", "142                         0.982857                        60.0  \n", "187                         0.998342                        45.0  \n", "225                         0.991063                        60.0  \n", "285                         0.752347                        70.0  \n", "291                         0.537948                        67.0  \n", "336                         0.961402                        60.0  \n", "393                         0.974359                        60.0  \n", "449                         0.968516                        57.0  \n", "529                         0.934622                        86.0  \n", "561                         0.997472                        46.0  \n", "562                         0.998576                        86.0  \n", "572                         0.983690                        51.0  \n", "716                         0.999170                        86.0  \n", "725                         0.953270                        86.0  \n", "749                         0.973832                        45.0  "]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df[(fuzzy_df['ClaimInvoiceTotalIncVAT_FuzzyMatchScore']<90) & (fuzzy_df['AnimalName'].isnull()==False) & (fuzzy_df['AnimalName']!='null') ][[ 'FileName','AnimalName','AnimalName_BestMatch', 'AnimalName_MatchConfidenceScore',\n", "       'AnimalName_FuzzyMatchScore']]"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>VetName</th>\n", "      <th>VetName_BestMatch</th>\n", "      <th>VetName_MatchConfidenceScore</th>\n", "      <th>VetName_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>159f8c91-6653-4828-bc5a-8eaacff73077</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>Maple</td>\n", "      <td>0.999386</td>\n", "      <td>54.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>1b34886e-5dde-4838-9cc2-95ff7f885cc9</td>\n", "      <td>Dr. <PERSON></td>\n", "      <td>TotalS</td>\n", "      <td>0.883854</td>\n", "      <td>45.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>1e760e93-2315-4d83-9bce-d1d45a8f3137</td>\n", "      <td><PERSON></td>\n", "      <td>Catheterisation</td>\n", "      <td>0.998984</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>2069e25e-8f71-487f-8600-abc47c84f8ca</td>\n", "      <td>Dr<PERSON> (<PERSON><PERSON><PERSON>) Law, Dr <PERSON></td>\n", "      <td>Details on 28/0212024 - Finalised 29/02/2024 (...</td>\n", "      <td>0.918621</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>24740aa1-ff99-4e3c-b4f2-4dcaa1686ea3</td>\n", "      <td><PERSON>, MVS, MANZCVs</td>\n", "      <td><PERSON>, MVS,MANZCVs Murray Caisley B...</td>\n", "      <td>0.950014</td>\n", "      <td>87.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>28cc435c-256f-4278-8906-a9edbe2e750f</td>\n", "      <td>Dr <PERSON> &amp; Dr <PERSON></td>\n", "      <td>Dr <PERSON> BVetMed Cert ES (Orth) QLD #...</td>\n", "      <td>0.980929</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>03eda42d-e0fd-4063-bb56-ad481d186098</td>\n", "      <td>Dr. See</td>\n", "      <td>Service Provided</td>\n", "      <td>0.973236</td>\n", "      <td>57.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>03f34127-7e87-4798-8ae3-a751101aaf18</td>\n", "      <td>Dr. <PERSON></td>\n", "      <td>Veterinarian:Dr.<PERSON> vet reg #1932</td>\n", "      <td>0.976137</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>2f2bf547-11ff-41a1-89bf-0c396744e249</td>\n", "      <td>Dr <PERSON> (Hons) QLD Vet Rego 4514</td>\n", "      <td>Maranoa Vet Pty Ltd</td>\n", "      <td>0.906115</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>363b69a5-a38d-4a33-b4c6-421677ed7290</td>\n", "      <td>Dr.<PERSON><PERSON>, Dr.<PERSON><PERSON>, Dr.<PERSON><PERSON><PERSON>, Dr.D.ROBSON</td>\n", "      <td>Dr.D.JAMESDr.R.DYER</td>\n", "      <td>0.969030</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>4186dcdb-4ae7-4e31-9cbc-10757f34e264</td>\n", "      <td>Dr. <PERSON></td>\n", "      <td>User Code:<PERSON><PERSON></td>\n", "      <td>0.955767</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>346</th>\n", "      <td>53bfeb89-5547-4cbe-a871-8babef02c1b8</td>\n", "      <td><PERSON></td>\n", "      <td>27/2/2024 Dr Monica</td>\n", "      <td>0.938245</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>353</th>\n", "      <td>54aa6748-5ad7-41f6-9421-51439eb56eda</td>\n", "      <td><PERSON></td>\n", "      <td>M.</td>\n", "      <td>0.710641</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td>0976a646-c2aa-4621-9324-ef0281aca14d</td>\n", "      <td>Dr. <PERSON></td>\n", "      <td>COMMITTED TO CARING</td>\n", "      <td>0.963761</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>626</th>\n", "      <td>8e710413-2be4-4ec9-8f43-23acaa5ef64f</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Name of attending vet&amp;practiceNoomi No</td>\n", "      <td>0.900242</td>\n", "      <td>66.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>686</th>\n", "      <td>9c10cbaa-5a1e-464e-98bf-47c2f9fc2a0d</td>\n", "      <td>Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> &amp; Associ...</td>\n", "      <td>Mrs <PERSON></td>\n", "      <td>0.973669</td>\n", "      <td>86.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>704</th>\n", "      <td>9e17f605-79e1-4558-a5d9-a56a28799351</td>\n", "      <td>Dr <PERSON>(hons)</td>\n", "      <td>ns)BVScDr <PERSON> BVSc(hons)</td>\n", "      <td>0.931985</td>\n", "      <td>89.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>720</th>\n", "      <td>a19275e2-c213-445f-b04b-b6c08b28e030</td>\n", "      <td>Dr <PERSON> | Dr <PERSON> | Dr <PERSON> ...</td>\n", "      <td>Dr <PERSON> I Dr <PERSON></td>\n", "      <td>0.924369</td>\n", "      <td>87.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>732</th>\n", "      <td>a33112a3-a2fc-42ad-91d3-ee610eb40af9</td>\n", "      <td>Dr. <PERSON><PERSON>-<PERSON></td>\n", "      <td>Amount</td>\n", "      <td>0.999940</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 FileName  \\\n", "32   159f8c91-6653-4828-bc5a-8eaacff73077   \n", "59   1b34886e-5dde-4838-9cc2-95ff7f885cc9   \n", "73   1e760e93-2315-4d83-9bce-d1d45a8f3137   \n", "81   2069e25e-8f71-487f-8600-abc47c84f8ca   \n", "104  24740aa1-ff99-4e3c-b4f2-4dcaa1686ea3   \n", "130  28cc435c-256f-4278-8906-a9edbe2e750f   \n", "142  03eda42d-e0fd-4063-bb56-ad481d186098   \n", "143  03f34127-7e87-4798-8ae3-a751101aaf18   \n", "171  2f2bf547-11ff-41a1-89bf-0c396744e249   \n", "201  363b69a5-a38d-4a33-b4c6-421677ed7290   \n", "273  4186dcdb-4ae7-4e31-9cbc-10757f34e264   \n", "346  53bfeb89-5547-4cbe-a871-8babef02c1b8   \n", "353  54aa6748-5ad7-41f6-9421-51439eb56eda   \n", "472  0976a646-c2aa-4621-9324-ef0281aca14d   \n", "626  8e710413-2be4-4ec9-8f43-23acaa5ef64f   \n", "686  9c10cbaa-5a1e-464e-98bf-47c2f9fc2a0d   \n", "704  9e17f605-79e1-4558-a5d9-a56a28799351   \n", "720  a19275e2-c213-445f-b04b-b6c08b28e030   \n", "732  a33112a3-a2fc-42ad-91d3-ee610eb40af9   \n", "\n", "                                               VetName  \\\n", "32                                      <PERSON> <PERSON><PERSON><PERSON>   \n", "59                                Dr. <PERSON>   \n", "73                                   <PERSON>   \n", "81         Dr<PERSON> (<PERSON><PERSON><PERSON>) Law, Dr <PERSON>   \n", "104                     <PERSON>, MVS, MANZCVs   \n", "130                 Dr <PERSON> & Dr <PERSON>   \n", "142                                            Dr. See   \n", "143                                 <PERSON><PERSON> <PERSON>   \n", "171   Dr <PERSON> (Hons) QLD Vet Rego 4514   \n", "201  Dr.<PERSON><PERSON>, Dr.<PERSON><PERSON>, Dr.<PERSON><PERSON><PERSON><PERSON>, Dr.<PERSON>.ROBSON   \n", "273                                   <PERSON><PERSON> <PERSON>   \n", "346                                          <PERSON>   \n", "353                                         <PERSON>   \n", "472                                        <PERSON><PERSON>   \n", "626                                        Noomi North   \n", "686  Drs <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> & Associ...   \n", "704                        <PERSON> <PERSON>(hons)   \n", "720  Dr <PERSON> | Dr <PERSON> | Dr <PERSON> ...   \n", "732                            <PERSON><PERSON> <PERSON><PERSON>-<PERSON>   \n", "\n", "                                     VetName_BestMatch  \\\n", "32                                               Maple   \n", "59                                              TotalS   \n", "73                                     Catheterisation   \n", "81   Details on 28/0212024 - Finalised 29/02/2024 (...   \n", "104  <PERSON>, <PERSON><PERSON>,MANZCVs Murray Caisley B...   \n", "130  Dr <PERSON> BVetMed Cert ES (Orth) QLD #...   \n", "142                                   Service Provided   \n", "143       Veterinarian:Dr.<PERSON> vet reg #1932   \n", "171                                Maranoa Vet Pty Ltd   \n", "201                                Dr.D.JAMESDr.R.DY<PERSON>   \n", "273                          User Code:Dr.<PERSON>   \n", "346                                27/2/2024 Dr Monica   \n", "353                                                 M.   \n", "472                                COMMITTED TO CARING   \n", "626             Name of attending vet&practiceNoomi No   \n", "686                                  <PERSON>   \n", "704                 ns)BVScDr <PERSON> B<PERSON>c(hons)   \n", "720                 Dr <PERSON> I Dr <PERSON>   \n", "732                                             Amount   \n", "\n", "     VetName_MatchConfidenceScore  VetName_FuzzyMatchScore  \n", "32                       0.999386                     54.0  \n", "59                       0.883854                     45.0  \n", "73                       0.998984                     50.0  \n", "81                       0.918621                     86.0  \n", "104                      0.950014                     87.0  \n", "130                      0.980929                     86.0  \n", "142                      0.973236                     57.0  \n", "143                      0.976137                     86.0  \n", "171                      0.906115                     86.0  \n", "201                      0.969030                     86.0  \n", "273                      0.955767                     86.0  \n", "346                      0.938245                     86.0  \n", "353                      0.710641                     60.0  \n", "472                      0.963761                     50.0  \n", "626                      0.900242                     66.0  \n", "686                      0.973669                     86.0  \n", "704                      0.931985                     89.0  \n", "720                      0.924369                     87.0  \n", "732                      0.999940                     60.0  "]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df[fuzzy_df['FuzzyMatchScore']<90][[ 'AnimalName','AnimalName_BestMatch', 'AnimalName_MatchConfidenceScore',\n", "       'AnimalName_FuzzyMatchScore','VetName', 'VetName_BestMatch',\n", "       'VetName_MatchConfidenceScore', 'VetName_FuzzyMatchScore']]\n", "\n", "fuzzy_df[(fuzzy_df['VetName_FuzzyMatchScore']<90) & (fuzzy_df['VetName'].isnull()==False) & (fuzzy_df['VetName']!='null') ][[ 'FileName','VetName','VetName_BestMatch', 'VetName_MatchConfidenceScore',\n", "       'VetName_FuzzyMatchScore']]"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"text/plain": ["726"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["len(final_df)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>Amount Claimed (OCR)</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "      <th>TreatmentSum</th>\n", "      <th>ClaimInvoiceTotalIncVAT</th>\n", "      <th>ClaimInvoiceTotalIncVAT_BestMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_MatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>35335750-1c07-45d3-ad84-1ca812b22c3b</td>\n", "      <td>830.76</td>\n", "      <td>1012.76</td>\n", "      <td>878.76</td>\n", "      <td>1012.76</td>\n", "      <td>$1,012.76</td>\n", "      <td>0.998551</td>\n", "      <td>80.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>384</th>\n", "      <td>5ef37831-fd22-43ed-8960-95eff7c0bdfe</td>\n", "      <td>1017.50</td>\n", "      <td>1017.50</td>\n", "      <td>1017.50</td>\n", "      <td>1017.50</td>\n", "      <td>1.017.50</td>\n", "      <td>0.966288</td>\n", "      <td>80.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>6b5b0ae6-9c30-40fd-bacb-6031d66d1ba3</td>\n", "      <td>404.70</td>\n", "      <td>404.70</td>\n", "      <td>353.30</td>\n", "      <td>404.70</td>\n", "      <td>162.70</td>\n", "      <td>0.996838</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>517</th>\n", "      <td>798e2953-205a-4c30-835d-0623791d82b8</td>\n", "      <td>NaN</td>\n", "      <td>627.25</td>\n", "      <td>627.25</td>\n", "      <td>627.25</td>\n", "      <td>$15.68</td>\n", "      <td>0.996713</td>\n", "      <td>55.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>592</th>\n", "      <td>8e710413-2be4-4ec9-8f43-23acaa5ef64f</td>\n", "      <td>1117.90</td>\n", "      <td>1117.90</td>\n", "      <td>1117.90</td>\n", "      <td>1058.90</td>\n", "      <td>109</td>\n", "      <td>0.851557</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>667</th>\n", "      <td>9ea1788c-e173-4ff9-822a-4fe59edf7a3e</td>\n", "      <td>NaN</td>\n", "      <td>4404.26</td>\n", "      <td>-530.38</td>\n", "      <td>4003.87</td>\n", "      <td>4,003.87</td>\n", "      <td>0.991711</td>\n", "      <td>80.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>708</th>\n", "      <td>0e4a495c-b6d8-47a5-8fe4-94040434dc96</td>\n", "      <td>NaN</td>\n", "      <td>165.90</td>\n", "      <td>201.13</td>\n", "      <td>165.90</td>\n", "      <td>$6.97</td>\n", "      <td>0.993004</td>\n", "      <td>60.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 FileName  Amount Claimed (OCR)  \\\n", "185  35335750-1c07-45d3-ad84-1ca812b22c3b                830.76   \n", "384  5ef37831-fd22-43ed-8960-95eff7c0bdfe               1017.50   \n", "459  6b5b0ae6-9c30-40fd-bacb-6031d66d1ba3                404.70   \n", "517  798e2953-205a-4c30-835d-0623791d82b8                   NaN   \n", "592  8e710413-2be4-4ec9-8f43-23acaa5ef64f               1117.90   \n", "667  9ea1788c-e173-4ff9-822a-4fe59edf7a3e                   NaN   \n", "708  0e4a495c-b6d8-47a5-8fe4-94040434dc96                   NaN   \n", "\n", "     Amount Claimed (UPM)  TreatmentSum ClaimInvoiceTotalIncVAT  \\\n", "185               1012.76        878.76                 1012.76   \n", "384               1017.50       1017.50                 1017.50   \n", "459                404.70        353.30                  404.70   \n", "517                627.25        627.25                  627.25   \n", "592               1117.90       1117.90                 1058.90   \n", "667               4404.26       -530.38                 4003.87   \n", "708                165.90        201.13                  165.90   \n", "\n", "    ClaimInvoiceTotalIncVAT_BestMatch  \\\n", "185                         $1,012.76   \n", "384                          1.017.50   \n", "459                            162.70   \n", "517                            $15.68   \n", "592                               109   \n", "667                          4,003.87   \n", "708                             $6.97   \n", "\n", "     ClaimInvoiceTotalIncVAT_MatchConfidenceScore  \\\n", "185                                      0.998551   \n", "384                                      0.966288   \n", "459                                      0.996838   \n", "517                                      0.996713   \n", "592                                      0.851557   \n", "667                                      0.991711   \n", "708                                      0.993004   \n", "\n", "     ClaimInvoiceTotalIncVAT_FuzzyMatchScore  \n", "185                                     80.0  \n", "384                                     80.0  \n", "459                                     50.0  \n", "517                                     55.0  \n", "592                                     60.0  \n", "667                                     80.0  \n", "708                                     60.0  "]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# fuzzy_df[(fuzzy_df['ClaimInvoiceTotalIncVAT_FuzzyMatchScore']<90) & (fuzzy_df['ClaimInvoiceTotalIncVAT'].isnull()==False) & (fuzzy_df['ClaimInvoiceTotalIncVAT']!='null') ][[ 'FileName','ClaimInvoiceTotalIncVAT','ClaimInvoiceTotalIncVAT_BestMatch', 'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       # 'ClaimInvoiceTotalIncVAT_FuzzyMatchScore']]\n", "\n", "# & (fuzzy_df['ClaimTmtEqual_check']==1) (fuzzy_df['AmountCheck_1']==1) & \n", "\n", "fuzzy_df[(fuzzy_df['ClaimInvoiceTotalIncVAT_FuzzyMatchScore']<90)  ][[ 'FileName', 'Amount Claimed (OCR)', 'Amount Claimed (UPM)','TreatmentSum','ClaimInvoiceTotalIncVAT','ClaimInvoiceTotalIncVAT_BestMatch', 'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_FuzzyMatchScore']]\n", "\n"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>Amount Claimed (OCR)</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "      <th>TreatmentSum</th>\n", "      <th>ClaimInvoiceTotalIncVAT</th>\n", "      <th>ClaimInvoiceTotalIncVAT_BestMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_MatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>24f71df5-bc4e-4e58-b2f5-a08501048ee6</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1846.97</td>\n", "      <td>1846.97</td>\n", "      <td>$1,846.97</td>\n", "      <td>0.937986</td>\n", "      <td>93.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>187</th>\n", "      <td>35f40d30-efae-494a-a965-45a75b5e2ec0</td>\n", "      <td>137.60</td>\n", "      <td>462.20</td>\n", "      <td>137.60</td>\n", "      <td>137.60</td>\n", "      <td>137.60</td>\n", "      <td>0.999379</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>226</th>\n", "      <td>3d37f895-8822-4691-9495-d5380910bb99</td>\n", "      <td>NaN</td>\n", "      <td>342.35</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>$0.00</td>\n", "      <td>0.999902</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>316</th>\n", "      <td>51c10067-07f0-4444-8c12-faacf643d509</td>\n", "      <td>159.70</td>\n", "      <td>159.70</td>\n", "      <td>264.70</td>\n", "      <td>264.70</td>\n", "      <td>264.70</td>\n", "      <td>0.990535</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>342</th>\n", "      <td>0829cc5c-e252-4727-ad37-b759ef987ae7</td>\n", "      <td>NaN</td>\n", "      <td>202.00</td>\n", "      <td>165.00</td>\n", "      <td>165.00</td>\n", "      <td>$165.00</td>\n", "      <td>0.986815</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354</th>\n", "      <td>57a8846d-897a-4d3b-899c-8d5bcf047e38</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>2000.00</td>\n", "      <td>$2,000.00</td>\n", "      <td>($2,000.00)</td>\n", "      <td>0.962540</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>5beca4bf-cfc0-4d59-834c-ac15914574cc</td>\n", "      <td>301.50</td>\n", "      <td>193.50</td>\n", "      <td>301.50</td>\n", "      <td>301.50</td>\n", "      <td>$301.50</td>\n", "      <td>0.999264</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>419</th>\n", "      <td>65bde9dc-8062-4df8-a027-88da273a5117</td>\n", "      <td>98.41</td>\n", "      <td>0.00</td>\n", "      <td>98.41</td>\n", "      <td>98.41</td>\n", "      <td>$ 98.41</td>\n", "      <td>0.971351</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>466</th>\n", "      <td>6d074e93-8f5c-4667-81d4-4f74f20ed83e</td>\n", "      <td>465.75</td>\n", "      <td>455.75</td>\n", "      <td>465.75</td>\n", "      <td>465.75</td>\n", "      <td>465.75</td>\n", "      <td>0.999391</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>493</th>\n", "      <td>749c9501-2761-419e-9192-5bb4ce28a4c0</td>\n", "      <td>162.50</td>\n", "      <td>162.50</td>\n", "      <td>261.50</td>\n", "      <td>261.50</td>\n", "      <td>261.50</td>\n", "      <td>0.993507</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>510</th>\n", "      <td>77c88571-0e42-4378-a77a-08569f37678b</td>\n", "      <td>1553.30</td>\n", "      <td>2253.30</td>\n", "      <td>1553.30</td>\n", "      <td>1553.30</td>\n", "      <td>1553.30</td>\n", "      <td>0.999420</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>571</th>\n", "      <td>88b71e66-8000-4900-865b-e76919f47da8</td>\n", "      <td>NaN</td>\n", "      <td>176.00</td>\n", "      <td>95.25</td>\n", "      <td>95.25</td>\n", "      <td>$95.25</td>\n", "      <td>0.992234</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>612</th>\n", "      <td>93ff97a2-d331-41f6-8ce2-61711b86cfe9</td>\n", "      <td>126.50</td>\n", "      <td>115.00</td>\n", "      <td>103.50</td>\n", "      <td>103.50</td>\n", "      <td>$103.50</td>\n", "      <td>0.986496</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>633</th>\n", "      <td>997ff95e-f66f-41c5-a20e-c7b7658e81b8</td>\n", "      <td>NaN</td>\n", "      <td>198.70</td>\n", "      <td>163.50</td>\n", "      <td>163.50</td>\n", "      <td>163.50</td>\n", "      <td>0.997086</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>9986f78f-dcbd-40f5-8f46-549c9a8ff38c</td>\n", "      <td>653.55</td>\n", "      <td>893.11</td>\n", "      <td>653.55</td>\n", "      <td>653.55</td>\n", "      <td>653.55</td>\n", "      <td>0.999094</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>658</th>\n", "      <td>0daa51f5-827a-4e46-8009-2c0266d2e46e</td>\n", "      <td>408.00</td>\n", "      <td>448.80</td>\n", "      <td>408.00</td>\n", "      <td>408.00</td>\n", "      <td>408.00</td>\n", "      <td>0.999991</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>662</th>\n", "      <td>9cac954c-9ebd-4465-8e68-9d0f431611eb</td>\n", "      <td>NaN</td>\n", "      <td>254.10</td>\n", "      <td>256.31</td>\n", "      <td>256.31</td>\n", "      <td>256.31</td>\n", "      <td>0.997172</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>717</th>\n", "      <td>10845d55-c224-4775-a5a0-e9aca735ffd4</td>\n", "      <td>151.20</td>\n", "      <td>151.20</td>\n", "      <td>157.20</td>\n", "      <td>157.20</td>\n", "      <td>20</td>\n", "      <td>0.986932</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 FileName  Amount Claimed (OCR)  \\\n", "97   24f71df5-bc4e-4e58-b2f5-a08501048ee6                   NaN   \n", "187  35f40d30-efae-494a-a965-45a75b5e2ec0                137.60   \n", "226  3d37f895-8822-4691-9495-d5380910bb99                   NaN   \n", "316  51c10067-07f0-4444-8c12-faacf643d509                159.70   \n", "342  0829cc5c-e252-4727-ad37-b759ef987ae7                   NaN   \n", "354  57a8846d-897a-4d3b-899c-8d5bcf047e38                   NaN   \n", "370  5beca4bf-cfc0-4d59-834c-ac15914574cc                301.50   \n", "419  65bde9dc-8062-4df8-a027-88da273a5117                 98.41   \n", "466  6d074e93-8f5c-4667-81d4-4f74f20ed83e                465.75   \n", "493  749c9501-2761-419e-9192-5bb4ce28a4c0                162.50   \n", "510  77c88571-0e42-4378-a77a-08569f37678b               1553.30   \n", "571  88b71e66-8000-4900-865b-e76919f47da8                   NaN   \n", "612  93ff97a2-d331-41f6-8ce2-61711b86cfe9                126.50   \n", "633  997ff95e-f66f-41c5-a20e-c7b7658e81b8                   NaN   \n", "634  9986f78f-dcbd-40f5-8f46-549c9a8ff38c                653.55   \n", "658  0daa51f5-827a-4e46-8009-2c0266d2e46e                408.00   \n", "662  9cac954c-9ebd-4465-8e68-9d0f431611eb                   NaN   \n", "717  10845d55-c224-4775-a5a0-e9aca735ffd4                151.20   \n", "\n", "     Amount Claimed (UPM)  TreatmentSum ClaimInvoiceTotalIncVAT  \\\n", "97                    NaN       1846.97                 1846.97   \n", "187                462.20        137.60                  137.60   \n", "226                342.35          0.00                    0.00   \n", "316                159.70        264.70                  264.70   \n", "342                202.00        165.00                  165.00   \n", "354                  0.00       2000.00               $2,000.00   \n", "370                193.50        301.50                  301.50   \n", "419                  0.00         98.41                   98.41   \n", "466                455.75        465.75                  465.75   \n", "493                162.50        261.50                  261.50   \n", "510               2253.30       1553.30                 1553.30   \n", "571                176.00         95.25                   95.25   \n", "612                115.00        103.50                  103.50   \n", "633                198.70        163.50                  163.50   \n", "634                893.11        653.55                  653.55   \n", "658                448.80        408.00                  408.00   \n", "662                254.10        256.31                  256.31   \n", "717                151.20        157.20                  157.20   \n", "\n", "    ClaimInvoiceTotalIncVAT_BestMatch  \\\n", "97                          $1,846.97   \n", "187                            137.60   \n", "226                             $0.00   \n", "316                            264.70   \n", "342                           $165.00   \n", "354                       ($2,000.00)   \n", "370                           $301.50   \n", "419                           $ 98.41   \n", "466                            465.75   \n", "493                            261.50   \n", "510                           1553.30   \n", "571                            $95.25   \n", "612                           $103.50   \n", "633                            163.50   \n", "634                            653.55   \n", "658                            408.00   \n", "662                            256.31   \n", "717                                20   \n", "\n", "     ClaimInvoiceTotalIncVAT_MatchConfidenceScore  \\\n", "97                                       0.937986   \n", "187                                      0.999379   \n", "226                                      0.999902   \n", "316                                      0.990535   \n", "342                                      0.986815   \n", "354                                      0.962540   \n", "370                                      0.999264   \n", "419                                      0.971351   \n", "466                                      0.999391   \n", "493                                      0.993507   \n", "510                                      0.999420   \n", "571                                      0.992234   \n", "612                                      0.986496   \n", "633                                      0.997086   \n", "634                                      0.999094   \n", "658                                      0.999991   \n", "662                                      0.997172   \n", "717                                      0.986932   \n", "\n", "     ClaimInvoiceTotalIncVAT_FuzzyMatchScore  \n", "97                                      93.0  \n", "187                                    100.0  \n", "226                                    100.0  \n", "316                                    100.0  \n", "342                                    100.0  \n", "354                                    100.0  \n", "370                                    100.0  \n", "419                                    100.0  \n", "466                                    100.0  \n", "493                                    100.0  \n", "510                                    100.0  \n", "571                                    100.0  \n", "612                                    100.0  \n", "633                                    100.0  \n", "634                                    100.0  \n", "658                                    100.0  \n", "662                                    100.0  \n", "717                                    100.0  "]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["#  &(fuzzy_df['AmountCheck_1']==1)  &(fuzzy_df['ClaimTmtEqual_check']==1) \n", "fuzzy_df[(fuzzy_df['ClaimInvoiceTotalIncVAT_MatchConfidenceScore']>=0.9) & (fuzzy_df['ClaimTmtEqual_check']==1)&(fuzzy_df['AmountCheck_1']==0) ][[ 'FileName', 'Amount Claimed (OCR)', 'Amount Claimed (UPM)','TreatmentSum','ClaimInvoiceTotalIncVAT','ClaimInvoiceTotalIncVAT_BestMatch', 'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_FuzzyMatchScore']]\n"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>FileName</th>\n", "      <th>DocumentMetadata</th>\n", "      <th>AnimalName</th>\n", "      <th>VetName</th>\n", "      <th>PracticeName</th>\n", "      <th><PERSON><PERSON>ddress</th>\n", "      <th>PracticeABN</th>\n", "      <th>PracticePhoneNumber</th>\n", "      <th>PracticeFaxNumber</th>\n", "      <th>...</th>\n", "      <th>FuzzyMatchScore</th>\n", "      <th>AnimalName_BestMatch</th>\n", "      <th>AnimalName_MatchConfidenceScore</th>\n", "      <th>AnimalName_FuzzyMatchScore</th>\n", "      <th>VetName_BestMatch</th>\n", "      <th>VetName_MatchConfidenceScore</th>\n", "      <th>VetName_FuzzyMatchScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_BestMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_MatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>12915f61-35ca-4af6-976f-80250f39d018</td>\n", "      <td>{'header': 'EVERVET SOUTH YARRA', 'body': ['In...</td>\n", "      <td><PERSON><PERSON><PERSON> (Pipo)</td>\n", "      <td><PERSON></td>\n", "      <td>EVERVET</td>\n", "      <td>66 Toorak Road, South Yarra, Victoria 3141</td>\n", "      <td>0490796561</td>\n", "      <td>Ph:9510 1335</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td>ANIMAL:Bibo (Pipo)</td>\n", "      <td>0.967514</td>\n", "      <td>72.0</td>\n", "      <td><PERSON></td>\n", "      <td>0.966726</td>\n", "      <td>100.0</td>\n", "      <td>$162.50</td>\n", "      <td>0.999142</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>12a700b3-8405-40fa-99a6-1ea2f0f81134</td>\n", "      <td>{'header': 'Kardinia Veterinary Clinic &amp; Anima...</td>\n", "      <td>Bonnie</td>\n", "      <td>Dr <PERSON> B<PERSON></td>\n", "      <td>Kardinia Veterinary Clinic &amp; Animal Hospital</td>\n", "      <td>355 Moorabool Street, Geelong Victoria 3220</td>\n", "      <td>None</td>\n", "      <td>03 5221 5122</td>\n", "      <td>03 5222 3930</td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td>Bonnie</td>\n", "      <td>0.998133</td>\n", "      <td>100.0</td>\n", "      <td>Dr <PERSON> B<PERSON></td>\n", "      <td>0.964711</td>\n", "      <td>100.0</td>\n", "      <td>153.00</td>\n", "      <td>0.997218</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>12ace777-0681-44c8-a267-85f27217e8fe</td>\n", "      <td>{'header': 'PAYMENT RECEIPT', 'body': ['Receip...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>None</td>\n", "      <td>My Family Vet</td>\n", "      <td>141 Canvey Road Upper Kedron, Queensland 4055</td>\n", "      <td>None</td>\n", "      <td>07 3518 4418</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>Patient: 100081 Katness</td>\n", "      <td>0.999622</td>\n", "      <td>48.0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>$285.24</td>\n", "      <td>0.999524</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8</td>\n", "      <td>12ae6ded-0fbf-4073-8b05-794d8e8b8e51</td>\n", "      <td>{'header': 'Vets on Balwyn logo, Practice deta...</td>\n", "      <td>O<PERSON></td>\n", "      <td>Dr <PERSON>c DVM</td>\n", "      <td>Vets on Balwyn</td>\n", "      <td>341 Balwyn Road, North Balwyn VIC 3104</td>\n", "      <td>**************</td>\n", "      <td>P(03) 9857 8100</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td>O<PERSON></td>\n", "      <td>0.994413</td>\n", "      <td>100.0</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>0.974024</td>\n", "      <td>92.0</td>\n", "      <td>137.80</td>\n", "      <td>0.998072</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10</td>\n", "      <td>12b3b224-33c7-416f-8499-52b3bae91115</td>\n", "      <td>{'header': 'Niddrie Vet Clinic, 577 Keilor Rd,...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Niddrie Vet Clinic</td>\n", "      <td>577 Keilor Rd, Niddrie VIC 3042</td>\n", "      <td>**************</td>\n", "      <td>(03) 9379 8913</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>Charges for Le Bron</td>\n", "      <td>0.946105</td>\n", "      <td>54.0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>71.00</td>\n", "      <td>0.992221</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>765</th>\n", "      <td>2604</td>\n", "      <td>1151d46d-890a-4202-8c8f-00c98bd49ff8</td>\n", "      <td>{'header': 'Wellness Centre', 'body': 'Tax Inv...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr <PERSON></td>\n", "      <td>Wellness Centre</td>\n", "      <td>24 PRAIANO AVENUE Berwick VIC 3806</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.996182</td>\n", "      <td>100.0</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>0.997924</td>\n", "      <td>100.0</td>\n", "      <td>165.50</td>\n", "      <td>0.998379</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>2606</td>\n", "      <td>118b605a-a3d1-4459-8abd-74e3306f2cb6</td>\n", "      <td>{'header': 'Maribyrnong Veterinary Clinic, 95a...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Maribyrnong Veterinary Clinic</td>\n", "      <td>95a <PERSON>mond Rd Maidstone Vic 3012</td>\n", "      <td>**************</td>\n", "      <td>(03) 9318 3349</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>90</td>\n", "      <td>Patient: <PERSON></td>\n", "      <td>0.999619</td>\n", "      <td>60.0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>$2,410.51</td>\n", "      <td>0.992525</td>\n", "      <td>93.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>767</th>\n", "      <td>2609</td>\n", "      <td>11b0c020-8d5a-4db4-b89d-edb322d6ea26</td>\n", "      <td>{'header': 'Greencross Vets', 'body': 'Invoice...</td>\n", "      <td>Odin</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>Greencross Vets</td>\n", "      <td>13 Marshall Lane, Kenmore QLD 4069</td>\n", "      <td>**************</td>\n", "      <td>07 3378 7188</td>\n", "      <td>07 3378 7171</td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td>Odin</td>\n", "      <td>0.999330</td>\n", "      <td>100.0</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>0.989626</td>\n", "      <td>100.0</td>\n", "      <td>117.50</td>\n", "      <td>0.962667</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>768</th>\n", "      <td>2611</td>\n", "      <td>11c06a3c-1980-46b8-8795-7a4c1a189b3f</td>\n", "      <td>{'header': 'ANIMAL EMERGENCY SERVICE', 'body':...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr <PERSON></td>\n", "      <td>ANIMAL EMERGENCY SERVICE</td>\n", "      <td>Cnr Lexington &amp; Logan Roads, Underwood QLD 4119</td>\n", "      <td>**************</td>\n", "      <td>***********</td>\n", "      <td>***********</td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.998048</td>\n", "      <td>100.0</td>\n", "      <td>Dr <PERSON></td>\n", "      <td>0.931505</td>\n", "      <td>100.0</td>\n", "      <td>$473.85</td>\n", "      <td>0.984124</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>769</th>\n", "      <td>2617</td>\n", "      <td>12268c80-e3fe-4485-a607-5091d9bb82b3</td>\n", "      <td>{'header': 'WAVES (WA Vet Emergency &amp; Specialt...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>WAVES (WA Vet Emergency &amp; Specialty)</td>\n", "      <td>1/640 Beeliar Drive, Success, WA 6164</td>\n", "      <td>**************</td>\n", "      <td>9412 5700</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>100</td>\n", "      <td>Patient Name: <PERSON><PERSON></td>\n", "      <td>0.989392</td>\n", "      <td>48.0</td>\n", "      <td>Dr. <PERSON><PERSON></td>\n", "      <td>0.984850</td>\n", "      <td>100.0</td>\n", "      <td>$262.00</td>\n", "      <td>0.999982</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>770 rows × 99 columns</p>\n", "</div>"], "text/plain": ["     index                              FileName  \\\n", "0        0  12915f61-35ca-4af6-976f-80250f39d018   \n", "1        2  12a700b3-8405-40fa-99a6-1ea2f0f81134   \n", "2        3  12ace777-0681-44c8-a267-85f27217e8fe   \n", "3        8  12ae6ded-0fbf-4073-8b05-794d8e8b8e51   \n", "4       10  12b3b224-33c7-416f-8499-52b3bae91115   \n", "..     ...                                   ...   \n", "765   2604  1151d46d-890a-4202-8c8f-00c98bd49ff8   \n", "766   2606  118b605a-a3d1-4459-8abd-74e3306f2cb6   \n", "767   2609  11b0c020-8d5a-4db4-b89d-edb322d6ea26   \n", "768   2611  11c06a3c-1980-46b8-8795-7a4c1a189b3f   \n", "769   2617  12268c80-e3fe-4485-a607-5091d9bb82b3   \n", "\n", "                                      DocumentMetadata   AnimalName  \\\n", "0    {'header': 'EVERVET SOUTH YARRA', 'body': ['In...  <PERSON><PERSON>o (Pipo)   \n", "1    {'header': 'Kardinia Veterinary Clinic & Anima...       Bonnie   \n", "2    {'header': 'PAYMENT RECEIPT', 'body': ['Receip...      Katness   \n", "3    {'header': 'Vets on Balwyn logo, Practice deta...        Ollie   \n", "4    {'header': 'Niddrie Vet Clinic, 577 Keilor Rd,...      <PERSON>   \n", "..                                                 ...          ...   \n", "765  {'header': 'Wellness Centre', 'body': 'Tax Inv...         Ludo   \n", "766  {'header': 'Maribyrnong Veterinary Clinic, 95a...       Archie   \n", "767  {'header': 'Greencross Vets', 'body': 'Invoice...         Odin   \n", "768  {'header': 'ANIMAL EMERGENCY SERVICE', 'body':...         Yogi   \n", "769  {'header': 'WAVES (WA Vet Emergency & Specialt...       <PERSON><PERSON>   \n", "\n", "                         VetName  \\\n", "0                     May <PERSON>   \n", "1         <PERSON> <PERSON>   \n", "2                           None   \n", "3    <PERSON> <PERSON> BSc DVM   \n", "4                           None   \n", "..                           ...   \n", "765          <PERSON> <PERSON>   \n", "766                         None   \n", "767                 <PERSON>   \n", "768            <PERSON> <PERSON>   \n", "769             <PERSON><PERSON> <PERSON><PERSON>   \n", "\n", "                                     PracticeName  \\\n", "0                                         EVERVET   \n", "1    Kardinia Veterinary Clinic & Animal Hospital   \n", "2                                   My Family Vet   \n", "3                                  Vets on Balwyn   \n", "4                              Niddrie Vet Clinic   \n", "..                                            ...   \n", "765                               Wellness Centre   \n", "766                 Maribyrnong Veterinary Clinic   \n", "767                               Greencross Vets   \n", "768                      ANIMAL EMERGENCY SERVICE   \n", "769          WAVES (WA Vet Emergency & Specialty)   \n", "\n", "                                     PracticeAddress     PracticeABN  \\\n", "0         66 Toorak Road, South Yarra, Victoria 3141      0490796561   \n", "1        355 Moorabool Street, Geelong Victoria 3220            None   \n", "2      141 Canvey Road Upper Kedron, Queensland 4055            None   \n", "3             341 Balwyn Road, North Balwyn VIC 3104  **************   \n", "4                    577 Keilor Rd, Niddrie VIC 3042  **************   \n", "..                                               ...             ...   \n", "765               24 PRAIANO AVENUE Berwick VIC 3806            None   \n", "766               95a Rosamond Rd Maidstone Vic 3012  **************   \n", "767               13 <PERSON>, Kenmore QLD 4069  **************   \n", "768  Cnr Lexington & Logan Roads, Underwood QLD 4119  **************   \n", "769            1/640 Beeliar Drive, Success, WA 6164  **************   \n", "\n", "    PracticePhoneNumber PracticeFaxNumber  ... FuzzyMatchScore  \\\n", "0          Ph:9510 1335              None  ...             100   \n", "1          03 5221 5122      03 5222 3930  ...             100   \n", "2          07 3518 4418              None  ...              90   \n", "3       P(03) 9857 8100              None  ...             100   \n", "4        (03) 9379 8913              None  ...              90   \n", "..                  ...               ...  ...             ...   \n", "765                None              None  ...             100   \n", "766      (03) 9318 3349              None  ...              90   \n", "767        07 3378 7188      07 3378 7171  ...             100   \n", "768         ***********       ***********  ...             100   \n", "769           9412 5700                    ...             100   \n", "\n", "        AnimalName_BestMatch AnimalName_MatchConfidenceScore  \\\n", "0         ANIMAL:Bibo (Pipo)                        0.967514   \n", "1                     Bonnie                        0.998133   \n", "2    Patient: 100081 Katness                        0.999622   \n", "3                      <PERSON><PERSON>                        0.994413   \n", "4        Charges for Le Bron                        0.946105   \n", "..                       ...                             ...   \n", "765                     Ludo                        0.996182   \n", "766          Patient: <PERSON>                        0.999619   \n", "767                     <PERSON>din                        0.999330   \n", "768                     Yogi                        0.998048   \n", "769     Patient Name: Hu<PERSON>                        0.989392   \n", "\n", "    AnimalName_FuzzyMatchScore        VetName_BestMatch  \\\n", "0                         72.0               May <PERSON>   \n", "1                        100.0   <PERSON> <PERSON>   \n", "2                         48.0                     None   \n", "3                        100.0  <PERSON>   \n", "4                         54.0                     None   \n", "..                         ...                      ...   \n", "765                      100.0      <PERSON>   \n", "766                       60.0                     None   \n", "767                      100.0             <PERSON>   \n", "768                      100.0        <PERSON>   \n", "769                       48.0         <PERSON><PERSON> <PERSON><PERSON>   \n", "\n", "    VetName_MatchConfidenceScore VetName_FuzzyMatchScore  \\\n", "0                       0.966726                   100.0   \n", "1                       0.964711                   100.0   \n", "2                            NaN                     NaN   \n", "3                       0.974024                    92.0   \n", "4                            NaN                     NaN   \n", "..                           ...                     ...   \n", "765                     0.997924                   100.0   \n", "766                          NaN                     NaN   \n", "767                     0.989626                   100.0   \n", "768                     0.931505                   100.0   \n", "769                     0.984850                   100.0   \n", "\n", "    ClaimInvoiceTotalIncVAT_BestMatch  \\\n", "0                             $162.50   \n", "1                              153.00   \n", "2                             $285.24   \n", "3                              137.80   \n", "4                               71.00   \n", "..                                ...   \n", "765                            165.50   \n", "766                         $2,410.51   \n", "767                            117.50   \n", "768                           $473.85   \n", "769                           $262.00   \n", "\n", "    ClaimInvoiceTotalIncVAT_MatchConfidenceScore  \\\n", "0                                       0.999142   \n", "1                                       0.997218   \n", "2                                       0.999524   \n", "3                                       0.998072   \n", "4                                       0.992221   \n", "..                                           ...   \n", "765                                     0.998379   \n", "766                                     0.992525   \n", "767                                     0.962667   \n", "768                                     0.984124   \n", "769                                     0.999982   \n", "\n", "    ClaimInvoiceTotalIncVAT_FuzzyMatchScore  \n", "0                                     100.0  \n", "1                                     100.0  \n", "2                                     100.0  \n", "3                                     100.0  \n", "4                                     100.0  \n", "..                                      ...  \n", "765                                   100.0  \n", "766                                    93.0  \n", "767                                   100.0  \n", "768                                   100.0  \n", "769                                   100.0  \n", "\n", "[770 rows x 99 columns]"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df[(fuzzy_df['ClaimInvoiceTotalIncVAT_MatchConfidenceScore']>=0.9) & (fuzzy_df['ClaimTmtEqual_check']==1)&(fuzzy_df['AmountCheck_1']==0) ]"]}, {"cell_type": "code", "execution_count": 180, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>FileName</th>\n", "      <th>DocumentMetadata</th>\n", "      <th>AnimalName</th>\n", "      <th>VetName</th>\n", "      <th>PracticeName</th>\n", "      <th><PERSON><PERSON>ddress</th>\n", "      <th>PracticeABN</th>\n", "      <th>PracticePhoneNumber</th>\n", "      <th>PracticeFaxNumber</th>\n", "      <th>...</th>\n", "      <th>AnimalName_FuzzyMatchScore</th>\n", "      <th>VetName_BestMatch</th>\n", "      <th>VetName_MatchConfidenceScore</th>\n", "      <th>VetName_FuzzyMatchScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_BestMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_MatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_FuzzyMatchScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_clean_ExactMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_clean_ExactMatchIndicator</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>540</td>\n", "      <td>339b87f3-1cb8-4add-8106-0bacb1d18eac</td>\n", "      <td>{'header': 'ORIGINAL TAX INVOICE/STATEMENT', '...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>HOME VISITING VET</td>\n", "      <td>PO BOX 445 ELTHAM VIC 3095</td>\n", "      <td>***********</td>\n", "      <td>0402 055 003</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>40.0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>528</td>\n", "      <td>0.623061</td>\n", "      <td>100.0</td>\n", "      <td>528</td>\n", "      <td>0.623061</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>646</td>\n", "      <td>39fec0f4-e3e8-4e53-9cc4-10c4e74e30b4</td>\n", "      <td>{'header': 'BURWOOD ROAD CLINIC, Dr. Canada A....</td>\n", "      <td>Motea</td>\n", "      <td>Dr. <PERSON> <PERSON><PERSON> BVSc. M.A.C.V.Sc.</td>\n", "      <td>BURWOOD ROAD CLINIC</td>\n", "      <td>98 Burwood Rd, Hawthorn 3122</td>\n", "      <td>**************</td>\n", "      <td>9819 1468</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>M.A.C.V.Sc.</td>\n", "      <td>0.986370</td>\n", "      <td>100.0</td>\n", "      <td>867</td>\n", "      <td>0.850070</td>\n", "      <td>100.0</td>\n", "      <td>867</td>\n", "      <td>0.850070</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>455</th>\n", "      <td>1543</td>\n", "      <td>6ab4f7cc-7e87-4ce7-9f09-0ecb02798c0e</td>\n", "      <td>{'header': 'CEIPT Waverley Gardens Vet', 'body...</td>\n", "      <td>ANNIgher</td>\n", "      <td>Waverley Gardens Vet</td>\n", "      <td>Waverley Gardens Veterinary Hospital</td>\n", "      <td>Police Road Mulgrave, Victoria 3170</td>\n", "      <td>None</td>\n", "      <td>(03) 9547 7877</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>Waverley Gardens</td>\n", "      <td>0.957413</td>\n", "      <td>100.0</td>\n", "      <td>AUD217.45</td>\n", "      <td>0.976733</td>\n", "      <td>100.0</td>\n", "      <td>UD217.45</td>\n", "      <td>0.895276</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>566</th>\n", "      <td>1931</td>\n", "      <td>878193f7-aef4-443b-adc2-a6ad172036a3</td>\n", "      <td>{'header': 'Compassionate Veterinary Medicine,...</td>\n", "      <td>opo</td>\n", "      <td>Dr.<PERSON><PERSON>b <PERSON></td>\n", "      <td>Compassionate Veterinary Medicine</td>\n", "      <td>14 Elmhurst Drive, Clarkson WA 6030</td>\n", "      <td>ABN99226194265</td>\n", "      <td>0403832446</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>Dr.<PERSON><PERSON>b <PERSON></td>\n", "      <td>0.891243</td>\n", "      <td>100.0</td>\n", "      <td>$70</td>\n", "      <td>0.532004</td>\n", "      <td>100.0</td>\n", "      <td>$70</td>\n", "      <td>0.532004</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 99 columns</p>\n", "</div>"], "text/plain": ["     index                              FileName  \\\n", "177    540  339b87f3-1cb8-4add-8106-0bacb1d18eac   \n", "207    646  39fec0f4-e3e8-4e53-9cc4-10c4e74e30b4   \n", "455   1543  6ab4f7cc-7e87-4ce7-9f09-0ecb02798c0e   \n", "566   1931  878193f7-aef4-443b-adc2-a6ad172036a3   \n", "\n", "                                      DocumentMetadata AnimalName  \\\n", "177  {'header': 'ORIGINAL TAX INVOICE/STATEMENT', '...     <PERSON>   \n", "207  {'header': 'BURWOOD ROAD CLINIC, Dr. Canada A....      Motea   \n", "455  {'header': 'CEIPT Waverley Gardens Vet', 'body...   ANNIgher   \n", "566  {'header': 'Compassionate Veterinary Medicine,...        opo   \n", "\n", "                                    VetName  \\\n", "177                                    None   \n", "207  Dr. <PERSON> <PERSON><PERSON> BVSc. M.A.C.V.Sc.   \n", "455                    Waverley Gardens Vet   \n", "566                             Dr.Dtb NooK   \n", "\n", "                             PracticeName  \\\n", "177                     HOME VISITING VET   \n", "207                   BURWOOD ROAD CLINIC   \n", "455  Waverley Gardens Veterinary Hospital   \n", "566     Compassionate Veterinary Medicine   \n", "\n", "                         PracticeAddress     PracticeABN PracticePhoneNumber  \\\n", "177           PO BOX 445 ELTHAM VIC 3095     ***********        0402 055 003   \n", "207         98 Burwood Rd, Hawthorn 3122  **************           9819 1468   \n", "455  Police Road Mulgrave, Victoria 3170            None      (03) 9547 7877   \n", "566  14 Elmhurst Drive, Clarkson WA 6030  ABN99226194265          0403832446   \n", "\n", "    PracticeFaxNumber  ... AnimalName_FuzzyMatchScore VetName_BestMatch  \\\n", "177              None  ...                       40.0              None   \n", "207              None  ...                      100.0       M.A.C.V.Sc.   \n", "455              None  ...                      100.0  Waverley Gardens   \n", "566              None  ...                      100.0       Dr.Dtb NooK   \n", "\n", "    VetName_MatchConfidenceScore VetName_FuzzyMatchScore  \\\n", "177                          NaN                     NaN   \n", "207                     0.986370                   100.0   \n", "455                     0.957413                   100.0   \n", "566                     0.891243                   100.0   \n", "\n", "    ClaimInvoiceTotalIncVAT_BestMatch  \\\n", "177                               528   \n", "207                               867   \n", "455                         AUD217.45   \n", "566                               $70   \n", "\n", "    ClaimInvoiceTotalIncVAT_MatchConfidenceScore  \\\n", "177                                     0.623061   \n", "207                                     0.850070   \n", "455                                     0.976733   \n", "566                                     0.532004   \n", "\n", "    ClaimInvoiceTotalIncVAT_FuzzyMatchScore  \\\n", "177                                   100.0   \n", "207                                   100.0   \n", "455                                   100.0   \n", "566                                   100.0   \n", "\n", "    ClaimInvoiceTotalIncVAT_clean_ExactMatch  \\\n", "177                                      528   \n", "207                                      867   \n", "455                                 UD217.45   \n", "566                                      $70   \n", "\n", "    ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore  \\\n", "177                                           0.623061        \n", "207                                           0.850070        \n", "455                                           0.895276        \n", "566                                           0.532004        \n", "\n", "    ClaimInvoiceTotalIncVAT_clean_ExactMatchIndicator  \n", "177                                              True  \n", "207                                              True  \n", "455                                              True  \n", "566                                              True  \n", "\n", "[4 rows x 99 columns]"]}, "execution_count": 180, "metadata": {}, "output_type": "execute_result"}], "source": ["final_df[(final_df['ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore']<0.9)]"]}, {"cell_type": "code", "execution_count": 177, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>Amount Claimed (OCR)</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "      <th>TreatmentSum</th>\n", "      <th>ClaimInvoiceTotalIncVAT</th>\n", "      <th>ClaimInvoiceTotalIncVAT_clean_ExactMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_FuzzyMatchScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_BestMatch</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>517</th>\n", "      <td>798e2953-205a-4c30-835d-0623791d82b8</td>\n", "      <td>NaN</td>\n", "      <td>627.25</td>\n", "      <td>627.25</td>\n", "      <td>627.25</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>55.0</td>\n", "      <td>$15.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>678</th>\n", "      <td>a19275e2-c213-445f-b04b-b6c08b28e030</td>\n", "      <td>240.91</td>\n", "      <td>265.00</td>\n", "      <td>265.00</td>\n", "      <td>265.00</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>100.0</td>\n", "      <td>-265.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 FileName  Amount Claimed (OCR)  \\\n", "517  798e2953-205a-4c30-835d-0623791d82b8                   NaN   \n", "678  a19275e2-c213-445f-b04b-b6c08b28e030                240.91   \n", "\n", "     Amount Claimed (UPM)  TreatmentSum ClaimInvoiceTotalIncVAT  \\\n", "517                627.25        627.25                  627.25   \n", "678                265.00        265.00                  265.00   \n", "\n", "    ClaimInvoiceTotalIncVAT_clean_ExactMatch  \\\n", "517                                     None   \n", "678                                     None   \n", "\n", "     ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore  \\\n", "517                                                NaN         \n", "678                                                NaN         \n", "\n", "     ClaimInvoiceTotalIncVAT_FuzzyMatchScore ClaimInvoiceTotalIncVAT_BestMatch  \n", "517                                     55.0                            $15.68  \n", "678                                    100.0                           -265.00  "]}, "execution_count": 177, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# fuzzy_df[(fuzzy_df['ClaimInvoiceTotalIncVAT_FuzzyMatchScore']<90) & (fuzzy_df['ClaimInvoiceTotalIncVAT'].isnull()==False) & (fuzzy_df['ClaimInvoiceTotalIncVAT']!='null') ][[ 'FileName','ClaimInvoiceTotalIncVAT','ClaimInvoiceTotalIncVAT_BestMatch', 'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       # 'ClaimInvoiceTotalIncVAT_FuzzyMatchScore']]\n", "\n", "# & (fuzzy_df['ClaimTmtEqual_check']==1) (fuzzy_df['AmountCheck_1']==1) & \n", "\n", "final_df[(final_df['ClaimInvoiceTotalIncVAT_clean_ExactMatchIndicator']==0) &(final_df['AmountCheck_1']==1) &(final_df['ClaimTmtEqual_check']==1)][[ 'FileName', 'Amount Claimed (OCR)', 'Amount Claimed (UPM)','TreatmentSum','ClaimInvoiceTotalIncVAT','ClaimInvoiceTotalIncVAT_clean_ExactMatch',\n", "       'ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_FuzzyMatchScore','ClaimInvoiceTotalIncVAT_BestMatch']]\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Confidence Score > 0.8"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["266"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["len(fuzzy_df_08)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FileName</th>\n", "      <th>Amount Claimed (OCR)</th>\n", "      <th>Amount Claimed (UPM)</th>\n", "      <th>TreatmentSum</th>\n", "      <th>ClaimInvoiceTotalIncVAT</th>\n", "      <th>ClaimInvoiceTotalIncVAT_BestMatch</th>\n", "      <th>ClaimInvoiceTotalIncVAT_MatchConfidenceScore</th>\n", "      <th>ClaimInvoiceTotalIncVAT_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>12a700b3-8405-40fa-99a6-1ea2f0f81134</td>\n", "      <td>153.00</td>\n", "      <td>153.00</td>\n", "      <td>153.00</td>\n", "      <td>153.00</td>\n", "      <td>153.00</td>\n", "      <td>0.997218</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>12ae6ded-0fbf-4073-8b05-794d8e8b8e51</td>\n", "      <td>137.80</td>\n", "      <td>137.80</td>\n", "      <td>137.80</td>\n", "      <td>137.80</td>\n", "      <td>137.80</td>\n", "      <td>0.998072</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1308dbb6-e5c1-46a5-90d1-1694cdf145f8</td>\n", "      <td>227.70</td>\n", "      <td>227.70</td>\n", "      <td>227.70</td>\n", "      <td>227.70</td>\n", "      <td>227.70</td>\n", "      <td>0.999947</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>14075600-1a42-4a5c-8e19-3757ef966018</td>\n", "      <td>255.00</td>\n", "      <td>255.00</td>\n", "      <td>255.00</td>\n", "      <td>255.00</td>\n", "      <td>255.00</td>\n", "      <td>0.999919</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>1451437a-b4e3-411d-bea9-035754733985</td>\n", "      <td>639.25</td>\n", "      <td>639.25</td>\n", "      <td>639.25</td>\n", "      <td>639.25</td>\n", "      <td>639.25</td>\n", "      <td>0.999995</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745</th>\n", "      <td>a73fc9ba-6a2b-4a0a-b902-c5d44fb9345f</td>\n", "      <td>154.13</td>\n", "      <td>154.13</td>\n", "      <td>154.13</td>\n", "      <td>$154.13</td>\n", "      <td>$154.13</td>\n", "      <td>0.999968</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>746</th>\n", "      <td>a76f4e08-a28c-43f0-bc00-0593bff5d17d</td>\n", "      <td>424.00</td>\n", "      <td>424.00</td>\n", "      <td>424.00</td>\n", "      <td>424.00</td>\n", "      <td>424.00</td>\n", "      <td>0.998709</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>755</th>\n", "      <td>0f56ef52-3f4f-4729-81b0-22acf8a100af</td>\n", "      <td>72.95</td>\n", "      <td>72.95</td>\n", "      <td>72.95</td>\n", "      <td>$72.95</td>\n", "      <td>$ 72.95</td>\n", "      <td>0.997620</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>758</th>\n", "      <td>0fce4b79-b6e8-4262-bd2f-0aa0bf501474</td>\n", "      <td>230.10</td>\n", "      <td>230.10</td>\n", "      <td>230.10</td>\n", "      <td>230.10</td>\n", "      <td>-230.10</td>\n", "      <td>0.991895</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>768</th>\n", "      <td>11c06a3c-1980-46b8-8795-7a4c1a189b3f</td>\n", "      <td>473.85</td>\n", "      <td>473.85</td>\n", "      <td>473.85</td>\n", "      <td>473.85</td>\n", "      <td>$473.85</td>\n", "      <td>0.984124</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>242 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                                 FileName  Amount Claimed (OCR)  \\\n", "1    12a700b3-8405-40fa-99a6-1ea2f0f81134                153.00   \n", "3    12ae6ded-0fbf-4073-8b05-794d8e8b8e51                137.80   \n", "7    1308dbb6-e5c1-46a5-90d1-1694cdf145f8                227.70   \n", "11   14075600-1a42-4a5c-8e19-3757ef966018                255.00   \n", "12   1451437a-b4e3-411d-bea9-035754733985                639.25   \n", "..                                    ...                   ...   \n", "745  a73fc9ba-6a2b-4a0a-b902-c5d44fb9345f                154.13   \n", "746  a76f4e08-a28c-43f0-bc00-0593bff5d17d                424.00   \n", "755  0f56ef52-3f4f-4729-81b0-22acf8a100af                 72.95   \n", "758  0fce4b79-b6e8-4262-bd2f-0aa0bf501474                230.10   \n", "768  11c06a3c-1980-46b8-8795-7a4c1a189b3f                473.85   \n", "\n", "     Amount Claimed (UPM)  TreatmentSum ClaimInvoiceTotalIncVAT  \\\n", "1                  153.00        153.00                  153.00   \n", "3                  137.80        137.80                  137.80   \n", "7                  227.70        227.70                  227.70   \n", "11                 255.00        255.00                  255.00   \n", "12                 639.25        639.25                  639.25   \n", "..                    ...           ...                     ...   \n", "745                154.13        154.13                 $154.13   \n", "746                424.00        424.00                  424.00   \n", "755                 72.95         72.95                  $72.95   \n", "758                230.10        230.10                  230.10   \n", "768                473.85        473.85                  473.85   \n", "\n", "    ClaimInvoiceTotalIncVAT_BestMatch  \\\n", "1                              153.00   \n", "3                              137.80   \n", "7                              227.70   \n", "11                             255.00   \n", "12                             639.25   \n", "..                                ...   \n", "745                           $154.13   \n", "746                            424.00   \n", "755                           $ 72.95   \n", "758                           -230.10   \n", "768                           $473.85   \n", "\n", "     ClaimInvoiceTotalIncVAT_MatchConfidenceScore  \\\n", "1                                        0.997218   \n", "3                                        0.998072   \n", "7                                        0.999947   \n", "11                                       0.999919   \n", "12                                       0.999995   \n", "..                                            ...   \n", "745                                      0.999968   \n", "746                                      0.998709   \n", "755                                      0.997620   \n", "758                                      0.991895   \n", "768                                      0.984124   \n", "\n", "     ClaimInvoiceTotalIncVAT_FuzzyMatchScore  \n", "1                                      100.0  \n", "3                                      100.0  \n", "7                                      100.0  \n", "11                                     100.0  \n", "12                                     100.0  \n", "..                                       ...  \n", "745                                    100.0  \n", "746                                    100.0  \n", "755                                    100.0  \n", "758                                    100.0  \n", "768                                    100.0  \n", "\n", "[242 rows x 8 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df_08 = fuzzy_df[(fuzzy_df['Document Confidence']>=0.8) & (fuzzy_df['AI Type']>=1)]  \n", "\n", "# (fuzzy_df['AmountCheck_1']==1) & (fuzzy_df['ClaimTmtEqual_check']==1) & \n", "fuzzy_df_08[(fuzzy_df_08['ClaimInvoiceTotalIncVAT_MatchConfidenceScore']>=0.9) & (fuzzy_df_08['AmountCheck_4']==1)][[ 'FileName', 'Amount Claimed (OCR)', 'Amount Claimed (UPM)','TreatmentSum','ClaimInvoiceTotalIncVAT','ClaimInvoiceTotalIncVAT_BestMatch', 'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_FuzzyMatchScore']]"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<PERSON>', '<PERSON>']"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["#[['AnimalName', 'VetName','Text', 'Score']]\n", "invoice_merge[['AnimalName']].iloc[35][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Invoice Number"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["upm_invno = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\UPM_010324_treatment.xlsx')\n", "claim_invno = pd.read_csv(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_invocieno.csv')"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['ClaimNo', 'Invoice Number'], dtype='object')"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["claim_invno.columns"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["def group_and_concatenate_unique(df, group_col, concat_col):\n", "    # Group by the specified column and concatenate unique values of the other column as a list\n", "    result_df = df.groupby(group_col)[concat_col].agg(lambda x: list(set(x))).reset_index()\n", "    return result_df\n", "\n", "def preprocess_upm_invoice_no(df):\n", "    upm_invoice_no = group_and_concatenate_unique(df, 'ClaimNo', 'Invoice Number')\n", "\n", "    return upm_invoice_no\n", "\n", "claim_invoice_no = preprocess_upm_invoice_no(claim_invno)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# ## old table raw treatment\n", "def group_and_concatenate_unique(df, group_col, concat_col):\n", "    # Group by the specified column and concatenate unique values of the other column as a list\n", "    result_df = df.groupby(group_col)[concat_col].agg(lambda x: list(set(x))).reset_index()\n", "    return result_df\n", "\n", "def preprocess_upm_invoice_no(df):\n", "    # df['Date Treatment'] = df['Date Treatment'].apply(lambda x: standardize_and_extract_date(x))\n", "    upm_invoice_no = group_and_concatenate_unique(df, 'ClaimNo', 'Invoice Number')\n", "    upm_tmt= group_and_concatenate_unique(df, ['ClaimNo','Invoice Number'], [ 'Service Provider Name', 'Date Treatment',\n", "       'Treatment Drug Description', 'Amount Inc Vat'])\n", "\n", "    return upm_invoice_no, upm_tmt\n", "\n", "upm_invoice_no, upm_tmt = preprocess_upm_invoice_no(upm_invno)\n", "\n", "\n", "\n", "# def compare_multiple_columns_on_key(df1, df2, key, cols_df1, cols_df2):\n", "#     # Validate input\n", "#     if not all(k in df1.columns and k in df2.columns for k in key):\n", "#         raise ValueError(f\"One or more key columns '{key}' not found in the dataframes\")\n", "#     if len(cols_df1) != len(cols_df2):\n", "#         raise ValueError(\"Lists cols_df1 and cols_df2 must have the same length\")\n", "\n", "#     # Validate column existence\n", "#     for col in cols_df1:\n", "#         if col not in df1.columns:\n", "#             raise ValueError(f\"Column '{col}' not found in df1\")\n", "#     for col in cols_df2:\n", "#         if col not in df2.columns:\n", "#             raise ValueError(f\"Column '{col}' not found in df2\")\n", "\n", "#     # Merge df1 and df2 on the key column(s)\n", "#     merged_df = pd.merge(df1, df2[key + cols_df2], on=key, how='left')\n", "\n", "#     # Iterate over the column pairs and perform comparisons\n", "#     for col_df1, col_df2 in zip(cols_df1, cols_df2):\n", "#         # Comparison function\n", "#         def compare(row):\n", "#             df1_val = row[col_df1]\n", "#             df2_val = row[col_df2]\n", "            \n", "#             # Handle null or None values in df1\n", "#             if pd.isnull(df1_val):\n", "#                 return None\n", "#             return df1_val in df2_val if isinstance(df2_val, list) else df1_val == df2_val\n", "\n", "#         # Apply comparison for each row and store the result\n", "#         df1[f'{col_df1}_check'] = merged_df.apply(compare, axis=1)\n", "\n", "#     return df1\n", "\n", "# # Example usage\n", "# # result_df_merge and upm_tmt are your dataframes\n", "# keys = ['ClaimNo', 'InvoiceNo']\n", "# df1_check = ['TreatmentDate', 'Description', 'TreatmentAmountIncVAT', 'TreatmentAmountExVAT', 'TreatmentVAT', 'TreatmentDiscountExVAT']\n", "# df2_check = ['DateTreatment', 'TreatmentDrugRaw', 'AmountInclVat', 'AmountExclVat', 'AmountVat', 'AmountDiscount']\n", "# compare_df = compare_multiple_columns_on_key(result_df_merge, upm_tmt, keys, df1_check, df2_check)\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ClaimNo</th>\n", "      <th>Invoice Number</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>********</td>\n", "      <td>[1000273]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>********</td>\n", "      <td>[509290]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>[3584817, 3584818]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>********</td>\n", "      <td>[689443]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>********</td>\n", "      <td>[689443]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>693</th>\n", "      <td>C6944666</td>\n", "      <td>[742843]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>694</th>\n", "      <td>********</td>\n", "      <td>[147204]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>695</th>\n", "      <td>C6944677</td>\n", "      <td>[978441]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>696</th>\n", "      <td>C6944679</td>\n", "      <td>[54566]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>697</th>\n", "      <td>C6944682</td>\n", "      <td>[400285]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>698 rows × 2 columns</p>\n", "</div>"], "text/plain": ["      ClaimNo      Invoice Number\n", "0    ********           [1000273]\n", "1    ********            [509290]\n", "2    ********  [3584817, 3584818]\n", "3    ********            [689443]\n", "4    ********            [689443]\n", "..        ...                 ...\n", "693  C6944666            [742843]\n", "694  ********            [147204]\n", "695  C6944677            [978441]\n", "696  C6944679             [54566]\n", "697  C6944682            [400285]\n", "\n", "[698 rows x 2 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["upm_invoice_no"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['index', 'FileName', 'DocumentMetadata', 'AnimalName', 'VetName',\n", "       'PracticeName', 'PracticeAddress', 'PracticeABN', 'PracticePhoneNumber',\n", "       'PracticeFaxNumber',\n", "       ...\n", "       'ClaimInvoiceTotalIncVAT_BestMatch',\n", "       'ClaimInvoiceTotalIncVAT_MatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_FuzzyMatchScore', 'InvoiceNo_BestMatch',\n", "       'InvoiceNo_MatchConfidenceScore', 'InvoiceNo_FuzzyMatchScore',\n", "       'ClaimInvoiceTotalIncVAT_clean_ExactMatch',\n", "       'ClaimInvoiceTotalIncVAT_clean_ExactMatchConfidenceScore',\n", "       'ClaimInvoiceTotalIncVAT_clean_ExactMatchIndicator', 'Amount_Score'],\n", "      dtype='object', length=103)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df.columns\n", "# ['InvoiceIndex', 'InvoiceNo','Claim Number']"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>InvoiceIndex</th>\n", "      <th>InvoiceNo</th>\n", "      <th>Claim Number</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1660010</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1147504</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>#601065</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>754120</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>470341</td>\n", "      <td>C6941086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>765</th>\n", "      <td>1</td>\n", "      <td>1052130</td>\n", "      <td>C6943403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>1</td>\n", "      <td>150906</td>\n", "      <td>C6942648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>767</th>\n", "      <td>1</td>\n", "      <td>64340080</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>768</th>\n", "      <td>1</td>\n", "      <td>1356465</td>\n", "      <td>********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>769</th>\n", "      <td>1</td>\n", "      <td>147204</td>\n", "      <td>********</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>770 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    InvoiceIndex InvoiceNo Claim Number\n", "0              1   1660010     ********\n", "1              1   1147504     ********\n", "2              1   #601065     ********\n", "3              1    754120     ********\n", "4              1    470341     C6941086\n", "..           ...       ...          ...\n", "765            1   1052130     C6943403\n", "766            1    150906     C6942648\n", "767            1  64340080     ********\n", "768            1   1356465     ********\n", "769            1    147204     ********\n", "\n", "[770 rows x 3 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzzy_df[['InvoiceIndex', 'InvoiceNo','Claim Number']]"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>FileName</th>\n", "      <th>DocumentMetadata</th>\n", "      <th>AnimalName</th>\n", "      <th>VetName</th>\n", "      <th>PracticeName</th>\n", "      <th><PERSON><PERSON>ddress</th>\n", "      <th>PracticeABN</th>\n", "      <th>PracticePhoneNumber</th>\n", "      <th>PracticeFaxNumber</th>\n", "      <th>...</th>\n", "      <th>is_InitialJsonValid</th>\n", "      <th>is_FinalJsonValid</th>\n", "      <th>is_JsonModified</th>\n", "      <th>is_KeyValid</th>\n", "      <th>TreatmentAmountIncVAT_clean</th>\n", "      <th>InvoiceTotalIncVAT_clean</th>\n", "      <th>ClaimInvoiceTotalIncVAT_clean</th>\n", "      <th>InvoiceSum</th>\n", "      <th>ClaimSum</th>\n", "      <th>TreatmentSum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>22</td>\n", "      <td>13bd981b-e785-43b0-a817-3dc7196c7d21</td>\n", "      <td>{'header': 'Mitcham Pet Hospital | Dr Ho<PERSON>n S...</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Dr <PERSON>, BVSc</td>\n", "      <td><PERSON>am <PERSON></td>\n", "      <td>599 Whitehorse Road, Mitcham VIC 3132</td>\n", "      <td>***********</td>\n", "      <td>9874 6151</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>83.60</td>\n", "      <td>83.60</td>\n", "      <td>181.50</td>\n", "      <td>181.50</td>\n", "      <td>363.00</td>\n", "      <td>181.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>53</td>\n", "      <td>14fd2d17-3995-42b2-b4f9-0c1264e7b555</td>\n", "      <td>{'header': 'Maribyrnong Veterinary Clinic, 95a...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Maribyrnong Veterinary Clinic</td>\n", "      <td>95a <PERSON>mond Rd Maidstone Vic 3012</td>\n", "      <td>**************</td>\n", "      <td>(03) 9318 3349</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>41.20</td>\n", "      <td>41.20</td>\n", "      <td>2410.51</td>\n", "      <td>215.21</td>\n", "      <td>7231.53</td>\n", "      <td>215.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>54</td>\n", "      <td>14fd2d17-3995-42b2-b4f9-0c1264e7b555</td>\n", "      <td>{'header': 'Maribyrnong Veterinary Clinic, 95a...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Maribyrnong Veterinary Clinic</td>\n", "      <td>95a <PERSON>mond Rd Maidstone Vic 3012</td>\n", "      <td>**************</td>\n", "      <td>(03) 9318 3349</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>41.20</td>\n", "      <td>41.20</td>\n", "      <td>2410.51</td>\n", "      <td>215.21</td>\n", "      <td>7231.53</td>\n", "      <td>215.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>229</td>\n", "      <td>1e8a59ad-73a1-4ae5-80b4-687f69484bc1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>281</td>\n", "      <td>23269376-d7ab-4adf-8b20-91921e49098a</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>778</th>\n", "      <td>2421</td>\n", "      <td>a1726f83-e8e3-453e-b1d8-76c7307b174e</td>\n", "      <td>{'header': 'COLLAROY VETERINARY SERVICES, 1185...</td>\n", "      <td><PERSON></td>\n", "      <td>Mr <PERSON></td>\n", "      <td>COLLAROY VETERINARY SERVICES</td>\n", "      <td>1185 PITTWATER RD COLLAROY 2097</td>\n", "      <td>**************</td>\n", "      <td>PH: 9971-8487</td>\n", "      <td>FX: 9971-6708</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>280.00</td>\n", "      <td>735.40</td>\n", "      <td>1140.40</td>\n", "      <td>4942.40</td>\n", "      <td>10263.60</td>\n", "      <td>1140.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>811</th>\n", "      <td>2533</td>\n", "      <td>0e4a495c-b6d8-47a5-8fe4-94040434dc96</td>\n", "      <td>{'header': 'RSPCA Victoria - ADOPT - VET CARE ...</td>\n", "      <td><PERSON><PERSON>gy</td>\n", "      <td><PERSON></td>\n", "      <td>RSPCA Burwood East</td>\n", "      <td>3 Burwood Hwy, Burwood East VIC 3151</td>\n", "      <td>ABN **************</td>\n", "      <td>P: (03) 9224 2222</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>97.96</td>\n", "      <td>97.96</td>\n", "      <td>165.90</td>\n", "      <td>429.76</td>\n", "      <td>829.50</td>\n", "      <td>201.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>820</th>\n", "      <td>2576</td>\n", "      <td>100edbdc-c8ed-42da-85fc-e6b6b66c0383</td>\n", "      <td>{'header': 'Vet HQ ABN: *********** Ms <PERSON><PERSON> D...</td>\n", "      <td><PERSON></td>\n", "      <td>Ms <PERSON><PERSON></td>\n", "      <td>Vet HQ</td>\n", "      <td>Level 1, 46 Queen Street Woollahra NSW 2025</td>\n", "      <td>***********</td>\n", "      <td>9326 1255</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>66.00</td>\n", "      <td>132.00</td>\n", "      <td>282.00</td>\n", "      <td>714.00</td>\n", "      <td>1410.00</td>\n", "      <td>282.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>828</th>\n", "      <td>2607</td>\n", "      <td>118b605a-a3d1-4459-8abd-74e3306f2cb6</td>\n", "      <td>{'header': 'Maribyrnong Veterinary Clinic, 95a...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Maribyrnong Veterinary Clinic</td>\n", "      <td>95a <PERSON>mond Rd Maidstone Vic 3012</td>\n", "      <td>**************</td>\n", "      <td>(03) 9318 3349</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>41.20</td>\n", "      <td>41.20</td>\n", "      <td>2410.51</td>\n", "      <td>215.21</td>\n", "      <td>7231.53</td>\n", "      <td>215.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>829</th>\n", "      <td>2608</td>\n", "      <td>118b605a-a3d1-4459-8abd-74e3306f2cb6</td>\n", "      <td>{'header': 'Maribyrnong Veterinary Clinic, 95a...</td>\n", "      <td><PERSON></td>\n", "      <td>None</td>\n", "      <td>Maribyrnong Veterinary Clinic</td>\n", "      <td>95a <PERSON>mond Rd Maidstone Vic 3012</td>\n", "      <td>**************</td>\n", "      <td>(03) 9318 3349</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>41.20</td>\n", "      <td>41.20</td>\n", "      <td>2410.51</td>\n", "      <td>215.21</td>\n", "      <td>7231.53</td>\n", "      <td>215.21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>74 rows × 43 columns</p>\n", "</div>"], "text/plain": ["     index                              FileName  \\\n", "10      22  13bd981b-e785-43b0-a817-3dc7196c7d21   \n", "18      53  14fd2d17-3995-42b2-b4f9-0c1264e7b555   \n", "19      54  14fd2d17-3995-42b2-b4f9-0c1264e7b555   \n", "77     229  1e8a59ad-73a1-4ae5-80b4-687f69484bc1   \n", "99     281  23269376-d7ab-4adf-8b20-91921e49098a   \n", "..     ...                                   ...   \n", "778   2421  a1726f83-e8e3-453e-b1d8-76c7307b174e   \n", "811   2533  0e4a495c-b6d8-47a5-8fe4-94040434dc96   \n", "820   2576  100edbdc-c8ed-42da-85fc-e6b6b66c0383   \n", "828   2607  118b605a-a3d1-4459-8abd-74e3306f2cb6   \n", "829   2608  118b605a-a3d1-4459-8abd-74e3306f2cb6   \n", "\n", "                                      DocumentMetadata   AnimalName  \\\n", "10   {'header': 'Mitcham Pet Hospital | Dr <PERSON> S...  <PERSON><PERSON>   \n", "18   {'header': 'Maribyrnong Veterinary Clinic, 95a...       Archie   \n", "19   {'header': 'Maribyrnong Veterinary Clinic, 95a...       Archie   \n", "77                                                None         None   \n", "99                                                None         None   \n", "..                                                 ...          ...   \n", "778  {'header': 'COLLAROY VETERINARY SERVICES, 1185...        <PERSON>   \n", "811  {'header': 'RSPCA Victoria - ADOPT - VET CARE ...        <PERSON><PERSON>gy   \n", "820  {'header': 'Vet HQ ABN: *********** Ms <PERSON><PERSON>...      Freddie   \n", "828  {'header': 'Maribyrnong Veterinary Clinic, 95a...       Archie   \n", "829  {'header': 'Maribyrnong Veterinary Clinic, 95a...       Archie   \n", "\n", "                      VetName                   PracticeName  \\\n", "10   Dr <PERSON><PERSON><PERSON> DVM, BVSc           Mitcham Pet Hospital   \n", "18                       None  Maribyrnong Veterinary Clinic   \n", "19                       None  Maribyrnong Veterinary Clinic   \n", "77                       None                           None   \n", "99                       None                           None   \n", "..                        ...                            ...   \n", "778             Mr <PERSON> Jeffreys   COLLAROY VETERINARY SERVICES   \n", "811          <PERSON>             RSPCA Burwood East   \n", "820        Ms <PERSON><PERSON>                         Vet HQ   \n", "828                      None  Maribyrnong Veterinary Clinic   \n", "829                      None  Maribyrnong Veterinary Clinic   \n", "\n", "                                 PracticeAddress         PracticeABN  \\\n", "10         599 Whitehorse Road, Mitcham VIC 3132         ***********   \n", "18            95a Rosamond Rd Maidstone Vic 3012      **************   \n", "19            95a Rosamond Rd Maidstone Vic 3012      **************   \n", "77                                          None                None   \n", "99                                          None                None   \n", "..                                           ...                 ...   \n", "778              1185 PITTWATER RD COLLAROY 2097      **************   \n", "811         3 Burwood Hwy, Burwood East VIC 3151  ABN **************   \n", "820  Level 1, 46 Queen Street Woollahra NSW 2025         ***********   \n", "828           95a Rosamond Rd Maidstone Vic 3012      **************   \n", "829           95a Rosamond Rd Maidstone Vic 3012      **************   \n", "\n", "    PracticePhoneNumber PracticeFaxNumber  ... is_InitialJsonValid  \\\n", "10            9874 6151              None  ...                True   \n", "18       (03) 9318 3349              None  ...               <PERSON>alse   \n", "19       (03) 9318 3349              None  ...               <PERSON>alse   \n", "77                 None              None  ...               <PERSON><PERSON>e   \n", "99                 None              None  ...               False   \n", "..                  ...               ...  ...                 ...   \n", "778       PH: 9971-8487     FX: 9971-6708  ...                True   \n", "811   P: (03) 9224 2222              None  ...                True   \n", "820           9326 1255              None  ...                True   \n", "828      (03) 9318 3349              None  ...               <PERSON><PERSON>e   \n", "829      (03) 9318 3349              None  ...               <PERSON><PERSON>e   \n", "\n", "    is_FinalJsonValid is_JsonModified is_KeyValid TreatmentAmountIncVAT_clean  \\\n", "10               True           False        True                       83.60   \n", "18               True            True        True                       41.20   \n", "19               True            True        True                       41.20   \n", "77              False           False        None                         NaN   \n", "99              False           False        None                         NaN   \n", "..                ...             ...         ...                         ...   \n", "778              True           False        True                      280.00   \n", "811              True           False        True                       97.96   \n", "820              True           False        True                       66.00   \n", "828              True            True        True                       41.20   \n", "829              True            True        True                       41.20   \n", "\n", "    InvoiceTotalIncVAT_clean ClaimInvoiceTotalIncVAT_clean InvoiceSum  \\\n", "10                     83.60                        181.50     181.50   \n", "18                     41.20                       2410.51     215.21   \n", "19                     41.20                       2410.51     215.21   \n", "77                       NaN                           NaN       0.00   \n", "99                       NaN                           NaN       0.00   \n", "..                       ...                           ...        ...   \n", "778                   735.40                       1140.40    4942.40   \n", "811                    97.96                        165.90     429.76   \n", "820                   132.00                        282.00     714.00   \n", "828                    41.20                       2410.51     215.21   \n", "829                    41.20                       2410.51     215.21   \n", "\n", "     ClaimSum TreatmentSum  \n", "10     363.00       181.50  \n", "18    7231.53       215.21  \n", "19    7231.53       215.21  \n", "77       0.00         0.00  \n", "99       0.00         0.00  \n", "..        ...          ...  \n", "778  10263.60      1140.40  \n", "811    829.50       201.13  \n", "820   1410.00       282.00  \n", "828   7231.53       215.21  \n", "829   7231.53       215.21  \n", "\n", "[74 rows x 43 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["invoice_lvl[invoice_lvl['InvoiceIndex']!=1]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fuzzy Matching Rows for InvoiceNo: 100%|██████████| 833/833 [00:01<00:00, 489.88it/s]\n"]}], "source": ["invoice_lvl = result_df_final.drop_duplicates(['FileName','InvoiceNo']).reset_index()\n", "\n", "true_invoice = pd.read_excel(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_merged.xlsx')\n", "true_invoice['FileName'] = true_invoice['DocFile'].apply(lambda x: x.split('.')[0])\n", "invoice_lvl_merge = invoice_lvl.merge(true_invoice,on='FileName', how='left')\n", "\n", "invoice_lvl_merge['AmountCheck_1'] = check_column_difference(invoice_lvl_merge, 'ClaimInvoiceTotalIncVAT_clean', 'Amount Claimed (UPM)')\n", "invoice_lvl_merge['AmountCheck_2'] = check_column_difference(invoice_lvl_merge, 'ClaimSum', 'Amount Claimed (UPM)')\n", "invoice_lvl_merge['AmountCheck_3'] = check_column_difference(invoice_lvl_merge, 'InvoiceSum', 'Amount Claimed (UPM)')\n", "invoice_lvl_merge['AmountCheck_4'] = check_column_difference(invoice_lvl_merge, 'TreatmentSum', 'Amount Claimed (UPM)')\n", "invoice_lvl_merge['ClaimTmtEqual_check'] =  check_column_difference(invoice_lvl_merge, 'ClaimInvoiceTotalIncVAT_clean', 'TreatmentSum')\n", "\n", "invoice_merge = pd.merge(invoice_lvl_merge,ocr,left_on='FileName', right_on='filename', how='left')\n", "\n", "import ast\n", "invoice_merge['Text'] =invoice_merge['Text'].apply(ast.literal_eval)\n", "invoice_merge['Score'] = invoice_merge['Score'].apply(ast.literal_eval)\n", "\n", "invoice_fuzzy_df = fuzzy_match_multiple_columns(invoice_merge, ['InvoiceNo'], 'Text', 'Score')\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["\n", "def compare_multiple_columns_on_key(df1, df2, key, cols_df1, cols_df2, amount_cols=None):\n", "    if amount_cols is None:\n", "        amount_cols = []\n", "\n", "    # Validate input\n", "    if not all(k in df1.columns and k in df2.columns for k in key):\n", "        raise ValueError(f\"One or more key columns '{key}' not found in the dataframes\")\n", "    if len(cols_df1) != len(cols_df2):\n", "        raise ValueError(\"Lists cols_df1 and cols_df2 must have the same length\")\n", "\n", "    # Validate column existence\n", "    for col in cols_df1:\n", "        if col not in df1.columns:\n", "            raise ValueError(f\"Column '{col}' not found in df1\")\n", "    for col in cols_df2:\n", "        if col not in df2.columns:\n", "            raise ValueError(f\"Column '{col}' not found in df2\")\n", "\n", "    # Merge df1 and df2 on the key column(s)\n", "    merged_df = pd.merge(df1, df2[key + cols_df2], on=key, how='left')\n", "\n", "    # Function to compare amounts with tolerance\n", "    def compare_amounts(val1, val2):\n", "        try:\n", "            # Check if val2 is a list and compare each element in val2 to val1\n", "            if isinstance(val2, list):\n", "                return any(abs(float(val1) - float(item)) <= 0.05 for item in val2)\n", "            # Otherwise, just compare val1 and val2 directly\n", "            else:\n", "                return abs(float(val1) - float(val2)) < 0.05\n", "\n", "        except (ValueErro<PERSON>, TypeError):\n", "            return False\n", "\n", "    # Iterate over the column pairs and perform comparisons\n", "    for col_df1, col_df2 in zip(cols_df1, cols_df2):\n", "        if col_df1 in amount_cols:\n", "            # Special comparison for amount columns\n", "            compare = lambda row: compare_amounts(row[col_df1], row[col_df2])\n", "            # print( lambda row:  row[col_df1], row[col_df2])\n", "        else:\n", "            # Standard comparison for other columns\n", "            compare = lambda row: row[col_df1] in row[col_df2] if isinstance(row[col_df2], list) else row[col_df1] == row[col_df2]\n", "\n", "        # Apply comparison for each row and store the result\n", "        df1[f'{col_df1}_check'] = merged_df.apply(compare, axis=1).astype(int)\n", "    return df1\n", "\n", "# Example usage\n", "keys = ['ClaimNo']\n", "df1_check = ['InvoiceNo']\n", "df2_check = ['Invoice Number']\n", "amount_cols = None # Specify which columns are amount columns\n", "compare_df = compare_multiple_columns_on_key(invoice_fuzzy_df, claim_invoice_no, keys, df1_check, df2_check, amount_cols)\n", "compare_df = compare_df.merge(claim_invoice_no[['ClaimNo','Invoice Number']], on='ClaimNo', how='left')"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzz.partial_ratio( \"Invoice Number538372\",\"538372\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["93"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["fuzz.partial_ratio(\"0104022\t\", \"INVOICE 0104022\")\n", "                        "]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim Number</th>\n", "      <th>InvoiceIndex</th>\n", "      <th>Invoice Number</th>\n", "      <th>InvoiceNo</th>\n", "      <th>InvoiceNo_BestMatch</th>\n", "      <th>InvoiceNo_MatchConfidenceScore</th>\n", "      <th>InvoiceNo_FuzzyMatchScore</th>\n", "      <th>InvoiceNo_check</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>********</td>\n", "      <td>1</td>\n", "      <td>[0104022]</td>\n", "      <td>0104022</td>\n", "      <td>INVOICE 0104022</td>\n", "      <td>0.945180</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>C6942907</td>\n", "      <td>1</td>\n", "      <td>[238386]</td>\n", "      <td>238386</td>\n", "      <td>TaxInvoice No:238386</td>\n", "      <td>0.943547</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>C6942865</td>\n", "      <td>1</td>\n", "      <td>[440492]</td>\n", "      <td>440492</td>\n", "      <td>Tax Invoice No:440492</td>\n", "      <td>0.935325</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>C6944651</td>\n", "      <td>1</td>\n", "      <td>[260097]</td>\n", "      <td>260097</td>\n", "      <td>Invoice No: 260097-Issued on Thu 4/Jan/2024</td>\n", "      <td>0.948899</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>C6942535</td>\n", "      <td>1</td>\n", "      <td>[453474]</td>\n", "      <td>453474</td>\n", "      <td>Tax Invoice No:453474</td>\n", "      <td>0.938713</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>814</th>\n", "      <td>C6944463</td>\n", "      <td>1</td>\n", "      <td>[163016]</td>\n", "      <td>163016</td>\n", "      <td>ivoice No:163016</td>\n", "      <td>0.914476</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>821</th>\n", "      <td>C6940073</td>\n", "      <td>1</td>\n", "      <td>[ADJ7027580]</td>\n", "      <td>ADJ7027580</td>\n", "      <td>TAX INVOICEADJ7027580</td>\n", "      <td>0.946778</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>822</th>\n", "      <td>C6944636</td>\n", "      <td>1</td>\n", "      <td>[282140]</td>\n", "      <td>282140</td>\n", "      <td>Invoice Number:282140</td>\n", "      <td>0.948476</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>824</th>\n", "      <td>C6942178</td>\n", "      <td>1</td>\n", "      <td>[1013075]</td>\n", "      <td>1013075</td>\n", "      <td>1013075</td>\n", "      <td>0.945476</td>\n", "      <td>100.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>825</th>\n", "      <td>C6944440</td>\n", "      <td>1</td>\n", "      <td>[445737]</td>\n", "      <td>200671</td>\n", "      <td>00</td>\n", "      <td>0.879853</td>\n", "      <td>100.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>65 rows × 8 columns</p>\n", "</div>"], "text/plain": ["    Claim Number InvoiceIndex Invoice Number   InvoiceNo  \\\n", "11      ********            1      [0104022]     0104022   \n", "36      C6942907            1       [238386]      238386   \n", "62      C6942865            1       [440492]      440492   \n", "93      C6944651            1       [260097]      260097   \n", "123     C6942535            1       [453474]      453474   \n", "..           ...          ...            ...         ...   \n", "814     C6944463            1       [163016]      163016   \n", "821     C6940073            1   [ADJ7027580]  ADJ7027580   \n", "822     C6944636            1       [282140]      282140   \n", "824     C6942178            1      [1013075]     1013075   \n", "825     C6944440            1       [445737]      200671   \n", "\n", "                             InvoiceNo_BestMatch  \\\n", "11                               INVOICE 0104022   \n", "36                          TaxInvoice No:238386   \n", "62                         Tax Invoice No:440492   \n", "93   Invoice No: 260097-Issued on Thu 4/Jan/2024   \n", "123                        Tax Invoice No:453474   \n", "..                                           ...   \n", "814                             ivoice No:163016   \n", "821                        TAX INVOICEADJ7027580   \n", "822                        Invoice Number:282140   \n", "824                                      1013075   \n", "825                                           00   \n", "\n", "     InvoiceNo_MatchConfidenceScore  InvoiceNo_FuzzyMatchScore  \\\n", "11                         0.945180                      100.0   \n", "36                         0.943547                      100.0   \n", "62                         0.935325                      100.0   \n", "93                         0.948899                      100.0   \n", "123                        0.938713                      100.0   \n", "..                              ...                        ...   \n", "814                        0.914476                      100.0   \n", "821                        0.946778                      100.0   \n", "822                        0.948476                      100.0   \n", "824                        0.945476                      100.0   \n", "825                        0.879853                      100.0   \n", "\n", "     InvoiceNo_check  \n", "11                 1  \n", "36                 1  \n", "62                 1  \n", "93                 1  \n", "123                1  \n", "..               ...  \n", "814                1  \n", "821                1  \n", "822                1  \n", "824                1  \n", "825                0  \n", "\n", "[65 rows x 8 columns]"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["compare_df[(compare_df['InvoiceNo_MatchConfidenceScore']<0.95)& (compare_df['Invoice Number'].isnull()==False)][['Claim Number','InvoiceIndex','Invoice Number','InvoiceNo','InvoiceNo_BestMatch',\n", "       'InvoiceNo_MatchConfidenceScore', 'InvoiceNo_FuzzyMatchScore','InvoiceNo_check']]"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim Number</th>\n", "      <th>InvoiceIndex</th>\n", "      <th>Invoice Number</th>\n", "      <th>InvoiceNo</th>\n", "      <th>InvoiceNo_BestMatch</th>\n", "      <th>InvoiceNo_MatchConfidenceScore</th>\n", "      <th>InvoiceNo_FuzzyMatchScore</th>\n", "      <th>InvoiceNo_check</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>********</td>\n", "      <td>1</td>\n", "      <td>[1422863]</td>\n", "      <td>1422863</td>\n", "      <td>ABN 3319085692</td>\n", "      <td>0.985286</td>\n", "      <td>43.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>632</th>\n", "      <td>C6941604</td>\n", "      <td>2</td>\n", "      <td>[nan, 1970735]</td>\n", "      <td>1971902-PCP</td>\n", "      <td>Invoice No: 1971902 - PCP</td>\n", "      <td>0.979809</td>\n", "      <td>82.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>633</th>\n", "      <td>C6941604</td>\n", "      <td>3</td>\n", "      <td>[nan, 1970735]</td>\n", "      <td>1972780-LDA</td>\n", "      <td>Invoice No: 1972780 - LDA</td>\n", "      <td>0.994139</td>\n", "      <td>82.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>773</th>\n", "      <td>C6942333</td>\n", "      <td>1</td>\n", "      <td>[64210]</td>\n", "      <td>64210</td>\n", "      <td>ABN: **************</td>\n", "      <td>0.977183</td>\n", "      <td>60.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Claim Number InvoiceIndex  Invoice Number    InvoiceNo  \\\n", "254     ********            1       [1422863]      1422863   \n", "632     C6941604            2  [nan, 1970735]  1971902-PCP   \n", "633     C6941604            3  [nan, 1970735]  1972780-LDA   \n", "773     C6942333            1         [64210]        64210   \n", "\n", "           InvoiceNo_BestMatch  InvoiceNo_MatchConfidenceScore  \\\n", "254             ABN 3319085692                        0.985286   \n", "632  Invoice No: 1971902 - PCP                        0.979809   \n", "633  Invoice No: 1972780 - LDA                        0.994139   \n", "773        ABN: **************                        0.977183   \n", "\n", "     InvoiceNo_FuzzyMatchScore  InvoiceNo_check  \n", "254                       43.0                1  \n", "632                       82.0                0  \n", "633                       82.0                0  \n", "773                       60.0                1  "]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["compare_df[(compare_df['InvoiceNo_FuzzyMatchScore']<90)][['Claim Number','InvoiceIndex','Invoice Number','InvoiceNo','InvoiceNo_BestMatch',\n", "       'InvoiceNo_MatchConfidenceScore', 'InvoiceNo_FuzzyMatchScore','InvoiceNo_check']]"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Claim Number</th>\n", "      <th>InvoiceIndex</th>\n", "      <th>Invoice Number</th>\n", "      <th>InvoiceNo</th>\n", "      <th>InvoiceNo_BestMatch</th>\n", "      <th>InvoiceNo_MatchConfidenceScore</th>\n", "      <th>InvoiceNo_FuzzyMatchScore</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>********</td>\n", "      <td>1</td>\n", "      <td>[401233]</td>\n", "      <td>#601065</td>\n", "      <td>#601065</td>\n", "      <td>0.999929</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>********</td>\n", "      <td>1</td>\n", "      <td>[538373]</td>\n", "      <td>538372</td>\n", "      <td>Invoice Number538372</td>\n", "      <td>0.969638</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>C6942348</td>\n", "      <td>1</td>\n", "      <td>[222903]</td>\n", "      <td>5195</td>\n", "      <td>1</td>\n", "      <td>0.988584</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>C6944544</td>\n", "      <td>1</td>\n", "      <td>[1011279]</td>\n", "      <td>688329</td>\n", "      <td>688329</td>\n", "      <td>0.999760</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>C6941596</td>\n", "      <td>1</td>\n", "      <td>[324965]</td>\n", "      <td>000247</td>\n", "      <td>000247</td>\n", "      <td>0.994449</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>772</th>\n", "      <td>C6943845</td>\n", "      <td>1</td>\n", "      <td>[486087]</td>\n", "      <td>#486087</td>\n", "      <td>#486087</td>\n", "      <td>0.998494</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>789</th>\n", "      <td>C6941879</td>\n", "      <td>1</td>\n", "      <td>[1011733]</td>\n", "      <td>688733</td>\n", "      <td>688733</td>\n", "      <td>0.999779</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>795</th>\n", "      <td>C6940406</td>\n", "      <td>1</td>\n", "      <td>[N0E047D39171C]</td>\n", "      <td>N0e047d39b71c</td>\n", "      <td>TAX INVOICE 29 Feb 2024# N0e047d39b71c</td>\n", "      <td>0.983121</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>796</th>\n", "      <td>C6943362</td>\n", "      <td>1</td>\n", "      <td>[1449533AE]</td>\n", "      <td>6094AE</td>\n", "      <td>Ref-6094AE-17/01/2024-13:46:31</td>\n", "      <td>0.906876</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>825</th>\n", "      <td>C6944440</td>\n", "      <td>1</td>\n", "      <td>[445737]</td>\n", "      <td>200671</td>\n", "      <td>00</td>\n", "      <td>0.879853</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>111 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    Claim Number InvoiceIndex   Invoice Number      InvoiceNo  \\\n", "2       ********            1         [401233]        #601065   \n", "9       ********            1         [538373]         538372   \n", "28      C6942348            1         [222903]           5195   \n", "50      C6944544            1        [1011279]         688329   \n", "53      C6941596            1         [324965]         000247   \n", "..           ...          ...              ...            ...   \n", "772     C6943845            1         [486087]        #486087   \n", "789     C6941879            1        [1011733]         688733   \n", "795     C6940406            1  [N0E047D39171C]  N0e047d39b71c   \n", "796     C6943362            1      [1449533AE]         6094AE   \n", "825     C6944440            1         [445737]         200671   \n", "\n", "                        InvoiceNo_BestMatch  InvoiceNo_MatchConfidenceScore  \\\n", "2                                   #601065                        0.999929   \n", "9                      Invoice Number538372                        0.969638   \n", "28                                        1                        0.988584   \n", "50                                   688329                        0.999760   \n", "53                                   000247                        0.994449   \n", "..                                      ...                             ...   \n", "772                                 #486087                        0.998494   \n", "789                                  688733                        0.999779   \n", "795  TAX INVOICE 29 Feb 2024# N0e047d39b71c                        0.983121   \n", "796          Ref-6094AE-17/01/2024-13:46:31                        0.906876   \n", "825                                      00                        0.879853   \n", "\n", "     InvoiceNo_FuzzyMatchScore  \n", "2                        100.0  \n", "9                        100.0  \n", "28                       100.0  \n", "50                       100.0  \n", "53                       100.0  \n", "..                         ...  \n", "772                      100.0  \n", "789                      100.0  \n", "795                      100.0  \n", "796                      100.0  \n", "825                      100.0  \n", "\n", "[111 rows x 7 columns]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["compare_df[(compare_df['InvoiceNo_check']==0) & (compare_df['Invoice Number'].isnull()==False)][['Claim Number','InvoiceIndex','Invoice Number','InvoiceNo','InvoiceNo_BestMatch',\n", "       'InvoiceNo_MatchConfidenceScore', 'InvoiceNo_FuzzyMatchScore']]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["compare_df[['filename','Claim Number']].to_csv(r'C:\\Users\\<USER>\\Documents\\Projects\\OCR_inhouse\\data\\final\\preprocess\\invoices_010324_claimno.csv')"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["Series([], Name: Claim Number, dtype: object)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["compare_df[compare_df['Invoice Number'].isnull()]['Claim Number']"]}], "metadata": {"kernelspec": {"display_name": "claryt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}