This is a repository for our proof of concept OCR In-House

## Donut model
Donut is used for document triage.
1. Data preparation is done in `donut/src/donut_prepare.py`, including:
    - Convert all the images in invoice, medical_record and other folders under unclassified folder to jpg format.
    - Move all the jpg images to the corresponding folder in `donut/data/csp_dataset`
    - Splitting the data into train and test sets
2. Run `donut/src/donut_train.py` to train the model